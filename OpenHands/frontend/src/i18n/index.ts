import i18n from "i18next";
import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";

export const AvailableLanguages = [
  { label: "English", value: "en" },
  { label: "日本語", value: "ja" },
  { label: "简体中文", value: "zh-CN" },
  { label: "繁體中文", value: "zh-TW" },
  { label: "한국어", value: "ko-KR" },
  { label: "Norsk", value: "no" },
  { label: "Arabic", value: "ar" },
  { label: "Deutsch", value: "de" },
  { label: "Français", value: "fr" },
  { label: "Italiano", value: "it" },
  { label: "Português", value: "pt" },
  { label: "Español", value: "es" },
  { label: "Türkçe", value: "tr" },
  { label: "Українська", value: "uk" },
];

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: "en",
    debug: import.meta.env.NODE_ENV === "development",
    load: "currentOnly",
    // 语言映射配置，解决zh -> zh-CN的映射问题
    supportedLngs: ["en", "ja", "zh-CN", "zh-TW", "ko-KR", "no", "ar", "de", "fr", "it", "pt", "es", "tr", "uk"],
    // 语言检测配置
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng',
    },
    // 后端配置
    backend: {
      loadPath: '/locales/{{lng}}/translation.json',
    },
    // 语言映射
    load: 'languageOnly',
    preload: ['en'],
    // 处理语言代码映射
    cleanCode: true,
    // 语言回退配置
    nonExplicitSupportedLngs: true,
  });

export default i18n;
