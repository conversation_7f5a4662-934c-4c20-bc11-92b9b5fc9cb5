import{g as c}from"./map-provider-g8SgAMsv.js";const _=t=>{const e=t.get("llm-provider-input")?.toString(),s=e?c(e):void 0,r=t.get("llm-model-input")?.toString(),o=`${s}/${r}`,n=t.get("llm-api-key-input")?.toString(),i=t.get("agent")?.toString(),L=t.get("language")?.toString();return{LLM_MODEL:o,LLM_API_KEY:n,AGENT:i,LANGUAGE:L}},g=t=>{const e=Array.from(t.keys()),s=e.includes("use-advanced-options");let r,o,n=!1,i,L=!0;return s&&(r=t.get("custom-model")?.toString(),o=t.get("base-url")?.toString(),n=e.includes("confirmation-mode"),n&&(i=t.get("security-analyzer")?.toString()),L=e.includes("enable-default-condenser")),{CUSTOM_LLM_MODEL:r,LLM_BASE_URL:o,CONFIRMATION_MODE:n,SECURITY_ANALYZER:i,ENABLE_DEFAULT_CONDENSER:L}},d=t=>{if(!t)return null;const e=parseFloat(t);return e&&e>=1&&Number.isFinite(e)?e:null},u=t=>{const{LLM_MODEL:e,LLM_API_KEY:s,AGENT:r,LANGUAGE:o}=_(t),{CUSTOM_LLM_MODEL:n,LLM_BASE_URL:i,CONFIRMATION_MODE:L,SECURITY_ANALYZER:l,ENABLE_DEFAULT_CONDENSER:E}=g(t);return{LLM_MODEL:n||e,LLM_API_KEY_SET:!!s,AGENT:r,LANGUAGE:o,LLM_BASE_URL:i,CONFIRMATION_MODE:L,SECURITY_ANALYZER:l,ENABLE_DEFAULT_CONDENSER:E,llm_api_key:s}};export{u as e,d as p};
