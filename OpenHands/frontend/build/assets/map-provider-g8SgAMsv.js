const r={openai:"OpenAI",azure:"Azure",azure_ai:"Azure AI Studio",vertex_ai:"VertexAI",palm:"PaLM",gemini:"Gemini",anthropic:"Anthropic",sagemaker:"AWS SageMaker",bedrock:"AWS Bedrock",mistral:"Mistral AI",anyscale:"Anyscale",databricks:"Databricks",ollama:"Ollama",perlexity:"Perplexity AI",friendliai:"FriendliAI",groq:"Groq",fireworks_ai:"Fireworks AI",cloudflare:"Cloudflare Workers AI",deepinfra:"DeepInfra",ai21:"AI21",replicate:"Replicate",voyage:"Voyage AI",openrouter:"OpenRouter",openhands:"OpenHands"},n=e=>Object.keys(r).includes(e)?r[e]:e,t=e=>{const a=Object.entries(r).find(([,i])=>i===e);return a?a[0]:e};export{t as g,n as m};
