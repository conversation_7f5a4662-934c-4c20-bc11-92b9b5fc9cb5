const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-D_FgxOY2.js","assets/features-animation-CM7oex5Y.js","assets/chunk-S6H5EOGR-Bwn62IP6.js","assets/chunk-C37GKA54-CBbYr_fP.js","assets/utils-KsbccAr1.js","assets/index-yKbcr7Pf.js","assets/preload-helper-BXl3LOEh.js"])))=>i.map(i=>d[i]);
import{r as ae,j as d,R as We,h as Ya,f as Rt,g as Ja}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as ta}from"./react-redux-B5osdedR.js";import{p as Za}from"./module-5laXsVNO.js";import{M as aa,J as Qa,f as fa}from"./constants-DCYeMzG2.js";import{e as Xa,f as er,g as tr}from"./index-DvLMSsrd.js";import{b as ar}from"./index-Do49u1Ze.js";import{c as ut,a as rr}from"./utils-KsbccAr1.js";import{I as ne}from"./declaration-xyc84-tJ.js";import{u as yt}from"./useTranslation-BG59QWH_.js";import{M as wa}from"./modal-backdrop-ve4Sk5I2.js";import{u as nr}from"./useQuery-Cu2nkJ8V.js";import{O as ka}from"./open-hands-Ce72Fmtl.js";import{u as sr}from"./use-conversation-id-0JHAicdF.js";import{A as Bt}from"./agent-state-CFaY3go2.js";import{B as or}from"./brand-button-3Z8FN4qR.js";import{F as ir,G as lr,H as cr,A as Lt,I as zt,y as wt,J as Xt,K as ur,M as dr,o as ea,N as pr,O as ha,L as ma,C as ga,T as br,r as fr,P as hr,Q as mr,R as gr,f as vr,u as yr,m as xr,c as jr,S as Er,U as wr,v as kr,x as Dt,V as Cr,E as Or,W as Sr,X as Nr}from"./chunk-S6H5EOGR-Bwn62IP6.js";import{_ as _r}from"./preload-helper-BXl3LOEh.js";import{t as Mr}from"./vscode-url-helper-5Tt6LlE2.js";import{u as Ar,i as Ir}from"./ws-client-provider-Dmsj8lkD.js";var va=ir({slots:{wrapper:["flex","w-screen","h-[100dvh]","fixed","inset-0","z-50","overflow-x-auto","justify-center","h-[--visual-viewport-height]"],base:["flex","flex-col","relative","bg-white","z-50","w-full","box-border","bg-content1","outline-solid outline-transparent","mx-1","my-1","sm:mx-6","sm:my-16"],backdrop:"z-50",header:"flex py-4 px-6 flex-initial text-large font-semibold",body:"flex flex-1 flex-col gap-3 px-6 py-2",footer:"flex flex-row gap-2 px-6 py-4 justify-end",closeButton:["absolute","appearance-none","outline-solid outline-transparent","select-none","top-1","end-1","p-2","text-foreground-500","rounded-full","hover:bg-default-100","active:bg-default-200","tap-highlight-transparent",...lr]},variants:{size:{xs:{base:"max-w-xs"},sm:{base:"max-w-sm"},md:{base:"max-w-md"},lg:{base:"max-w-lg"},xl:{base:"max-w-xl"},"2xl":{base:"max-w-2xl"},"3xl":{base:"max-w-3xl"},"4xl":{base:"max-w-4xl"},"5xl":{base:"max-w-5xl"},full:{base:"my-0 mx-0 sm:mx-0 sm:my-0 max-w-full h-[100dvh] min-h-[100dvh] !rounded-none"}},radius:{none:{base:"rounded-none"},sm:{base:"rounded-small"},md:{base:"rounded-medium"},lg:{base:"rounded-large"}},placement:{auto:{wrapper:"items-end sm:items-center"},center:{wrapper:"items-center sm:items-center"},top:{wrapper:"items-start sm:items-start"},"top-center":{wrapper:"items-start sm:items-center"},bottom:{wrapper:"items-end sm:items-end"},"bottom-center":{wrapper:"items-end sm:items-center"}},shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},backdrop:{transparent:{backdrop:"hidden"},opaque:{backdrop:"bg-overlay/50 backdrop-opacity-disabled"},blur:{backdrop:"backdrop-blur-md backdrop-saturate-150 bg-overlay/30"}},scrollBehavior:{normal:{base:"overflow-y-hidden"},inside:{base:"max-h-[calc(100%_-_8rem)]",body:"overflow-y-auto"},outside:{wrapper:"items-start sm:items-start overflow-y-auto",base:"my-16"}},disableAnimation:{false:{wrapper:["[--scale-enter:100%]","[--scale-exit:100%]","[--slide-enter:0px]","[--slide-exit:80px]","sm:[--scale-enter:100%]","sm:[--scale-exit:103%]","sm:[--slide-enter:0px]","sm:[--slide-exit:0px]"]}}},defaultVariants:{size:"md",radius:"lg",shadow:"sm",placement:"auto",backdrop:"opaque",scrollBehavior:"normal"},compoundVariants:[{backdrop:["opaque","blur"],class:{backdrop:"w-screen h-screen fixed inset-0"}}]}),[Pr,Vt]=cr({name:"ModalContext",errorMessage:"useModalContext: `context` is undefined. Seems you forgot to wrap all popover components within `<Modal />`"}),Ca=Lt((_,A)=>{const{as:D,children:$,className:W,...U}=_,{slots:Q,classNames:h,bodyId:i,setBodyMounted:w}=Vt(),S=zt(A),j=D||"div";return ae.useEffect(()=>(w(!0),()=>w(!1)),[w]),d.jsx(j,{ref:S,className:Q.body({class:wt(h?.body,W)}),id:i,...U,children:$})});Ca.displayName="HeroUI.ModalBody";var Rr=Ca,Dr={enter:{scale:"var(--scale-enter)",y:"var(--slide-enter)",opacity:1,willChange:"auto",transition:{scale:{duration:.4,ease:Xt.ease},opacity:{duration:.4,ease:Xt.ease},y:{type:"spring",bounce:0,duration:.6}}},exit:{scale:"var(--scale-exit)",y:"var(--slide-exit)",opacity:0,willChange:"transform",transition:{duration:.3,ease:Xt.ease}}},tt=typeof document<"u"&&window.visualViewport,Tr=We.createContext(!1);function Fr(){return!1}function Br(){return!0}function Lr(_){return()=>{}}function zr(){return typeof We.useSyncExternalStore=="function"?We.useSyncExternalStore(Lr,Fr,Br):ae.useContext(Tr)}function Vr(){let _=zr(),[A,D]=ae.useState(()=>_?{width:0,height:0}:ya());return ae.useEffect(()=>{let $=()=>{D(W=>{let U=ya();return U.width===W.width&&U.height===W.height?W:U})};return tt?tt.addEventListener("resize",$):window.addEventListener("resize",$),()=>{tt?tt.removeEventListener("resize",$):window.removeEventListener("resize",$)}},[]),A}function ya(){return{width:tt&&tt?.width||window.innerWidth,height:tt&&tt?.height||window.innerHeight}}var xa=()=>_r(()=>import("./index-D_FgxOY2.js"),__vite__mapDeps([0,1,2,3,4,5,6])).then(_=>_.default),Oa=_=>{const{as:A,children:D,role:$="dialog",...W}=_,{Component:U,domRef:Q,slots:h,classNames:i,motionProps:w,backdrop:S,closeButton:j,hideCloseButton:x,disableAnimation:C,getDialogProps:O,getBackdropProps:R,getCloseButtonProps:K,onClose:c}=Vt(),o=A||U||"div",l=Vr(),{dialogProps:r}=ur({role:$},Q),u=ae.isValidElement(j)?ae.cloneElement(j,K()):d.jsx("button",{...K(),children:d.jsx(dr,{})}),t=ae.useCallback(L=>{L.key==="Tab"&&L.nativeEvent.isComposing&&(L.stopPropagation(),L.preventDefault())},[]),f=O(ea(r,W)),g=d.jsxs(o,{...f,onKeyDown:pr(f.onKeyDown,t),children:[d.jsx(ha,{onDismiss:c}),!x&&u,typeof D=="function"?D(c):D,d.jsx(ha,{onDismiss:c})]}),E=ae.useMemo(()=>S==="transparent"?null:C?d.jsx("div",{...R()}):d.jsx(ma,{features:xa,children:d.jsx(ga.div,{animate:"enter",exit:"exit",initial:"exit",variants:br.fade,...R()})}),[S,C,R]),P={"--visual-viewport-height":l.height+"px"},T=C?d.jsx("div",{className:h.wrapper({class:i?.wrapper}),"data-slot":"wrapper",style:P,children:g}):d.jsx(ma,{features:xa,children:d.jsx(ga.div,{animate:"enter",className:h.wrapper({class:i?.wrapper}),"data-slot":"wrapper",exit:"exit",initial:"exit",variants:Dr,...w,style:P,children:g})});return d.jsxs("div",{tabIndex:-1,children:[E,T]})};Oa.displayName="HeroUI.ModalContent";var qr=Oa,Sa=Lt((_,A)=>{const{as:D,children:$,className:W,...U}=_,{slots:Q,classNames:h}=Vt(),i=zt(A),w=D||"footer";return d.jsx(w,{ref:i,className:Q.footer({class:wt(h?.footer,W)}),...U,children:$})});Sa.displayName="HeroUI.ModalFooter";var $r=Sa,Na=Lt((_,A)=>{const{as:D,children:$,className:W,...U}=_,{slots:Q,classNames:h,headerId:i,setHeaderMounted:w}=Vt(),S=zt(A),j=D||"header";return ae.useEffect(()=>(w(!0),()=>w(!1)),[w]),d.jsx(j,{ref:S,className:Q.header({class:wt(h?.header,W)}),id:i,...U,children:$})});Na.displayName="HeroUI.ModalHeader";var Kr=Na;function Ur(_={shouldBlockScroll:!0},A,D){let{overlayProps:$,underlayProps:W}=fr({..._,isOpen:A.isOpen,onClose:A.close},D);return hr({isDisabled:!A.isOpen||!_.shouldBlockScroll}),mr(),ae.useEffect(()=>{if(A.isOpen&&D.current)return gr([D.current])},[A.isOpen,D]),{modalProps:vr($),underlayProps:W}}function Wr(_){var A,D,$;const W=yr(),[U,Q]=xr(_,va.variantKeys),{ref:h,as:i,className:w,classNames:S,isOpen:j,defaultOpen:x,onOpenChange:C,motionProps:O,closeButton:R,isDismissable:K=!0,hideCloseButton:c=!1,shouldBlockScroll:o=!0,portalContainer:l,isKeyboardDismissDisabled:r=!1,onClose:u,...t}=U,f=i||"section",g=zt(h),E=ae.useRef(null),[P,T]=ae.useState(!1),[L,G]=ae.useState(!1),X=(D=(A=_.disableAnimation)!=null?A:W?.disableAnimation)!=null?D:!1,ie=ae.useId(),ke=ae.useId(),I=ae.useId(),ge=jr({isOpen:j,defaultOpen:x,onOpenChange:xe=>{C?.(xe),xe||u?.()}}),{modalProps:$e,underlayProps:Re}=Ur({isDismissable:K,shouldBlockScroll:o,isKeyboardDismissDisabled:r},ge,g),{buttonProps:De}=Er({onPress:ge.close},E),{isFocusVisible:He,focusProps:Ke}=wr(),rt=wt(S?.base,w),J=ae.useMemo(()=>va({...Q,disableAnimation:X}),[kr(Q),X]),Te=(xe={},dt=null)=>{var Ce;return{ref:Cr(dt,g),...ea($e,t,xe),className:J.base({class:wt(rt,xe.className)}),id:ie,"data-open":Dt(ge.isOpen),"data-dismissable":Dt(K),"aria-modal":Dt(!0),"data-placement":(Ce=_?.placement)!=null?Ce:"right","aria-labelledby":P?ke:void 0,"aria-describedby":L?I:void 0}},de=ae.useCallback((xe={})=>({className:J.backdrop({class:S?.backdrop}),...Re,...xe}),[J,S,Re]),Ue=()=>({role:"button",tabIndex:0,"aria-label":"Close","data-focus-visible":Dt(He),className:J.closeButton({class:S?.closeButton}),...ea(De,Ke)});return{Component:f,slots:J,domRef:g,headerId:ke,bodyId:I,motionProps:O,classNames:S,isDismissable:K,closeButton:R,hideCloseButton:c,portalContainer:l,shouldBlockScroll:o,backdrop:($=_.backdrop)!=null?$:"opaque",isOpen:ge.isOpen,onClose:ge.close,disableAnimation:X,setBodyMounted:G,setHeaderMounted:T,getDialogProps:Te,getBackdropProps:de,getCloseButtonProps:Ue}}var _a=Lt((_,A)=>{const{children:D,...$}=_,W=Wr({...$,ref:A}),U=d.jsx(Sr,{portalContainer:W.portalContainer,children:D});return d.jsx(Pr,{value:W,children:W.disableAnimation&&W.isOpen?U:d.jsx(Or,{children:W.isOpen?U:null})})});_a.displayName="HeroUI.Modal";var Hr=_a;function Gr({testId:_,children:A,className:D,ref:$}){return d.jsx("ul",{"data-testid":_,ref:$,className:ut("bg-tertiary rounded-md overflow-hidden",D),children:A})}function ft({children:_,testId:A,onClick:D,isDisabled:$}){return d.jsx("button",{"data-testid":A||"context-menu-list-item",type:"button",onClick:D,disabled:$,className:ut("text-sm px-4 h-10 w-full text-start hover:bg-white/10 cursor-pointer","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent text-nowrap"),children:_})}const Yr=_=>{const A=We.useRef(null);return We.useEffect(()=>{const D=$=>{A.current&&!A.current.contains($.target)&&_()};return document.addEventListener("click",D),()=>document.removeEventListener("click",D)},[]),A};function Jr({testId:_,variant:A="default",onClick:D,text:$,className:W,icon:U,type:Q="button",disabled:h,intent:i}){return d.jsxs("button",{"data-testid":_,type:Q==="submit"?"submit":"button",disabled:h,onClick:D,className:rr(A==="default"&&"text-sm font-[500] py-[10px] rounded-sm",A==="text-like"&&"text-xs leading-4 font-normal",U&&"flex items-center justify-center gap-2",h&&"opacity-50 cursor-not-allowed",W),name:i&&"intent",value:i,children:[U,$]})}function ra({title:_}){return d.jsx("span",{className:"text-xl leading-6 -tracking-[0.01em] font-semibold",children:_})}function Zr({description:_,children:A}){return d.jsx("span",{className:"text-xs text-[#A3A3A3]",children:A||_})}function cs({testId:_,title:A,description:D,buttons:$}){return d.jsxs(aa,{testID:_,children:[d.jsxs("div",{className:"flex flex-col gap-2 self-start",children:[d.jsx(ra,{title:A}),d.jsx(Zr,{description:D})]}),d.jsx("div",{className:"flex flex-col gap-2 w-full",children:$.map((W,U)=>d.jsx(Jr,{onClick:W.onClick,text:W.text,className:W.className},U))})]})}function Qr({selectedRepository:_,variant:A="default"}){return A==="compact"?d.jsx("span",{"data-testid":"conversation-card-selected-repository",className:"text-xs text-neutral-400",children:_.selected_repository}):d.jsxs("div",{className:"flex items-center gap-1",children:[_.git_provider==="github"&&d.jsx(Xa,{size:14}),_.git_provider==="gitlab"&&d.jsx(er,{}),_.git_provider==="bitbucket"&&d.jsx(tr,{}),d.jsx("span",{"data-testid":"conversation-card-selected-repository",className:"text-xs text-neutral-400",children:_.selected_repository}),d.jsx("code",{"data-testid":"conversation-card-selected-branch",className:"text-xs text-neutral-400 border border-neutral-700 rounded px-1 py-0.5 w-fit bg-neutral-800",children:_.selected_branch})]})}const Xr=_=>ae.createElement("svg",{width:18,height:18,viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",..._},ae.createElement("path",{d:"M9.04004 3.10986C12.06 3.10986 14.57 5.34986 14.98 8.25986C15.05 8.74986 15.47 9.10986 15.96 9.10986C16.57 9.10986 17.04 8.56986 16.96 7.96986C16.41 4.08986 13.07 1.10986 9.04004 1.10986C4.62004 1.10986 1.04004 4.68986 1.04004 9.10986C1.04004 13.1399 4.02004 16.4799 7.90004 17.0299C8.50004 17.1199 9.04004 16.6399 9.04004 16.0299C9.04004 15.5399 8.68004 15.1199 8.19004 15.0499C5.28004 14.6399 3.04004 12.1299 3.04004 9.10986C3.04004 5.79986 5.73004 3.10986 9.04004 3.10986Z",fill:"#60BB46"}),ae.createElement("path",{d:"M12.3504 9.11L7.40039 6.25V11.96L12.3504 9.11Z",fill:"#60BB46"})),en=_=>ae.createElement("svg",{width:18,height:18,viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",..._},ae.createElement("path",{d:"M9.87012 2.02002C9.87012 1.46773 9.4224 1.02002 8.87012 1.02002C8.31783 1.02002 7.87012 1.46773 7.87012 2.02002V8.02002C7.87012 8.5723 8.31783 9.02002 8.87012 9.02002C9.4224 9.02002 9.87012 8.5723 9.87012 8.02002V2.02002Z",fill:"#EFC818"}),ae.createElement("path",{d:"M10.8698 2.44003V2.58003C10.8698 2.95003 11.0698 3.30003 11.4098 3.45003C13.6798 4.45003 15.2198 6.82003 14.9698 9.50003C14.6998 12.49 12.0998 14.89 9.08979 14.94C5.73979 14.99 2.98979 12.28 2.98979 8.94003C2.98979 6.59003 4.34979 4.56003 6.30979 3.58003C6.63979 3.42003 6.85979 3.10003 6.85979 2.74003V2.57003C6.85979 1.88003 6.13979 1.45003 5.50979 1.75003C2.43979 3.23003 0.449793 6.64003 1.13979 10.43C1.70979 13.59 4.23979 16.16 7.38979 16.78C12.5098 17.78 16.9998 13.88 16.9998 8.94003C16.9998 5.63003 14.9898 2.80003 12.1198 1.58003C11.5298 1.33003 10.8698 1.80003 10.8698 2.44003Z",fill:"#EFC818"})),tn=_=>ae.createElement("svg",{width:18,height:18,viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",..._},ae.createElement("path",{d:"M9.87012 2C9.87012 1.44772 9.4224 1 8.87012 1C8.31783 1 7.87012 1.44772 7.87012 2V8C7.87012 8.55228 8.31783 9 8.87012 9C9.4224 9 9.87012 8.55228 9.87012 8V2Z",fill:"#A7A9AC"}),ae.createElement("path",{d:"M10.8698 2.42001V2.56001C10.8698 2.93001 11.0698 3.28001 11.4098 3.43001C13.6798 4.43001 15.2198 6.80001 14.9698 9.48001C14.6998 12.47 12.0998 14.87 9.08979 14.92C5.73979 14.97 2.98979 12.26 2.98979 8.92001C2.98979 6.57001 4.34979 4.54001 6.30979 3.56001C6.63979 3.40001 6.85979 3.08001 6.85979 2.72001V2.55001C6.85979 1.86001 6.13979 1.43001 5.50979 1.73001C2.43979 3.20001 0.449793 6.62001 1.13979 10.41C1.70979 13.57 4.23979 16.14 7.38979 16.76C12.5098 17.76 16.9998 13.86 16.9998 8.92001C16.9998 5.61001 14.9898 2.78001 12.1198 1.56001C11.5298 1.31001 10.8698 1.78001 10.8698 2.42001Z",fill:"#A7A9AC"})),an={STOPPED:tn,RUNNING:Xr,STARTING:en};function rn({conversationStatus:_}){const A=an[_];return d.jsx("div",{"data-testid":`${_}-indicator`,children:d.jsx(A,{})})}function nn({onClick:_}){return d.jsx("button",{"data-testid":"ellipsis-button",type:"button",onClick:_,className:"cursor-pointer",children:d.jsx(ar,{fill:"#a3a3a3"})})}/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sn=_=>_.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),on=_=>_.replace(/^([A-Z])|[\s-_]+(\w)/g,(A,D,$)=>$?$.toUpperCase():D.toLowerCase()),ja=_=>{const A=on(_);return A.charAt(0).toUpperCase()+A.slice(1)},Ma=(..._)=>_.filter((A,D,$)=>!!A&&A.trim()!==""&&$.indexOf(A)===D).join(" ").trim(),ln=_=>{for(const A in _)if(A.startsWith("aria-")||A==="role"||A==="title")return!0};/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var cn={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const un=ae.forwardRef(({color:_="currentColor",size:A=24,strokeWidth:D=2,absoluteStrokeWidth:$,className:W="",children:U,iconNode:Q,...h},i)=>ae.createElement("svg",{ref:i,...cn,width:A,height:A,stroke:_,strokeWidth:$?Number(D)*24/Number(A):D,className:Ma("lucide",W),...!U&&!ln(h)&&{"aria-hidden":"true"},...h},[...Q.map(([w,S])=>ae.createElement(w,S)),...Array.isArray(U)?U:[U]]));/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const at=(_,A)=>{const D=ae.forwardRef(({className:$,...W},U)=>ae.createElement(un,{ref:U,iconNode:A,className:Ma(`lucide-${sn(ja(_))}`,`lucide-${_}`,$),...W}));return D.displayName=ja(_),D};/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dn=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],pn=at("bot",dn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bn=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Aa=at("chevron-down",bn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fn=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ia=at("chevron-right",fn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hn=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],mn=at("download",hn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gn=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],vn=at("pencil",gn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]],xn=at("power",yn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jn=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],En=at("refresh-cw",jn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wn=[["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]],kn=at("trash",wn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cn=[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]],On=at("wallet",Cn);/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sn=[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.106-3.105c.32-.322.863-.22.983.218a6 6 0 0 1-8.259 7.057l-7.91 7.91a1 1 0 0 1-2.999-3l7.91-7.91a6 6 0 0 1 7.057-8.259c.438.12.54.662.219.984z",key:"1ngwbx"}]],Nn=at("wrench",Sn);function Tt(){return d.jsx("div",{className:"w-full h-[1px] bg-[#525252]"})}function ht({icon:_,text:A,className:D,iconClassName:$}){return d.jsxs("div",{className:ut("flex items-center gap-3 px-1",D),children:[d.jsx(_,{className:ut("w-4 h-4 shrink-0",$)}),A]})}function _n({onClose:_,onDelete:A,onStop:D,onEdit:$,onDisplayCost:W,onShowAgentTools:U,onShowMicroagents:Q,onDownloadViaVSCode:h,position:i="bottom"}){const{t:w}=yt(),S=Yr(_),j=!!$,x=!!h,C=!!(U||Q),O=!!W,R=!!(D||A);return d.jsxs(Gr,{ref:S,testId:"context-menu",className:ut("right-0 absolute mt-3",i==="top"&&"bottom-full",i==="bottom"&&"top-full"),children:[$&&d.jsx(ft,{testId:"edit-button",onClick:$,children:d.jsx(ht,{icon:vn,text:w(ne.BUTTON$EDIT_TITLE)})}),j&&(x||C||O||R)&&d.jsx(Tt,{}),h&&d.jsx(ft,{testId:"download-vscode-button",onClick:h,children:d.jsx(ht,{icon:mn,text:w(ne.BUTTON$DOWNLOAD_VIA_VSCODE)})}),x&&(C||O||R)&&d.jsx(Tt,{}),U&&d.jsx(ft,{testId:"show-agent-tools-button",onClick:U,children:d.jsx(ht,{icon:Nn,text:w(ne.BUTTON$SHOW_AGENT_TOOLS_AND_METADATA)})}),Q&&d.jsx(ft,{testId:"show-microagents-button",onClick:Q,children:d.jsx(ht,{icon:pn,text:w(ne.CONVERSATION$SHOW_MICROAGENTS)})}),C&&(O||R)&&d.jsx(Tt,{}),W&&d.jsx(ft,{testId:"display-cost-button",onClick:W,children:d.jsx(ht,{icon:On,text:w(ne.BUTTON$DISPLAY_COST)})}),O&&R&&d.jsx(Tt,{}),D&&d.jsx(ft,{testId:"stop-button",onClick:D,children:d.jsx(ht,{icon:xn,text:w(ne.BUTTON$STOP)})}),A&&d.jsx(ft,{testId:"delete-button",onClick:A,children:d.jsx(ht,{icon:kn,text:w(ne.BUTTON$DELETE)})})]})}var Ft={exports:{}},Mn=Ft.exports,Ea;function An(){return Ea||(Ea=1,function(_,A){(function(D,$){_.exports=$(Ya())})(Mn,D=>(()=>{var $={9735:(h,i)=>{i.__esModule=!0,i.default={scheme:"apathy",author:"jannik siebert (https://github.com/janniks)",base00:"#031A16",base01:"#0B342D",base02:"#184E45",base03:"#2B685E",base04:"#5F9C92",base05:"#81B5AC",base06:"#A7CEC8",base07:"#D2E7E4",base08:"#3E9688",base09:"#3E7996",base0A:"#3E4C96",base0B:"#883E96",base0C:"#963E4C",base0D:"#96883E",base0E:"#4C963E",base0F:"#3E965B"},h.exports=i.default},294:(h,i)=>{i.__esModule=!0,i.default={scheme:"ashes",author:"jannik siebert (https://github.com/janniks)",base00:"#1C2023",base01:"#393F45",base02:"#565E65",base03:"#747C84",base04:"#ADB3BA",base05:"#C7CCD1",base06:"#DFE2E5",base07:"#F3F4F5",base08:"#C7AE95",base09:"#C7C795",base0A:"#AEC795",base0B:"#95C7AE",base0C:"#95AEC7",base0D:"#AE95C7",base0E:"#C795AE",base0F:"#C79595"},h.exports=i.default},1733:(h,i)=>{i.__esModule=!0,i.default={scheme:"atelier dune",author:"bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/dune)",base00:"#20201d",base01:"#292824",base02:"#6e6b5e",base03:"#7d7a68",base04:"#999580",base05:"#a6a28c",base06:"#e8e4cf",base07:"#fefbec",base08:"#d73737",base09:"#b65611",base0A:"#cfb017",base0B:"#60ac39",base0C:"#1fad83",base0D:"#6684e1",base0E:"#b854d4",base0F:"#d43552"},h.exports=i.default},8974:(h,i)=>{i.__esModule=!0,i.default={scheme:"atelier forest",author:"bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/forest)",base00:"#1b1918",base01:"#2c2421",base02:"#68615e",base03:"#766e6b",base04:"#9c9491",base05:"#a8a19f",base06:"#e6e2e0",base07:"#f1efee",base08:"#f22c40",base09:"#df5320",base0A:"#d5911a",base0B:"#5ab738",base0C:"#00ad9c",base0D:"#407ee7",base0E:"#6666ea",base0F:"#c33ff3"},h.exports=i.default},6933:(h,i)=>{i.__esModule=!0,i.default={scheme:"atelier heath",author:"bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/heath)",base00:"#1b181b",base01:"#292329",base02:"#695d69",base03:"#776977",base04:"#9e8f9e",base05:"#ab9bab",base06:"#d8cad8",base07:"#f7f3f7",base08:"#ca402b",base09:"#a65926",base0A:"#bb8a35",base0B:"#379a37",base0C:"#159393",base0D:"#516aec",base0E:"#7b59c0",base0F:"#cc33cc"},h.exports=i.default},523:(h,i)=>{i.__esModule=!0,i.default={scheme:"atelier lakeside",author:"bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/lakeside/)",base00:"#161b1d",base01:"#1f292e",base02:"#516d7b",base03:"#5a7b8c",base04:"#7195a8",base05:"#7ea2b4",base06:"#c1e4f6",base07:"#ebf8ff",base08:"#d22d72",base09:"#935c25",base0A:"#8a8a0f",base0B:"#568c3b",base0C:"#2d8f6f",base0D:"#257fad",base0E:"#5d5db1",base0F:"#b72dd2"},h.exports=i.default},1223:(h,i)=>{i.__esModule=!0,i.default={scheme:"atelier seaside",author:"bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/seaside/)",base00:"#131513",base01:"#242924",base02:"#5e6e5e",base03:"#687d68",base04:"#809980",base05:"#8ca68c",base06:"#cfe8cf",base07:"#f0fff0",base08:"#e6193c",base09:"#87711d",base0A:"#c3c322",base0B:"#29a329",base0C:"#1999b3",base0D:"#3d62f5",base0E:"#ad2bee",base0F:"#e619c3"},h.exports=i.default},1233:(h,i)=>{i.__esModule=!0,i.default={scheme:"bespin",author:"jan t. sott",base00:"#28211c",base01:"#36312e",base02:"#5e5d5c",base03:"#666666",base04:"#797977",base05:"#8a8986",base06:"#9d9b97",base07:"#baae9e",base08:"#cf6a4c",base09:"#cf7d34",base0A:"#f9ee98",base0B:"#54be0d",base0C:"#afc4db",base0D:"#5ea6ea",base0E:"#9b859d",base0F:"#937121"},h.exports=i.default},2847:(h,i)=>{i.__esModule=!0,i.default={scheme:"brewer",author:"timothée poisot (http://github.com/tpoisot)",base00:"#0c0d0e",base01:"#2e2f30",base02:"#515253",base03:"#737475",base04:"#959697",base05:"#b7b8b9",base06:"#dadbdc",base07:"#fcfdfe",base08:"#e31a1c",base09:"#e6550d",base0A:"#dca060",base0B:"#31a354",base0C:"#80b1d3",base0D:"#3182bd",base0E:"#756bb1",base0F:"#b15928"},h.exports=i.default},8120:(h,i)=>{i.__esModule=!0,i.default={scheme:"bright",author:"chris kempson (http://chriskempson.com)",base00:"#000000",base01:"#303030",base02:"#505050",base03:"#b0b0b0",base04:"#d0d0d0",base05:"#e0e0e0",base06:"#f5f5f5",base07:"#ffffff",base08:"#fb0120",base09:"#fc6d24",base0A:"#fda331",base0B:"#a1c659",base0C:"#76c7b7",base0D:"#6fb3d2",base0E:"#d381c3",base0F:"#be643c"},h.exports=i.default},6305:(h,i)=>{i.__esModule=!0,i.default={scheme:"chalk",author:"chris kempson (http://chriskempson.com)",base00:"#151515",base01:"#202020",base02:"#303030",base03:"#505050",base04:"#b0b0b0",base05:"#d0d0d0",base06:"#e0e0e0",base07:"#f5f5f5",base08:"#fb9fb1",base09:"#eda987",base0A:"#ddb26f",base0B:"#acc267",base0C:"#12cfc0",base0D:"#6fc2ef",base0E:"#e1a3ee",base0F:"#deaf8f"},h.exports=i.default},525:(h,i)=>{i.__esModule=!0,i.default={scheme:"codeschool",author:"brettof86",base00:"#232c31",base01:"#1c3657",base02:"#2a343a",base03:"#3f4944",base04:"#84898c",base05:"#9ea7a6",base06:"#a7cfa3",base07:"#b5d8f6",base08:"#2a5491",base09:"#43820d",base0A:"#a03b1e",base0B:"#237986",base0C:"#b02f30",base0D:"#484d79",base0E:"#c59820",base0F:"#c98344"},h.exports=i.default},4124:(h,i)=>{i.__esModule=!0,i.default={scheme:"colors",author:"mrmrs (http://clrs.cc)",base00:"#111111",base01:"#333333",base02:"#555555",base03:"#777777",base04:"#999999",base05:"#bbbbbb",base06:"#dddddd",base07:"#ffffff",base08:"#ff4136",base09:"#ff851b",base0A:"#ffdc00",base0B:"#2ecc40",base0C:"#7fdbff",base0D:"#0074d9",base0E:"#b10dc9",base0F:"#85144b"},h.exports=i.default},7167:(h,i)=>{i.__esModule=!0,i.default={scheme:"default",author:"chris kempson (http://chriskempson.com)",base00:"#181818",base01:"#282828",base02:"#383838",base03:"#585858",base04:"#b8b8b8",base05:"#d8d8d8",base06:"#e8e8e8",base07:"#f8f8f8",base08:"#ab4642",base09:"#dc9656",base0A:"#f7ca88",base0B:"#a1b56c",base0C:"#86c1b9",base0D:"#7cafc2",base0E:"#ba8baf",base0F:"#a16946"},h.exports=i.default},4582:(h,i)=>{i.__esModule=!0,i.default={scheme:"eighties",author:"chris kempson (http://chriskempson.com)",base00:"#2d2d2d",base01:"#393939",base02:"#515151",base03:"#747369",base04:"#a09f93",base05:"#d3d0c8",base06:"#e8e6df",base07:"#f2f0ec",base08:"#f2777a",base09:"#f99157",base0A:"#ffcc66",base0B:"#99cc99",base0C:"#66cccc",base0D:"#6699cc",base0E:"#cc99cc",base0F:"#d27b53"},h.exports=i.default},7096:(h,i)=>{i.__esModule=!0,i.default={scheme:"embers",author:"jannik siebert (https://github.com/janniks)",base00:"#16130F",base01:"#2C2620",base02:"#433B32",base03:"#5A5047",base04:"#8A8075",base05:"#A39A90",base06:"#BEB6AE",base07:"#DBD6D1",base08:"#826D57",base09:"#828257",base0A:"#6D8257",base0B:"#57826D",base0C:"#576D82",base0D:"#6D5782",base0E:"#82576D",base0F:"#825757"},h.exports=i.default},9887:(h,i)=>{i.__esModule=!0,i.default={scheme:"flat",author:"chris kempson (http://chriskempson.com)",base00:"#2C3E50",base01:"#34495E",base02:"#7F8C8D",base03:"#95A5A6",base04:"#BDC3C7",base05:"#e0e0e0",base06:"#f5f5f5",base07:"#ECF0F1",base08:"#E74C3C",base09:"#E67E22",base0A:"#F1C40F",base0B:"#2ECC71",base0C:"#1ABC9C",base0D:"#3498DB",base0E:"#9B59B6",base0F:"#be643c"},h.exports=i.default},7199:(h,i)=>{i.__esModule=!0,i.default={scheme:"google",author:"seth wright (http://sethawright.com)",base00:"#1d1f21",base01:"#282a2e",base02:"#373b41",base03:"#969896",base04:"#b4b7b4",base05:"#c5c8c6",base06:"#e0e0e0",base07:"#ffffff",base08:"#CC342B",base09:"#F96A38",base0A:"#FBA922",base0B:"#198844",base0C:"#3971ED",base0D:"#3971ED",base0E:"#A36AC7",base0F:"#3971ED"},h.exports=i.default},1985:(h,i)=>{i.__esModule=!0,i.default={scheme:"grayscale",author:"alexandre gavioli (https://github.com/alexx2/)",base00:"#101010",base01:"#252525",base02:"#464646",base03:"#525252",base04:"#ababab",base05:"#b9b9b9",base06:"#e3e3e3",base07:"#f7f7f7",base08:"#7c7c7c",base09:"#999999",base0A:"#a0a0a0",base0B:"#8e8e8e",base0C:"#868686",base0D:"#686868",base0E:"#747474",base0F:"#5e5e5e"},h.exports=i.default},8093:(h,i)=>{i.__esModule=!0,i.default={scheme:"green screen",author:"chris kempson (http://chriskempson.com)",base00:"#001100",base01:"#003300",base02:"#005500",base03:"#007700",base04:"#009900",base05:"#00bb00",base06:"#00dd00",base07:"#00ff00",base08:"#007700",base09:"#009900",base0A:"#007700",base0B:"#00bb00",base0C:"#005500",base0D:"#009900",base0E:"#00bb00",base0F:"#005500"},h.exports=i.default},1615:(h,i)=>{i.__esModule=!0,i.default={scheme:"harmonic16",author:"jannik siebert (https://github.com/janniks)",base00:"#0b1c2c",base01:"#223b54",base02:"#405c79",base03:"#627e99",base04:"#aabcce",base05:"#cbd6e2",base06:"#e5ebf1",base07:"#f7f9fb",base08:"#bf8b56",base09:"#bfbf56",base0A:"#8bbf56",base0B:"#56bf8b",base0C:"#568bbf",base0D:"#8b56bf",base0E:"#bf568b",base0F:"#bf5656"},h.exports=i.default},9063:(h,i)=>{i.__esModule=!0,i.default={scheme:"hopscotch",author:"jan t. sott",base00:"#322931",base01:"#433b42",base02:"#5c545b",base03:"#797379",base04:"#989498",base05:"#b9b5b8",base06:"#d5d3d5",base07:"#ffffff",base08:"#dd464c",base09:"#fd8b19",base0A:"#fdcc59",base0B:"#8fc13e",base0C:"#149b93",base0D:"#1290bf",base0E:"#c85e7c",base0F:"#b33508"},h.exports=i.default},9446:(h,i,w)=>{function S(Fe){return Fe&&Fe.__esModule?Fe.default:Fe}i.__esModule=!0;var j=w(1308);i.threezerotwofour=S(j);var x=w(9735);i.apathy=S(x);var C=w(294);i.ashes=S(C);var O=w(1733);i.atelierDune=S(O);var R=w(8974);i.atelierForest=S(R);var K=w(6933);i.atelierHeath=S(K);var c=w(523);i.atelierLakeside=S(c);var o=w(1223);i.atelierSeaside=S(o);var l=w(1233);i.bespin=S(l);var r=w(2847);i.brewer=S(r);var u=w(8120);i.bright=S(u);var t=w(6305);i.chalk=S(t);var f=w(525);i.codeschool=S(f);var g=w(4124);i.colors=S(g);var E=w(7167);i.default=S(E);var P=w(4582);i.eighties=S(P);var T=w(7096);i.embers=S(T);var L=w(9887);i.flat=S(L);var G=w(7199);i.google=S(G);var X=w(1985);i.grayscale=S(X);var ie=w(8093);i.greenscreen=S(ie);var ke=w(1615);i.harmonic=S(ke);var I=w(9063);i.hopscotch=S(I);var ge=w(9033);i.isotope=S(ge);var $e=w(4112);i.marrakesh=S($e);var Re=w(9600);i.mocha=S(Re);var De=w(1240);i.monokai=S(De);var He=w(9768);i.ocean=S(He);var Ke=w(8293);i.paraiso=S(Ke);var rt=w(3093);i.pop=S(rt);var J=w(1951);i.railscasts=S(J);var Te=w(6368);i.shapeshifter=S(Te);var de=w(2317);i.solarized=S(de);var Ue=w(1091);i.summerfruit=S(Ue);var xe=w(6943);i.tomorrow=S(xe);var dt=w(5670);i.tube=S(dt);var Ce=w(2536);i.twilight=S(Ce)},9033:(h,i)=>{i.__esModule=!0,i.default={scheme:"isotope",author:"jan t. sott",base00:"#000000",base01:"#404040",base02:"#606060",base03:"#808080",base04:"#c0c0c0",base05:"#d0d0d0",base06:"#e0e0e0",base07:"#ffffff",base08:"#ff0000",base09:"#ff9900",base0A:"#ff0099",base0B:"#33ff00",base0C:"#00ffff",base0D:"#0066ff",base0E:"#cc00ff",base0F:"#3300ff"},h.exports=i.default},4112:(h,i)=>{i.__esModule=!0,i.default={scheme:"marrakesh",author:"alexandre gavioli (http://github.com/alexx2/)",base00:"#201602",base01:"#302e00",base02:"#5f5b17",base03:"#6c6823",base04:"#86813b",base05:"#948e48",base06:"#ccc37a",base07:"#faf0a5",base08:"#c35359",base09:"#b36144",base0A:"#a88339",base0B:"#18974e",base0C:"#75a738",base0D:"#477ca1",base0E:"#8868b3",base0F:"#b3588e"},h.exports=i.default},9600:(h,i)=>{i.__esModule=!0,i.default={scheme:"mocha",author:"chris kempson (http://chriskempson.com)",base00:"#3B3228",base01:"#534636",base02:"#645240",base03:"#7e705a",base04:"#b8afad",base05:"#d0c8c6",base06:"#e9e1dd",base07:"#f5eeeb",base08:"#cb6077",base09:"#d28b71",base0A:"#f4bc87",base0B:"#beb55b",base0C:"#7bbda4",base0D:"#8ab3b5",base0E:"#a89bb9",base0F:"#bb9584"},h.exports=i.default},1240:(h,i)=>{i.__esModule=!0,i.default={scheme:"monokai",author:"wimer hazenberg (http://www.monokai.nl)",base00:"#272822",base01:"#383830",base02:"#49483e",base03:"#75715e",base04:"#a59f85",base05:"#f8f8f2",base06:"#f5f4f1",base07:"#f9f8f5",base08:"#f92672",base09:"#fd971f",base0A:"#f4bf75",base0B:"#a6e22e",base0C:"#a1efe4",base0D:"#66d9ef",base0E:"#ae81ff",base0F:"#cc6633"},h.exports=i.default},9768:(h,i)=>{i.__esModule=!0,i.default={scheme:"ocean",author:"chris kempson (http://chriskempson.com)",base00:"#2b303b",base01:"#343d46",base02:"#4f5b66",base03:"#65737e",base04:"#a7adba",base05:"#c0c5ce",base06:"#dfe1e8",base07:"#eff1f5",base08:"#bf616a",base09:"#d08770",base0A:"#ebcb8b",base0B:"#a3be8c",base0C:"#96b5b4",base0D:"#8fa1b3",base0E:"#b48ead",base0F:"#ab7967"},h.exports=i.default},8293:(h,i)=>{i.__esModule=!0,i.default={scheme:"paraiso",author:"jan t. sott",base00:"#2f1e2e",base01:"#41323f",base02:"#4f424c",base03:"#776e71",base04:"#8d8687",base05:"#a39e9b",base06:"#b9b6b0",base07:"#e7e9db",base08:"#ef6155",base09:"#f99b15",base0A:"#fec418",base0B:"#48b685",base0C:"#5bc4bf",base0D:"#06b6ef",base0E:"#815ba4",base0F:"#e96ba8"},h.exports=i.default},3093:(h,i)=>{i.__esModule=!0,i.default={scheme:"pop",author:"chris kempson (http://chriskempson.com)",base00:"#000000",base01:"#202020",base02:"#303030",base03:"#505050",base04:"#b0b0b0",base05:"#d0d0d0",base06:"#e0e0e0",base07:"#ffffff",base08:"#eb008a",base09:"#f29333",base0A:"#f8ca12",base0B:"#37b349",base0C:"#00aabb",base0D:"#0e5a94",base0E:"#b31e8d",base0F:"#7a2d00"},h.exports=i.default},1951:(h,i)=>{i.__esModule=!0,i.default={scheme:"railscasts",author:"ryan bates (http://railscasts.com)",base00:"#2b2b2b",base01:"#272935",base02:"#3a4055",base03:"#5a647e",base04:"#d4cfc9",base05:"#e6e1dc",base06:"#f4f1ed",base07:"#f9f7f3",base08:"#da4939",base09:"#cc7833",base0A:"#ffc66d",base0B:"#a5c261",base0C:"#519f50",base0D:"#6d9cbe",base0E:"#b6b3eb",base0F:"#bc9458"},h.exports=i.default},6368:(h,i)=>{i.__esModule=!0,i.default={scheme:"shapeshifter",author:"tyler benziger (http://tybenz.com)",base00:"#000000",base01:"#040404",base02:"#102015",base03:"#343434",base04:"#555555",base05:"#ababab",base06:"#e0e0e0",base07:"#f9f9f9",base08:"#e92f2f",base09:"#e09448",base0A:"#dddd13",base0B:"#0ed839",base0C:"#23edda",base0D:"#3b48e3",base0E:"#f996e2",base0F:"#69542d"},h.exports=i.default},2317:(h,i)=>{i.__esModule=!0,i.default={scheme:"solarized",author:"ethan schoonover (http://ethanschoonover.com/solarized)",base00:"#002b36",base01:"#073642",base02:"#586e75",base03:"#657b83",base04:"#839496",base05:"#93a1a1",base06:"#eee8d5",base07:"#fdf6e3",base08:"#dc322f",base09:"#cb4b16",base0A:"#b58900",base0B:"#859900",base0C:"#2aa198",base0D:"#268bd2",base0E:"#6c71c4",base0F:"#d33682"},h.exports=i.default},1091:(h,i)=>{i.__esModule=!0,i.default={scheme:"summerfruit",author:"christopher corley (http://cscorley.github.io/)",base00:"#151515",base01:"#202020",base02:"#303030",base03:"#505050",base04:"#B0B0B0",base05:"#D0D0D0",base06:"#E0E0E0",base07:"#FFFFFF",base08:"#FF0086",base09:"#FD8900",base0A:"#ABA800",base0B:"#00C918",base0C:"#1faaaa",base0D:"#3777E6",base0E:"#AD00A1",base0F:"#cc6633"},h.exports=i.default},1308:(h,i)=>{i.__esModule=!0,i.default={scheme:"threezerotwofour",author:"jan t. sott (http://github.com/idleberg)",base00:"#090300",base01:"#3a3432",base02:"#4a4543",base03:"#5c5855",base04:"#807d7c",base05:"#a5a2a2",base06:"#d6d5d4",base07:"#f7f7f7",base08:"#db2d20",base09:"#e8bbd0",base0A:"#fded02",base0B:"#01a252",base0C:"#b5e4f4",base0D:"#01a0e4",base0E:"#a16a94",base0F:"#cdab53"},h.exports=i.default},6943:(h,i)=>{i.__esModule=!0,i.default={scheme:"tomorrow",author:"chris kempson (http://chriskempson.com)",base00:"#1d1f21",base01:"#282a2e",base02:"#373b41",base03:"#969896",base04:"#b4b7b4",base05:"#c5c8c6",base06:"#e0e0e0",base07:"#ffffff",base08:"#cc6666",base09:"#de935f",base0A:"#f0c674",base0B:"#b5bd68",base0C:"#8abeb7",base0D:"#81a2be",base0E:"#b294bb",base0F:"#a3685a"},h.exports=i.default},5670:(h,i)=>{i.__esModule=!0,i.default={scheme:"london tube",author:"jan t. sott",base00:"#231f20",base01:"#1c3f95",base02:"#5a5758",base03:"#737171",base04:"#959ca1",base05:"#d9d8d8",base06:"#e7e7e8",base07:"#ffffff",base08:"#ee2e24",base09:"#f386a1",base0A:"#ffd204",base0B:"#00853e",base0C:"#85cebc",base0D:"#009ddc",base0E:"#98005d",base0F:"#b06110"},h.exports=i.default},2536:(h,i)=>{i.__esModule=!0,i.default={scheme:"twilight",author:"david hart (http://hart-dev.com)",base00:"#1e1e1e",base01:"#323537",base02:"#464b50",base03:"#5f5a60",base04:"#838184",base05:"#a7a7a7",base06:"#c3c3c3",base07:"#ffffff",base08:"#cf6a4c",base09:"#cda869",base0A:"#f9ee98",base0B:"#8f9d6a",base0C:"#afc4db",base0D:"#7587a6",base0E:"#9b859d",base0F:"#9b703f"},h.exports=i.default},6481:(h,i,w)=>{var S=w(4176),j={};for(var x in S)S.hasOwnProperty(x)&&(j[S[x]]=x);var C=h.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var O in C)if(C.hasOwnProperty(O)){if(!("channels"in C[O]))throw new Error("missing channels property: "+O);if(!("labels"in C[O]))throw new Error("missing channel labels property: "+O);if(C[O].labels.length!==C[O].channels)throw new Error("channel and label counts mismatch: "+O);var R=C[O].channels,K=C[O].labels;delete C[O].channels,delete C[O].labels,Object.defineProperty(C[O],"channels",{value:R}),Object.defineProperty(C[O],"labels",{value:K})}C.rgb.hsl=function(c){var o,l,r=c[0]/255,u=c[1]/255,t=c[2]/255,f=Math.min(r,u,t),g=Math.max(r,u,t),E=g-f;return g===f?o=0:r===g?o=(u-t)/E:u===g?o=2+(t-r)/E:t===g&&(o=4+(r-u)/E),(o=Math.min(60*o,360))<0&&(o+=360),l=(f+g)/2,[o,100*(g===f?0:l<=.5?E/(g+f):E/(2-g-f)),100*l]},C.rgb.hsv=function(c){var o,l,r,u,t,f=c[0]/255,g=c[1]/255,E=c[2]/255,P=Math.max(f,g,E),T=P-Math.min(f,g,E),L=function(G){return(P-G)/6/T+.5};return T===0?u=t=0:(t=T/P,o=L(f),l=L(g),r=L(E),f===P?u=r-l:g===P?u=.3333333333333333+o-r:E===P&&(u=.6666666666666666+l-o),u<0?u+=1:u>1&&(u-=1)),[360*u,100*t,100*P]},C.rgb.hwb=function(c){var o=c[0],l=c[1],r=c[2];return[C.rgb.hsl(c)[0],100*(.00392156862745098*Math.min(o,Math.min(l,r))),100*(r=1-.00392156862745098*Math.max(o,Math.max(l,r)))]},C.rgb.cmyk=function(c){var o,l=c[0]/255,r=c[1]/255,u=c[2]/255;return[100*((1-l-(o=Math.min(1-l,1-r,1-u)))/(1-o)||0),100*((1-r-o)/(1-o)||0),100*((1-u-o)/(1-o)||0),100*o]},C.rgb.keyword=function(c){var o=j[c];if(o)return o;var l,r,u,t=1/0;for(var f in S)if(S.hasOwnProperty(f)){var g=S[f],E=(r=c,u=g,Math.pow(r[0]-u[0],2)+Math.pow(r[1]-u[1],2)+Math.pow(r[2]-u[2],2));E<t&&(t=E,l=f)}return l},C.keyword.rgb=function(c){return S[c]},C.rgb.xyz=function(c){var o=c[0]/255,l=c[1]/255,r=c[2]/255;return[100*(.4124*(o=o>.04045?Math.pow((o+.055)/1.055,2.4):o/12.92)+.3576*(l=l>.04045?Math.pow((l+.055)/1.055,2.4):l/12.92)+.1805*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)),100*(.2126*o+.7152*l+.0722*r),100*(.0193*o+.1192*l+.9505*r)]},C.rgb.lab=function(c){var o=C.rgb.xyz(c),l=o[0],r=o[1],u=o[2];return r/=100,u/=108.883,l=(l/=95.047)>.008856?Math.pow(l,.3333333333333333):7.787*l+.13793103448275862,[116*(r=r>.008856?Math.pow(r,.3333333333333333):7.787*r+.13793103448275862)-16,500*(l-r),200*(r-(u=u>.008856?Math.pow(u,.3333333333333333):7.787*u+.13793103448275862))]},C.hsl.rgb=function(c){var o,l,r,u,t,f=c[0]/360,g=c[1]/100,E=c[2]/100;if(g===0)return[t=255*E,t,t];o=2*E-(l=E<.5?E*(1+g):E+g-E*g),u=[0,0,0];for(var P=0;P<3;P++)(r=f+.3333333333333333*-(P-1))<0&&r++,r>1&&r--,t=6*r<1?o+6*(l-o)*r:2*r<1?l:3*r<2?o+(l-o)*(.6666666666666666-r)*6:o,u[P]=255*t;return u},C.hsl.hsv=function(c){var o=c[0],l=c[1]/100,r=c[2]/100,u=l,t=Math.max(r,.01);return l*=(r*=2)<=1?r:2-r,u*=t<=1?t:2-t,[o,100*(r===0?2*u/(t+u):2*l/(r+l)),100*((r+l)/2)]},C.hsv.rgb=function(c){var o=c[0]/60,l=c[1]/100,r=c[2]/100,u=Math.floor(o)%6,t=o-Math.floor(o),f=255*r*(1-l),g=255*r*(1-l*t),E=255*r*(1-l*(1-t));switch(r*=255,u){case 0:return[r,E,f];case 1:return[g,r,f];case 2:return[f,r,E];case 3:return[f,g,r];case 4:return[E,f,r];case 5:return[r,f,g]}},C.hsv.hsl=function(c){var o,l,r,u=c[0],t=c[1]/100,f=c[2]/100,g=Math.max(f,.01);return r=(2-t)*f,l=t*g,[u,100*(l=(l/=(o=(2-t)*g)<=1?o:2-o)||0),100*(r/=2)]},C.hwb.rgb=function(c){var o,l,r,u,t,f,g,E=c[0]/360,P=c[1]/100,T=c[2]/100,L=P+T;switch(L>1&&(P/=L,T/=L),r=6*E-(o=Math.floor(6*E)),1&o&&(r=1-r),u=P+r*((l=1-T)-P),o){default:case 6:case 0:t=l,f=u,g=P;break;case 1:t=u,f=l,g=P;break;case 2:t=P,f=l,g=u;break;case 3:t=P,f=u,g=l;break;case 4:t=u,f=P,g=l;break;case 5:t=l,f=P,g=u}return[255*t,255*f,255*g]},C.cmyk.rgb=function(c){var o=c[0]/100,l=c[1]/100,r=c[2]/100,u=c[3]/100;return[255*(1-Math.min(1,o*(1-u)+u)),255*(1-Math.min(1,l*(1-u)+u)),255*(1-Math.min(1,r*(1-u)+u))]},C.xyz.rgb=function(c){var o,l,r,u=c[0]/100,t=c[1]/100,f=c[2]/100;return l=-.9689*u+1.8758*t+.0415*f,r=.0557*u+-.204*t+1.057*f,o=(o=3.2406*u+-1.5372*t+-.4986*f)>.0031308?1.055*Math.pow(o,.4166666666666667)-.055:12.92*o,l=l>.0031308?1.055*Math.pow(l,.4166666666666667)-.055:12.92*l,r=r>.0031308?1.055*Math.pow(r,.4166666666666667)-.055:12.92*r,[255*(o=Math.min(Math.max(0,o),1)),255*(l=Math.min(Math.max(0,l),1)),255*(r=Math.min(Math.max(0,r),1))]},C.xyz.lab=function(c){var o=c[0],l=c[1],r=c[2];return l/=100,r/=108.883,o=(o/=95.047)>.008856?Math.pow(o,.3333333333333333):7.787*o+.13793103448275862,[116*(l=l>.008856?Math.pow(l,.3333333333333333):7.787*l+.13793103448275862)-16,500*(o-l),200*(l-(r=r>.008856?Math.pow(r,.3333333333333333):7.787*r+.13793103448275862))]},C.lab.xyz=function(c){var o,l,r,u=c[0];o=c[1]/500+(l=(u+16)/116),r=l-c[2]/200;var t=Math.pow(l,3),f=Math.pow(o,3),g=Math.pow(r,3);return l=t>.008856?t:(l-.13793103448275862)/7.787,o=f>.008856?f:(o-.13793103448275862)/7.787,r=g>.008856?g:(r-.13793103448275862)/7.787,[o*=95.047,l*=100,r*=108.883]},C.lab.lch=function(c){var o,l=c[0],r=c[1],u=c[2];return(o=360*Math.atan2(u,r)/2/Math.PI)<0&&(o+=360),[l,Math.sqrt(r*r+u*u),o]},C.lch.lab=function(c){var o,l=c[0],r=c[1];return o=c[2]/360*2*Math.PI,[l,r*Math.cos(o),r*Math.sin(o)]},C.rgb.ansi16=function(c){var o=c[0],l=c[1],r=c[2],u=1 in arguments?arguments[1]:C.rgb.hsv(c)[2];if((u=Math.round(u/50))===0)return 30;var t=30+(Math.round(r/255)<<2|Math.round(l/255)<<1|Math.round(o/255));return u===2&&(t+=60),t},C.hsv.ansi16=function(c){return C.rgb.ansi16(C.hsv.rgb(c),c[2])},C.rgb.ansi256=function(c){var o=c[0],l=c[1],r=c[2];return o===l&&l===r?o<8?16:o>248?231:Math.round((o-8)/247*24)+232:16+36*Math.round(o/255*5)+6*Math.round(l/255*5)+Math.round(r/255*5)},C.ansi16.rgb=function(c){var o=c%10;if(o===0||o===7)return c>50&&(o+=3.5),[o=o/10.5*255,o,o];var l=.5*(1+~~(c>50));return[(1&o)*l*255,(o>>1&1)*l*255,(o>>2&1)*l*255]},C.ansi256.rgb=function(c){if(c>=232){var o=10*(c-232)+8;return[o,o,o]}var l;return c-=16,[Math.floor(c/36)/5*255,Math.floor((l=c%36)/6)/5*255,l%6/5*255]},C.rgb.hex=function(c){var o=(((255&Math.round(c[0]))<<16)+((255&Math.round(c[1]))<<8)+(255&Math.round(c[2]))).toString(16).toUpperCase();return"000000".substring(o.length)+o},C.hex.rgb=function(c){var o=c.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!o)return[0,0,0];var l=o[0];o[0].length===3&&(l=l.split("").map(function(u){return u+u}).join(""));var r=parseInt(l,16);return[r>>16&255,r>>8&255,255&r]},C.rgb.hcg=function(c){var o,l=c[0]/255,r=c[1]/255,u=c[2]/255,t=Math.max(Math.max(l,r),u),f=Math.min(Math.min(l,r),u),g=t-f;return o=g<=0?0:t===l?(r-u)/g%6:t===r?2+(u-l)/g:4+(l-r)/g+4,o/=6,[360*(o%=1),100*g,100*(g<1?f/(1-g):0)]},C.hsl.hcg=function(c){var o=c[1]/100,l=c[2]/100,r=1,u=0;return(r=l<.5?2*o*l:2*o*(1-l))<1&&(u=(l-.5*r)/(1-r)),[c[0],100*r,100*u]},C.hsv.hcg=function(c){var o=c[1]/100,l=c[2]/100,r=o*l,u=0;return r<1&&(u=(l-r)/(1-r)),[c[0],100*r,100*u]},C.hcg.rgb=function(c){var o=c[0]/360,l=c[1]/100,r=c[2]/100;if(l===0)return[255*r,255*r,255*r];var u,t=[0,0,0],f=o%1*6,g=f%1,E=1-g;switch(Math.floor(f)){case 0:t[0]=1,t[1]=g,t[2]=0;break;case 1:t[0]=E,t[1]=1,t[2]=0;break;case 2:t[0]=0,t[1]=1,t[2]=g;break;case 3:t[0]=0,t[1]=E,t[2]=1;break;case 4:t[0]=g,t[1]=0,t[2]=1;break;default:t[0]=1,t[1]=0,t[2]=E}return u=(1-l)*r,[255*(l*t[0]+u),255*(l*t[1]+u),255*(l*t[2]+u)]},C.hcg.hsv=function(c){var o=c[1]/100,l=o+c[2]/100*(1-o),r=0;return l>0&&(r=o/l),[c[0],100*r,100*l]},C.hcg.hsl=function(c){var o=c[1]/100,l=c[2]/100*(1-o)+.5*o,r=0;return l>0&&l<.5?r=o/(2*l):l>=.5&&l<1&&(r=o/(2*(1-l))),[c[0],100*r,100*l]},C.hcg.hwb=function(c){var o=c[1]/100,l=o+c[2]/100*(1-o);return[c[0],100*(l-o),100*(1-l)]},C.hwb.hcg=function(c){var o=c[1]/100,l=1-c[2]/100,r=l-o,u=0;return r<1&&(u=(l-r)/(1-r)),[c[0],100*r,100*u]},C.apple.rgb=function(c){return[c[0]/65535*255,c[1]/65535*255,c[2]/65535*255]},C.rgb.apple=function(c){return[c[0]/255*65535,c[1]/255*65535,c[2]/255*65535]},C.gray.rgb=function(c){return[c[0]/100*255,c[0]/100*255,c[0]/100*255]},C.gray.hsl=C.gray.hsv=function(c){return[0,0,c[0]]},C.gray.hwb=function(c){return[0,100,c[0]]},C.gray.cmyk=function(c){return[0,0,0,c[0]]},C.gray.lab=function(c){return[c[0],0,0]},C.gray.hex=function(c){var o=255&Math.round(c[0]/100*255),l=((o<<16)+(o<<8)+o).toString(16).toUpperCase();return"000000".substring(l.length)+l},C.rgb.gray=function(c){return[(c[0]+c[1]+c[2])/3/255*100]}},4732:(h,i,w)=>{var S=w(6481),j=w(1157),x={};Object.keys(S).forEach(function(C){x[C]={},Object.defineProperty(x[C],"channels",{value:S[C].channels}),Object.defineProperty(x[C],"labels",{value:S[C].labels});var O=j(C);Object.keys(O).forEach(function(R){var K=O[R];x[C][R]=function(c){var o=function(l){if(l==null)return l;arguments.length>1&&(l=Array.prototype.slice.call(arguments));var r=c(l);if(typeof r=="object")for(var u=r.length,t=0;t<u;t++)r[t]=Math.round(r[t]);return r};return"conversion"in c&&(o.conversion=c.conversion),o}(K),x[C][R].raw=function(c){var o=function(l){return l==null?l:(arguments.length>1&&(l=Array.prototype.slice.call(arguments)),c(l))};return"conversion"in c&&(o.conversion=c.conversion),o}(K)})}),h.exports=x},1157:(h,i,w)=>{var S=w(6481);function j(O){var R=function(){for(var f={},g=Object.keys(S),E=g.length,P=0;P<E;P++)f[g[P]]={distance:-1,parent:null};return f}(),K=[O];for(R[O].distance=0;K.length;)for(var c=K.pop(),o=Object.keys(S[c]),l=o.length,r=0;r<l;r++){var u=o[r],t=R[u];t.distance===-1&&(t.distance=R[c].distance+1,t.parent=c,K.unshift(u))}return R}function x(O,R){return function(K){return R(O(K))}}function C(O,R){for(var K=[R[O].parent,O],c=S[R[O].parent][O],o=R[O].parent;R[o].parent;)K.unshift(R[o].parent),c=x(S[R[o].parent][o],c),o=R[o].parent;return c.conversion=K,c}h.exports=function(O){for(var R=j(O),K={},c=Object.keys(R),o=c.length,l=0;l<o;l++){var r=c[l];R[r].parent!==null&&(K[r]=C(r,R))}return K}},4176:h=>{h.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},4877:h=>{h.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},6138:(h,i,w)=>{var S=w(4877),j=w(301),x=Object.hasOwnProperty,C=Object.create(null);for(var O in S)x.call(S,O)&&(C[S[O]]=O);var R=h.exports={to:{},get:{}};function K(o,l,r){return Math.min(Math.max(l,o),r)}function c(o){var l=Math.round(o).toString(16).toUpperCase();return l.length<2?"0"+l:l}R.get=function(o){var l,r;switch(o.substring(0,3).toLowerCase()){case"hsl":l=R.get.hsl(o),r="hsl";break;case"hwb":l=R.get.hwb(o),r="hwb";break;default:l=R.get.rgb(o),r="rgb"}return l?{model:r,value:l}:null},R.get.rgb=function(o){if(!o)return null;var l,r,u,t=[0,0,0,1];if(l=o.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(u=l[2],l=l[1],r=0;r<3;r++){var f=2*r;t[r]=parseInt(l.slice(f,f+2),16)}u&&(t[3]=parseInt(u,16)/255)}else if(l=o.match(/^#([a-f0-9]{3,4})$/i)){for(u=(l=l[1])[3],r=0;r<3;r++)t[r]=parseInt(l[r]+l[r],16);u&&(t[3]=parseInt(u+u,16)/255)}else if(l=o.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)){for(r=0;r<3;r++)t[r]=parseInt(l[r+1],0);l[4]&&(l[5]?t[3]=.01*parseFloat(l[4]):t[3]=parseFloat(l[4]))}else{if(!(l=o.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)))return(l=o.match(/^(\w+)$/))?l[1]==="transparent"?[0,0,0,0]:x.call(S,l[1])?((t=S[l[1]])[3]=1,t):null:null;for(r=0;r<3;r++)t[r]=Math.round(2.55*parseFloat(l[r+1]));l[4]&&(l[5]?t[3]=.01*parseFloat(l[4]):t[3]=parseFloat(l[4]))}for(r=0;r<3;r++)t[r]=K(t[r],0,255);return t[3]=K(t[3],0,1),t},R.get.hsl=function(o){if(!o)return null;var l=o.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(l){var r=parseFloat(l[4]);return[(parseFloat(l[1])%360+360)%360,K(parseFloat(l[2]),0,100),K(parseFloat(l[3]),0,100),K(isNaN(r)?1:r,0,1)]}return null},R.get.hwb=function(o){if(!o)return null;var l=o.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(l){var r=parseFloat(l[4]);return[(parseFloat(l[1])%360+360)%360,K(parseFloat(l[2]),0,100),K(parseFloat(l[3]),0,100),K(isNaN(r)?1:r,0,1)]}return null},R.to.hex=function(){var o=j(arguments);return"#"+c(o[0])+c(o[1])+c(o[2])+(o[3]<1?c(Math.round(255*o[3])):"")},R.to.rgb=function(){var o=j(arguments);return o.length<4||o[3]===1?"rgb("+Math.round(o[0])+", "+Math.round(o[1])+", "+Math.round(o[2])+")":"rgba("+Math.round(o[0])+", "+Math.round(o[1])+", "+Math.round(o[2])+", "+o[3]+")"},R.to.rgb.percent=function(){var o=j(arguments),l=Math.round(o[0]/255*100),r=Math.round(o[1]/255*100),u=Math.round(o[2]/255*100);return o.length<4||o[3]===1?"rgb("+l+"%, "+r+"%, "+u+"%)":"rgba("+l+"%, "+r+"%, "+u+"%, "+o[3]+")"},R.to.hsl=function(){var o=j(arguments);return o.length<4||o[3]===1?"hsl("+o[0]+", "+o[1]+"%, "+o[2]+"%)":"hsla("+o[0]+", "+o[1]+"%, "+o[2]+"%, "+o[3]+")"},R.to.hwb=function(){var o=j(arguments),l="";return o.length>=4&&o[3]!==1&&(l=", "+o[3]),"hwb("+o[0]+", "+o[1]+"%, "+o[2]+"%"+l+")"},R.to.keyword=function(o){return C[o.slice(0,3)]}},3639:(h,i,w)=>{var S=w(6138),j=w(4732),x=[].slice,C=["keyword","gray","hex"],O={};Object.keys(j).forEach(function(r){O[x.call(j[r].labels).sort().join("")]=r});var R={};function K(r,u){if(!(this instanceof K))return new K(r,u);if(u&&u in C&&(u=null),u&&!(u in j))throw new Error("Unknown model: "+u);var t,f;if(r==null)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(r instanceof K)this.model=r.model,this.color=r.color.slice(),this.valpha=r.valpha;else if(typeof r=="string"){var g=S.get(r);if(g===null)throw new Error("Unable to parse color from string: "+r);this.model=g.model,f=j[this.model].channels,this.color=g.value.slice(0,f),this.valpha=typeof g.value[f]=="number"?g.value[f]:1}else if(r.length){this.model=u||"rgb",f=j[this.model].channels;var E=x.call(r,0,f);this.color=l(E,f),this.valpha=typeof r[f]=="number"?r[f]:1}else if(typeof r=="number")r&=16777215,this.model="rgb",this.color=[r>>16&255,r>>8&255,255&r],this.valpha=1;else{this.valpha=1;var P=Object.keys(r);"alpha"in r&&(P.splice(P.indexOf("alpha"),1),this.valpha=typeof r.alpha=="number"?r.alpha:0);var T=P.sort().join("");if(!(T in O))throw new Error("Unable to parse color from object: "+JSON.stringify(r));this.model=O[T];var L=j[this.model].labels,G=[];for(t=0;t<L.length;t++)G.push(r[L[t]]);this.color=l(G)}if(R[this.model])for(f=j[this.model].channels,t=0;t<f;t++){var X=R[this.model][t];X&&(this.color[t]=X(this.color[t]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}function c(r,u,t){return(r=Array.isArray(r)?r:[r]).forEach(function(f){(R[f]||(R[f]=[]))[u]=t}),r=r[0],function(f){var g;return arguments.length?(t&&(f=t(f)),(g=this[r]()).color[u]=f,g):(g=this[r]().color[u],t&&(g=t(g)),g)}}function o(r){return function(u){return Math.max(0,Math.min(r,u))}}function l(r,u){for(var t=0;t<u;t++)typeof r[t]!="number"&&(r[t]=0);return r}K.prototype={toString:function(){return this.string()},toJSON:function(){return this[this.model]()},string:function(r){var u=this.model in S.to?this:this.rgb(),t=(u=u.round(typeof r=="number"?r:1)).valpha===1?u.color:u.color.concat(this.valpha);return S.to[u.model](t)},percentString:function(r){var u=this.rgb().round(typeof r=="number"?r:1),t=u.valpha===1?u.color:u.color.concat(this.valpha);return S.to.rgb.percent(t)},array:function(){return this.valpha===1?this.color.slice():this.color.concat(this.valpha)},object:function(){for(var r={},u=j[this.model].channels,t=j[this.model].labels,f=0;f<u;f++)r[t[f]]=this.color[f];return this.valpha!==1&&(r.alpha=this.valpha),r},unitArray:function(){var r=this.rgb().color;return r[0]/=255,r[1]/=255,r[2]/=255,this.valpha!==1&&r.push(this.valpha),r},unitObject:function(){var r=this.rgb().object();return r.r/=255,r.g/=255,r.b/=255,this.valpha!==1&&(r.alpha=this.valpha),r},round:function(r){return r=Math.max(r||0,0),new K(this.color.map(function(u){return function(t){return function(f,g){return Number(f.toFixed(g))}(t,u)}}(r)).concat(this.valpha),this.model)},alpha:function(r){return arguments.length?new K(this.color.concat(Math.max(0,Math.min(1,r))),this.model):this.valpha},red:c("rgb",0,o(255)),green:c("rgb",1,o(255)),blue:c("rgb",2,o(255)),hue:c(["hsl","hsv","hsl","hwb","hcg"],0,function(r){return(r%360+360)%360}),saturationl:c("hsl",1,o(100)),lightness:c("hsl",2,o(100)),saturationv:c("hsv",1,o(100)),value:c("hsv",2,o(100)),chroma:c("hcg",1,o(100)),gray:c("hcg",2,o(100)),white:c("hwb",1,o(100)),wblack:c("hwb",2,o(100)),cyan:c("cmyk",0,o(100)),magenta:c("cmyk",1,o(100)),yellow:c("cmyk",2,o(100)),black:c("cmyk",3,o(100)),x:c("xyz",0,o(100)),y:c("xyz",1,o(100)),z:c("xyz",2,o(100)),l:c("lab",0,o(100)),a:c("lab",1),b:c("lab",2),keyword:function(r){return arguments.length?new K(r):j[this.model].keyword(this.color)},hex:function(r){return arguments.length?new K(r):S.to.hex(this.rgb().round().color)},rgbNumber:function(){var r=this.rgb().color;return(255&r[0])<<16|(255&r[1])<<8|255&r[2]},luminosity:function(){for(var r=this.rgb().color,u=[],t=0;t<r.length;t++){var f=r[t]/255;u[t]=f<=.03928?f/12.92:Math.pow((f+.055)/1.055,2.4)}return .2126*u[0]+.7152*u[1]+.0722*u[2]},contrast:function(r){var u=this.luminosity(),t=r.luminosity();return u>t?(u+.05)/(t+.05):(t+.05)/(u+.05)},level:function(r){var u=this.contrast(r);return u>=7.1?"AAA":u>=4.5?"AA":""},isDark:function(){var r=this.rgb().color;return(299*r[0]+587*r[1]+114*r[2])/1e3<128},isLight:function(){return!this.isDark()},negate:function(){for(var r=this.rgb(),u=0;u<3;u++)r.color[u]=255-r.color[u];return r},lighten:function(r){var u=this.hsl();return u.color[2]+=u.color[2]*r,u},darken:function(r){var u=this.hsl();return u.color[2]-=u.color[2]*r,u},saturate:function(r){var u=this.hsl();return u.color[1]+=u.color[1]*r,u},desaturate:function(r){var u=this.hsl();return u.color[1]-=u.color[1]*r,u},whiten:function(r){var u=this.hwb();return u.color[1]+=u.color[1]*r,u},blacken:function(r){var u=this.hwb();return u.color[2]+=u.color[2]*r,u},grayscale:function(){var r=this.rgb().color,u=.3*r[0]+.59*r[1]+.11*r[2];return K.rgb(u,u,u)},fade:function(r){return this.alpha(this.valpha-this.valpha*r)},opaquer:function(r){return this.alpha(this.valpha+this.valpha*r)},rotate:function(r){var u=this.hsl(),t=u.color[0];return t=(t=(t+r)%360)<0?360+t:t,u.color[0]=t,u},mix:function(r,u){if(!r||!r.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof r);var t=r.rgb(),f=this.rgb(),g=u===void 0?.5:u,E=2*g-1,P=t.alpha()-f.alpha(),T=((E*P==-1?E:(E+P)/(1+E*P))+1)/2,L=1-T;return K.rgb(T*t.red()+L*f.red(),T*t.green()+L*f.green(),T*t.blue()+L*f.blue(),t.alpha()*g+f.alpha()*(1-g))}},Object.keys(j).forEach(function(r){if(C.indexOf(r)===-1){var u=j[r].channels;K.prototype[r]=function(){if(this.model===r)return new K(this);if(arguments.length)return new K(arguments,r);var t,f=typeof arguments[u]=="number"?u:this.valpha;return new K((t=j[this.model][r].raw(this.color),Array.isArray(t)?t:[t]).concat(f),r)},K[r]=function(t){return typeof t=="number"&&(t=l(x.call(arguments),u)),new K(t,r)}}}),h.exports=K},9784:h=>{var i,w=typeof Reflect=="object"?Reflect:null,S=w&&typeof w.apply=="function"?w.apply:function(f,g,E){return Function.prototype.apply.call(f,g,E)};i=w&&typeof w.ownKeys=="function"?w.ownKeys:Object.getOwnPropertySymbols?function(f){return Object.getOwnPropertyNames(f).concat(Object.getOwnPropertySymbols(f))}:function(f){return Object.getOwnPropertyNames(f)};var j=Number.isNaN||function(f){return f!=f};function x(){x.init.call(this)}h.exports=x,h.exports.once=function(f,g){return new Promise(function(E,P){function T(G){f.removeListener(g,L),P(G)}function L(){typeof f.removeListener=="function"&&f.removeListener("error",T),E([].slice.call(arguments))}t(f,g,L,{once:!0}),g!=="error"&&function(G,X,ie){typeof G.on=="function"&&t(G,"error",X,ie)}(f,T,{once:!0})})},x.EventEmitter=x,x.prototype._events=void 0,x.prototype._eventsCount=0,x.prototype._maxListeners=void 0;var C=10;function O(f){if(typeof f!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof f)}function R(f){return f._maxListeners===void 0?x.defaultMaxListeners:f._maxListeners}function K(f,g,E,P){var T,L,G,X;if(O(E),(L=f._events)===void 0?(L=f._events=Object.create(null),f._eventsCount=0):(L.newListener!==void 0&&(f.emit("newListener",g,E.listener?E.listener:E),L=f._events),G=L[g]),G===void 0)G=L[g]=E,++f._eventsCount;else if(typeof G=="function"?G=L[g]=P?[E,G]:[G,E]:P?G.unshift(E):G.push(E),(T=R(f))>0&&G.length>T&&!G.warned){G.warned=!0;var ie=new Error("Possible EventEmitter memory leak detected. "+G.length+" "+String(g)+" listeners added. Use emitter.setMaxListeners() to increase limit");ie.name="MaxListenersExceededWarning",ie.emitter=f,ie.type=g,ie.count=G.length,X=ie,console&&console.warn&&console.warn(X)}return f}function c(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function o(f,g,E){var P={fired:!1,wrapFn:void 0,target:f,type:g,listener:E},T=c.bind(P);return T.listener=E,P.wrapFn=T,T}function l(f,g,E){var P=f._events;if(P===void 0)return[];var T=P[g];return T===void 0?[]:typeof T=="function"?E?[T.listener||T]:[T]:E?function(L){for(var G=new Array(L.length),X=0;X<G.length;++X)G[X]=L[X].listener||L[X];return G}(T):u(T,T.length)}function r(f){var g=this._events;if(g!==void 0){var E=g[f];if(typeof E=="function")return 1;if(E!==void 0)return E.length}return 0}function u(f,g){for(var E=new Array(g),P=0;P<g;++P)E[P]=f[P];return E}function t(f,g,E,P){if(typeof f.on=="function")P.once?f.once(g,E):f.on(g,E);else{if(typeof f.addEventListener!="function")throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof f);f.addEventListener(g,function T(L){P.once&&f.removeEventListener(g,T),E(L)})}}Object.defineProperty(x,"defaultMaxListeners",{enumerable:!0,get:function(){return C},set:function(f){if(typeof f!="number"||f<0||j(f))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+f+".");C=f}}),x.init=function(){this._events!==void 0&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},x.prototype.setMaxListeners=function(f){if(typeof f!="number"||f<0||j(f))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+f+".");return this._maxListeners=f,this},x.prototype.getMaxListeners=function(){return R(this)},x.prototype.emit=function(f){for(var g=[],E=1;E<arguments.length;E++)g.push(arguments[E]);var P=f==="error",T=this._events;if(T!==void 0)P=P&&T.error===void 0;else if(!P)return!1;if(P){var L;if(g.length>0&&(L=g[0]),L instanceof Error)throw L;var G=new Error("Unhandled error."+(L?" ("+L.message+")":""));throw G.context=L,G}var X=T[f];if(X===void 0)return!1;if(typeof X=="function")S(X,this,g);else{var ie=X.length,ke=u(X,ie);for(E=0;E<ie;++E)S(ke[E],this,g)}return!0},x.prototype.addListener=function(f,g){return K(this,f,g,!1)},x.prototype.on=x.prototype.addListener,x.prototype.prependListener=function(f,g){return K(this,f,g,!0)},x.prototype.once=function(f,g){return O(g),this.on(f,o(this,f,g)),this},x.prototype.prependOnceListener=function(f,g){return O(g),this.prependListener(f,o(this,f,g)),this},x.prototype.removeListener=function(f,g){var E,P,T,L,G;if(O(g),(P=this._events)===void 0)return this;if((E=P[f])===void 0)return this;if(E===g||E.listener===g)--this._eventsCount==0?this._events=Object.create(null):(delete P[f],P.removeListener&&this.emit("removeListener",f,E.listener||g));else if(typeof E!="function"){for(T=-1,L=E.length-1;L>=0;L--)if(E[L]===g||E[L].listener===g){G=E[L].listener,T=L;break}if(T<0)return this;T===0?E.shift():function(X,ie){for(;ie+1<X.length;ie++)X[ie]=X[ie+1];X.pop()}(E,T),E.length===1&&(P[f]=E[0]),P.removeListener!==void 0&&this.emit("removeListener",f,G||g)}return this},x.prototype.off=x.prototype.removeListener,x.prototype.removeAllListeners=function(f){var g,E,P;if((E=this._events)===void 0)return this;if(E.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):E[f]!==void 0&&(--this._eventsCount==0?this._events=Object.create(null):delete E[f]),this;if(arguments.length===0){var T,L=Object.keys(E);for(P=0;P<L.length;++P)(T=L[P])!=="removeListener"&&this.removeAllListeners(T);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(typeof(g=E[f])=="function")this.removeListener(f,g);else if(g!==void 0)for(P=g.length-1;P>=0;P--)this.removeListener(f,g[P]);return this},x.prototype.listeners=function(f){return l(this,f,!0)},x.prototype.rawListeners=function(f){return l(this,f,!1)},x.listenerCount=function(f,g){return typeof f.listenerCount=="function"?f.listenerCount(g):r.call(f,g)},x.prototype.listenerCount=r,x.prototype.eventNames=function(){return this._eventsCount>0?i(this._events):[]}},8336:h=>{h.exports=function(i){return!(!i||typeof i=="string")&&(i instanceof Array||Array.isArray(i)||i.length>=0&&(i.splice instanceof Function||Object.getOwnPropertyDescriptor(i,i.length-1)&&i.constructor.name!=="String"))}},3989:h=>{var i="__lodash_placeholder__",w=32,S=1/0,j=NaN,x=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",w],["partialRight",64],["rearg",256]],C="[object Function]",O="[object GeneratorFunction]",R=/^\s+|\s+$/g,K=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,c=/\{\n\/\* \[wrapped with (.+)\] \*/,o=/,? & /,l=/^[-+]0x[0-9a-f]+$/i,r=/^0b[01]+$/i,u=/^\[object .+?Constructor\]$/,t=/^0o[0-7]+$/i,f=/^(?:0|[1-9]\d*)$/,g=parseInt,E=typeof Rt=="object"&&Rt&&Rt.Object===Object&&Rt,P=typeof self=="object"&&self&&self.Object===Object&&self,T=E||P||Function("return this")();function L(M,y,F){switch(F.length){case 0:return M.call(y);case 1:return M.call(y,F[0]);case 2:return M.call(y,F[0],F[1]);case 3:return M.call(y,F[0],F[1],F[2])}return M.apply(y,F)}function G(M,y){return!!(M&&M.length)&&function(F,Y,ee){if(Y!=Y)return function(pe,Se,Ne,je){for(var Be=pe.length,be=Ne+-1;++be<Be;)if(Se(pe[be],be,pe))return be;return-1}(F,X,ee);for(var ue=ee-1,he=F.length;++ue<he;)if(F[ue]===Y)return ue;return-1}(M,y,0)>-1}function X(M){return M!=M}function ie(M,y){for(var F=-1,Y=M.length,ee=0,ue=[];++F<Y;){var he=M[F];he!==y&&he!==i||(M[F]=i,ue[ee++]=F)}return ue}var ke,I,ge,$e=Function.prototype,Re=Object.prototype,De=T["__core-js_shared__"],He=(ke=/[^.]+$/.exec(De&&De.keys&&De.keys.IE_PROTO||""))?"Symbol(src)_1."+ke:"",Ke=$e.toString,rt=Re.hasOwnProperty,J=Re.toString,Te=RegExp("^"+Ke.call(rt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),de=Object.create,Ue=Math.max,xe=Math.min,dt=(I=Ot(Object,"defineProperty"),(ge=Ot.name)&&ge.length>2?I:void 0);function Ce(M){if(!Oe(M)||function(F){return!!He&&He in F}(M))return!1;var y=function(F){var Y=Oe(F)?J.call(F):"";return Y==C||Y==O}(M)||function(F){var Y=!1;if(F!=null&&typeof F.toString!="function")try{Y=!!(F+"")}catch{}return Y}(M)?Te:u;return y.test(function(F){if(F!=null){try{return Ke.call(F)}catch{}try{return F+""}catch{}}return""}(M))}function Fe(M){return function(){var y=arguments;switch(y.length){case 0:return new M;case 1:return new M(y[0]);case 2:return new M(y[0],y[1]);case 3:return new M(y[0],y[1],y[2]);case 4:return new M(y[0],y[1],y[2],y[3]);case 5:return new M(y[0],y[1],y[2],y[3],y[4]);case 6:return new M(y[0],y[1],y[2],y[3],y[4],y[5]);case 7:return new M(y[0],y[1],y[2],y[3],y[4],y[5],y[6])}var F,Y=Oe(F=M.prototype)?de(F):{},ee=M.apply(Y,y);return Oe(ee)?ee:Y}}function xt(M,y,F,Y,ee,ue,he,pe,Se,Ne){var je=128&y,Be=1&y,be=2&y,Ye=24&y,_e=512&y,Je=be?void 0:Fe(M);return function Me(){for(var oe=arguments.length,le=Array(oe),ve=oe;ve--;)le[ve]=arguments[ve];if(Ye)var Ae=Ct(Me),nt=function(te,Ee){for(var Ie=te.length,ze=0;Ie--;)te[Ie]===Ee&&ze++;return ze}(le,Ae);if(Y&&(le=function(te,Ee,Ie,ze){for(var ye=-1,Qe=te.length,Xe=Ie.length,we=-1,Ve=Ee.length,et=Ue(Qe-Xe,0),ot=Array(Ve+et),pt=!ze;++we<Ve;)ot[we]=Ee[we];for(;++ye<Xe;)(pt||ye<Qe)&&(ot[Ie[ye]]=te[ye]);for(;et--;)ot[we++]=te[ye++];return ot}(le,Y,ee,Ye)),ue&&(le=function(te,Ee,Ie,ze){for(var ye=-1,Qe=te.length,Xe=-1,we=Ie.length,Ve=-1,et=Ee.length,ot=Ue(Qe-we,0),pt=Array(ot+et),Ut=!ze;++ye<ot;)pt[ye]=te[ye];for(var _t=ye;++Ve<et;)pt[_t+Ve]=Ee[Ve];for(;++Xe<we;)(Ut||ye<Qe)&&(pt[_t+Ie[Xe]]=te[ye++]);return pt}(le,ue,he,Ye)),oe-=nt,Ye&&oe<Ne){var Ze=ie(le,Ae);return kt(M,y,xt,Me.placeholder,F,le,Ze,pe,Se,Ne-oe)}var Le=Be?F:this,st=be?Le[M]:M;return oe=le.length,pe?le=function(te,Ee){for(var Ie=te.length,ze=xe(Ee.length,Ie),ye=function(Xe,we){var Ve=-1,et=Xe.length;for(we||(we=Array(et));++Ve<et;)we[Ve]=Xe[Ve];return we}(te);ze--;){var Qe=Ee[ze];te[ze]=Ge(Qe,Ie)?ye[Qe]:void 0}return te}(le,pe):_e&&oe>1&&le.reverse(),je&&Se<oe&&(le.length=Se),this&&this!==T&&this instanceof Me&&(st=Je||Fe(st)),st.apply(Le,le)}}function kt(M,y,F,Y,ee,ue,he,pe,Se,Ne){var je=8&y;y|=je?w:64,4&(y&=~(je?64:w))||(y&=-4);var Be=F(M,y,ee,je?ue:void 0,je?he:void 0,je?void 0:ue,je?void 0:he,pe,Se,Ne);return Be.placeholder=Y,jt(Be,M,y)}function qt(M,y,F,Y,ee,ue,he,pe){var Se=2&y;if(!Se&&typeof M!="function")throw new TypeError("Expected a function");var Ne=Y?Y.length:0;if(Ne||(y&=-97,Y=ee=void 0),he=he===void 0?he:Ue(Nt(he),0),pe=pe===void 0?pe:Nt(pe),Ne-=ee?ee.length:0,64&y){var je=Y,Be=ee;Y=ee=void 0}var be=[M,y,F,Y,ee,je,Be,ue,he,pe];if(M=be[0],y=be[1],F=be[2],Y=be[3],ee=be[4],!(pe=be[9]=be[9]==null?Se?0:M.length:Ue(be[9]-Ne,0))&&24&y&&(y&=-25),y&&y!=1)Ye=y==8||y==16?function(_e,Je,Me){var oe=Fe(_e);return function le(){for(var ve=arguments.length,Ae=Array(ve),nt=ve,Ze=Ct(le);nt--;)Ae[nt]=arguments[nt];var Le=ve<3&&Ae[0]!==Ze&&Ae[ve-1]!==Ze?[]:ie(Ae,Ze);return(ve-=Le.length)<Me?kt(_e,Je,xt,le.placeholder,void 0,Ae,Le,void 0,void 0,Me-ve):L(this&&this!==T&&this instanceof le?oe:_e,this,Ae)}}(M,y,pe):y!=w&&y!=33||ee.length?xt.apply(void 0,be):function(_e,Je,Me,oe){var le=1&Je,ve=Fe(_e);return function Ae(){for(var nt=-1,Ze=arguments.length,Le=-1,st=oe.length,te=Array(st+Ze),Ee=this&&this!==T&&this instanceof Ae?ve:_e;++Le<st;)te[Le]=oe[Le];for(;Ze--;)te[Le++]=arguments[++nt];return L(Ee,le?Me:this,te)}}(M,y,F,Y);else var Ye=function(_e,Je,Me){var oe=1&Je,le=Fe(_e);return function ve(){return(this&&this!==T&&this instanceof ve?le:_e).apply(oe?Me:this,arguments)}}(M,y,F);return jt(Ye,M,y)}function Ct(M){return M.placeholder}function Ot(M,y){var F=function(Y,ee){return Y?.[ee]}(M,y);return Ce(F)?F:void 0}function $t(M){var y=M.match(c);return y?y[1].split(o):[]}function B(M,y){var F=y.length,Y=F-1;return y[Y]=(F>1?"& ":"")+y[Y],y=y.join(F>2?", ":" "),M.replace(K,`{
/* [wrapped with `+y+`] */
`)}function Ge(M,y){return!!(y=y??9007199254740991)&&(typeof M=="number"||f.test(M))&&M>-1&&M%1==0&&M<y}var jt=dt?function(M,y,F){var Y,ee=y+"";return dt(M,"toString",{configurable:!0,enumerable:!1,value:(Y=B(ee,St($t(ee),F)),function(){return Y})})}:function(M){return M};function St(M,y){return function(F,Y){for(var ee=-1,ue=F?F.length:0;++ee<ue&&Y(F[ee],ee,F)!==!1;);}(x,function(F){var Y="_."+F[0];y&F[1]&&!G(M,Y)&&M.push(Y)}),M.sort()}function mt(M,y,F){var Y=qt(M,8,void 0,void 0,void 0,void 0,void 0,y=F?void 0:y);return Y.placeholder=mt.placeholder,Y}function Oe(M){var y=typeof M;return!!M&&(y=="object"||y=="function")}function Kt(M){return M?(M=function(y){if(typeof y=="number")return y;if(function(ee){return typeof ee=="symbol"||function(ue){return!!ue&&typeof ue=="object"}(ee)&&J.call(ee)=="[object Symbol]"}(y))return j;if(Oe(y)){var F=typeof y.valueOf=="function"?y.valueOf():y;y=Oe(F)?F+"":F}if(typeof y!="string")return y===0?y:+y;y=y.replace(R,"");var Y=r.test(y);return Y||t.test(y)?g(y.slice(2),Y?2:8):l.test(y)?j:+y}(M))===S||M===-1/0?17976931348623157e292*(M<0?-1:1):M==M?M:0:M===0?M:0}function Nt(M){var y=Kt(M),F=y%1;return y==y?F?y-F:y:0}mt.placeholder={},h.exports=mt},301:(h,i,w)=>{var S=w(8336),j=Array.prototype.concat,x=Array.prototype.slice,C=h.exports=function(O){for(var R=[],K=0,c=O.length;K<c;K++){var o=O[K];S(o)?R=j.call(R,x.call(o)):R.push(o)}return R};C.wrap=function(O){return function(){return O(C(arguments))}}},4119:h=>{h.exports=D}},W={};function U(h){var i=W[h];if(i!==void 0)return i.exports;var w=W[h]={exports:{}};return $[h](w,w.exports,U),w.exports}U.n=h=>{var i=h&&h.__esModule?()=>h.default:()=>h;return U.d(i,{a:i}),i},U.d=(h,i)=>{for(var w in i)U.o(i,w)&&!U.o(h,w)&&Object.defineProperty(h,w,{enumerable:!0,get:i[w]})},U.o=(h,i)=>Object.prototype.hasOwnProperty.call(h,i),U.r=h=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(h,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(h,"__esModule",{value:!0})};var Q={};return(()=>{function h(s){return h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(s)}function i(s){var e=function(a,n){if(h(a)!="object"||!a)return a;var p=a[Symbol.toPrimitive];if(p!==void 0){var v=p.call(a,n);if(h(v)!="object")return v;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(a)}(s,"string");return h(e)=="symbol"?e:e+""}function w(s,e,a){return(e=i(e))in s?Object.defineProperty(s,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):s[e]=a,s}function S(s,e){var a=Object.keys(s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(s);e&&(n=n.filter(function(p){return Object.getOwnPropertyDescriptor(s,p).enumerable})),a.push.apply(a,n)}return a}function j(s){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?S(Object(a),!0).forEach(function(n){w(s,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(a)):S(Object(a)).forEach(function(n){Object.defineProperty(s,n,Object.getOwnPropertyDescriptor(a,n))})}return s}function x(s,e){if(!(s instanceof e))throw new TypeError("Cannot call a class as a function")}function C(s,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(s,i(n.key),n)}}function O(s,e,a){return e&&C(s.prototype,e),a&&C(s,a),Object.defineProperty(s,"prototype",{writable:!1}),s}function R(s){return R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},R(s)}function K(){try{var s=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(K=function(){return!!s})()}function c(s,e){if(e&&(h(e)=="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}(s)}function o(s,e,a){return e=R(e),c(s,K()?Reflect.construct(e,a||[],R(s).constructor):e.apply(s,a))}function l(s,e){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,n){return a.__proto__=n,a},l(s,e)}function r(s,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(e&&e.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),e&&l(s,e)}U.r(Q),U.d(Q,{default:()=>Ga});var u=U(4119),t=U.n(u);function f(){var s=this.constructor.getDerivedStateFromProps(this.props,this.state);s!=null&&this.setState(s)}function g(s){this.setState((function(e){var a=this.constructor.getDerivedStateFromProps(s,e);return a??null}).bind(this))}function E(s,e){try{var a=this.props,n=this.state;this.props=s,this.state=e,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(a,n)}finally{this.props=a,this.state=n}}function P(s){var e=s.prototype;if(!e||!e.isReactComponent)throw new Error("Can only polyfill class components");if(typeof s.getDerivedStateFromProps!="function"&&typeof e.getSnapshotBeforeUpdate!="function")return s;var a=null,n=null,p=null;if(typeof e.componentWillMount=="function"?a="componentWillMount":typeof e.UNSAFE_componentWillMount=="function"&&(a="UNSAFE_componentWillMount"),typeof e.componentWillReceiveProps=="function"?n="componentWillReceiveProps":typeof e.UNSAFE_componentWillReceiveProps=="function"&&(n="UNSAFE_componentWillReceiveProps"),typeof e.componentWillUpdate=="function"?p="componentWillUpdate":typeof e.UNSAFE_componentWillUpdate=="function"&&(p="UNSAFE_componentWillUpdate"),a!==null||n!==null||p!==null){var v=s.displayName||s.name,m=typeof s.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error(`Unsafe legacy lifecycles will not be called for components using new component APIs.

`+v+" uses "+m+" but also contains the following legacy lifecycles:"+(a!==null?`
  `+a:"")+(n!==null?`
  `+n:"")+(p!==null?`
  `+p:"")+`

The above lifecycles should be removed. Learn more about this warning here:
https://fb.me/react-async-component-lifecycle-hooks`)}if(typeof s.getDerivedStateFromProps=="function"&&(e.componentWillMount=f,e.componentWillReceiveProps=g),typeof e.getSnapshotBeforeUpdate=="function"){if(typeof e.componentDidUpdate!="function")throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");e.componentWillUpdate=E;var b=e.componentDidUpdate;e.componentDidUpdate=function(k,N,V){var q=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:V;b.call(this,k,N,q)}}return s}function T(s,e){if(s==null)return{};var a={};for(var n in s)if({}.hasOwnProperty.call(s,n)){if(e.includes(n))continue;a[n]=s[n]}return a}function L(s,e){if(s==null)return{};var a,n,p=T(s,e);if(Object.getOwnPropertySymbols){var v=Object.getOwnPropertySymbols(s);for(n=0;n<v.length;n++)a=v[n],e.includes(a)||{}.propertyIsEnumerable.call(s,a)&&(p[a]=s[a])}return p}function G(s,e){if(e&&s?.constructor===e)return"bigNumber";var a=function(n){return{}.toString.call(n).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}(s);return a==="number"&&(a=isNaN(s)?"nan":(0|s)!=s?"float":"integer"),a}function X(s){return s.replace(/\\/g,"\\\\").replace(/\n/g,"\\n").replace(/\t/g,"\\t").replace(/\r/g,"\\r").replace(/\f/g,"\\f")}f.__suppressDeprecationWarning=!0,g.__suppressDeprecationWarning=!0,E.__suppressDeprecationWarning=!0;var ie={scheme:"rjv-default",author:"mac gainor",base00:"rgba(0, 0, 0, 0)",base01:"rgb(245, 245, 245)",base02:"rgb(235, 235, 235)",base03:"#93a1a1",base04:"rgba(0, 0, 0, 0.3)",base05:"#586e75",base06:"#073642",base07:"#002b36",base08:"#d33682",base09:"#cb4b16",base0A:"#dc322f",base0B:"#859900",base0C:"#6c71c4",base0D:"#586e75",base0E:"#2aa198",base0F:"#268bd2"},ke={scheme:"rjv-grey",author:"mac gainor",base00:"rgba(1, 1, 1, 0)",base01:"rgba(1, 1, 1, 0.1)",base02:"rgba(0, 0, 0, 0.2)",base03:"rgba(1, 1, 1, 0.3)",base04:"rgba(0, 0, 0, 0.4)",base05:"rgba(1, 1, 1, 0.5)",base06:"rgba(1, 1, 1, 0.6)",base07:"rgba(1, 1, 1, 0.7)",base08:"rgba(1, 1, 1, 0.8)",base09:"rgba(1, 1, 1, 0.8)",base0A:"rgba(1, 1, 1, 0.8)",base0B:"rgba(1, 1, 1, 0.8)",base0C:"rgba(1, 1, 1, 0.8)",base0D:"rgba(1, 1, 1, 0.8)",base0E:"rgba(1, 1, 1, 0.8)",base0F:"rgba(1, 1, 1, 0.8)"};const I={globalFontFamily:"monospace",globalCursor:"default",braceFontWeight:"bold",braceCursor:"pointer",ellipsisFontSize:"18px",ellipsisLineHeight:"10px",ellipsisCursor:"pointer",keyMargin:"0px 5px",keyLetterSpacing:"0.5px",keyFontStyle:"none",keyVerticalAlign:"top",keyOpacity:"0.85",keyOpacityHover:"1",keyValPaddingTop:"3px",keyValPaddingBottom:"3px",keyValPaddingRight:"5px",keyValBorderLeft:"1px solid",keyValBorderHover:"2px solid",pushedContentMarginLeft:"6px",variableValuePaddingRight:"6px",nullFontSize:"11px",nullFontWeight:"bold",nullPadding:"1px 2px",nullBorderRadius:"3px",nanFontSize:"11px",nanFontWeight:"bold",nanPadding:"1px 2px",nanBorderRadius:"3px",undefinedFontSize:"11px",undefinedPadding:"1px 2px",undefinedBorderRadius:"3px",dataTypeFontSize:"11px",dataTypeMarginRight:"4px",datatypeOpacity:"0.8",objectSizeBorderRadius:"3px",objectSizeFontStyle:"italic",objectSizeMargin:"0px 6px 0px 0px",clipboardCursor:"pointer",clipboardCheckMarginLeft:"-12px",metaDataPadding:"0px 0px 0px 10px",arrayGroupMetaPadding:"0px 0px 0px 4px",iconContainerWidth:"17px",tooltipPadding:"4px",editInputMinWidth:"130px",editInputBorderRadius:"2px",editInputPadding:"5px",editInputMarginRight:"4px",editInputFontFamily:"monospace",iconCursor:"pointer",iconFontSize:"15px",iconPaddingRight:"1px",dateValueMarginLeft:"2px",iconMarginRight:"3px",detectedRowPaddingTop:"3px",addKeyCoverBackground:"rgba(255, 255, 255, 0.3)",addKeyCoverPosition:"absolute",addKeyCoverPositionPx:"0px",addKeyModalWidth:"200px",addKeyModalMargin:"auto",addKeyModalPadding:"10px",addKeyModalRadius:"3px"};function ge(s,e){(e==null||e>s.length)&&(e=s.length);for(var a=0,n=Array(e);a<e;a++)n[a]=s[a];return n}function $e(s,e){if(s){if(typeof s=="string")return ge(s,e);var a={}.toString.call(s).slice(8,-1);return a==="Object"&&s.constructor&&(a=s.constructor.name),a==="Map"||a==="Set"?Array.from(s):a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?ge(s,e):void 0}}function Re(s,e){return function(a){if(Array.isArray(a))return a}(s)||function(a,n){var p=a==null?null:typeof Symbol<"u"&&a[Symbol.iterator]||a["@@iterator"];if(p!=null){var v,m,b,k,N=[],V=!0,q=!1;try{if(b=(p=p.call(a)).next,n===0){if(Object(p)!==p)return;V=!1}else for(;!(V=(v=b.call(p)).done)&&(N.push(v.value),N.length!==n);V=!0);}catch(H){q=!0,m=H}finally{try{if(!V&&p.return!=null&&(k=p.return(),Object(k)!==k))return}finally{if(q)throw m}}return N}}(s,e)||$e(s,e)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}var De=U(9446),He=U(3639),Ke=U.n(He),rt=U(3989),J=U.n(rt);function Te(s,e){var a=Object.keys(s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(s);e&&(n=n.filter(function(p){return Object.getOwnPropertyDescriptor(s,p).enumerable})),a.push.apply(a,n)}return a}function de(s){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?Te(Object(a),!0).forEach(function(n){w(s,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(a)):Te(Object(a)).forEach(function(n){Object.defineProperty(s,n,Object.getOwnPropertyDescriptor(a,n))})}return s}var Ue=De.default,xe=Object.keys(Ue),dt=function(s){var e,a=function(k){var N=k[0]/255,V=k[1]/255,q=k[2]/255;return[.299*N+.587*V+.114*q,-.14713*N+-.28886*V+.436*q,.615*N+-.51499*V+-.10001*q]}(Ke()(s).array()),n=Re(a,3),p=n[0],v=n[1],m=n[2],b=function(k){var N,V,q,H=k[0],z=k[1],Z=k[2];return N=1*H+0*z+1.13983*Z,V=1*H+-.39465*z+-.5806*Z,q=1*H+2.02311*z+0*Z,[255*(N=Math.min(Math.max(0,N),1)),255*(V=Math.min(Math.max(0,V),1)),255*(q=Math.min(Math.max(0,q),1))]}([(e=p,e<.25?1:e<.5?.9-e:1.1-e),v,m]);return Ke().rgb(b).hex()},Ce=function(s){return function(e){return{className:[e.className,s.className].filter(Boolean).join(" "),style:de(de({},e.style||{}),s.style||{})}}},Fe=function(s,e){var a=Object.keys(e);for(var n in s)a.indexOf(n)===-1&&a.push(n);return a.reduce(function(p,v){return p[v]=function(m,b){if(m===void 0)return b;if(b===void 0)return m;var k=h(m),N=h(b);switch(k){case"string":switch(N){case"string":return[b,m].filter(Boolean).join(" ");case"object":return Ce({className:m,style:b});case"function":return function(V){for(var q=arguments.length,H=new Array(q>1?q-1:0),z=1;z<q;z++)H[z-1]=arguments[z];return Ce({className:m})(b.apply(void 0,[V].concat(H)))}}break;case"object":switch(N){case"string":return Ce({className:b,style:m});case"object":return de(de({},b),m);case"function":return function(V){for(var q=arguments.length,H=new Array(q>1?q-1:0),z=1;z<q;z++)H[z-1]=arguments[z];return Ce({style:m})(b.apply(void 0,[V].concat(H)))}}break;case"function":switch(N){case"string":return function(V){for(var q=arguments.length,H=new Array(q>1?q-1:0),z=1;z<q;z++)H[z-1]=arguments[z];return m.apply(void 0,[Ce(V)({className:b})].concat(H))};case"object":return function(V){for(var q=arguments.length,H=new Array(q>1?q-1:0),z=1;z<q;z++)H[z-1]=arguments[z];return m.apply(void 0,[Ce(V)({style:b})].concat(H))};case"function":return function(V){for(var q=arguments.length,H=new Array(q>1?q-1:0),z=1;z<q;z++)H[z-1]=arguments[z];return m.apply(void 0,[b.apply(void 0,[V].concat(H))].concat(H))}}}}(s[v],e[v]),p},{})},xt=function(s,e){for(var a=arguments.length,n=new Array(a>2?a-2:0),p=2;p<a;p++)n[p-2]=arguments[p];if(e===null)return s;Array.isArray(e)||(e=[e]);var v=e.map(function(m){return s[m]}).filter(Boolean).reduce(function(m,b){return typeof b=="string"?m.className=[m.className,b].filter(Boolean).join(" "):h(b)==="object"?m.style=de(de({},m.style),b):typeof b=="function"&&(m=de(de({},m),b.apply(void 0,[m].concat(n)))),m},{className:"",style:{}});return v.className||delete v.className,Object.keys(v.style).length===0&&delete v.style,v},kt=function(s){return Object.keys(s).reduce(function(e,a){return e[a]=/^base/.test(a)?dt(s[a]):a==="scheme"?s[a]+":inverted":s[a],e},{})},qt=J()(function(s){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=e.defaultBase16,p=n===void 0?Ue:n,v=e.base16Themes,m=Ot(a,v===void 0?null:v);m&&(a=de(de({},m),a));for(var b=xe.reduce(function(Z,se){return Z[se]=a[se]||p[se],Z},{}),k=Object.keys(a).reduce(function(Z,se){return xe.indexOf(se)===-1&&(Z[se]=a[se]),Z},{}),N=s(b),V=Fe(k,N),q=arguments.length,H=new Array(q>3?q-3:0),z=3;z<q;z++)H[z-3]=arguments[z];return J()(xt,2).apply(void 0,[V].concat(H))},3),Ct=function(s){return!!s.extend},Ot=function(s,e){if(s&&Ct(s)&&s.extend&&(s=s.extend),typeof s=="string"){var a=Re(s.split(":"),2),n=a[0],p=a[1];s=e?e[n]:De[n],p==="inverted"&&(s=kt(s))}return s&&Object.prototype.hasOwnProperty.call(s,"base00")?s:void 0},$t=function(s){var e=function(a){return{backgroundColor:a.base00,ellipsisColor:a.base09,braceColor:a.base07,expandedIcon:a.base0D,collapsedIcon:a.base0E,keyColor:a.base07,arrayKeyColor:a.base0C,objectSize:a.base04,copyToClipboard:a.base0F,copyToClipboardCheck:a.base0D,objectBorder:a.base02,dataTypes:{boolean:a.base0E,date:a.base0D,float:a.base0B,function:a.base0D,integer:a.base0F,string:a.base09,nan:a.base08,null:a.base0A,undefined:a.base05,regexp:a.base0A,background:a.base02,bigNumber:a.base09},editVariable:{editIcon:a.base0E,cancelIcon:a.base09,removeIcon:a.base09,addIcon:a.base0E,checkIcon:a.base0E,background:a.base01,color:a.base0A,border:a.base07},addKeyModal:{background:a.base05,border:a.base04,color:a.base0A,labelColor:a.base01},validationFailure:{background:a.base09,iconColor:a.base01,fontColor:a.base01}}}(s);return{"app-container":{fontFamily:I.globalFontFamily,cursor:I.globalCursor,backgroundColor:e.backgroundColor,position:"relative"},ellipsis:{display:"inline-block",color:e.ellipsisColor,fontSize:I.ellipsisFontSize,lineHeight:I.ellipsisLineHeight,cursor:I.ellipsisCursor},"brace-row":{display:"inline-block",cursor:"pointer"},brace:{display:"inline-block",cursor:I.braceCursor,fontWeight:I.braceFontWeight,color:e.braceColor},"expanded-icon":{color:e.expandedIcon},"collapsed-icon":{color:e.collapsedIcon},colon:{display:"inline-block",margin:I.keyMargin,color:e.keyColor,verticalAlign:"top"},objectKeyVal:function(a,n){return{style:j({paddingTop:I.keyValPaddingTop,paddingRight:I.keyValPaddingRight,paddingBottom:I.keyValPaddingBottom,borderLeft:I.keyValBorderLeft+" "+e.objectBorder,":hover":{paddingLeft:n.paddingLeft-1+"px",borderLeft:I.keyValBorderHover+" "+e.objectBorder}},n)}},"object-key-val-no-border":{padding:I.keyValPadding},"pushed-content":{marginLeft:I.pushedContentMarginLeft},variableValue:function(a,n){return{style:j({display:"inline-block",paddingRight:I.variableValuePaddingRight,position:"relative"},n)}},"object-name":{display:"inline-block",color:e.keyColor,letterSpacing:I.keyLetterSpacing,fontStyle:I.keyFontStyle,verticalAlign:I.keyVerticalAlign,opacity:I.keyOpacity,":hover":{opacity:I.keyOpacityHover}},"array-key":{display:"inline-block",color:e.arrayKeyColor,letterSpacing:I.keyLetterSpacing,fontStyle:I.keyFontStyle,verticalAlign:I.keyVerticalAlign,opacity:I.keyOpacity,":hover":{opacity:I.keyOpacityHover}},"object-size":{color:e.objectSize,borderRadius:I.objectSizeBorderRadius,fontStyle:I.objectSizeFontStyle,margin:I.objectSizeMargin,cursor:"default"},"data-type-label":{fontSize:I.dataTypeFontSize,marginRight:I.dataTypeMarginRight,opacity:I.datatypeOpacity},boolean:{display:"inline-block",color:e.dataTypes.boolean},date:{display:"inline-block",color:e.dataTypes.date},"date-value":{marginLeft:I.dateValueMarginLeft},float:{display:"inline-block",color:e.dataTypes.float},function:{display:"inline-block",color:e.dataTypes.function,cursor:"pointer",whiteSpace:"pre-line"},"function-value":{fontStyle:"italic"},integer:{display:"inline-block",color:e.dataTypes.integer},bigNumber:{display:"inline-block",color:e.dataTypes.bigNumber},string:{display:"inline-block",color:e.dataTypes.string},nan:{display:"inline-block",color:e.dataTypes.nan,fontSize:I.nanFontSize,fontWeight:I.nanFontWeight,backgroundColor:e.dataTypes.background,padding:I.nanPadding,borderRadius:I.nanBorderRadius},null:{display:"inline-block",color:e.dataTypes.null,fontSize:I.nullFontSize,fontWeight:I.nullFontWeight,backgroundColor:e.dataTypes.background,padding:I.nullPadding,borderRadius:I.nullBorderRadius},undefined:{display:"inline-block",color:e.dataTypes.undefined,fontSize:I.undefinedFontSize,padding:I.undefinedPadding,borderRadius:I.undefinedBorderRadius,backgroundColor:e.dataTypes.background},regexp:{display:"inline-block",color:e.dataTypes.regexp},"copy-to-clipboard":{cursor:I.clipboardCursor},"copy-icon":{color:e.copyToClipboard,fontSize:I.iconFontSize,marginRight:I.iconMarginRight,verticalAlign:"top"},"copy-icon-copied":{color:e.copyToClipboardCheck,marginLeft:I.clipboardCheckMarginLeft},"array-group-meta-data":{display:"inline-block",padding:I.arrayGroupMetaPadding},"object-meta-data":{display:"inline-block",padding:I.metaDataPadding},"icon-container":{display:"inline-block",width:I.iconContainerWidth},tooltip:{padding:I.tooltipPadding},removeVarIcon:{verticalAlign:"top",display:"inline-block",color:e.editVariable.removeIcon,cursor:I.iconCursor,fontSize:I.iconFontSize,marginRight:I.iconMarginRight},addVarIcon:{verticalAlign:"top",display:"inline-block",color:e.editVariable.addIcon,cursor:I.iconCursor,fontSize:I.iconFontSize,marginRight:I.iconMarginRight},editVarIcon:{verticalAlign:"top",display:"inline-block",color:e.editVariable.editIcon,cursor:I.iconCursor,fontSize:I.iconFontSize,marginRight:I.iconMarginRight},"edit-icon-container":{display:"inline-block",verticalAlign:"top"},"check-icon":{display:"inline-block",cursor:I.iconCursor,color:e.editVariable.checkIcon,fontSize:I.iconFontSize,paddingRight:I.iconPaddingRight},"cancel-icon":{display:"inline-block",cursor:I.iconCursor,color:e.editVariable.cancelIcon,fontSize:I.iconFontSize,paddingRight:I.iconPaddingRight},"edit-input":{display:"inline-block",minWidth:I.editInputMinWidth,borderRadius:I.editInputBorderRadius,backgroundColor:e.editVariable.background,color:e.editVariable.color,padding:I.editInputPadding,marginRight:I.editInputMarginRight,fontFamily:I.editInputFontFamily},"detected-row":{paddingTop:I.detectedRowPaddingTop},"key-modal-request":{position:I.addKeyCoverPosition,top:I.addKeyCoverPositionPx,left:I.addKeyCoverPositionPx,right:I.addKeyCoverPositionPx,bottom:I.addKeyCoverPositionPx,backgroundColor:I.addKeyCoverBackground},"key-modal":{width:I.addKeyModalWidth,backgroundColor:e.addKeyModal.background,marginLeft:I.addKeyModalMargin,marginRight:I.addKeyModalMargin,padding:I.addKeyModalPadding,borderRadius:I.addKeyModalRadius,marginTop:"15px",position:"relative"},"key-modal-label":{color:e.addKeyModal.labelColor,marginLeft:"2px",marginBottom:"5px",fontSize:"11px"},"key-modal-input-container":{overflow:"hidden"},"key-modal-input":{width:"100%",padding:"3px 6px",fontFamily:"monospace",color:e.addKeyModal.color,border:"none",boxSizing:"border-box",borderRadius:"2px"},"key-modal-cancel":{backgroundColor:e.editVariable.removeIcon,position:"absolute",top:"0px",right:"0px",borderRadius:"0px 3px 0px 3px",cursor:"pointer"},"key-modal-cancel-icon":{color:e.addKeyModal.labelColor,fontSize:I.iconFontSize,transform:"rotate(45deg)"},"key-modal-submit":{color:e.editVariable.addIcon,fontSize:I.iconFontSize,position:"absolute",right:"2px",top:"3px",cursor:"pointer"},"function-ellipsis":{display:"inline-block",color:e.ellipsisColor,fontSize:I.ellipsisFontSize,lineHeight:I.ellipsisLineHeight,cursor:I.ellipsisCursor},"validation-failure":{float:"right",padding:"3px 6px",borderRadius:"2px",cursor:"pointer",color:e.validationFailure.fontColor,backgroundColor:e.validationFailure.background},"validation-failure-label":{marginRight:"6px"},"validation-failure-clear":{position:"relative",verticalAlign:"top",cursor:"pointer",color:e.validationFailure.iconColor,fontSize:I.iconFontSize,transform:"rotate(45deg)"}}};function B(s,e,a){return s||console.error("theme has not been set"),function(n){var p=ie;return n!==!1&&n!=="none"||(p=ke),qt($t,{defaultBase16:p})(n)}(s)(e,a)}var Ge=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=(a.rjvId,a.type_name),p=a.displayDataTypes,v=a.theme;return p?t().createElement("span",Object.assign({className:"data-type-label"},B(v,"data-type-label")),n):null}}])}(t().PureComponent),jt=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props;return t().createElement("div",B(a.theme,"boolean"),t().createElement(Ge,Object.assign({type_name:"bool"},a)),a.value?"true":"false")}}])}(t().PureComponent),St=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props;return t().createElement("div",B(a.theme,"date"),t().createElement(Ge,Object.assign({type_name:"date"},a)),t().createElement("span",Object.assign({className:"date-value"},B(a.theme,"date-value")),a.value.toLocaleTimeString("en-us",{weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})))}}])}(t().PureComponent),mt=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props;return t().createElement("div",B(a.theme,"float"),t().createElement(Ge,Object.assign({type_name:"float"},a)),this.props.value)}}])}(t().PureComponent);function Oe(s){return function(e){if(Array.isArray(e))return ge(e)}(s)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(s)||$e(s)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}var Kt=U(9784),Nt=function(){return O(function s(){x(this,s),this.handler=function(){}},[{key:"register",value:function(s){this.handler=s}},{key:"dispatch",value:function(s){var e;(e=this.handler)===null||e===void 0||e.call(this,s)}}])}();globalThis.__globalDispatcherInstance||(globalThis.__globalDispatcherInstance=new Nt);const M=globalThis.__globalDispatcherInstance;var y=new(function(s){function e(){var a;x(this,e);for(var n=arguments.length,p=new Array(n),v=0;v<n;v++)p[v]=arguments[v];return(a=o(this,e,[].concat(p))).objects={},a.set=function(m,b,k,N){a.objects[m]===void 0&&(a.objects[m]={}),a.objects[m][b]===void 0&&(a.objects[m][b]={}),a.objects[m][b][k]=N},a.get=function(m,b,k,N){return a.objects[m]===void 0||a.objects[m][b]===void 0||a.objects[m][b][k]==null?N:a.objects[m][b][k]},a.handleAction=function(m){var b=m.rjvId,k=m.data;switch(m.name){case"RESET":a.emit("reset-"+b);break;case"VARIABLE_UPDATED":m.data.updated_src=a.updateSrc(b,k),a.set(b,"action","variable-update",j(j({},k),{},{type:"variable-edited"})),a.emit("variable-update-"+b);break;case"VARIABLE_REMOVED":m.data.updated_src=a.updateSrc(b,k),a.set(b,"action","variable-update",j(j({},k),{},{type:"variable-removed"})),a.emit("variable-update-"+b);break;case"VARIABLE_ADDED":m.data.updated_src=a.updateSrc(b,k),a.set(b,"action","variable-update",j(j({},k),{},{type:"variable-added"})),a.emit("variable-update-"+b);break;case"ADD_VARIABLE_KEY_REQUEST":a.set(b,"action","new-key-request",k),a.emit("add-key-request-"+b)}},a.updateSrc=function(m,b){var k=b.name,N=b.namespace,V=b.new_value,q=(b.existing_value,b.variable_removed);N.shift();var H,z=a.get(m,"global","src"),Z=a.deepCopy(z,Oe(N)),se=Z,re=function(ce,it){var me=typeof Symbol<"u"&&ce[Symbol.iterator]||ce["@@iterator"];if(!me){if(Array.isArray(ce)||(me=$e(ce))||it){me&&(ce=me);var It=0,lt=function(){};return{s:lt,n:function(){return It>=ce.length?{done:!0}:{done:!1,value:ce[It++]}},e:function(Pe){throw Pe},f:lt}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var bt,ct=!0,fe=!1;return{s:function(){me=me.call(ce)},n:function(){var Pe=me.next();return ct=Pe.done,Pe},e:function(Pe){fe=!0,bt=Pe},f:function(){try{ct||me.return==null||me.return()}finally{if(fe)throw bt}}}}(N);try{for(re.s();!(H=re.n()).done;)se=se[H.value]}catch(ce){re.e(ce)}finally{re.f()}return q?G(se)=="array"?se.splice(k,1):delete se[k]:k!==null?se[k]=V:Z=V,a.set(m,"global","src",Z),Z},a.deepCopy=function(m,b){var k,N=G(m),V=b.shift();return N=="array"?k=Oe(m):N=="object"&&(k=j({},m)),V!==void 0&&(k[V]=a.deepCopy(m[V],b)),k},a}return r(e,s),O(e)}(Kt.EventEmitter));M.register(y.handleAction.bind(y));const F=y;var Y=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).toggleCollapsed=function(){n.setState({collapsed:!n.state.collapsed},function(){F.set(n.props.rjvId,n.props.namespace,"collapsed",n.state.collapsed)})},n.getFunctionDisplay=function(p){var v=n.props;return p?t().createElement("span",null,n.props.value.toString().slice(9,-1).replace(/\{[\s\S]+/,""),t().createElement("span",{className:"function-collapsed",style:{fontWeight:"bold"}},t().createElement("span",null,"{"),t().createElement("span",B(v.theme,"ellipsis"),"..."),t().createElement("span",null,"}"))):n.props.value.toString().slice(9,-1)},n.state={collapsed:F.get(a.rjvId,a.namespace,"collapsed",!0)},n}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=this.state.collapsed;return t().createElement("div",B(a.theme,"function"),t().createElement(Ge,Object.assign({type_name:"function"},a)),t().createElement("span",Object.assign({},B(a.theme,"function-value"),{className:"rjv-function-container",onClick:this.toggleCollapsed}),this.getFunctionDisplay(n)))}}])}(t().PureComponent),ee=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){return t().createElement("div",B(this.props.theme,"nan"),"NaN")}}])}(t().PureComponent),ue=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){return t().createElement("div",B(this.props.theme,"null"),"NULL")}}])}(t().PureComponent),he=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props;return t().createElement("div",B(a.theme,"integer"),t().createElement(Ge,Object.assign({type_name:"int"},a)),this.props.value)}}])}(t().PureComponent),pe=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props;return t().createElement("div",B(a.theme,"regexp"),t().createElement(Ge,Object.assign({type_name:"regexp"},a)),this.props.value.toString())}}])}(t().PureComponent),Se=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).toggleCollapsed=function(){n.setState({collapsed:!n.state.collapsed},function(){F.set(n.props.rjvId,n.props.namespace,"collapsed",n.state.collapsed)})},n.state={collapsed:F.get(a.rjvId,a.namespace,"collapsed",!0)},n}return r(e,s),O(e,[{key:"render",value:function(){var a=this.state.collapsed,n=this.props,p=n.collapseStringsAfterLength,v=n.theme,m=n.escapeStrings,b=n.value,k=G(p)==="integer",N={style:{cursor:"default"}};return m&&(b=X(b)),k&&b.length>p&&(N.style.cursor="pointer",a&&(b=t().createElement("span",null,b.substring(0,p),t().createElement("span",B(v,"ellipsis")," ...")))),t().createElement("div",B(v,"string"),t().createElement(Ge,Object.assign({type_name:"string"},n)),t().createElement("span",Object.assign({className:"string-value"},N,{onClick:this.toggleCollapsed}),'"',b,'"'))}}])}(t().PureComponent),Ne=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){return t().createElement("div",B(this.props.theme,"undefined"),"undefined")}}])}(t().PureComponent),je=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props;return t().createElement("div",B(a.theme,"bigNumber"),t().createElement(Ge,Object.assign({type_name:"bigNumber"},a)),this.props.value.toString())}}])}(t().PureComponent);function Be(){return Be=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var n in a)({}).hasOwnProperty.call(a,n)&&(s[n]=a[n])}return s},Be.apply(null,arguments)}const be=u.useLayoutEffect;var Ye=function(s,e){typeof s!="function"?s.current=e:s(e)};const _e=function(s,e){var a=(0,u.useRef)();return(0,u.useCallback)(function(n){s.current=n,a.current&&Ye(a.current,null),a.current=e,e&&Ye(e,n)},[e])};var Je={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0"},Me=function(s){Object.keys(Je).forEach(function(e){s.style.setProperty(e,Je[e],"important")})},oe=null,le=function(){},ve=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak"],Ae=!!document.documentElement.currentStyle,nt=function(s){var e,a,n=(e=s,a=u.useRef(e),be(function(){a.current=e}),a);(0,u.useLayoutEffect)(function(){var p=function(v){n.current(v)};return window.addEventListener("resize",p),function(){window.removeEventListener("resize",p)}},[])},Ze=function(s,e){var a=s.cacheMeasurements,n=s.maxRows,p=s.minRows,v=s.onChange,m=v===void 0?le:v,b=s.onHeightChange,k=b===void 0?le:b,N=T(s,["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"]),V=N.value!==void 0,q=(0,u.useRef)(null),H=_e(q,e),z=(0,u.useRef)(0),Z=(0,u.useRef)(),se=function(){var re=q.current,ce=a&&Z.current?Z.current:function(lt){var bt=window.getComputedStyle(lt);if(bt===null)return null;var ct,fe=(ct=bt,ve.reduce(function(Et,gt){return Et[gt]=ct[gt],Et},{})),Pe=fe.boxSizing;return Pe===""?null:(Ae&&Pe==="border-box"&&(fe.width=parseFloat(fe.width)+parseFloat(fe.borderRightWidth)+parseFloat(fe.borderLeftWidth)+parseFloat(fe.paddingRight)+parseFloat(fe.paddingLeft)+"px"),{sizingStyle:fe,paddingSize:parseFloat(fe.paddingBottom)+parseFloat(fe.paddingTop),borderSize:parseFloat(fe.borderBottomWidth)+parseFloat(fe.borderTopWidth)})}(re);if(ce){Z.current=ce;var it=function(lt,bt,ct,fe){ct===void 0&&(ct=1),fe===void 0&&(fe=1/0),oe||((oe=document.createElement("textarea")).setAttribute("tabindex","-1"),oe.setAttribute("aria-hidden","true"),Me(oe)),oe.parentNode===null&&document.body.appendChild(oe);var Pe=lt.paddingSize,Et=lt.borderSize,gt=lt.sizingStyle,pa=gt.boxSizing;Object.keys(gt).forEach(function(Qt){var vt=Qt;oe.style[vt]=gt[vt]}),Me(oe),oe.value=bt;var Pt=function(Qt,vt){var ba=Qt.scrollHeight;return vt.sizingStyle.boxSizing==="border-box"?ba+vt.borderSize:ba-vt.paddingSize}(oe,lt);oe.value="x";var Yt=oe.scrollHeight-Pe,Jt=Yt*ct;pa==="border-box"&&(Jt=Jt+Pe+Et),Pt=Math.max(Jt,Pt);var Zt=Yt*fe;return pa==="border-box"&&(Zt=Zt+Pe+Et),[Pt=Math.min(Zt,Pt),Yt]}(ce,re.value||re.placeholder||"x",p,n),me=it[0],It=it[1];z.current!==me&&(z.current=me,re.style.setProperty("height",me+"px","important"),k(me,{rowHeight:It}))}};return(0,u.useLayoutEffect)(se),nt(se),(0,u.createElement)("textarea",Be({},N,{onChange:function(re){V||se(),m(re)},ref:H}))};const Le=(0,u.forwardRef)(Ze);function st(s,e){s=s.trim();try{if((s=structuredClone(s))[0]==="[")return te("array",JSON.parse(s));if(s[0]==="{")return te("object",JSON.parse(s));if(s.match(/\-?\d+\.\d+/)&&s.match(/\-?\d+\.\d+/)[0]===s)return e&&parseFloat(s).toString()!==s?te("bigNumber",s):te("float",parseFloat(s));if(s.match(/\-?\d+e-\d+/)&&s.match(/\-?\d+e-\d+/)[0]===s)return te("float",Number(s));if(s.match(/\-?\d+/)&&s.match(/\-?\d+/)[0]===s)return e&&parseInt(s).toString()!==s?te("bigNumber",s):te("integer",parseInt(s));if(s.match(/\-?\d+e\+\d+/)&&s.match(/\-?\d+e\+\d+/)[0]===s)return te("integer",Number(s))}catch{}switch(s=s.toLowerCase()){case"undefined":return te("undefined",void 0);case"nan":return te("nan",NaN);case"null":return te("null",null);case"true":return te("boolean",!0);case"false":return te("boolean",!1);default:if(s=Date.parse(s))return te("date",new Date(s))}return te(!1,null)}function te(s,e){return{type:s,value:e}}var Ee=["style"],Ie=["style"],ze=["style"],ye=["style"],Qe=["style"],Xe=["style"],we=["style"],Ve=["style"],et=["style"],ot=["style"],pt=["style"],Ut=["style"],_t=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,Ee);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 24 24",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("path",{d:"M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7,13H17V11H7"})))}}])}(t().PureComponent),Pa=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,Ie);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 24 24",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("path",{d:"M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M13,7H11V11H7V13H11V17H13V13H17V11H13V7Z"})))}}])}(t().PureComponent),Ra=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,ze),v=qe(n).style;return t().createElement("span",p,t().createElement("svg",{fill:v.color,width:v.height,height:v.width,style:v,viewBox:"0 0 1792 1792"},t().createElement("path",{d:"M1344 800v64q0 14-9 23t-23 9h-832q-14 0-23-9t-9-23v-64q0-14 9-23t23-9h832q14 0 23 9t9 23zm128 448v-832q0-66-47-113t-113-47h-832q-66 0-113 47t-47 113v832q0 66 47 113t113 47h832q66 0 113-47t47-113zm128-832v832q0 119-84.5 203.5t-203.5 84.5h-832q-119 0-203.5-84.5t-84.5-203.5v-832q0-119 84.5-203.5t203.5-84.5h832q119 0 203.5 84.5t84.5 203.5z"})))}}])}(t().PureComponent),Da=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,ye),v=qe(n).style;return t().createElement("span",p,t().createElement("svg",{fill:v.color,width:v.height,height:v.width,style:v,viewBox:"0 0 1792 1792"},t().createElement("path",{d:"M1344 800v64q0 14-9 23t-23 9h-352v352q0 14-9 23t-23 9h-64q-14 0-23-9t-9-23v-352h-352q-14 0-23-9t-9-23v-64q0-14 9-23t23-9h352v-352q0-14 9-23t23-9h64q14 0 23 9t9 23v352h352q14 0 23 9t9 23zm128 448v-832q0-66-47-113t-113-47h-832q-66 0-113 47t-47 113v832q0 66 47 113t113 47h832q66 0 113-47t47-113zm128-832v832q0 119-84.5 203.5t-203.5 84.5h-832q-119 0-203.5-84.5t-84.5-203.5v-832q0-119 84.5-203.5t203.5-84.5h832q119 0 203.5 84.5t84.5 203.5z"})))}}])}(t().PureComponent),Ta=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,Qe);return t().createElement("span",p,t().createElement("svg",{style:j(j({},qe(n).style),{},{paddingLeft:"2px",verticalAlign:"top"}),viewBox:"0 0 15 15",fill:"currentColor"},t().createElement("path",{d:"M0 14l6-6-6-6z"})))}}])}(t().PureComponent),Fa=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,Xe);return t().createElement("span",p,t().createElement("svg",{style:j(j({},qe(n).style),{},{paddingLeft:"2px",verticalAlign:"top"}),viewBox:"0 0 15 15",fill:"currentColor"},t().createElement("path",{d:"M0 5l6 6 6-6z"})))}}])}(t().PureComponent),na=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,we);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 40 40",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("g",null,t().createElement("path",{d:"m30 35h-25v-22.5h25v7.5h2.5v-12.5c0-1.4-1.1-2.5-2.5-2.5h-7.5c0-2.8-2.2-5-5-5s-5 2.2-5 5h-7.5c-1.4 0-2.5 1.1-2.5 2.5v27.5c0 1.4 1.1 2.5 2.5 2.5h25c1.4 0 2.5-1.1 2.5-2.5v-5h-2.5v5z m-20-27.5h2.5s2.5-1.1 2.5-2.5 1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5 1.3 2.5 2.5 2.5h2.5s2.5 1.1 2.5 2.5h-20c0-1.5 1.1-2.5 2.5-2.5z m-2.5 20h5v-2.5h-5v2.5z m17.5-5v-5l-10 7.5 10 7.5v-5h12.5v-5h-12.5z m-17.5 10h7.5v-2.5h-7.5v2.5z m12.5-17.5h-12.5v2.5h12.5v-2.5z m-7.5 5h-5v2.5h5v-2.5z"}))))}}])}(t().PureComponent),Wt=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,Ve);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 40 40",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("g",null,t().createElement("path",{d:"m28.6 25q0-0.5-0.4-1l-4-4 4-4q0.4-0.5 0.4-1 0-0.6-0.4-1.1l-2-2q-0.4-0.4-1-0.4-0.6 0-1 0.4l-4.1 4.1-4-4.1q-0.4-0.4-1-0.4-0.6 0-1 0.4l-2 2q-0.5 0.5-0.5 1.1 0 0.5 0.5 1l4 4-4 4q-0.5 0.5-0.5 1 0 0.7 0.5 1.1l2 2q0.4 0.4 1 0.4 0.6 0 1-0.4l4-4.1 4.1 4.1q0.4 0.4 1 0.4 0.6 0 1-0.4l2-2q0.4-0.4 0.4-1z m8.7-5q0 4.7-2.3 8.6t-6.3 6.2-8.6 2.3-8.6-2.3-6.2-6.2-2.3-8.6 2.3-8.6 6.2-6.2 8.6-2.3 8.6 2.3 6.3 6.2 2.3 8.6z"}))))}}])}(t().PureComponent),Ba=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,et);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 40 40",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("g",null,t().createElement("path",{d:"m30.1 21.4v-2.8q0-0.6-0.4-1t-1-0.5h-5.7v-5.7q0-0.6-0.4-1t-1-0.4h-2.9q-0.6 0-1 0.4t-0.4 1v5.7h-5.7q-0.6 0-1 0.5t-0.5 1v2.8q0 0.6 0.5 1t1 0.5h5.7v5.7q0 0.5 0.4 1t1 0.4h2.9q0.6 0 1-0.4t0.4-1v-5.7h5.7q0.6 0 1-0.5t0.4-1z m7.2-1.4q0 4.7-2.3 8.6t-6.3 6.2-8.6 2.3-8.6-2.3-6.2-6.2-2.3-8.6 2.3-8.6 6.2-6.2 8.6-2.3 8.6 2.3 6.3 6.2 2.3 8.6z"}))))}}])}(t().PureComponent),sa=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,ot);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 40 40",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("g",null,t().createElement("path",{d:"m31.6 21.6h-10v10h-3.2v-10h-10v-3.2h10v-10h3.2v10h10v3.2z"}))))}}])}(t().PureComponent),La=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,pt);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 40 40",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("g",null,t().createElement("path",{d:"m19.8 26.4l2.6-2.6-3.4-3.4-2.6 2.6v1.3h2.2v2.1h1.2z m9.8-16q-0.3-0.4-0.7 0l-7.8 7.8q-0.4 0.4 0 0.7t0.7 0l7.8-7.8q0.4-0.4 0-0.7z m1.8 13.2v4.3q0 2.6-1.9 4.5t-4.5 1.9h-18.6q-2.6 0-4.5-1.9t-1.9-4.5v-18.6q0-2.7 1.9-4.6t4.5-1.8h18.6q1.4 0 2.6 0.5 0.3 0.2 0.4 0.5 0.1 0.4-0.2 0.7l-1.1 1.1q-0.3 0.3-0.7 0.1-0.5-0.1-1-0.1h-18.6q-1.4 0-2.5 1.1t-1 2.5v18.6q0 1.4 1 2.5t2.5 1h18.6q1.5 0 2.5-1t1.1-2.5v-2.9q0-0.2 0.2-0.4l1.4-1.5q0.3-0.3 0.8-0.1t0.4 0.6z m-2.1-16.5l6.4 6.5-15 15h-6.4v-6.5z m9.9 3l-2.1 2-6.4-6.4 2.1-2q0.6-0.7 1.5-0.7t1.5 0.7l3.4 3.4q0.6 0.6 0.6 1.5t-0.6 1.5z"}))))}}])}(t().PureComponent),Ht=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.style,p=L(a,Ut);return t().createElement("span",p,t().createElement("svg",Object.assign({},qe(n),{viewBox:"0 0 40 40",fill:"currentColor",preserveAspectRatio:"xMidYMid meet"}),t().createElement("g",null,t().createElement("path",{d:"m31.7 16.4q0-0.6-0.4-1l-2.1-2.1q-0.4-0.4-1-0.4t-1 0.4l-9.1 9.1-5-5q-0.5-0.4-1-0.4t-1 0.4l-2.1 2q-0.4 0.4-0.4 1 0 0.6 0.4 1l8.1 8.1q0.4 0.4 1 0.4 0.6 0 1-0.4l12.2-12.1q0.4-0.4 0.4-1z m5.6 3.6q0 4.7-2.3 8.6t-6.3 6.2-8.6 2.3-8.6-2.3-6.2-6.2-2.3-8.6 2.3-8.6 6.2-6.2 8.6-2.3 8.6 2.3 6.3 6.2 2.3 8.6z"}))))}}])}(t().PureComponent);function qe(s){return s||(s={}),{style:j(j({verticalAlign:"middle"},s),{},{color:s.color?s.color:"#000000",height:"1em",width:"1em"})}}var oa=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).copiedTimer=null,n.copyToClipboardFallback=function(p){var v=document.createElement("textarea");v.value=p,document.body.appendChild(v),v.select(),document.execCommand("copy"),document.body.removeChild(v)},n.handleCopy=function(){var p=n.props,v=p.clickCallback,m=p.src,b=p.namespace,k=JSON.stringify(n.clipboardValue(m),null,"  ");navigator.clipboard?navigator.clipboard.writeText(k).catch(function(){n.copyToClipboardFallback(k)}):n.copyToClipboardFallback(k),n.copiedTimer=setTimeout(function(){n.setState({copied:!1})},5500),n.setState({copied:!0},function(){typeof v=="function"&&v({src:m,namespace:b,name:b[b.length-1]})})},n.getClippyIcon=function(){var p=n.props.theme;return n.state.copied?t().createElement("span",null,t().createElement(na,Object.assign({className:"copy-icon"},B(p,"copy-icon"))),t().createElement("span",B(p,"copy-icon-copied"),"✔")):t().createElement(na,Object.assign({className:"copy-icon"},B(p,"copy-icon")))},n.clipboardValue=function(p){switch(G(p)){case"function":case"regexp":return p.toString();default:return p}},n.state={copied:!1},n}return r(e,s),O(e,[{key:"componentWillUnmount",value:function(){this.copiedTimer&&(clearTimeout(this.copiedTimer),this.copiedTimer=null)}},{key:"render",value:function(){var a=this.props,n=(a.src,a.theme),p=a.hidden,v=a.rowHovered,m=B(n,"copy-to-clipboard").style,b="inline";return p&&(b="none"),t().createElement("span",{className:"copy-to-clipboard-container",title:"Copy to clipboard",style:{verticalAlign:"top",display:v?"inline-block":"none"}},t().createElement("span",{style:j(j({},m),{},{display:b}),onClick:this.handleCopy},this.getClippyIcon()))}}])}(t().PureComponent);const za=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).getEditIcon=function(){var p=n.props,v=p.variable,m=p.theme;return t().createElement("div",{className:"click-to-edit",style:{verticalAlign:"top",display:n.state.hovered?"inline-block":"none"}},t().createElement(La,Object.assign({className:"click-to-edit-icon"},B(m,"editVarIcon"),{onClick:function(){n.prepopInput(v)}})))},n.prepopInput=function(p){if(n.props.onEdit!==!1){var v=function(b,k){var N;switch(G(b,k)){case"undefined":N="undefined";break;case"nan":N="NaN";break;case"string":N=b;break;case"bigNumber":case"date":case"function":case"regexp":N=b.toString();break;default:try{N=JSON.stringify(b,null,"  ")}catch{N=""}}return N}(p.value,n.props.bigNumber),m=st(v,n.props.bigNumber);n.setState({editMode:!0,editValue:v,parsedInput:{type:m.type,value:m.value}})}},n.getRemoveIcon=function(){var p=n.props,v=p.variable,m=p.namespace,b=p.theme,k=p.rjvId;return t().createElement("div",{className:"click-to-remove",style:{verticalAlign:"top",display:n.state.hovered?"inline-block":"none"}},t().createElement(Wt,Object.assign({className:"click-to-remove-icon"},B(b,"removeVarIcon"),{onClick:function(){M.dispatch({name:"VARIABLE_REMOVED",rjvId:k,data:{name:v.name,namespace:m,existing_value:v.value,variable_removed:!0}})}})))},n.getValue=function(p,v){var m=!v&&p.type,b=n.props;switch(m){case!1:return n.getEditInput();case"string":return t().createElement(Se,Object.assign({value:p.value},b));case"integer":return t().createElement(he,Object.assign({value:p.value},b));case"float":return t().createElement(mt,Object.assign({value:p.value},b));case"boolean":return t().createElement(jt,Object.assign({value:p.value},b));case"function":return t().createElement(Y,Object.assign({value:p.value},b));case"null":return t().createElement(ue,b);case"nan":return t().createElement(ee,b);case"undefined":return t().createElement(Ne,b);case"date":return t().createElement(St,Object.assign({value:p.value},b));case"regexp":return t().createElement(pe,Object.assign({value:p.value},b));case"bigNumber":return t().createElement(je,Object.assign({value:p.value},b));default:return t().createElement("div",{className:"object-value"},JSON.stringify(p.value))}},n.getEditInput=function(){var p=n.props,v=p.keyModifier,m=p.selectOnFocus,b=p.theme,k=n.state.editValue;return t().createElement("div",null,t().createElement(Le,Object.assign({type:"text",ref:function(N){N&&N[m?"select":"focus"]()},value:k,className:"variable-editor",onChange:function(N){var V=N.target.value,q=st(V,n.props.bigNumber);n.setState({editValue:V,parsedInput:{type:q.type,value:q.value}})},onKeyDown:function(N){switch(N.key){case"Escape":n.setState({editMode:!1,editValue:""});break;case"Enter":v(N,"submit")&&n.submitEdit(!0)}N.stopPropagation()},placeholder:"update this value",minRows:2},B(b,"edit-input"))),t().createElement("div",B(b,"edit-icon-container"),t().createElement(Wt,Object.assign({className:"edit-cancel"},B(b,"cancel-icon"),{onClick:function(N){N&&N.stopPropagation(),n.setState({editMode:!1,editValue:""})}})),t().createElement(Ht,Object.assign({className:"edit-check string-value"},B(b,"check-icon"),{onClick:function(N){N&&N.stopPropagation(),n.submitEdit()}})),t().createElement("div",null,n.showDetected())))},n.submitEdit=function(p){var v=n.props,m=v.variable,b=v.namespace,k=v.rjvId,N=v.bigNumber,V=n.state,q=V.editValue,H=V.parsedInput,z=q;p&&H.type&&(z=H.value,N&&H.type==="bigNumber"&&(z=new N(z))),n.setState({editMode:!1}),M.dispatch({name:"VARIABLE_UPDATED",rjvId:k,data:{name:m.name,namespace:b,existing_value:m.value,new_value:z,variable_removed:!1}})},n.showDetected=function(){var p=n.props,v=p.theme,m=(p.variable,p.namespace,p.rjvId,n.state.parsedInput),b=(m.type,m.value,n.getDetectedInput());if(b)return t().createElement("div",null,t().createElement("div",B(v,"detected-row"),b,t().createElement(Ht,{className:"edit-check detected",style:j({verticalAlign:"top",paddingLeft:"3px"},B(v,"check-icon").style),onClick:function(k){k&&k.stopPropagation(),n.submitEdit(!0)}})))},n.getDetectedInput=function(){var p=n.state.parsedInput,v=p.type,m=p.value,b=n.props,k=b.theme;if(v!==!1)switch(v.toLowerCase()){case"object":return t().createElement("span",null,t().createElement("span",{style:j(j({},B(k,"brace").style),{},{cursor:"default"})},"{"),t().createElement("span",{style:j(j({},B(k,"ellipsis").style),{},{cursor:"default"})},"..."),t().createElement("span",{style:j(j({},B(k,"brace").style),{},{cursor:"default"})},"}"));case"array":return t().createElement("span",null,t().createElement("span",{style:j(j({},B(k,"brace").style),{},{cursor:"default"})},"["),t().createElement("span",{style:j(j({},B(k,"ellipsis").style),{},{cursor:"default"})},"..."),t().createElement("span",{style:j(j({},B(k,"brace").style),{},{cursor:"default"})},"]"));case"string":return t().createElement(Se,Object.assign({value:m},b));case"integer":return t().createElement(he,Object.assign({value:m},b));case"float":return t().createElement(mt,Object.assign({value:m},b));case"boolean":return t().createElement(jt,Object.assign({value:m},b));case"function":return t().createElement(Y,Object.assign({value:m},b));case"null":return t().createElement(ue,b);case"nan":return t().createElement(ee,b);case"undefined":return t().createElement(Ne,b);case"date":return t().createElement(St,Object.assign({value:new Date(m)},b));case"bignumber":return t().createElement(je,Object.assign({value:m},b))}},n.state={editMode:!1,editValue:"",hovered:!1,renameKey:!1,parsedInput:{type:!1,value:null}},n}return r(e,s),O(e,[{key:"render",value:function(){var a=this,n=this.props,p=n.variable,v=n.singleIndent,m=n.type,b=n.theme,k=n.namespace,N=n.indentWidth,V=n.enableClipboard,q=n.onEdit,H=n.onDelete,z=n.onSelect,Z=n.displayArrayKey,se=n.quotesOnKeys,re=n.keyModifier,ce=this.state.editMode;return t().createElement("div",Object.assign({},B(b,"objectKeyVal",{paddingLeft:N*v}),{onMouseEnter:function(){return a.setState(j(j({},a.state),{},{hovered:!0}))},onMouseLeave:function(){return a.setState(j(j({},a.state),{},{hovered:!1}))},className:"variable-row",key:p.name}),m=="array"?Z?t().createElement("span",Object.assign({},B(b,"array-key"),{key:p.name+"_"+k}),p.name,t().createElement("div",B(b,"colon"),":")):null:t().createElement("span",null,t().createElement("span",Object.assign({},B(b,"object-name"),{className:"object-key",key:p.name+"_"+k}),!!se&&t().createElement("span",{style:{verticalAlign:"top"}},'"'),t().createElement("span",{style:{display:"inline-block"}},X(p.name)),!!se&&t().createElement("span",{style:{verticalAlign:"top"}},'"')),t().createElement("span",B(b,"colon"),":")),t().createElement("div",Object.assign({className:"variable-value",onClick:z===!1&&q===!1?null:function(it){var me=Oe(k);re(it,"edit")&&q!==!1?a.prepopInput(p):z!==!1&&(me.shift(),z(j(j({},p),{},{namespace:me})))}},B(b,"variableValue",{cursor:z===!1?"default":"pointer"})),this.getValue(p,ce)),V?t().createElement(oa,{rowHovered:this.state.hovered,hidden:ce,src:p.value,clickCallback:V,theme:b,namespace:[].concat(Oe(k),[p.name])}):null,q!==!1&&ce==0?this.getEditIcon():null,H!==!1&&ce==0?this.getRemoveIcon():null)}}])}(t().PureComponent);var ia=function(s){function e(){var a;x(this,e);for(var n=arguments.length,p=new Array(n),v=0;v<n;v++)p[v]=arguments[v];return(a=o(this,e,[].concat(p))).getObjectSize=function(){var m=a.props,b=m.size,k=m.theme;if(m.displayObjectSize)return t().createElement("span",Object.assign({className:"object-size"},B(k,"object-size")),b," item",b===1?"":"s")},a.getAddAttribute=function(m){var b=a.props,k=b.theme,N=b.namespace,V=b.name,q=b.src,H=b.rjvId,z=b.depth;return t().createElement("span",{className:"click-to-add",style:{verticalAlign:"top",display:m?"inline-block":"none"}},t().createElement(Ba,Object.assign({className:"click-to-add-icon"},B(k,"addVarIcon"),{onClick:function(){var Z={name:z>0?V:null,namespace:N.splice(0,N.length-1),existing_value:q,variable_removed:!1,key_name:null};G(q)==="object"?M.dispatch({name:"ADD_VARIABLE_KEY_REQUEST",rjvId:H,data:Z}):M.dispatch({name:"VARIABLE_ADDED",rjvId:H,data:j(j({},Z),{},{new_value:[].concat(Oe(q),[null])})})}})))},a.getRemoveObject=function(m){var b=a.props,k=b.theme,N=(b.hover,b.namespace),V=b.name,q=b.src,H=b.rjvId;if(N.length!==1)return t().createElement("span",{className:"click-to-remove",style:{display:m?"inline-block":"none"}},t().createElement(Wt,Object.assign({className:"click-to-remove-icon"},B(k,"removeVarIcon"),{onClick:function(){M.dispatch({name:"VARIABLE_REMOVED",rjvId:H,data:{name:V,namespace:N.splice(0,N.length-1),existing_value:q,variable_removed:!0}})}})))},a.render=function(){var m=a.props,b=m.theme,k=m.onDelete,N=m.onAdd,V=m.enableClipboard,q=m.src,H=m.namespace,z=m.rowHovered;return t().createElement("div",Object.assign({},B(b,"object-meta-data"),{className:"object-meta-data",onClick:function(Z){Z.stopPropagation()}}),a.getObjectSize(),V?t().createElement(oa,{rowHovered:z,clickCallback:V,src:q,theme:b,namespace:H}):null,N!==!1?a.getAddAttribute(z):null,k!==!1?a.getRemoveObject(z):null)},a}return r(e,s),O(e)}(t().PureComponent);function la(s){var e=s.parent_type,a=s.namespace,n=s.quotesOnKeys,p=s.theme,v=s.jsvRoot,m=s.name,b=s.displayArrayKey,k=s.name?s.name:"";return!v||m!==!1&&m!==null?e=="array"?b?t().createElement("span",Object.assign({},B(p,"array-key"),{key:a}),t().createElement("span",{className:"array-key"},k),t().createElement("span",B(p,"colon"),":")):t().createElement("span",null):t().createElement("span",Object.assign({},B(p,"object-name"),{key:a}),t().createElement("span",{className:"object-key"},n&&t().createElement("span",{style:{verticalAlign:"top"}},'"'),t().createElement("span",null,k),n&&t().createElement("span",{style:{verticalAlign:"top"}},'"')),t().createElement("span",B(p,"colon"),":")):t().createElement("span",null)}function ca(s){var e=s.theme;switch(s.iconStyle){case"triangle":return t().createElement(Fa,Object.assign({},B(e,"expanded-icon"),{className:"expanded-icon"}));case"square":return t().createElement(Ra,Object.assign({},B(e,"expanded-icon"),{className:"expanded-icon"}));default:return t().createElement(_t,Object.assign({},B(e,"expanded-icon"),{className:"expanded-icon"}))}}function ua(s){var e=s.theme;switch(s.iconStyle){case"triangle":return t().createElement(Ta,Object.assign({},B(e,"collapsed-icon"),{className:"collapsed-icon"}));case"square":return t().createElement(Da,Object.assign({},B(e,"collapsed-icon"),{className:"collapsed-icon"}));default:return t().createElement(Pa,Object.assign({},B(e,"collapsed-icon"),{className:"collapsed-icon"}))}}var Va=["src","groupArraysAfterLength","depth","name","theme","jsvRoot","namespace","parent_type"],da=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).toggleCollapsed=function(p){var v=[];for(var m in n.state.expanded)v.push(n.state.expanded[m]);v[p]=!v[p],n.setState({expanded:v})},n.state={expanded:[]},n}return r(e,s),O(e,[{key:"getExpandedIcon",value:function(a){var n=this.props,p=n.theme,v=n.iconStyle;return this.state.expanded[a]?t().createElement(ca,{theme:p,iconStyle:v}):t().createElement(ua,{theme:p,iconStyle:v})}},{key:"render",value:function(){var a=this,n=this.props,p=n.src,v=n.groupArraysAfterLength,m=(n.depth,n.name),b=n.theme,k=n.jsvRoot,N=n.namespace,V=(n.parent_type,L(n,Va)),q=0,H=5*this.props.indentWidth;k||(q=5*this.props.indentWidth);var z=v,Z=Math.ceil(p.length/z);return t().createElement("div",Object.assign({className:"object-key-val"},B(b,k?"jsv-root":"objectKeyVal",{paddingLeft:q})),t().createElement(la,this.props),t().createElement("span",null,t().createElement(ia,Object.assign({size:p.length},this.props))),Oe(Array(Z)).map(function(se,re){return t().createElement("div",Object.assign({key:re,className:"object-key-val array-group"},B(b,"objectKeyVal",{marginLeft:6,paddingLeft:H})),t().createElement("span",B(b,"brace-row"),t().createElement("div",Object.assign({className:"icon-container"},B(b,"icon-container"),{onClick:function(ce){a.toggleCollapsed(re)}}),a.getExpandedIcon(re)),a.state.expanded[re]?t().createElement(Mt,Object.assign({key:m+re,depth:0,name:!1,collapsed:!1,groupArraysAfterLength:z,index_offset:re*z,src:p.slice(re*z,re*z+z),namespace:N,type:"array",parent_type:"array_group",theme:b},V)):t().createElement("span",Object.assign({},B(b,"brace"),{onClick:function(ce){a.toggleCollapsed(re)},className:"array-group-brace"}),"[",t().createElement("div",Object.assign({},B(b,"array-group-meta-data"),{className:"array-group-meta-data"}),t().createElement("span",Object.assign({className:"object-size"},B(b,"object-size")),re*z," - ",re*z+z>p.length?p.length:re*z+z)),"]")))}))}}])}(t().PureComponent),qa=["depth","src","namespace","name","type","parent_type","theme","jsvRoot","iconStyle"],Gt=function(s){function e(a){var n;x(this,e),(n=o(this,e,[a])).toggleCollapsed=function(){n.setState({expanded:!n.state.expanded},function(){F.set(n.props.rjvId,n.props.namespace,"expanded",n.state.expanded)})},n.getObjectContent=function(v,m,b){return t().createElement("div",{className:"pushed-content object-container"},t().createElement("div",Object.assign({className:"object-content"},B(n.props.theme,"pushed-content")),n.renderObjectContents(m,b)))},n.getEllipsis=function(){return n.state.size===0?null:t().createElement("div",Object.assign({},B(n.props.theme,"ellipsis"),{className:"node-ellipsis",onClick:n.toggleCollapsed}),"...")},n.getObjectMetaData=function(v){var m=n.props,b=(m.rjvId,m.theme,n.state),k=b.size,N=b.hovered;return t().createElement(ia,Object.assign({rowHovered:N,size:k},n.props))},n.renderObjectContents=function(v,m){var b,k=n.props,N=k.depth,V=k.parent_type,q=k.index_offset,H=k.groupArraysAfterLength,z=k.namespace,Z=n.state.object_type,se=[],re=Object.keys(v||{});return n.props.sortKeys&&Z!=="array"&&(re=re.sort()),re.forEach(function(ce){if(b=new $a(ce,v[ce],m.bigNumber),V==="array_group"&&q&&(b.name=parseInt(b.name)+q),Object.prototype.hasOwnProperty.call(v,ce))if(b.type==="object")se.push(t().createElement(Mt,Object.assign({key:b.name,depth:N+1,name:b.name,src:b.value,namespace:z.concat(b.name),parent_type:Z},m)));else if(b.type==="array"){var it=Mt;H&&b.value.length>H&&(it=da),se.push(t().createElement(it,Object.assign({key:b.name,depth:N+1,name:b.name,src:b.value,namespace:z.concat(b.name),type:"array",parent_type:Z},m)))}else se.push(t().createElement(za,Object.assign({key:b.name+"_"+z,variable:b,singleIndent:5,namespace:z,type:n.props.type},m)))}),se};var p=e.getState(a);return n.state=j(j({},p),{},{prevProps:{}}),n}return r(e,s),O(e,[{key:"getBraceStart",value:function(a,n){var p=this,v=this.props,m=v.src,b=v.theme,k=v.iconStyle;if(v.parent_type==="array_group")return t().createElement("span",null,t().createElement("span",B(b,"brace"),a==="array"?"[":"{"),n?this.getObjectMetaData(m):null);var N=n?ca:ua;return t().createElement("span",null,t().createElement("span",Object.assign({onClick:function(V){p.toggleCollapsed()}},B(b,"brace-row")),t().createElement("div",Object.assign({className:"icon-container"},B(b,"icon-container")),t().createElement(N,{theme:b,iconStyle:k})),t().createElement(la,this.props),t().createElement("span",B(b,"brace"),a==="array"?"[":"{")),n?this.getObjectMetaData(m):null)}},{key:"render",value:function(){var a=this,n=this.props,p=n.depth,v=n.src,m=(n.namespace,n.name,n.type,n.parent_type),b=n.theme,k=n.jsvRoot,N=n.iconStyle,V=L(n,qa),q=this.state,H=q.object_type,z=q.expanded,Z={};return k||m==="array_group"?m==="array_group"&&(Z.borderLeft=0,Z.display="inline"):Z.paddingLeft=5*this.props.indentWidth,t().createElement("div",Object.assign({className:"object-key-val",onMouseEnter:function(){return a.setState(j(j({},a.state),{},{hovered:!0}))},onMouseLeave:function(){return a.setState(j(j({},a.state),{},{hovered:!1}))}},B(b,k?"jsv-root":"objectKeyVal",Z)),this.getBraceStart(H,z),z?this.getObjectContent(p,v,j({theme:b,iconStyle:N},V)):this.getEllipsis(),t().createElement("span",{className:"brace-row"},t().createElement("span",{style:j(j({},B(b,"brace").style),{},{paddingLeft:z?"3px":"0px"})},H==="array"?"]":"}"),z?null:this.getObjectMetaData(v)))}}],[{key:"getDerivedStateFromProps",value:function(a,n){var p=n.prevProps;return a.src!==p.src||a.collapsed!==p.collapsed||a.name!==p.name||a.namespace!==p.namespace||a.rjvId!==p.rjvId?j(j({},e.getState(a)),{},{prevProps:a}):null}}])}(t().PureComponent);Gt.getState=function(s){var e=Object.keys(s.src).length,a=(s.collapsed===!1||s.collapsed!==!0&&s.collapsed>s.depth)&&(!s.shouldCollapse||s.shouldCollapse({name:s.name,src:s.src,type:G(s.src),namespace:s.namespace})===!1)&&e!==0;return{expanded:F.get(s.rjvId,s.namespace,"expanded",a),object_type:s.type==="array"?"array":"object",parent_type:s.type==="array"?"array":"object",size:e,hovered:!1}};var $a=O(function s(e,a,n){x(this,s),this.name=e,this.value=a,this.type=G(a,n)});P(Gt);const Mt=Gt;var Ka=function(s){function e(){var a;x(this,e);for(var n=arguments.length,p=new Array(n),v=0;v<n;v++)p[v]=arguments[v];return(a=o(this,e,[].concat(p))).render=function(){var m,b,k,N,V=a.props,q=[V.name],H=Mt;return typeof V.name!="object"||Array.isArray(V.name)||(q=[((m=V.name)===null||m===void 0?void 0:m.displayName)||((b=V.name)===null||b===void 0?void 0:b.name)||((k=V.name)===null||k===void 0||(N=k.type)===null||N===void 0?void 0:N.name)||"Anonymous"]),Array.isArray(V.src)&&V.groupArraysAfterLength&&V.src.length>V.groupArraysAfterLength&&(H=da),t().createElement("div",{className:"pretty-json-container object-container"},t().createElement("div",{className:"object-content"},t().createElement(H,Object.assign({namespace:q,depth:0,jsvRoot:!0},V))))},a}return r(e,s),O(e)}(t().PureComponent),Ua=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).closeModal=function(){M.dispatch({rjvId:n.props.rjvId,name:"RESET"})},n.submit=function(){n.props.submit(n.state.input)},n.state={input:a.input?a.input:""},n}return r(e,s),O(e,[{key:"render",value:function(){var a=this,n=this.props,p=n.theme,v=n.rjvId,m=n.isValid,b=this.state.input,k=m(b);return t().createElement("div",Object.assign({className:"key-modal-request"},B(p,"key-modal-request"),{onClick:this.closeModal}),t().createElement("div",Object.assign({},B(p,"key-modal"),{onClick:function(N){N.stopPropagation()}}),t().createElement("div",B(p,"key-modal-label"),"Key Name:"),t().createElement("div",{style:{position:"relative"}},t().createElement("input",Object.assign({},B(p,"key-modal-input"),{className:"key-modal-input",ref:function(N){return N&&N.focus()},spellCheck:!1,value:b,placeholder:"...",onChange:function(N){a.setState({input:N.target.value})},onKeyPress:function(N){k&&N.key==="Enter"?a.submit():N.key==="Escape"&&a.closeModal()}})),k?t().createElement(Ht,Object.assign({},B(p,"key-modal-submit"),{className:"key-modal-submit",onClick:function(N){return a.submit()}})):null),t().createElement("span",B(p,"key-modal-cancel"),t().createElement(sa,Object.assign({},B(p,"key-modal-cancel-icon"),{className:"key-modal-cancel",onClick:function(){M.dispatch({rjvId:v,name:"RESET"})}})))))}}])}(t().PureComponent),Wa=function(s){function e(){var a;x(this,e);for(var n=arguments.length,p=new Array(n),v=0;v<n;v++)p[v]=arguments[v];return(a=o(this,e,[].concat(p))).isValid=function(m){var b=a.props.rjvId,k=F.get(b,"action","new-key-request");return m!=""&&Object.keys(k.existing_value).indexOf(m)===-1},a.submit=function(m){var b=a.props.rjvId,k=F.get(b,"action","new-key-request");k.new_value=j({},k.existing_value),k.new_value[m]=a.props.defaultValue,M.dispatch({name:"VARIABLE_ADDED",rjvId:b,data:k})},a}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.active,p=a.theme,v=a.rjvId;return n?t().createElement(Ua,{rjvId:v,theme:p,isValid:this.isValid,submit:this.submit}):null}}])}(t().PureComponent),Ha=function(s){function e(){return x(this,e),o(this,e,arguments)}return r(e,s),O(e,[{key:"render",value:function(){var a=this.props,n=a.message,p=a.active,v=a.theme,m=a.rjvId;return p?t().createElement("div",Object.assign({className:"validation-failure"},B(v,"validation-failure"),{onClick:function(){M.dispatch({rjvId:m,name:"RESET"})}}),t().createElement("span",B(v,"validation-failure-label"),n),t().createElement(sa,B(v,"validation-failure-clear"))):null}}])}(t().PureComponent),At=function(s){function e(a){var n;return x(this,e),(n=o(this,e,[a])).rjvId=Date.now().toString()+Math.random().toString(36).slice(2),n.getListeners=function(){return{reset:n.resetState,"variable-update":n.updateSrc,"add-key-request":n.addKeyRequest}},n.updateSrc=function(){var p,v=F.get(n.rjvId,"action","variable-update"),m=v.name,b=v.namespace,k=v.new_value,N=v.existing_value,V=v.updated_src,q=v.type,H=n.props,z=H.onEdit,Z=H.onDelete,se=H.onAdd,re={existing_src:n.state.src,new_value:k,updated_src:V,name:m,namespace:b,existing_value:N};switch(q){case"variable-added":p=se(re);break;case"variable-edited":p=z(re);break;case"variable-removed":p=Z(re)}p!==!1?(F.set(n.rjvId,"global","src",V),n.setState({src:V})):n.setState({validationFailure:!0})},n.addKeyRequest=function(){n.setState({addKeyRequest:!0})},n.resetState=function(){n.setState({validationFailure:!1,addKeyRequest:!1})},n.state={addKeyRequest:!1,editKeyRequest:!1,validationFailure:!1,src:e.defaultProps.src,name:e.defaultProps.name,theme:e.defaultProps.theme,validationMessage:e.defaultProps.validationMessage,prevSrc:e.defaultProps.src,prevName:e.defaultProps.name,prevTheme:e.defaultProps.theme},n}return r(e,s),O(e,[{key:"componentDidMount",value:function(){F.set(this.rjvId,"global","src",this.state.src);var a=this.getListeners();for(var n in a)F.on(n+"-"+this.rjvId,a[n]);this.setState({addKeyRequest:!1,editKeyRequest:!1})}},{key:"componentDidUpdate",value:function(a,n){n.addKeyRequest!==!1&&this.setState({addKeyRequest:!1}),n.editKeyRequest!==!1&&this.setState({editKeyRequest:!1}),a.src!==this.state.src&&F.set(this.rjvId,"global","src",this.state.src)}},{key:"componentWillUnmount",value:function(){var a=this.getListeners();for(var n in a)F.removeListener(n+"-"+this.rjvId,a[n])}},{key:"render",value:function(){var a=this.state,n=a.validationFailure,p=a.validationMessage,v=a.addKeyRequest,m=a.theme,b=a.src,k=a.name,N=this.props,V=N.style,q=N.defaultValue;return t().createElement("div",{className:"react-json-view",style:j(j({},B(m,"app-container").style),V)},t().createElement(Ha,{message:p,active:n,theme:m,rjvId:this.rjvId}),t().createElement(Ka,Object.assign({},this.props,{src:b,name:k,theme:m,type:G(b),rjvId:this.rjvId})),t().createElement(Wa,{active:v,theme:m,rjvId:this.rjvId,defaultValue:q}))}}],[{key:"getDerivedStateFromProps",value:function(a,n){if(a.src!==n.prevSrc||a.name!==n.prevName||a.theme!==n.prevTheme){var p={src:a.src,name:a.name,theme:a.theme,validationMessage:a.validationMessage,prevSrc:a.src,prevName:a.name,prevTheme:a.theme};return e.validateState(p)}return null}}])}(t().PureComponent);At.defaultProps={src:{},name:"root",theme:"rjv-default",collapsed:!1,collapseStringsAfterLength:!1,shouldCollapse:!1,sortKeys:!1,quotesOnKeys:!0,groupArraysAfterLength:100,indentWidth:4,enableClipboard:!0,escapeStrings:!0,displayObjectSize:!0,displayDataTypes:!0,onEdit:!1,onDelete:!1,onAdd:!1,onSelect:!1,iconStyle:"triangle",style:{},validationMessage:"Validation Error",defaultValue:null,displayArrayKey:!0,selectOnFocus:!1,keyModifier:function(s){return s.metaKey||s.ctrlKey},bigNumber:null},At.validateState=function(s){var e={};return G(s.theme)!=="object"||function(a){var n=["base00","base01","base02","base03","base04","base05","base06","base07","base08","base09","base0A","base0B","base0C","base0D","base0E","base0F"];if(G(a)==="object"){for(var p=0;p<n.length;p++)if(!(n[p]in a))return!1;return!0}return!1}(s.theme)||(console.error("react-json-view error:","theme prop must be a theme name or valid base-16 theme object.",'defaulting to "rjv-default" theme'),e.theme="rjv-default"),G(s.src)!=="object"&&G(s.src)!=="array"&&(console.error("react-json-view error:","src property must be a valid json object"),e.name="ERROR",e.src={message:"src property must be a valid json object"}),j(j({},s),e)},P(At);const Ga=At})(),Q})())}(Ft)),Ft.exports}var In=An();const Pn=Ja(In);function Rn({isOpen:_,onClose:A,systemMessage:D}){const{t:$}=yt(),[W,U]=ae.useState("system"),[Q,h]=ae.useState({});if(!D)return null;const i=w=>{h(S=>({...S,[w]:!S[w]}))};return _&&d.jsx(wa,{onClose:A,children:d.jsxs(aa,{width:"medium",className:"max-h-[80vh] flex flex-col items-start",children:[d.jsxs("div",{className:"flex flex-col gap-6 w-full",children:[d.jsx(ra,{title:$("SYSTEM_MESSAGE_MODAL$TITLE")}),d.jsxs("div",{className:"flex flex-col gap-2",children:[D.agent_class&&d.jsxs("div",{className:"text-sm",children:[d.jsx("span",{className:"font-semibold text-gray-300",children:$("SYSTEM_MESSAGE_MODAL$AGENT_CLASS")})," ",d.jsx("span",{className:"font-medium text-gray-100",children:D.agent_class})]}),D.openhands_version&&d.jsxs("div",{className:"text-sm",children:[d.jsx("span",{className:"font-semibold text-gray-300",children:$("SYSTEM_MESSAGE_MODAL$OPENHANDS_VERSION")})," ",d.jsx("span",{className:"text-gray-100",children:D.openhands_version})]})]})]}),d.jsxs("div",{className:"w-full",children:[d.jsxs("div",{className:"flex border-b mb-2",children:[d.jsx("button",{type:"button",className:ut("px-4 py-2 font-medium border-b-2 transition-colors",W==="system"?"border-primary text-gray-100":"border-transparent hover:text-gray-700 dark:hover:text-gray-300"),onClick:()=>U("system"),children:$("SYSTEM_MESSAGE_MODAL$SYSTEM_MESSAGE_TAB")}),D.tools&&D.tools.length>0&&d.jsx("button",{type:"button",className:ut("px-4 py-2 font-medium border-b-2 transition-colors",W==="tools"?"border-primary text-gray-100":"border-transparent hover:text-gray-700 dark:hover:text-gray-300"),onClick:()=>U("tools"),children:$("SYSTEM_MESSAGE_MODAL$TOOLS_TAB")})]}),d.jsxs("div",{className:"max-h-[51vh] overflow-auto rounded-md",children:[W==="system"&&d.jsx("div",{className:"p-4 whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-300 shadow-inner",children:D.content}),W==="tools"&&D.tools&&D.tools.length>0&&d.jsx("div",{className:"p-2 space-y-3",children:D.tools.map((w,S)=>{const j=w,x=j.function||j,C=x.name||j.type==="function"&&j.function?.name||"",O=x.description||j.type==="function"&&j.function?.description||"",R=x.parameters||j.type==="function"&&j.function?.parameters||null,K=Q[S]||!1;return d.jsxs("div",{className:"rounded-md overflow-hidden",children:[d.jsxs("button",{type:"button",onClick:()=>i(S),className:"w-full py-3 px-2 text-left flex items-center justify-between hover:bg-gray-700 transition-colors",children:[d.jsx("div",{className:"flex items-center",children:d.jsx("h3",{className:"font-bold text-gray-100",children:String(C)})}),d.jsx("span",{className:"text-gray-300",children:K?d.jsx(Aa,{size:18}):d.jsx(Ia,{size:18})})]}),K&&d.jsxs("div",{className:"px-2 pb-3 pt-1",children:[d.jsx("div",{className:"mt-2 mb-3",children:d.jsx("p",{className:"text-sm whitespace-pre-wrap text-gray-300 leading-relaxed",children:String(O)})}),R&&d.jsxs("div",{className:"mt-2",children:[d.jsx("h4",{className:"text-sm font-semibold text-gray-300",children:$("SYSTEM_MESSAGE_MODAL$PARAMETERS")}),d.jsx("div",{className:"text-sm mt-2 p-3 bg-gray-900 rounded-md overflow-auto text-gray-300 max-h-[400px] shadow-inner",children:d.jsx(Pn,{name:!1,src:R,theme:Qa})})]})]})]},S)})}),W==="tools"&&(!D.tools||D.tools.length===0)&&d.jsx("div",{className:"flex items-center justify-center h-full p-4",children:d.jsx("p",{className:"text-gray-400",children:$("SYSTEM_MESSAGE_MODAL$NO_TOOLS")})})]})]})]})})}const Dn=()=>{const{conversationId:_}=sr(),{curAgentState:A}=ta(D=>D.agent);return nr({queryKey:["conversation",_,"microagents"],queryFn:async()=>{if(!_)throw new Error("No conversation ID provided");return(await ka.getMicroagents(_)).microagents},enabled:!!_&&A!==Bt.LOADING&&A!==Bt.INIT,staleTime:1e3*60*5,gcTime:1e3*60*15})};function Tn({onClose:_}){const{t:A}=yt(),{curAgentState:D}=ta(x=>x.agent),[$,W]=ae.useState({}),{data:U,isLoading:Q,isError:h,refetch:i,isRefetching:w}=Dn(),S=x=>{W(C=>({...C,[x]:!C[x]}))},j=![Bt.LOADING,Bt.INIT].includes(D);return d.jsx(wa,{onClose:_,children:d.jsxs(aa,{width:"medium",className:"max-h-[80vh] flex flex-col items-start",testID:"microagents-modal",children:[d.jsx("div",{className:"flex flex-col gap-6 w-full",children:d.jsxs("div",{className:"flex items-center justify-between w-full",children:[d.jsx(ra,{title:A(ne.MICROAGENTS_MODAL$TITLE)}),j&&d.jsxs(or,{testId:"refresh-microagents",type:"button",variant:"primary",className:"flex items-center gap-2",onClick:i,isDisabled:Q||w,children:[d.jsx(En,{size:16,className:`${w?"animate-spin":""}`}),A(ne.BUTTON$REFRESH)]})]})}),j&&d.jsx("span",{className:"text-sm text-gray-400",children:A(ne.MICROAGENTS_MODAL$WARNING)}),d.jsxs("div",{className:"w-full h-[60vh] overflow-auto rounded-md",children:[!j&&d.jsx("div",{className:"w-full h-full flex items-center text-center justify-center text-2xl text-tertiary-light",children:A(ne.DIFF_VIEWER$WAITING_FOR_RUNTIME)}),Q&&d.jsx("div",{className:"flex justify-center items-center py-8",children:d.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"})}),!Q&&j&&(h||!U||U.length===0)&&d.jsx("div",{className:"flex items-center justify-center h-full p-4",children:d.jsx("p",{className:"text-gray-400",children:A(h?ne.MICROAGENTS_MODAL$FETCH_ERROR:ne.CONVERSATION$NO_MICROAGENTS)})}),!Q&&j&&U&&U.length>0&&d.jsx("div",{className:"p-2 space-y-3",children:U.map(x=>{const C=$[x.name]||!1;return d.jsxs("div",{className:"rounded-md overflow-hidden",children:[d.jsxs("button",{type:"button",onClick:()=>S(x.name),className:"w-full py-3 px-2 text-left flex items-center justify-between hover:bg-gray-700 transition-colors",children:[d.jsx("div",{className:"flex items-center",children:d.jsx("h3",{className:"font-bold text-gray-100",children:x.name})}),d.jsxs("div",{className:"flex items-center",children:[d.jsx("span",{className:"px-2 py-1 text-xs rounded-full bg-gray-800 mr-2",children:x.type==="repo"?"Repository":"Knowledge"}),d.jsx("span",{className:"text-gray-300",children:C?d.jsx(Aa,{size:18}):d.jsx(Ia,{size:18})})]})]}),C&&d.jsxs("div",{className:"px-2 pb-3 pt-1",children:[x.triggers&&x.triggers.length>0&&d.jsxs("div",{className:"mt-2 mb-3",children:[d.jsx("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:A(ne.MICROAGENTS_MODAL$TRIGGERS)}),d.jsx("div",{className:"flex flex-wrap gap-1",children:x.triggers.map(O=>d.jsx("span",{className:"px-2 py-1 text-xs rounded-full bg-blue-900",children:O},O))})]}),d.jsxs("div",{className:"mt-2",children:[d.jsx("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:A(ne.MICROAGENTS_MODAL$CONTENT)}),d.jsx("div",{className:"text-sm mt-2 p-3 bg-gray-900 rounded-md overflow-auto text-gray-300 max-h-[400px] shadow-inner",children:d.jsx("pre",{className:"whitespace-pre-wrap font-mono text-sm leading-relaxed",children:x.content||A(ne.MICROAGENTS_MODAL$NO_CONTENT)})})]})]})]},x.name)})})]})]})})}function Fn({currentCost:_,maxBudget:A}){const D=_/A*100,$=D>80;return d.jsx("div",{className:"w-full h-1.5 bg-neutral-700 rounded-full overflow-hidden mt-1",children:d.jsx("div",{className:`h-full transition-all duration-300 ${$?"bg-red-500":"bg-blue-500"}`,style:{width:`${Math.min(100,D)}%`}})})}function Bn({currentCost:_,maxBudget:A}){const{t:D}=yt(),$=_/A*100;return d.jsx("div",{className:"flex justify-end",children:d.jsx("span",{className:"text-xs text-neutral-400",children:D(ne.CONVERSATION$BUDGET_USAGE_FORMAT,{currentCost:`$${_.toFixed(4)}`,maxBudget:`$${A.toFixed(4)}`,usagePercentage:$.toFixed(2),used:D(ne.CONVERSATION$USED)})})})}function Ln({cost:_,maxBudgetPerTask:A}){const{t:D}=yt();return _===null?null:d.jsx("div",{className:"border-b border-neutral-700",children:A!==null&&A>0?d.jsxs(d.Fragment,{children:[d.jsx(Fn,{currentCost:_,maxBudget:A}),d.jsx(Bn,{currentCost:_,maxBudget:A})]}):d.jsx("span",{className:"text-xs text-neutral-400",children:D(ne.CONVERSATION$NO_BUDGET_LIMIT)})})}function zn({actions:_,closeModal:A}){return d.jsx(d.Fragment,{children:_.map(({action:D,isDisabled:$,label:W,className:U,closeAfterAction:Q})=>d.jsx(Nr,{type:"button",isDisabled:$,onPress:()=>{D(),Q&&A()},className:U,children:W},W))})}function Vn({maintitle:_,subtitle:A=void 0}){return d.jsxs(d.Fragment,{children:[d.jsx("h3",{children:_}),A&&d.jsx("span",{className:"text-neutral-400 text-sm font-light",children:A})]})}function qn({isOpen:_,onOpenChange:A,title:D,contentClassName:$="max-w-[30rem] p-[40px]",bodyClassName:W="px-0 py-[20px]",isDismissable:U=!0,subtitle:Q=void 0,actions:h=[],children:i=null,testID:w}){return d.jsx(Hr,{"data-testid":w,isOpen:_,onOpenChange:A,isDismissable:U,backdrop:"blur",hideCloseButton:!0,size:"sm",className:"bg-base-secondary rounded-lg",children:d.jsx(qr,{className:$,children:S=>d.jsxs(d.Fragment,{children:[D&&d.jsx(Kr,{className:"flex flex-col p-0",children:d.jsx(Vn,{maintitle:D,subtitle:Q})}),d.jsx(Rr,{className:W,children:i}),h&&h.length>0&&d.jsx($r,{className:"flex-row flex justify-start p-0",children:d.jsx(zn,{actions:h,closeModal:S})})]})})})}const $n=1e3*60*30;function us({onClick:_,onDelete:A,onStop:D,onChangeTitle:$,showOptions:W,isActive:U,title:Q,selectedRepository:h,lastUpdatedAt:i,createdAt:w,conversationStatus:S="STOPPED",variant:j="default",conversationId:x,contextMenuOpen:C=!1,onContextMenuToggle:O}){const{t:R}=yt(),{parsedEvents:K}=Ar(),[c,o]=We.useState("view"),[l,r]=We.useState(!1),[u,t]=We.useState(!1),[f,g]=We.useState(!1),E=We.useRef(null),P=K.find(Ir),T=ta(J=>J.metrics),L=()=>{if(E.current?.value){const J=E.current.value.trim();$?.(J),E.current.value=J}else E.current.value=Q;o("view")},G=J=>{J.key==="Enter"&&J.currentTarget.blur()},X=J=>{c==="edit"&&(J.preventDefault(),J.stopPropagation())},ie=J=>{J.preventDefault(),J.stopPropagation(),A?.(),O?.(!1)},ke=J=>{J.preventDefault(),J.stopPropagation(),D?.(),O?.(!1)},I=J=>{J.preventDefault(),J.stopPropagation(),o("edit"),O?.(!1)},ge=async J=>{if(J.preventDefault(),J.stopPropagation(),Za.capture("download_via_vscode_button_clicked"),x)try{const Te=await ka.getVSCodeUrl(x);if(Te.vscode_url){const de=Mr(Te.vscode_url);de&&window.open(de,"_blank")}}catch{}O?.(!1)},$e=J=>{J.stopPropagation(),r(!0)},Re=J=>{J.stopPropagation(),t(!0)},De=J=>{J.stopPropagation(),g(!0)};We.useEffect(()=>{c==="edit"&&E.current?.focus()},[c]);const He=!!(A||$||W),Ke=w?new Date(i).getTime()-new Date(w).getTime():0,rt=w&&Ke>$n;return d.jsxs(d.Fragment,{children:[d.jsxs("div",{"data-testid":"conversation-card",onClick:_,className:ut("h-auto w-full px-[18px] py-4 border-b border-neutral-600 cursor-pointer",j==="compact"&&"md:w-fit h-auto rounded-xl border border-[#525252]"),children:[d.jsxs("div",{className:"flex items-center justify-between w-full",children:[d.jsxs("div",{className:"flex items-center gap-2 flex-1 min-w-0 overflow-hidden mr-2",children:[U&&d.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"}),c==="edit"&&d.jsx("input",{ref:E,"data-testid":"conversation-card-title",onClick:X,onBlur:L,onKeyUp:G,type:"text",defaultValue:Q,className:"text-sm leading-6 font-semibold bg-transparent w-full"}),c==="view"&&d.jsx("p",{"data-testid":"conversation-card-title",className:"text-sm leading-6 font-semibold bg-transparent truncate overflow-hidden",title:Q,children:Q})]}),d.jsxs("div",{className:"flex items-center",children:[d.jsx(rn,{conversationStatus:S}),He&&d.jsx("div",{className:"pl-2",children:d.jsx(nn,{onClick:J=>{J.preventDefault(),J.stopPropagation(),O?.(!C)}})}),d.jsx("div",{className:"relative",children:C&&d.jsx(_n,{onClose:()=>O?.(!1),onDelete:A&&ie,onStop:S!=="STOPPED"?D&&ke:void 0,onEdit:$&&I,onDownloadViaVSCode:x&&W?ge:void 0,onDisplayCost:W?$e:void 0,onShowAgentTools:W&&P?Re:void 0,onShowMicroagents:W&&x?De:void 0,position:j==="compact"?"top":"bottom"})})]})]}),d.jsxs("div",{className:ut(j==="compact"&&"flex flex-col justify-between mt-1"),children:[h?.selected_repository&&d.jsx(Qr,{selectedRepository:h,variant:j}),(w||i)&&d.jsxs("p",{className:"text-xs text-neutral-400",children:[d.jsxs("span",{children:[R(ne.CONVERSATION$CREATED)," "]}),d.jsxs("time",{children:[fa(new Date(w||i))," ",R(ne.CONVERSATION$AGO)]}),rt&&d.jsxs(d.Fragment,{children:[d.jsxs("span",{children:[R(ne.CONVERSATION$UPDATED)," "]}),d.jsxs("time",{children:[fa(new Date(i))," ",R(ne.CONVERSATION$AGO)]})]})]})]})]}),d.jsx(qn,{isOpen:l,onOpenChange:r,title:R(ne.CONVERSATION$METRICS_INFO),testID:"metrics-modal",children:d.jsxs("div",{className:"space-y-4",children:[(T?.cost!==null||T?.usage!==null)&&d.jsx("div",{className:"rounded-md p-3",children:d.jsxs("div",{className:"grid gap-3",children:[T?.cost!==null&&d.jsxs("div",{className:"flex justify-between items-center pb-2",children:[d.jsx("span",{className:"text-lg font-semibold",children:R(ne.CONVERSATION$TOTAL_COST)}),d.jsxs("span",{className:"font-semibold",children:["$",T.cost.toFixed(4)]})]}),d.jsx(Ln,{cost:T?.cost??null,maxBudgetPerTask:T?.max_budget_per_task??null}),T?.usage!==null&&d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"flex justify-between items-center pb-2",children:[d.jsx("span",{children:R(ne.CONVERSATION$INPUT)}),d.jsx("span",{className:"font-semibold",children:T.usage.prompt_tokens.toLocaleString()})]}),d.jsxs("div",{className:"grid grid-cols-2 gap-2 pl-4 text-sm",children:[d.jsx("span",{className:"text-neutral-400",children:R(ne.CONVERSATION$CACHE_HIT)}),d.jsx("span",{className:"text-right",children:T.usage.cache_read_tokens.toLocaleString()}),d.jsx("span",{className:"text-neutral-400",children:R(ne.CONVERSATION$CACHE_WRITE)}),d.jsx("span",{className:"text-right",children:T.usage.cache_write_tokens.toLocaleString()})]}),d.jsxs("div",{className:"flex justify-between items-center border-b border-neutral-700 pb-2",children:[d.jsx("span",{children:R(ne.CONVERSATION$OUTPUT)}),d.jsx("span",{className:"font-semibold",children:T.usage.completion_tokens.toLocaleString()})]}),d.jsxs("div",{className:"flex justify-between items-center border-b border-neutral-700 pb-2",children:[d.jsx("span",{className:"font-semibold",children:R(ne.CONVERSATION$TOTAL)}),d.jsx("span",{className:"font-bold",children:(T.usage.prompt_tokens+T.usage.completion_tokens).toLocaleString()})]}),d.jsxs("div",{className:"flex flex-col gap-2",children:[d.jsx("div",{className:"flex items-center justify-between",children:d.jsx("span",{className:"font-semibold",children:R(ne.CONVERSATION$CONTEXT_WINDOW)})}),d.jsx("div",{className:"w-full h-1.5 bg-neutral-700 rounded-full overflow-hidden",children:d.jsx("div",{className:"h-full bg-blue-500 transition-all duration-300",style:{width:`${Math.min(100,T.usage.per_turn_token/T.usage.context_window*100)}%`}})}),d.jsx("div",{className:"flex justify-end",children:d.jsxs("span",{className:"text-xs text-neutral-400",children:[T.usage.per_turn_token.toLocaleString()," /"," ",T.usage.context_window.toLocaleString()," (",(T.usage.per_turn_token/T.usage.context_window*100).toFixed(2),"% ",R(ne.CONVERSATION$USED),")"]})})]})]})]})}),!T?.cost&&!T?.usage&&d.jsx("div",{className:"rounded-md p-4 text-center",children:d.jsx("p",{className:"text-neutral-400",children:R(ne.CONVERSATION$NO_METRICS)})})]})}),d.jsx(Rn,{isOpen:u,onClose:()=>t(!1),systemMessage:P?P.args:null}),f&&d.jsx(Tn,{onClose:()=>g(!1)})]})}export{cs as B,Gr as C,Jr as M,Pn as R,ft as a,ra as b,Zr as c,us as d,qn as e,Yr as u};
