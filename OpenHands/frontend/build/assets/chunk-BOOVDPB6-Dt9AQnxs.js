const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-D_FgxOY2.js","assets/features-animation-CM7oex5Y.js","assets/chunk-S6H5EOGR-Bwn62IP6.js","assets/chunk-C37GKA54-CBbYr_fP.js","assets/utils-KsbccAr1.js","assets/index-yKbcr7Pf.js","assets/preload-helper-BXl3LOEh.js"])))=>i.map(i=>d[i]);
import{_ as he}from"./preload-helper-BXl3LOEh.js";import{R as w,r as l,j as f}from"./chunk-C37GKA54-CBbYr_fP.js";import{i as xe,$ as Pe,b as Te,c as Ce,d as ge,e as oe,f as re,g as ye,h as Oe,j as Ee,k as we,u as Re,m as De,n as Ae,o as I,p as _e,q as je,r as Ie,t as X,v as Me,w as Z,x as D,y as Se,z as Ne,A as He,B as Le,C as ke,D as Fe,T as Ve,L as ze,E as Be}from"./chunk-S6H5EOGR-Bwn62IP6.js";import{R as Ke}from"./index-yKbcr7Pf.js";function Ue(e,t){if(e!=null){if(xe(e)){e(t);return}try{e.current=t}catch{throw new Error(`Cannot assign value '${t}' to ref '${e}'`)}}}function We(...e){return t=>{e.forEach(o=>Ue(o,t))}}const K=w.createContext(null);function qe(e){let{children:t}=e,o=l.useContext(K),[s,a]=l.useState(0),c=l.useMemo(()=>({parent:o,modalCount:s,addModal(){a(n=>n+1),o&&o.addModal()},removeModal(){a(n=>n-1),o&&o.removeModal()}}),[o,s]);return w.createElement(K.Provider,{value:c},t)}function Ye(){let e=l.useContext(K);return{modalProviderProps:{"aria-hidden":e&&e.modalCount>0?!0:void 0}}}function Ge(e){let{modalProviderProps:t}=Ye();return w.createElement("div",{"data-overlay-container":!0,...e,...t})}function Je(e){return w.createElement(qe,null,w.createElement(Ge,e))}function ee(e){let t=Pe(),{portalContainer:o=t?null:document.body,...s}=e,{getContainer:a}=Te();if(!e.portalContainer&&a&&(o=a()),w.useEffect(()=>{if(o?.closest("[data-overlay-container]"))throw new Error("An OverlayContainer must not be inside another container. Please change the portalContainer prop.")},[o]),!o)return null;let c=w.createElement(Je,s);return Ke.createPortal(c,o)}const Qe=1500,te=500;let O={},Xe=0,j=!1,h=null,E=null;function Ze(e={}){let{delay:t=Qe,closeDelay:o=te}=e,{isOpen:s,open:a,close:c}=Ce(e),n=l.useMemo(()=>`${++Xe}`,[]),r=l.useRef(null),p=l.useRef(c),m=()=>{O[n]=$},x=()=>{for(let i in O)i!==n&&(O[i](!0),delete O[i])},b=()=>{r.current&&clearTimeout(r.current),r.current=null,x(),m(),j=!0,a(),h&&(clearTimeout(h),h=null),E&&(clearTimeout(E),E=null)},$=i=>{i||o<=0?(r.current&&clearTimeout(r.current),r.current=null,p.current()):r.current||(r.current=setTimeout(()=>{r.current=null,p.current()},o)),h&&(clearTimeout(h),h=null),j&&(E&&clearTimeout(E),E=setTimeout(()=>{delete O[n],E=null,j=!1},Math.max(te,o)))},d=()=>{x(),m(),!s&&!h&&!j?h=setTimeout(()=>{h=null,j=!0,b()},t):s||b()};return l.useEffect(()=>{p.current=c},[c]),l.useEffect(()=>()=>{r.current&&clearTimeout(r.current),O[n]&&delete O[n]},[n]),{isOpen:s,open:i=>{!i&&t>0&&!r.current?d():b()},close:$}}function et(e,t){let o=ge(e,{labelable:!0}),{hoverProps:s}=oe({onHoverStart:()=>t?.open(!0),onHoverEnd:()=>t?.close()});return{tooltipProps:re(o,s,{role:"tooltip"})}}function tt(e,t,o){let{isDisabled:s,trigger:a}=e,c=ye(),n=l.useRef(!1),r=l.useRef(!1),p=()=>{(n.current||r.current)&&t.open(r.current)},m=u=>{!n.current&&!r.current&&t.close(u)};l.useEffect(()=>{let u=T=>{o&&o.current&&T.key==="Escape"&&(T.stopPropagation(),t.close(!0))};if(t.isOpen)return document.addEventListener("keydown",u,!0),()=>{document.removeEventListener("keydown",u,!0)}},[o,t]);let x=()=>{a!=="focus"&&(Ee()==="pointer"?n.current=!0:n.current=!1,p())},b=()=>{a!=="focus"&&(r.current=!1,n.current=!1,m())},$=()=>{r.current=!1,n.current=!1,m(!0)},d=()=>{we()&&(r.current=!0,p())},i=()=>{r.current=!1,n.current=!1,m(!0)},{hoverProps:R}=oe({isDisabled:s,onHoverStart:x,onHoverEnd:b}),{focusableProps:P}=Oe({isDisabled:s,onFocus:d,onBlur:i},o);return{triggerProps:{"aria-describedby":t.isOpen?c:void 0,...re(P,R,{onPointerDown:$,onKeyDown:$,tabIndex:void 0})},tooltipProps:{id:c}}}function ot(e){var t,o;const s=Re(),[a,c]=De(e,X.variantKeys),{ref:n,as:r,isOpen:p,content:m,children:x,defaultOpen:b,onOpenChange:$,isDisabled:d,trigger:i,shouldFlip:R=!0,containerPadding:P=12,placement:u="top",delay:T=0,closeDelay:F=500,showArrow:C=!1,offset:g=7,crossOffset:V=0,isDismissable:le,shouldCloseOnBlur:ae=!0,portalContainer:se,isKeyboardDismissDisabled:ie=!1,updatePositionDeps:U=[],shouldCloseOnInteractOutside:ce,className:de,onClose:W,motionProps:ue,classNames:A,...z}=a,fe=r||"div",B=(o=(t=e?.disableAnimation)!=null?t:s?.disableAnimation)!=null?o:!1,_=Ze({delay:T,closeDelay:F,isDisabled:d,defaultOpen:b,isOpen:p,onOpenChange:y=>{$?.(y),y||W?.()}}),M=l.useRef(null),S=l.useRef(null),N=l.useId(),v=_.isOpen&&!d;l.useImperativeHandle(n,()=>Ae(S));const{triggerProps:q,tooltipProps:pe}=tt({isDisabled:d,trigger:i},_,M),{tooltipProps:Y}=et({isOpen:v,...I(a,pe)},_),{overlayProps:G,placement:H,updatePosition:me}=_e({isOpen:v,targetRef:M,placement:Ne(u),overlayRef:S,offset:C?g+3:g,crossOffset:V,shouldFlip:R,containerPadding:P});je(()=>{U.length&&me()},U);const{overlayProps:J}=Ie({isOpen:v,onClose:_.close,isDismissable:le,shouldCloseOnBlur:ae,isKeyboardDismissDisabled:ie,shouldCloseOnInteractOutside:ce},S),L=l.useMemo(()=>{var y,k,Q;return X({...c,disableAnimation:B,radius:(y=e?.radius)!=null?y:"md",size:(k=e?.size)!=null?k:"md",shadow:(Q=e?.shadow)!=null?Q:"sm"})},[Me(c),B,e?.radius,e?.size,e?.shadow]),be=l.useCallback((y={},k=null)=>({...I(q,y),ref:We(k,M),"aria-describedby":v?N:void 0}),[q,v,N,_]),$e=l.useCallback(()=>({ref:S,"data-slot":"base","data-open":D(v),"data-arrow":D(C),"data-disabled":D(d),"data-placement":Z(H||"top",u),...I(Y,J,z),style:I(G.style,z.style,a.style),className:L.base({class:A?.base}),id:N}),[L,v,C,d,H,u,Y,J,z,G,a,N]),ve=l.useCallback(()=>({"data-slot":"content","data-open":D(v),"data-arrow":D(C),"data-disabled":D(d),"data-placement":Z(H||"top",u),className:L.content({class:Se(A?.content,de)})}),[L,v,C,d,H,u,A]);return{Component:fe,content:m,children:x,isOpen:v,triggerRef:M,showArrow:C,portalContainer:se,placement:u,disableAnimation:B,isDisabled:d,motionProps:ue,getTooltipContentProps:ve,getTriggerProps:be,getTooltipProps:$e}}var rt=()=>he(()=>import("./index-D_FgxOY2.js"),__vite__mapDeps([0,1,2,3,4,5,6])).then(e=>e.default),ne=He((e,t)=>{var o;const{Component:s,children:a,content:c,isOpen:n,portalContainer:r,placement:p,disableAnimation:m,motionProps:x,getTriggerProps:b,getTooltipProps:$,getTooltipContentProps:d}=ot({...e,ref:t});let i;try{if(l.Children.count(a)!==1)throw new Error;if(!l.isValidElement(a))i=f.jsx("p",{...b(),children:a});else{const g=a,V=(o=g.props.ref)!=null?o:g.ref;i=l.cloneElement(g,b(g.props,V))}}catch{i=f.jsx("span",{}),Le("Tooltip must have only one child node. Please, check your code.")}const{ref:R,id:P,style:u,...T}=$(),F=f.jsx("div",{ref:R,id:P,style:u,children:f.jsx(ke.div,{animate:"enter",exit:"exit",initial:"exit",variants:Ve.scaleSpring,...I(x,T),style:{...Fe(p)},children:f.jsx(s,{...d(),children:c})},`${P}-tooltip-inner`)},`${P}-tooltip-content`);return f.jsxs(f.Fragment,{children:[i,m?n&&f.jsx(ee,{portalContainer:r,children:f.jsx("div",{ref:R,id:P,style:u,...T,children:f.jsx(s,{...d(),children:c})})}):f.jsx(ze,{features:rt,children:f.jsx(Be,{children:n&&f.jsx(ee,{portalContainer:r,children:F})})})]})});ne.displayName="HeroUI.Tooltip";var it=ne;export{We as m,it as t};
