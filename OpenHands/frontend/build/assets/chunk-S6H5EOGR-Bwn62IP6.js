const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-D_FgxOY2.js","assets/features-animation-CM7oex5Y.js","assets/chunk-C37GKA54-CBbYr_fP.js","assets/utils-KsbccAr1.js","assets/index-yKbcr7Pf.js","assets/preload-helper-BXl3LOEh.js"])))=>i.map(i=>d[i]);
import{r as d,R as te,j as D}from"./chunk-C37GKA54-CBbYr_fP.js";import{a as uu,t as cu,h as du}from"./utils-KsbccAr1.js";import{R as fu,r as bl}from"./index-yKbcr7Pf.js";import{_ as vl}from"./preload-helper-BXl3LOEh.js";function gl(e={}){const{strict:t=!0,errorMessage:n="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:r}=e,o=d.createContext(void 0);o.displayName=r;function l(){var i;const a=d.useContext(o);if(!a&&t){const s=new Error(n);throw s.name="ContextError",(i=Error.captureStackTrace)==null||i.call(Error,s,l),s}return a}return[o.Provider,l,o]}function tb(e){return{UNSAFE_getDOMNode(){return e.current}}}function Ve(e){const t=d.useRef(null);return d.useImperativeHandle(e,()=>t.current),t}var nb=e=>e;function Lr(e){return Array.isArray(e)}function pu(e){return Lr(e)&&e.length===0}function ml(e){const t=typeof e;return e!=null&&(t==="object"||t==="function")&&!Lr(e)}function hu(e){return ml(e)&&Object.keys(e).length===0}function yl(e){return Lr(e)?pu(e):ml(e)?hu(e):e==null||e===""}function rb(e){return typeof e=="function"}var U=e=>e?"true":void 0;function $l(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=$l(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function pe(...e){for(var t=0,n,r,o="";t<e.length;)(n=e[t++])&&(r=$l(n))&&(o&&(o+=" "),o+=r);return o}var bu=(...e)=>{let t=" ";for(const n of e)if(typeof n=="string"&&n.length>0){t=n;break}return t},xl=e=>e?e.charAt(0).toUpperCase()+e.slice(1).toLowerCase():"";function ob(...e){return function(n){e.some(r=>(r?.(n),n?.defaultPrevented))}}function vu(e){return`${e}-${Math.floor(Math.random()*1e6)}`}function gu(e){for(const t in e)t.startsWith("on")&&delete e[t];return e}function yt(e){if(!e||typeof e!="object")return"";try{return JSON.stringify(e)}catch{return""}}function mu(e,t,n){return Math.min(Math.max(e,t),n)}var ro={};function lb(e,t,...n){const o=`[Hero UI] : ${e}`;typeof console>"u"||ro[o]||(ro[o]=!0)}function pt(...e){return(...t)=>{for(let n of e)typeof n=="function"&&n(...t)}}var oo=new Map;function yu(e,t){if(e===t)return e;let n=oo.get(e);if(n)return n.forEach(o=>o.current=t),t;let r=oo.get(t);return r?(r.forEach(o=>o.current=e),e):t}function ie(...e){let t={...e[0]};for(let n=1;n<e.length;n++){let r=e[n];for(let o in r){let l=t[o],i=r[o];typeof l=="function"&&typeof i=="function"&&o[0]==="o"&&o[1]==="n"&&o.charCodeAt(2)>=65&&o.charCodeAt(2)<=90?t[o]=pt(l,i):(o==="className"||o==="UNSAFE_className")&&typeof l=="string"&&typeof i=="string"?t[o]=pe(l,i):o==="id"&&l&&i?t.id=yu(l,i):t[o]=i!==void 0?i:l}}return t}function wl(...e){return e.length===1&&e[0]?e[0]:t=>{let n=!1;const r=e.map(o=>{const l=lo(o,t);return n||(n=typeof l=="function"),l});if(n)return()=>{r.forEach((o,l)=>{typeof o=="function"?o?.():lo(e[l],null)})}}}function lo(e,t){if(typeof e=="function")return()=>e(t);e!=null&&"current"in e&&(e.current=t)}var $u=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),xu=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),io=/^(data-.*)$/,wu=/^(aria-.*)$/,nn=/^(on[A-Z].*)$/;function ht(e,t={}){let{labelable:n=!0,enabled:r=!0,propNames:o,omitPropNames:l,omitEventNames:i,omitDataProps:a,omitEventProps:s}=t,u={};if(!r)return e;for(const c in e)l?.has(c)||i?.has(c)&&nn.test(c)||nn.test(c)&&!xu.has(c)||a&&io.test(c)||s&&nn.test(c)||(Object.prototype.hasOwnProperty.call(e,c)&&($u.has(c)||n&&wu.test(c)||o?.has(c)||io.test(c))||nn.test(c))&&(u[c]=e[c]);return u}var[ib,tt]=gl({name:"ProviderContext",strict:!1});const Cu=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),Eu=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function Su(e){if(Intl.Locale){let n=new Intl.Locale(e).maximize(),r=typeof n.getTextInfo=="function"?n.getTextInfo():n.textInfo;if(r)return r.direction==="rtl";if(n.script)return Cu.has(n.script)}let t=e.split("-")[0];return Eu.has(t)}const Cl={prefix:String(Math.round(Math.random()*1e10)),current:0},El=te.createContext(Cl),Pu=te.createContext(!1);let Gn=new WeakMap;function Tu(e=!1){let t=d.useContext(El),n=d.useRef(null);if(n.current===null&&!e){var r,o;let l=(o=te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)===null||o===void 0||(r=o.ReactCurrentOwner)===null||r===void 0?void 0:r.current;if(l){let i=Gn.get(l);i==null?Gn.set(l,{id:t.current,state:l.memoizedState}):l.memoizedState!==i.state&&(t.current=i.id,Gn.delete(l))}n.current=++t.current}return n.current}function Au(e){let t=d.useContext(El),n=Tu(!!e),r=`react-aria${t.prefix}`;return e||`${r}-${n}`}function ku(e){let t=te.useId(),[n]=d.useState(Mn()),r=n?"react-aria":`react-aria${Cl.prefix}`;return e||`${r}-${t}`}const Mu=typeof te.useId=="function"?ku:Au;function Du(){return!1}function Lu(){return!0}function Fu(e){return()=>{}}function Mn(){return typeof te.useSyncExternalStore=="function"?te.useSyncExternalStore(Fu,Du,Lu):d.useContext(Pu)}const Iu=Symbol.for("react-aria.i18n.locale");function Sl(){let e=typeof window<"u"&&window[Iu]||typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:Su(e)?"rtl":"ltr"}}let cr=Sl(),zt=new Set;function ao(){cr=Sl();for(let e of zt)e(cr)}function Ru(){let e=Mn(),[t,n]=d.useState(cr);return d.useEffect(()=>(zt.size===0&&window.addEventListener("languagechange",ao),zt.add(n),()=>{zt.delete(n),zt.size===0&&window.removeEventListener("languagechange",ao)}),[]),e?{locale:"en-US",direction:"ltr"}:t}const Bu=te.createContext(null);function Dn(){let e=Ru();return d.useContext(Bu)||e}const Ku=Symbol.for("react-aria.i18n.locale"),Nu=Symbol.for("react-aria.i18n.strings");let St;class Ln{getStringForLocale(t,n){let o=this.getStringsForLocale(n)[t];if(!o)throw new Error(`Could not find intl message ${t} in ${n} locale`);return o}getStringsForLocale(t){let n=this.strings[t];return n||(n=Ou(t,this.strings,this.defaultLocale),this.strings[t]=n),n}static getGlobalDictionaryForPackage(t){if(typeof window>"u")return null;let n=window[Ku];if(St===void 0){let o=window[Nu];if(!o)return null;St={};for(let l in o)St[l]=new Ln({[n]:o[l]},n)}let r=St?.[t];if(!r)throw new Error(`Strings for package "${t}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return r}constructor(t,n="en-US"){this.strings=Object.fromEntries(Object.entries(t).filter(([,r])=>r)),this.defaultLocale=n}}function Ou(e,t,n="en-US"){if(t[e])return t[e];let r=zu(e);if(t[r])return t[r];for(let o in t)if(o.startsWith(r+"-"))return t[o];return t[n]}function zu(e){return Intl.Locale?new Intl.Locale(e).language:e.split("-")[0]}const so=new Map,uo=new Map;class _u{format(t,n){let r=this.strings.getStringForLocale(t,this.locale);return typeof r=="function"?r(n,this):r}plural(t,n,r="cardinal"){let o=n["="+t];if(o)return typeof o=="function"?o():o;let l=this.locale+":"+r,i=so.get(l);i||(i=new Intl.PluralRules(this.locale,{type:r}),so.set(l,i));let a=i.select(t);return o=n[a]||n.other,typeof o=="function"?o():o}number(t){let n=uo.get(this.locale);return n||(n=new Intl.NumberFormat(this.locale),uo.set(this.locale,n)),n.format(t)}select(t,n){let r=t[n]||t.other;return typeof r=="function"?r():r}constructor(t,n){this.locale=t,this.strings=n}}const co=new WeakMap;function Vu(e){let t=co.get(e);return t||(t=new Ln(e),co.set(e,t)),t}function ju(e,t){return t&&Ln.getGlobalDictionaryForPackage(t)||Vu(e)}function Fr(e,t){let{locale:n}=Dn(),r=ju(e,t);return d.useMemo(()=>new _u(n,r),[n,r])}function Wu(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Hu(e,t,n){Wu(e,t),t.set(e,n)}const be=typeof document<"u"?te.useLayoutEffect:()=>{};var Yn;const Uu=(Yn=te.useInsertionEffect)!==null&&Yn!==void 0?Yn:be;function Ee(e){const t=d.useRef(null);return Uu(()=>{t.current=e},[e]),d.useCallback((...n)=>{const r=t.current;return r?.(...n)},[])}function Gu(e){let[t,n]=d.useState(e),r=d.useRef(null),o=Ee(()=>{if(!r.current)return;let i=r.current.next();if(i.done){r.current=null;return}t===i.value?o():n(i.value)});be(()=>{r.current&&o()});let l=Ee(i=>{r.current=i(t),o()});return[t,l]}let Yu=!!(typeof window<"u"&&window.document&&window.document.createElement),Mt=new Map,_t;typeof FinalizationRegistry<"u"&&(_t=new FinalizationRegistry(e=>{Mt.delete(e)}));function qe(e){let[t,n]=d.useState(e),r=d.useRef(null),o=Mu(t),l=d.useRef(null);if(_t&&_t.register(l,o),Yu){const i=Mt.get(o);i&&!i.includes(r)?i.push(r):Mt.set(o,[r])}return be(()=>{let i=o;return()=>{_t&&_t.unregister(l),Mt.delete(i)}},[o]),d.useEffect(()=>{let i=r.current;return i&&n(i),()=>{i&&(r.current=null)}}),o}function Xu(e,t){if(e===t)return e;let n=Mt.get(e);if(n)return n.forEach(o=>o.current=t),t;let r=Mt.get(t);return r?(r.forEach(o=>o.current=e),e):t}function Wt(e=[]){let t=qe(),[n,r]=Gu(t),o=d.useCallback(()=>{r(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,r]);return be(o,[t,o,...e]),n}function at(...e){return(...t)=>{for(let n of e)typeof n=="function"&&n(...t)}}const ae=e=>{var t;return(t=e?.ownerDocument)!==null&&t!==void 0?t:document},Be=e=>e&&"window"in e&&e.window===e?e:ae(e).defaultView||window;function qu(e){return e!==null&&typeof e=="object"&&"nodeType"in e&&typeof e.nodeType=="number"}function Zu(e){return qu(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}let Ju=!1;function Fn(){return Ju}function Ie(e,t){if(!Fn())return t&&e?e.contains(t):!1;if(!e||!t)return!1;let n=t;for(;n!==null;){if(n===e)return!0;n.tagName==="SLOT"&&n.assignedSlot?n=n.assignedSlot.parentNode:Zu(n)?n=n.host:n=n.parentNode}return!1}const ke=(e=document)=>{var t;if(!Fn())return e.activeElement;let n=e.activeElement;for(;n&&"shadowRoot"in n&&(!((t=n.shadowRoot)===null||t===void 0)&&t.activeElement);)n=n.shadowRoot.activeElement;return n};function ye(e){return Fn()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}class Qu{get currentNode(){return this._currentNode}set currentNode(t){if(!Ie(this.root,t))throw new Error("Cannot set currentNode to a node that is not contained by the root node.");const n=[];let r=t,o=t;for(this._currentNode=t;r&&r!==this.root;)if(r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const i=r,a=this._doc.createTreeWalker(i,this.whatToShow,{acceptNode:this._acceptNode});n.push(a),a.currentNode=o,this._currentSetFor.add(a),r=o=i.host}else r=r.parentNode;const l=this._doc.createTreeWalker(this.root,this.whatToShow,{acceptNode:this._acceptNode});n.push(l),l.currentNode=o,this._currentSetFor.add(l),this._walkerStack=n}get doc(){return this._doc}firstChild(){let t=this.currentNode,n=this.nextNode();return Ie(t,n)?(n&&(this.currentNode=n),n):(this.currentNode=t,null)}lastChild(){let n=this._walkerStack[0].lastChild();return n&&(this.currentNode=n),n}nextNode(){const t=this._walkerStack[0].nextNode();if(t){if(t.shadowRoot){var n;let o;if(typeof this.filter=="function"?o=this.filter(t):!((n=this.filter)===null||n===void 0)&&n.acceptNode&&(o=this.filter.acceptNode(t)),o===NodeFilter.FILTER_ACCEPT)return this.currentNode=t,t;let l=this.nextNode();return l&&(this.currentNode=l),l}return t&&(this.currentNode=t),t}else if(this._walkerStack.length>1){this._walkerStack.shift();let r=this.nextNode();return r&&(this.currentNode=r),r}else return null}previousNode(){const t=this._walkerStack[0];if(t.currentNode===t.root){if(this._currentSetFor.has(t))if(this._currentSetFor.delete(t),this._walkerStack.length>1){this._walkerStack.shift();let o=this.previousNode();return o&&(this.currentNode=o),o}else return null;return null}const n=t.previousNode();if(n){if(n.shadowRoot){var r;let l;if(typeof this.filter=="function"?l=this.filter(n):!((r=this.filter)===null||r===void 0)&&r.acceptNode&&(l=this.filter.acceptNode(n)),l===NodeFilter.FILTER_ACCEPT)return n&&(this.currentNode=n),n;let i=this.lastChild();return i&&(this.currentNode=i),i}return n&&(this.currentNode=n),n}else if(this._walkerStack.length>1){this._walkerStack.shift();let o=this.previousNode();return o&&(this.currentNode=o),o}else return null}nextSibling(){return null}previousSibling(){return null}parentNode(){return null}constructor(t,n,r,o){this._walkerStack=[],this._currentSetFor=new Set,this._acceptNode=i=>{if(i.nodeType===Node.ELEMENT_NODE){const s=i.shadowRoot;if(s){const u=this._doc.createTreeWalker(s,this.whatToShow,{acceptNode:this._acceptNode});return this._walkerStack.unshift(u),NodeFilter.FILTER_ACCEPT}else{var a;if(typeof this.filter=="function")return this.filter(i);if(!((a=this.filter)===null||a===void 0)&&a.acceptNode)return this.filter.acceptNode(i);if(this.filter===null)return NodeFilter.FILTER_ACCEPT}}return NodeFilter.FILTER_SKIP},this._doc=t,this.root=n,this.filter=o??null,this.whatToShow=r??NodeFilter.SHOW_ALL,this._currentNode=n,this._walkerStack.unshift(t.createTreeWalker(n,r,this._acceptNode));const l=n.shadowRoot;if(l){const i=this._doc.createTreeWalker(l,this.whatToShow,{acceptNode:this._acceptNode});this._walkerStack.unshift(i)}}}function ec(e,t,n,r){return Fn()?new Qu(e,t,n,r):e.createTreeWalker(t,n,r)}function Se(...e){let t={...e[0]};for(let n=1;n<e.length;n++){let r=e[n];for(let o in r){let l=t[o],i=r[o];typeof l=="function"&&typeof i=="function"&&o[0]==="o"&&o[1]==="n"&&o.charCodeAt(2)>=65&&o.charCodeAt(2)<=90?t[o]=at(l,i):(o==="className"||o==="UNSAFE_className")&&typeof l=="string"&&typeof i=="string"?t[o]=uu(l,i):o==="id"&&l&&i?t.id=Xu(l,i):t[o]=i!==void 0?i:l}}return t}const tc=new Set(["id"]),nc=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),rc=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),oc=new Set(["dir","lang","hidden","inert","translate"]),fo=new Set(["onClick","onAuxClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut","onGotPointerCapture","onLostPointerCapture","onScroll","onWheel","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionCancel","onTransitionEnd","onTransitionRun","onTransitionStart"]),lc=/^(data-.*)$/;function Yt(e,t={}){let{labelable:n,isLink:r,global:o,events:l=o,propNames:i}=t,a={};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(tc.has(s)||n&&nc.has(s)||r&&rc.has(s)||o&&oc.has(s)||l&&fo.has(s)||s.endsWith("Capture")&&fo.has(s.slice(0,-7))||i?.has(s)||lc.test(s))&&(a[s]=e[s]);return a}function je(e){if(ic())e.focus({preventScroll:!0});else{let t=ac(e);e.focus(),sc(t)}}let rn=null;function ic(){if(rn==null){rn=!1;try{document.createElement("div").focus({get preventScroll(){return rn=!0,!0}})}catch{}}return rn}function ac(e){let t=e.parentNode,n=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&n.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),n}function sc(e){for(let{element:t,scrollTop:n,scrollLeft:r}of e)t.scrollTop=n,t.scrollLeft=r}function In(e){var t;if(typeof window>"u"||window.navigator==null)return!1;let n=(t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands;return Array.isArray(n)&&n.some(r=>e.test(r.brand))||e.test(window.navigator.userAgent)}function Ir(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function nt(e){let t=null;return()=>(t==null&&(t=e()),t)}const st=nt(function(){return Ir(/^Mac/i)}),uc=nt(function(){return Ir(/^iPhone/i)}),Pl=nt(function(){return Ir(/^iPad/i)||st()&&navigator.maxTouchPoints>1}),Rn=nt(function(){return uc()||Pl()}),bn=nt(function(){return st()||Rn()}),Rr=nt(function(){return In(/AppleWebKit/i)&&!Tl()}),Tl=nt(function(){return In(/Chrome/i)}),Br=nt(function(){return In(/Android/i)}),cc=nt(function(){return In(/Firefox/i)}),dc=d.createContext({isNative:!0,open:pc,useHref:e=>e});function Bn(){return d.useContext(dc)}function bt(e,t,n=!0){var r,o;let{metaKey:l,ctrlKey:i,altKey:a,shiftKey:s}=t;cc()&&(!((o=window.event)===null||o===void 0||(r=o.type)===null||r===void 0)&&r.startsWith("key"))&&e.target==="_blank"&&(st()?l=!0:i=!0);let u=Rr()&&st()&&!Pl()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:l,ctrlKey:i,altKey:a,shiftKey:s}):new MouseEvent("click",{metaKey:l,ctrlKey:i,altKey:a,shiftKey:s,bubbles:!0,cancelable:!0});bt.isOpening=n,je(e),e.dispatchEvent(u),bt.isOpening=!1}bt.isOpening=!1;function fc(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let n=document.createElement("a");n.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(n.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(n.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(n.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(n.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(n.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(n),t(n),e.removeChild(n)}}function pc(e,t){fc(e,n=>bt(n,t))}function hc(e){let t=Bn();var n;const r=t.useHref((n=e?.href)!==null&&n!==void 0?n:"");return{href:e?.href?r:void 0,target:e?.target,rel:e?.rel,download:e?.download,ping:e?.ping,referrerPolicy:e?.referrerPolicy}}let lt=new Map,dr=new Set;function po(){if(typeof window>"u")return;function e(r){return"propertyName"in r}let t=r=>{if(!e(r)||!r.target)return;let o=lt.get(r.target);o||(o=new Set,lt.set(r.target,o),r.target.addEventListener("transitioncancel",n,{once:!0})),o.add(r.propertyName)},n=r=>{if(!e(r)||!r.target)return;let o=lt.get(r.target);if(o&&(o.delete(r.propertyName),o.size===0&&(r.target.removeEventListener("transitioncancel",n),lt.delete(r.target)),lt.size===0)){for(let l of dr)l();dr.clear()}};document.body.addEventListener("transitionrun",t),document.body.addEventListener("transitionend",n)}typeof document<"u"&&(document.readyState!=="loading"?po():document.addEventListener("DOMContentLoaded",po));function bc(){for(const[e]of lt)"isConnected"in e&&!e.isConnected&&lt.delete(e)}function Al(e){requestAnimationFrame(()=>{bc(),lt.size===0?e():dr.add(e)})}function Kn(){let e=d.useRef(new Map),t=d.useCallback((o,l,i,a)=>{let s=a?.once?(...u)=>{e.current.delete(i),i(...u)}:i;e.current.set(i,{type:l,eventTarget:o,fn:s,options:a}),o.addEventListener(l,s,a)},[]),n=d.useCallback((o,l,i,a)=>{var s;let u=((s=e.current.get(i))===null||s===void 0?void 0:s.fn)||i;o.removeEventListener(l,u,a),e.current.delete(i)},[]),r=d.useCallback(()=>{e.current.forEach((o,l)=>{n(o.eventTarget,o.type,l,o.options)})},[n]);return d.useEffect(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}function mn(e,t){let{id:n,"aria-label":r,"aria-labelledby":o}=e;return n=qe(n),o&&r?o=[...new Set([n,...o.trim().split(/\s+/)])].join(" "):o&&(o=o.trim().split(/\s+/).join(" ")),!r&&!o&&t&&(r=t),{id:n,"aria-label":r,"aria-labelledby":o}}function vc(e,t){const n=d.useRef(!0),r=d.useRef(null);d.useEffect(()=>(n.current=!0,()=>{n.current=!1}),[]),d.useEffect(()=>{let o=r.current;n.current?n.current=!1:(!o||t.some((l,i)=>!Object.is(l,o[i])))&&e(),r.current=t},t)}function ho(e,t){const n=d.useRef(!0),r=d.useRef(null);be(()=>(n.current=!0,()=>{n.current=!1}),[]),be(()=>{n.current?n.current=!1:(!r.current||t.some((o,l)=>!Object.is(o,r[l])))&&e(),r.current=t},t)}function gc(){return typeof window.ResizeObserver<"u"}function bo(e){const{ref:t,box:n,onResize:r}=e;d.useEffect(()=>{let o=t?.current;if(o)if(gc()){const l=new window.ResizeObserver(i=>{i.length&&r()});return l.observe(o,{box:n}),()=>{o&&l.unobserve(o)}}else return window.addEventListener("resize",r,!1),()=>{window.removeEventListener("resize",r,!1)}},[r,t,n])}function kl(e,t){be(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}function Ht(e,t){if(!e)return!1;let n=window.getComputedStyle(e),r=/(auto|scroll)/.test(n.overflow+n.overflowX+n.overflowY);return r&&t&&(r=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),r}function Ml(e,t){let n=e;for(Ht(n,t)&&(n=n.parentElement);n&&!Ht(n,t);)n=n.parentElement;return n||document.scrollingElement||document.documentElement}function mc(e,t){const n=[];for(;e&&e!==document.documentElement;)Ht(e,t)&&n.push(e),e=e.parentElement;return n}let yc=0;const Xn=new Map;function $c(e){let[t,n]=d.useState();return be(()=>{if(!e)return;let r=Xn.get(e);if(r)n(r.element.id);else{let o=`react-aria-description-${yc++}`;n(o);let l=document.createElement("div");l.id=o,l.style.display="none",l.textContent=e,document.body.appendChild(l),r={refCount:0,element:l},Xn.set(e,r)}return r.refCount++,()=>{r&&--r.refCount===0&&(r.element.remove(),Xn.delete(e))}},[e]),{"aria-describedby":e?t:void 0}}function on(e,t,n,r){let o=Ee(n),l=n==null;d.useEffect(()=>{if(l||!e.current)return;let i=e.current;return i.addEventListener(t,o,r),()=>{i.removeEventListener(t,o,r)}},[e,t,r,l,o])}function Dl(e,t){let n=vo(e,t,"left"),r=vo(e,t,"top"),o=t.offsetWidth,l=t.offsetHeight,i=e.scrollLeft,a=e.scrollTop,{borderTopWidth:s,borderLeftWidth:u,scrollPaddingTop:c,scrollPaddingRight:f,scrollPaddingBottom:p,scrollPaddingLeft:h}=getComputedStyle(e),v=i+parseInt(u,10),b=a+parseInt(s,10),m=v+e.clientWidth,g=b+e.clientHeight,$=parseInt(c,10)||0,C=parseInt(p,10)||0,w=parseInt(f,10)||0,A=parseInt(h,10)||0;n<=i+A?i=n-parseInt(u,10)-A:n+o>m-w&&(i+=n+o-m+w),r<=b+$?a=r-parseInt(s,10)-$:r+l>g-C&&(a+=r+l-g+C),e.scrollLeft=i,e.scrollTop=a}function vo(e,t,n){const r=n==="left"?"offsetLeft":"offsetTop";let o=0;for(;t.offsetParent&&(o+=t[r],t.offsetParent!==e);){if(t.offsetParent.contains(e)){o-=e[r];break}t=t.offsetParent}return o}function go(e,t){if(e&&document.contains(e)){let i=document.scrollingElement||document.documentElement;if(window.getComputedStyle(i).overflow==="hidden"){let s=mc(e);for(let u of s)Dl(u,e)}else{var n;let{left:s,top:u}=e.getBoundingClientRect();e==null||(n=e.scrollIntoView)===null||n===void 0||n.call(e,{block:"nearest"});let{left:c,top:f}=e.getBoundingClientRect();if(Math.abs(s-c)>1||Math.abs(u-f)>1){var r,o,l;t==null||(o=t.containingElement)===null||o===void 0||(r=o.scrollIntoView)===null||r===void 0||r.call(o,{block:"center",inline:"center"}),(l=e.scrollIntoView)===null||l===void 0||l.call(e,{block:"nearest"})}}}}function Ll(e){return e.mozInputSource===0&&e.isTrusted?!0:Br()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function xc(e){return!Br()&&e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"}function wc(e,t,n){let r=Ee(()=>{n&&n(t)});d.useEffect(()=>{var o;let l=e==null||(o=e.current)===null||o===void 0?void 0:o.form;return l?.addEventListener("reset",r),()=>{l?.removeEventListener("reset",r)}},[e,r])}const Cc="react-aria-clear-focus",Ec="react-aria-focus";function At(e){return st()?e.metaKey:e.ctrlKey}const Sc=typeof Element<"u"&&"checkVisibility"in Element.prototype;function Pc(e){const t=Be(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:n,visibility:r}=e.style,o=n!=="none"&&r!=="hidden"&&r!=="collapse";if(o){const{getComputedStyle:l}=e.ownerDocument.defaultView;let{display:i,visibility:a}=l(e);o=i!=="none"&&a!=="hidden"&&a!=="collapse"}return o}function Tc(e,t){return!e.hasAttribute("hidden")&&!e.hasAttribute("data-react-aria-prevent-focus")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function Kr(e,t){return Sc?e.checkVisibility():e.nodeName!=="#comment"&&Pc(e)&&Tc(e,t)&&(!e.parentElement||Kr(e.parentElement,e))}const Nr=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])',"permission"],Ac=Nr.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";Nr.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const kc=Nr.join(':not([hidden]):not([tabindex="-1"]),');function Fl(e){return e.matches(Ac)&&Kr(e)&&!Il(e)}function Mc(e){return e.matches(kc)&&Kr(e)&&!Il(e)}function Il(e){let t=e;for(;t!=null;){if(t instanceof t.ownerDocument.defaultView.HTMLElement&&t.inert)return!0;t=t.parentElement}return!1}function Ft(e,t,n){let[r,o]=d.useState(e||t),l=d.useRef(e!==void 0),i=e!==void 0;d.useEffect(()=>{l.current,l.current=i},[i]);let a=i?e:r,s=d.useCallback((u,...c)=>{let f=(p,...h)=>{n&&(Object.is(a,p)||n(p,...h)),i||(a=p)};typeof u=="function"?o((h,...v)=>{let b=u(i?a:h,...v);return f(b,...c),i?h:b}):(i||o(u),f(u,...c))},[i,a,n]);return[a,s]}function fr(e,t=-1/0,n=1/0){return Math.min(Math.max(e,t),n)}let qn=new Map;function Rl(e){let{locale:t}=Dn(),n=t+(e?Object.entries(e).sort((o,l)=>o[0]<l[0]?-1:1).join():"");if(qn.has(n))return qn.get(n);let r=new Intl.Collator(t,e);return qn.set(n,r),r}function Dc(e){let t=Rl({usage:"search",...e}),n=d.useCallback((l,i)=>i.length===0?!0:(l=l.normalize("NFC"),i=i.normalize("NFC"),t.compare(l.slice(0,i.length),i)===0),[t]),r=d.useCallback((l,i)=>i.length===0?!0:(l=l.normalize("NFC"),i=i.normalize("NFC"),t.compare(l.slice(-i.length),i)===0),[t]),o=d.useCallback((l,i)=>{if(i.length===0)return!0;l=l.normalize("NFC"),i=i.normalize("NFC");let a=0,s=i.length;for(;a+s<=l.length;a++){let u=l.slice(a,a+s);if(t.compare(i,u)===0)return!0}return!1},[t]);return d.useMemo(()=>({startsWith:n,endsWith:r,contains:o}),[n,r,o])}const Bl=d.createContext({});function Or(e){const t=d.useRef(null);return t.current===null&&(t.current=e()),t.current}const Kl=typeof window<"u",Nl=Kl?d.useLayoutEffect:d.useEffect,Nn=d.createContext(null),Lc=(e,t,n)=>n>t?t:n<e?e:n;function Fc(e){return typeof e=="object"&&e!==null}const Ol=e=>t=>typeof t=="string"&&t.startsWith(e),zl=Ol("--"),Ic=Ol("var(--"),ab=e=>Ic(e)?Rc.test(e.split("/*")[0].trim()):!1,Rc=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,zr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},pr={...zr,transform:e=>Lc(0,1,e)},ln={...zr,default:1},Xt=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),ut=Xt("deg"),Zn=Xt("%"),X=Xt("px"),sb=Xt("vh"),ub=Xt("vw"),mo={...Zn,parse:e=>Zn.parse(e)/100,transform:e=>Zn.transform(e*100)},On=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],_l=new Set(On),yo={...zr,transform:Math.round},Bc={rotate:ut,rotateX:ut,rotateY:ut,rotateZ:ut,scale:ln,scaleX:ln,scaleY:ln,scaleZ:ln,skew:ut,skewX:ut,skewY:ut,distance:X,translateX:X,translateY:X,translateZ:X,x:X,y:X,z:X,perspective:X,transformPerspective:X,opacity:pr,originX:mo,originY:mo,originZ:X},Vl={borderWidth:X,borderTopWidth:X,borderRightWidth:X,borderBottomWidth:X,borderLeftWidth:X,borderRadius:X,radius:X,borderTopLeftRadius:X,borderTopRightRadius:X,borderBottomRightRadius:X,borderBottomLeftRadius:X,width:X,maxWidth:X,height:X,maxHeight:X,top:X,right:X,bottom:X,left:X,padding:X,paddingTop:X,paddingRight:X,paddingBottom:X,paddingLeft:X,margin:X,marginTop:X,marginRight:X,marginBottom:X,marginLeft:X,backgroundPositionX:X,backgroundPositionY:X,...Bc,zIndex:yo,fillOpacity:pr,strokeOpacity:pr,numOctaves:yo},jl=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function Kc(e){return Fc(e)&&"offsetHeight"in e}const vt=e=>!!(e&&e.getVelocity),_r=d.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class Nc extends d.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,o=Kc(r)&&r.offsetWidth||0,l=this.props.sizeRef.current;l.height=n.offsetHeight||0,l.width=n.offsetWidth||0,l.top=n.offsetTop,l.left=n.offsetLeft,l.right=o-l.width-l.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Oc({children:e,isPresent:t,anchorX:n,root:r}){const o=d.useId(),l=d.useRef(null),i=d.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=d.useContext(_r);return d.useInsertionEffect(()=>{const{width:s,height:u,top:c,left:f,right:p}=i.current;if(t||!l.current||!s||!u)return;const h=n==="left"?`left: ${f}`:`right: ${p}`;l.current.dataset.motionPopId=o;const v=document.createElement("style");a&&(v.nonce=a);const b=r??document.head;return b.appendChild(v),v.sheet&&v.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${u}px !important;
            ${h}px !important;
            top: ${c}px !important;
          }
        `),()=>{b.contains(v)&&b.removeChild(v)}},[t]),D.jsx(Nc,{isPresent:t,childRef:l,sizeRef:i,children:d.cloneElement(e,{ref:l})})}const zc=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:l,mode:i,anchorX:a,root:s})=>{const u=Or(_c),c=d.useId();let f=!0,p=d.useMemo(()=>(f=!1,{id:c,initial:t,isPresent:n,custom:o,onExitComplete:h=>{u.set(h,!0);for(const v of u.values())if(!v)return;r&&r()},register:h=>(u.set(h,!1),()=>u.delete(h))}),[n,u,r]);return l&&f&&(p={...p}),d.useMemo(()=>{u.forEach((h,v)=>u.set(v,!1))},[n]),d.useEffect(()=>{!n&&!u.size&&r&&r()},[n]),i==="popLayout"&&(e=D.jsx(Oc,{isPresent:n,anchorX:a,root:s,children:e})),D.jsx(Nn.Provider,{value:p,children:e})};function _c(){return new Map}function Vc(e=!0){const t=d.useContext(Nn);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,l=d.useId();d.useEffect(()=>{if(e)return o(l)},[e]);const i=d.useCallback(()=>e&&r&&r(l),[l,r,e]);return!n&&r?[!1,i]:[!0]}const an=e=>e.key||"";function $o(e){const t=[];return d.Children.forEach(e,n=>{d.isValidElement(n)&&t.push(n)}),t}const Wl=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:l="sync",propagate:i=!1,anchorX:a="left",root:s})=>{const[u,c]=Vc(i),f=d.useMemo(()=>$o(e),[e]),p=i&&!u?[]:f.map(an),h=d.useRef(!0),v=d.useRef(f),b=Or(()=>new Map),[m,g]=d.useState(f),[$,C]=d.useState(f);Nl(()=>{h.current=!1,v.current=f;for(let k=0;k<$.length;k++){const K=an($[k]);p.includes(K)?b.delete(K):b.get(K)!==!0&&b.set(K,!1)}},[$,p.length,p.join("-")]);const w=[];if(f!==m){let k=[...f];for(let K=0;K<$.length;K++){const O=$[K],z=an(O);p.includes(z)||(k.splice(K,0,O),w.push(O))}return l==="wait"&&w.length&&(k=w),C($o(k)),g(f),null}const{forceRender:A}=d.useContext(Bl);return D.jsx(D.Fragment,{children:$.map(k=>{const K=an(k),O=i&&!u?!1:f===$||p.includes(K),z=()=>{if(b.has(K))b.set(K,!0);else return;let N=!0;b.forEach(x=>{x||(N=!1)}),N&&(A?.(),C(v.current),i&&c?.(),r&&r())};return D.jsx(zc,{isPresent:O,initial:!h.current||n?void 0:!1,custom:t,presenceAffectsLayout:o,mode:l,root:s,onExitComplete:O?void 0:z,anchorX:a,children:k},K)})})},Vr=d.createContext({strict:!1}),xo={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},yn={};for(const e in xo)yn[e]={isEnabled:t=>xo[e].some(n=>!!t[n])};function hr(e){for(const t in e)yn[t]={...yn[t],...e[t]}}function jr({children:e,features:t,strict:n=!1}){const[,r]=d.useState(!Jn(t)),o=d.useRef(void 0);if(!Jn(t)){const{renderer:l,...i}=t;o.current=l,hr(i)}return d.useEffect(()=>{Jn(t)&&t().then(({renderer:l,...i})=>{hr(i),o.current=l,r(!0)})},[]),D.jsx(Vr.Provider,{value:{renderer:o.current,strict:n},children:e})}function Jn(e){return typeof e=="function"}const jc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function $n(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||jc.has(e)}let Hl=e=>!$n(e);function Wc(e){typeof e=="function"&&(Hl=t=>t.startsWith("on")?!$n(t):e(t))}try{Wc(require("@emotion/is-prop-valid").default)}catch{}function Hc(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Hl(o)||n===!0&&$n(o)||!t&&!$n(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}const zn=d.createContext({});function Ul(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function br(e){return typeof e=="string"||Array.isArray(e)}const Uc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Gc=["initial",...Uc];function Wr(e){return Ul(e.animate)||Gc.some(t=>br(e[t]))}function Yc(e){return!!(Wr(e)||e.variants)}function Xc(e,t){if(Wr(e)){const{initial:n,animate:r}=e;return{initial:n===!1||br(n)?n:void 0,animate:br(r)?r:void 0}}return e.inherit!==!1?t:{}}function qc(e){const{initial:t,animate:n}=Xc(e,d.useContext(zn));return d.useMemo(()=>({initial:t,animate:n}),[wo(t),wo(n)])}function wo(e){return Array.isArray(e)?e.join(" "):e}const vr={};function cb(e){for(const t in e)vr[t]=e[t],zl(t)&&(vr[t].isCSSVariable=!0)}function Gl(e,{layout:t,layoutId:n}){return _l.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!vr[e]||e==="opacity")}const Zc={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Jc=On.length;function Qc(e,t,n){let r="",o=!0;for(let l=0;l<Jc;l++){const i=On[l],a=e[i];if(a===void 0)continue;let s=!0;if(typeof a=="number"?s=a===(i.startsWith("scale")?1:0):s=parseFloat(a)===0,!s||n){const u=jl(a,Vl[i]);if(!s){o=!1;const c=Zc[i]||i;r+=`${c}(${u}) `}n&&(t[i]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function Yl(e,t,n){const{style:r,vars:o,transformOrigin:l}=e;let i=!1,a=!1;for(const s in t){const u=t[s];if(_l.has(s)){i=!0;continue}else if(zl(s)){o[s]=u;continue}else{const c=jl(u,Vl[s]);s.startsWith("origin")?(a=!0,l[s]=c):r[s]=c}}if(t.transform||(i||n?r.transform=Qc(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:s="50%",originY:u="50%",originZ:c=0}=l;r.transformOrigin=`${s} ${u} ${c}`}}const Hr=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Xl(e,t,n){for(const r in t)!vt(t[r])&&!Gl(r,n)&&(e[r]=t[r])}function ed({transformTemplate:e},t){return d.useMemo(()=>{const n=Hr();return Yl(n,t,e),Object.assign({},n.vars,n.style)},[t])}function td(e,t){const n=e.style||{},r={};return Xl(r,n,e),Object.assign(r,ed(e,t)),r}function nd(e,t){const n={},r=td(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const rd={offset:"stroke-dashoffset",array:"stroke-dasharray"},od={offset:"strokeDashoffset",array:"strokeDasharray"};function ld(e,t,n=1,r=0,o=!0){e.pathLength=1;const l=o?rd:od;e[l.offset]=X.transform(-r);const i=X.transform(t),a=X.transform(n);e[l.array]=`${i} ${a}`}function id(e,{attrX:t,attrY:n,attrScale:r,pathLength:o,pathSpacing:l=1,pathOffset:i=0,...a},s,u,c){if(Yl(e,a,u),s){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:p}=e;f.transform&&(p.transform=f.transform,delete f.transform),(p.transform||f.transformOrigin)&&(p.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),p.transform&&(p.transformBox=c?.transformBox??"fill-box",delete f.transformBox),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&ld(f,o,l,i,!1)}const ql=()=>({...Hr(),attrs:{}}),ad=e=>typeof e=="string"&&e.toLowerCase()==="svg";function sd(e,t,n,r){const o=d.useMemo(()=>{const l=ql();return id(l,t,ad(r),e.transformTemplate,e.style),{...l.attrs,style:{...l.style}}},[t]);if(e.style){const l={};Xl(l,e.style,e),o.style={...l,...o.style}}return o}const ud=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Zl(e){return typeof e!="string"||e.includes("-")?!1:!!(ud.indexOf(e)>-1||/[A-Z]/u.test(e))}function cd(e,t,n,{latestValues:r},o,l=!1){const a=(Zl(e)?sd:nd)(t,r,o,e),s=Hc(t,typeof e=="string",l),u=e!==d.Fragment?{...s,...a,ref:n}:{},{children:c}=t,f=d.useMemo(()=>vt(c)?c.get():c,[c]);return d.createElement(e,{...u,children:f})}function Co(e){const t=[{},{}];return e?.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function dd(e,t,n,r){if(typeof t=="function"){const[o,l]=Co(r);t=t(n!==void 0?n:e.custom,o,l)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,l]=Co(r);t=t(n!==void 0?n:e.custom,o,l)}return t}function fd(e){return vt(e)?e.get():e}function pd({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:hd(n,r,o,e),renderState:t()}}function hd(e,t,n,r){const o={},l=r(e,{});for(const p in l)o[p]=fd(l[p]);let{initial:i,animate:a}=e;const s=Wr(e),u=Yc(e);t&&u&&!s&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||i===!1;const f=c?a:i;if(f&&typeof f!="boolean"&&!Ul(f)){const p=Array.isArray(f)?f:[f];for(let h=0;h<p.length;h++){const v=dd(e,p[h]);if(v){const{transitionEnd:b,transition:m,...g}=v;for(const $ in g){let C=g[$];if(Array.isArray(C)){const w=c?C.length-1:0;C=C[w]}C!==null&&(o[$]=C)}for(const $ in b)o[$]=b[$]}}}return o}const Jl=e=>(t,n)=>{const r=d.useContext(zn),o=d.useContext(Nn),l=()=>pd(e,t,r,o);return n?l():Or(l)};function Ql(e,t,n){const{style:r}=e,o={};for(const l in r)(vt(r[l])||t.style&&vt(t.style[l])||Gl(l,e)||n?.getValue(l)?.liveStyle!==void 0)&&(o[l]=r[l]);return o}const bd=Jl({scrapeMotionValuesFromProps:Ql,createRenderState:Hr});function vd(e,t,n){const r=Ql(e,t,n);for(const o in e)if(vt(e[o])||vt(t[o])){const l=On.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[l]=e[o]}return r}const gd=Jl({scrapeMotionValuesFromProps:vd,createRenderState:ql}),md=Symbol.for("motionComponentSymbol");function ei(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function yd(e,t,n){return d.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):ei(n)&&(n.current=r))},[t])}const $d=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),xd="framerAppearId",wd="data-"+$d(xd),Cd=d.createContext({});function Ed(e,t,n,r,o){const{visualElement:l}=d.useContext(zn),i=d.useContext(Vr),a=d.useContext(Nn),s=d.useContext(_r).reducedMotion,u=d.useRef(null);r=r||i.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:l,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:s}));const c=u.current,f=d.useContext(Cd);c&&!c.projection&&o&&(c.type==="html"||c.type==="svg")&&Sd(u.current,n,o,f);const p=d.useRef(!1);d.useInsertionEffect(()=>{c&&p.current&&c.update(n,a)});const h=n[wd],v=d.useRef(!!h&&!window.MotionHandoffIsComplete?.(h)&&window.MotionHasOptimisedAnimation?.(h));return Nl(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),v.current&&c.animationState&&c.animationState.animateChanges())}),d.useEffect(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(h)}),v.current=!1),c.enteringChildren=void 0)}),c}function Sd(e,t,n,r){const{layoutId:o,layout:l,drag:i,dragConstraints:a,layoutScroll:s,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:ti(e.parent)),e.projection.setOptions({layoutId:o,layout:l,alwaysMeasureLayout:!!i||a&&ei(a),visualElement:e,animationType:typeof l=="string"?l:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:s,layoutRoot:u})}function ti(e){if(e)return e.options.allowProjection!==!1?e.projection:ti(e.parent)}function Qn(e,{forwardMotionProps:t=!1}={},n,r){n&&hr(n);const o=Zl(e)?gd:bd;function l(a,s){let u;const c={...d.useContext(_r),...a,layoutId:Pd(a)},{isStatic:f}=c,p=qc(a),h=o(a,f);if(!f&&Kl){Td();const v=Ad(c);u=v.MeasureLayout,p.visualElement=Ed(e,h,c,r,v.ProjectionNode)}return D.jsxs(zn.Provider,{value:p,children:[u&&p.visualElement?D.jsx(u,{visualElement:p.visualElement,...c}):null,cd(e,a,yd(h,p.visualElement,s),h,f,t)]})}l.displayName=`motion.${typeof e=="string"?e:`create(${e.displayName??e.name??""})`}`;const i=d.forwardRef(l);return i[md]=e,i}function Pd({layoutId:e}){const t=d.useContext(Bl).id;return t&&e!==void 0?t+"-"+e:e}function Td(e,t){d.useContext(Vr).strict}function Ad(e){const{drag:t,layout:n}=yn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function kd(e,t){if(typeof Proxy>"u")return Qn;const n=new Map,r=(l,i)=>Qn(l,i,e,t),o=(l,i)=>r(l,i);return new Proxy(o,{get:(l,i)=>i==="create"?r:(n.has(i)||n.set(i,Qn(i,void 0,e,t)),n.get(i))})}const Ur=kd(),Xe={top:"top",bottom:"top",left:"left",right:"left"},xn={top:"bottom",bottom:"top",left:"right",right:"left"},Md={top:"left",left:"top"},gr={top:"height",left:"width"},ni={width:"totalWidth",height:"totalHeight"},sn={};let Te=typeof document<"u"?window.visualViewport:null;function Eo(e){let t=0,n=0,r=0,o=0,l=0,i=0,a={};var s;let u=((s=Te?.scale)!==null&&s!==void 0?s:1)>1;if(e.tagName==="BODY"){let v=document.documentElement;r=v.clientWidth,o=v.clientHeight;var c;t=(c=Te?.width)!==null&&c!==void 0?c:r;var f;n=(f=Te?.height)!==null&&f!==void 0?f:o,a.top=v.scrollTop||e.scrollTop,a.left=v.scrollLeft||e.scrollLeft,Te&&(l=Te.offsetTop,i=Te.offsetLeft)}else({width:t,height:n,top:l,left:i}=Dt(e)),a.top=e.scrollTop,a.left=e.scrollLeft,r=t,o=n;if(Rr()&&(e.tagName==="BODY"||e.tagName==="HTML")&&u){a.top=0,a.left=0;var p;l=(p=Te?.pageTop)!==null&&p!==void 0?p:0;var h;i=(h=Te?.pageLeft)!==null&&h!==void 0?h:0}return{width:t,height:n,totalWidth:r,totalHeight:o,scroll:a,top:l,left:i}}function Dd(e){return{top:e.scrollTop,left:e.scrollLeft,width:e.scrollWidth,height:e.scrollHeight}}function So(e,t,n,r,o,l,i){var a;let s=(a=o.scroll[e])!==null&&a!==void 0?a:0,u=r[gr[e]],c=r.scroll[Xe[e]]+l,f=u+r.scroll[Xe[e]]-l,p=t-s+i[e]-r[Xe[e]],h=t-s+n+i[e]-r[Xe[e]];return p<c?c-p:h>f?Math.max(f-h,c-p):0}function Ld(e){let t=window.getComputedStyle(e);return{top:parseInt(t.marginTop,10)||0,bottom:parseInt(t.marginBottom,10)||0,left:parseInt(t.marginLeft,10)||0,right:parseInt(t.marginRight,10)||0}}function Po(e){if(sn[e])return sn[e];let[t,n]=e.split(" "),r=Xe[t]||"right",o=Md[r];Xe[n]||(n="center");let l=gr[r],i=gr[o];return sn[e]={placement:t,crossPlacement:n,axis:r,crossAxis:o,size:l,crossSize:i},sn[e]}function er(e,t,n,r,o,l,i,a,s,u){let{placement:c,crossPlacement:f,axis:p,crossAxis:h,size:v,crossSize:b}=r,m={};var g;m[h]=(g=e[h])!==null&&g!==void 0?g:0;var $,C,w,A;f==="center"?m[h]+=((($=e[b])!==null&&$!==void 0?$:0)-((C=n[b])!==null&&C!==void 0?C:0))/2:f!==h&&(m[h]+=((w=e[b])!==null&&w!==void 0?w:0)-((A=n[b])!==null&&A!==void 0?A:0)),m[h]+=l;const k=e[h]-n[b]+s+u,K=e[h]+e[b]-s-u;if(m[h]=fr(m[h],k,K),c===p){const O=a?i[v]:t[ni[v]];m[xn[p]]=Math.floor(O-e[p]+o)}else m[p]=Math.floor(e[p]+e[v]+o);return m}function Fd(e,t,n,r,o,l,i,a){const s=r?n.height:t[ni.height];var u;let c=e.top!=null?n.top+e.top:n.top+(s-((u=e.bottom)!==null&&u!==void 0?u:0)-i);var f,p,h,v,b,m;let g=a!=="top"?Math.max(0,t.height+t.top+((f=t.scroll.top)!==null&&f!==void 0?f:0)-c-(((p=o.top)!==null&&p!==void 0?p:0)+((h=o.bottom)!==null&&h!==void 0?h:0)+l)):Math.max(0,c+i-(t.top+((v=t.scroll.top)!==null&&v!==void 0?v:0))-(((b=o.top)!==null&&b!==void 0?b:0)+((m=o.bottom)!==null&&m!==void 0?m:0)+l));return Math.min(t.height-l*2,g)}function To(e,t,n,r,o,l){let{placement:i,axis:a,size:s}=l;var u,c;if(i===a)return Math.max(0,n[a]-e[a]-((u=e.scroll[a])!==null&&u!==void 0?u:0)+t[a]-((c=r[a])!==null&&c!==void 0?c:0)-r[xn[a]]-o);var f;return Math.max(0,e[s]+e[a]+e.scroll[a]-t[a]-n[a]-n[s]-((f=r[a])!==null&&f!==void 0?f:0)-r[xn[a]]-o)}function Id(e,t,n,r,o,l,i,a,s,u,c,f,p,h,v,b){let m=Po(e),{size:g,crossAxis:$,crossSize:C,placement:w,crossPlacement:A}=m,k=er(t,a,n,m,c,f,u,p,v,b),K=c,O=To(a,u,t,o,l+c,m);if(i&&r[g]>O){let W=Po(`${xn[w]} ${A}`),re=er(t,a,n,W,c,f,u,p,v,b);To(a,u,t,o,l+c,W)>O&&(m=W,k=re,K=c)}let z="bottom";m.axis==="top"?m.placement==="top"?z="top":m.placement==="bottom"&&(z="bottom"):m.crossAxis==="top"&&(m.crossPlacement==="top"?z="bottom":m.crossPlacement==="bottom"&&(z="top"));let N=So($,k[$],n[C],a,s,l,u);k[$]+=N;let x=Fd(k,a,u,p,o,l,n.height,z);h&&h<x&&(x=h),n.height=Math.min(n.height,x),k=er(t,a,n,m,K,f,u,p,v,b),N=So($,k[$],n[C],a,s,l,u),k[$]+=N;let P={},R=t[$]+.5*t[C]-k[$]-o[Xe[$]];const E=v/2+b;var L,T,y,M;const S=Xe[$]==="left"?((L=o.left)!==null&&L!==void 0?L:0)+((T=o.right)!==null&&T!==void 0?T:0):((y=o.top)!==null&&y!==void 0?y:0)+((M=o.bottom)!==null&&M!==void 0?M:0),I=n[C]-S-v/2-b,_=t[$]+v/2-(k[$]+o[Xe[$]]),H=t[$]+t[C]-v/2-(k[$]+o[Xe[$]]),V=fr(R,_,H);return P[$]=fr(V,E,I),{position:k,maxHeight:x,arrowOffsetLeft:P.left,arrowOffsetTop:P.top,placement:m.placement}}function Rd(e){let{placement:t,targetNode:n,overlayNode:r,scrollNode:o,padding:l,shouldFlip:i,boundaryElement:a,offset:s,crossOffset:u,maxHeight:c,arrowSize:f=0,arrowBoundaryOffset:p=0}=e,h=r instanceof HTMLElement?Bd(r):document.documentElement,v=h===document.documentElement;const b=window.getComputedStyle(h).position;let m=!!b&&b!=="static",g=v?Dt(n):Ao(n,h);if(!v){let{marginTop:P,marginLeft:R}=window.getComputedStyle(n);g.top+=parseInt(P,10)||0,g.left+=parseInt(R,10)||0}let $=Dt(r),C=Ld(r);var w,A;$.width+=((w=C.left)!==null&&w!==void 0?w:0)+((A=C.right)!==null&&A!==void 0?A:0);var k,K;$.height+=((k=C.top)!==null&&k!==void 0?k:0)+((K=C.bottom)!==null&&K!==void 0?K:0);let O=Dd(o),z=Eo(a),N=Eo(h),x=a.tagName==="BODY"?Dt(h):Ao(h,a);return h.tagName==="HTML"&&a.tagName==="BODY"&&(N.scroll.top=0,N.scroll.left=0),Id(t,g,$,O,C,l,i,z,N,x,s,u,m,c,f,p)}function Dt(e){let{top:t,left:n,width:r,height:o}=e.getBoundingClientRect(),{scrollTop:l,scrollLeft:i,clientTop:a,clientLeft:s}=document.documentElement;return{top:t+l-a,left:n+i-s,width:r,height:o}}function Ao(e,t){let n=window.getComputedStyle(e),r;if(n.position==="fixed"){let{top:o,left:l,width:i,height:a}=e.getBoundingClientRect();r={top:o,left:l,width:i,height:a}}else{r=Dt(e);let o=Dt(t),l=window.getComputedStyle(t);o.top+=(parseInt(l.borderTopWidth,10)||0)-t.scrollTop,o.left+=(parseInt(l.borderLeftWidth,10)||0)-t.scrollLeft,r.top-=o.top,r.left-=o.left}return r.top-=parseInt(n.marginTop,10)||0,r.left-=parseInt(n.marginLeft,10)||0,r}function Bd(e){let t=e.offsetParent;if(t&&t===document.body&&window.getComputedStyle(t).position==="static"&&!ko(t)&&(t=document.documentElement),t==null)for(t=e.parentElement;t&&!ko(t);)t=t.parentElement;return t||document.documentElement}function ko(e){let t=window.getComputedStyle(e);return t.transform!=="none"||/transform|perspective/.test(t.willChange)||t.filter!=="none"||t.contain==="paint"||"backdropFilter"in t&&t.backdropFilter!=="none"||"WebkitBackdropFilter"in t&&t.WebkitBackdropFilter!=="none"}const ri=new WeakMap;function Kd(e){let{triggerRef:t,isOpen:n,onClose:r}=e;d.useEffect(()=>{if(!n||r===null)return;let o=l=>{let i=l.target;if(!t.current||i instanceof Node&&!i.contains(t.current)||l.target instanceof HTMLInputElement||l.target instanceof HTMLTextAreaElement)return;let a=r||ri.get(t.current);a&&a()};return window.addEventListener("scroll",o,!0),()=>{window.removeEventListener("scroll",o,!0)}},[n,r,t])}let me=typeof document<"u"?window.visualViewport:null;function Nd(e){let{direction:t}=Dn(),{arrowSize:n=0,targetRef:r,overlayRef:o,scrollRef:l=o,placement:i="bottom",containerPadding:a=12,shouldFlip:s=!0,boundaryElement:u=typeof document<"u"?document.body:null,offset:c=0,crossOffset:f=0,shouldUpdatePosition:p=!0,isOpen:h=!0,onClose:v,maxHeight:b,arrowBoundaryOffset:m=0}=e,[g,$]=d.useState(null),C=[p,i,o.current,r.current,l.current,a,s,u,c,f,h,t,b,m,n],w=d.useRef(me?.scale);d.useEffect(()=>{h&&(w.current=me?.scale)},[h]);let A=d.useCallback(()=>{if(p===!1||!h||!o.current||!r.current||!u||me?.scale!==w.current)return;let N=null;if(l.current&&l.current.contains(document.activeElement)){var x;let M=(x=document.activeElement)===null||x===void 0?void 0:x.getBoundingClientRect(),S=l.current.getBoundingClientRect();var P;if(N={type:"top",offset:((P=M?.top)!==null&&P!==void 0?P:0)-S.top},N.offset>S.height/2){N.type="bottom";var R;N.offset=((R=M?.bottom)!==null&&R!==void 0?R:0)-S.bottom}}let E=o.current;if(!b&&o.current){var L;E.style.top="0px",E.style.bottom="";var T;E.style.maxHeight=((T=(L=window.visualViewport)===null||L===void 0?void 0:L.height)!==null&&T!==void 0?T:window.innerHeight)+"px"}let y=Rd({placement:zd(i,t),overlayNode:o.current,targetNode:r.current,scrollNode:l.current||o.current,padding:a,shouldFlip:s,boundaryElement:u,offset:c,crossOffset:f,maxHeight:b,arrowSize:n,arrowBoundaryOffset:m});if(y.position){if(E.style.top="",E.style.bottom="",E.style.left="",E.style.right="",Object.keys(y.position).forEach(M=>E.style[M]=y.position[M]+"px"),E.style.maxHeight=y.maxHeight!=null?y.maxHeight+"px":"",N&&document.activeElement&&l.current){let M=document.activeElement.getBoundingClientRect(),S=l.current.getBoundingClientRect(),I=M[N.type]-S[N.type];l.current.scrollTop+=I-N.offset}$(y)}},C);be(A,C),Od(A),bo({ref:o,onResize:A}),bo({ref:r,onResize:A});let k=d.useRef(!1);be(()=>{let N,x=()=>{k.current=!0,clearTimeout(N),N=setTimeout(()=>{k.current=!1},500),A()},P=()=>{k.current&&x()};return me?.addEventListener("resize",x),me?.addEventListener("scroll",P),()=>{me?.removeEventListener("resize",x),me?.removeEventListener("scroll",P)}},[A]);let K=d.useCallback(()=>{k.current||v?.()},[v,k]);Kd({triggerRef:r,isOpen:h,onClose:v&&K});var O,z;return{overlayProps:{style:{position:"absolute",zIndex:1e5,...g?.position,maxHeight:(O=g?.maxHeight)!==null&&O!==void 0?O:"100vh"}},placement:(z=g?.placement)!==null&&z!==void 0?z:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:g?.arrowOffsetLeft,top:g?.arrowOffsetTop}},updatePosition:A}}function Od(e){be(()=>(window.addEventListener("resize",e,!1),()=>{window.removeEventListener("resize",e,!1)}),[e])}function zd(e,t){return t==="rtl"?e.replace("start","right").replace("end","left"):e.replace("start","left").replace("end","right")}function Gr(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function oi(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function li(e){let t=d.useRef({isFocused:!1,observer:null});be(()=>{const r=t.current;return()=>{r.observer&&(r.observer.disconnect(),r.observer=null)}},[]);let n=Ee(r=>{e?.(r)});return d.useCallback(r=>{if(r.target instanceof HTMLButtonElement||r.target instanceof HTMLInputElement||r.target instanceof HTMLTextAreaElement||r.target instanceof HTMLSelectElement){t.current.isFocused=!0;let o=r.target,l=i=>{if(t.current.isFocused=!1,o.disabled){let a=Gr(i);n(a)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};o.addEventListener("focusout",l,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&o.disabled){var i;(i=t.current.observer)===null||i===void 0||i.disconnect();let a=o===document.activeElement?null:document.activeElement;o.dispatchEvent(new FocusEvent("blur",{relatedTarget:a})),o.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:a}))}}),t.current.observer.observe(o,{attributes:!0,attributeFilter:["disabled"]})}},[n])}let wn=!1;function _d(e){for(;e&&!Fl(e);)e=e.parentElement;let t=Be(e),n=t.document.activeElement;if(!n||n===e)return;wn=!0;let r=!1,o=c=>{(c.target===n||r)&&c.stopImmediatePropagation()},l=c=>{(c.target===n||r)&&(c.stopImmediatePropagation(),!e&&!r&&(r=!0,je(n),s()))},i=c=>{(c.target===e||r)&&c.stopImmediatePropagation()},a=c=>{(c.target===e||r)&&(c.stopImmediatePropagation(),r||(r=!0,je(n),s()))};t.addEventListener("blur",o,!0),t.addEventListener("focusout",l,!0),t.addEventListener("focusin",a,!0),t.addEventListener("focus",i,!0);let s=()=>{cancelAnimationFrame(u),t.removeEventListener("blur",o,!0),t.removeEventListener("focusout",l,!0),t.removeEventListener("focusin",a,!0),t.removeEventListener("focus",i,!0),wn=!1,r=!1},u=requestAnimationFrame(s);return s}let kt="default",mr="",vn=new WeakMap;function Vd(e){if(Rn()){if(kt==="default"){const t=ae(e);mr=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}kt="disabled"}else if(e instanceof HTMLElement||e instanceof SVGElement){let t="userSelect"in e.style?"userSelect":"webkitUserSelect";vn.set(e,e.style[t]),e.style[t]="none"}}function Mo(e){if(Rn()){if(kt!=="disabled")return;kt="restoring",setTimeout(()=>{Al(()=>{if(kt==="restoring"){const t=ae(e);t.documentElement.style.webkitUserSelect==="none"&&(t.documentElement.style.webkitUserSelect=mr||""),mr="",kt="default"}})},300)}else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&vn.has(e)){let t=vn.get(e),n="userSelect"in e.style?"userSelect":"webkitUserSelect";e.style[n]==="none"&&(e.style[n]=t),e.getAttribute("style")===""&&e.removeAttribute("style"),vn.delete(e)}}const Yr=te.createContext({register:()=>{}});Yr.displayName="PressResponderContext";function jd(e,t){return t.get?t.get.call(e):t.value}function ii(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function Wd(e,t){var n=ii(e,t,"get");return jd(e,n)}function Hd(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}function Do(e,t,n){var r=ii(e,t,"set");return Hd(e,r,n),n}function Ud(e){let t=d.useContext(Yr);if(t){let{register:n,...r}=t;e=Se(r,e),n()}return kl(t,e.ref),e}var un=new WeakMap;class cn{continuePropagation(){Do(this,un,!1)}get shouldStopPropagation(){return Wd(this,un)}constructor(t,n,r,o){Hu(this,un,{writable:!0,value:void 0}),Do(this,un,!0);var l;let i=(l=o?.target)!==null&&l!==void 0?l:r.currentTarget;const a=i?.getBoundingClientRect();let s,u=0,c,f=null;r.clientX!=null&&r.clientY!=null&&(c=r.clientX,f=r.clientY),a&&(c!=null&&f!=null?(s=c-a.left,u=f-a.top):(s=a.width/2,u=a.height/2)),this.type=t,this.pointerType=n,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=u}}const Lo=Symbol("linkClicked"),Fo="react-aria-pressable-style",Io="data-react-aria-pressable";function qt(e){let{onPress:t,onPressChange:n,onPressStart:r,onPressEnd:o,onPressUp:l,onClick:i,isDisabled:a,isPressed:s,preventFocusOnPress:u,shouldCancelOnPointerExit:c,allowTextSelectionOnPress:f,ref:p,...h}=Ud(e),[v,b]=d.useState(!1),m=d.useRef({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:g,removeAllGlobalListeners:$}=Kn(),C=Ee((x,P)=>{let R=m.current;if(a||R.didFirePressStart)return!1;let E=!0;if(R.isTriggeringEvent=!0,r){let L=new cn("pressstart",P,x);r(L),E=L.shouldStopPropagation}return n&&n(!0),R.isTriggeringEvent=!1,R.didFirePressStart=!0,b(!0),E}),w=Ee((x,P,R=!0)=>{let E=m.current;if(!E.didFirePressStart)return!1;E.didFirePressStart=!1,E.isTriggeringEvent=!0;let L=!0;if(o){let T=new cn("pressend",P,x);o(T),L=T.shouldStopPropagation}if(n&&n(!1),b(!1),t&&R&&!a){let T=new cn("press",P,x);t(T),L&&(L=T.shouldStopPropagation)}return E.isTriggeringEvent=!1,L}),A=Ee((x,P)=>{let R=m.current;if(a)return!1;if(l){R.isTriggeringEvent=!0;let E=new cn("pressup",P,x);return l(E),R.isTriggeringEvent=!1,E.shouldStopPropagation}return!0}),k=Ee(x=>{let P=m.current;if(P.isPressed&&P.target){P.didFirePressStart&&P.pointerType!=null&&w(ct(P.target,x),P.pointerType,!1),P.isPressed=!1,P.isOverTarget=!1,P.activePointerId=null,P.pointerType=null,$(),f||Mo(P.target);for(let R of P.disposables)R();P.disposables=[]}}),K=Ee(x=>{c&&k(x)}),O=Ee(x=>{i?.(x)}),z=Ee((x,P)=>{if(i){let R=new MouseEvent("click",x);oi(R,P),i(Gr(R))}}),N=d.useMemo(()=>{let x=m.current,P={onKeyDown(E){if(tr(E.nativeEvent,E.currentTarget)&&Ie(E.currentTarget,ye(E.nativeEvent))){var L;Ro(ye(E.nativeEvent),E.key)&&E.preventDefault();let T=!0;if(!x.isPressed&&!E.repeat){x.target=E.currentTarget,x.isPressed=!0,x.pointerType="keyboard",T=C(E,"keyboard");let y=E.currentTarget,M=S=>{tr(S,y)&&!S.repeat&&Ie(y,ye(S))&&x.target&&A(ct(x.target,S),"keyboard")};g(ae(E.currentTarget),"keyup",at(M,R),!0)}T&&E.stopPropagation(),E.metaKey&&st()&&((L=x.metaKeyEvents)===null||L===void 0||L.set(E.key,E.nativeEvent))}else E.key==="Meta"&&(x.metaKeyEvents=new Map)},onClick(E){if(!(E&&!Ie(E.currentTarget,ye(E.nativeEvent)))&&E&&E.button===0&&!x.isTriggeringEvent&&!bt.isOpening){let L=!0;if(a&&E.preventDefault(),!x.ignoreEmulatedMouseEvents&&!x.isPressed&&(x.pointerType==="virtual"||Ll(E.nativeEvent))){let T=C(E,"virtual"),y=A(E,"virtual"),M=w(E,"virtual");O(E),L=T&&y&&M}else if(x.isPressed&&x.pointerType!=="keyboard"){let T=x.pointerType||E.nativeEvent.pointerType||"virtual",y=A(ct(E.currentTarget,E),T),M=w(ct(E.currentTarget,E),T,!0);L=y&&M,x.isOverTarget=!1,O(E),k(E)}x.ignoreEmulatedMouseEvents=!1,L&&E.stopPropagation()}}},R=E=>{var L;if(x.isPressed&&x.target&&tr(E,x.target)){var T;Ro(ye(E),E.key)&&E.preventDefault();let M=ye(E),S=Ie(x.target,ye(E));w(ct(x.target,E),"keyboard",S),S&&z(E,x.target),$(),E.key!=="Enter"&&Xr(x.target)&&Ie(x.target,M)&&!E[Lo]&&(E[Lo]=!0,bt(x.target,E,!1)),x.isPressed=!1,(T=x.metaKeyEvents)===null||T===void 0||T.delete(E.key)}else if(E.key==="Meta"&&(!((L=x.metaKeyEvents)===null||L===void 0)&&L.size)){var y;let M=x.metaKeyEvents;x.metaKeyEvents=void 0;for(let S of M.values())(y=x.target)===null||y===void 0||y.dispatchEvent(new KeyboardEvent("keyup",S))}};if(typeof PointerEvent<"u"){P.onPointerDown=T=>{if(T.button!==0||!Ie(T.currentTarget,ye(T.nativeEvent)))return;if(xc(T.nativeEvent)){x.pointerType="virtual";return}x.pointerType=T.pointerType;let y=!0;if(!x.isPressed){x.isPressed=!0,x.isOverTarget=!0,x.activePointerId=T.pointerId,x.target=T.currentTarget,f||Vd(x.target),y=C(T,x.pointerType);let M=ye(T.nativeEvent);"releasePointerCapture"in M&&M.releasePointerCapture(T.pointerId),g(ae(T.currentTarget),"pointerup",E,!1),g(ae(T.currentTarget),"pointercancel",L,!1)}y&&T.stopPropagation()},P.onMouseDown=T=>{if(Ie(T.currentTarget,ye(T.nativeEvent))&&T.button===0){if(u){let y=_d(T.target);y&&x.disposables.push(y)}T.stopPropagation()}},P.onPointerUp=T=>{!Ie(T.currentTarget,ye(T.nativeEvent))||x.pointerType==="virtual"||T.button===0&&!x.isPressed&&A(T,x.pointerType||T.pointerType)},P.onPointerEnter=T=>{T.pointerId===x.activePointerId&&x.target&&!x.isOverTarget&&x.pointerType!=null&&(x.isOverTarget=!0,C(ct(x.target,T),x.pointerType))},P.onPointerLeave=T=>{T.pointerId===x.activePointerId&&x.target&&x.isOverTarget&&x.pointerType!=null&&(x.isOverTarget=!1,w(ct(x.target,T),x.pointerType,!1),K(T))};let E=T=>{if(T.pointerId===x.activePointerId&&x.isPressed&&T.button===0&&x.target){if(Ie(x.target,ye(T))&&x.pointerType!=null){let y=!1,M=setTimeout(()=>{x.isPressed&&x.target instanceof HTMLElement&&(y?k(T):(je(x.target),x.target.click()))},80);g(T.currentTarget,"click",()=>y=!0,!0),x.disposables.push(()=>clearTimeout(M))}else k(T);x.isOverTarget=!1}},L=T=>{k(T)};P.onDragStart=T=>{Ie(T.currentTarget,ye(T.nativeEvent))&&k(T)}}return P},[g,a,u,$,f,k,K,w,C,A,O,z]);return d.useEffect(()=>{if(!p)return;const x=ae(p.current);if(!x||!x.head||x.getElementById(Fo))return;const P=x.createElement("style");P.id=Fo,P.textContent=`
@layer {
  [${Io}] {
    touch-action: pan-x pan-y pinch-zoom;
  }
}
    `.trim(),x.head.prepend(P)},[p]),d.useEffect(()=>{let x=m.current;return()=>{var P;f||Mo((P=x.target)!==null&&P!==void 0?P:void 0);for(let R of x.disposables)R();x.disposables=[]}},[f]),{isPressed:s||v,pressProps:Se(h,N,{[Io]:!0})}}function Xr(e){return e.tagName==="A"&&e.hasAttribute("href")}function tr(e,t){const{key:n,code:r}=e,o=t,l=o.getAttribute("role");return(n==="Enter"||n===" "||n==="Spacebar"||r==="Space")&&!(o instanceof Be(o).HTMLInputElement&&!ai(o,n)||o instanceof Be(o).HTMLTextAreaElement||o.isContentEditable)&&!((l==="link"||!l&&Xr(o))&&n!=="Enter")}function ct(e,t){let n=t.clientX,r=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:n,clientY:r}}function Gd(e){return e instanceof HTMLInputElement?!1:e instanceof HTMLButtonElement?e.type!=="submit"&&e.type!=="reset":!Xr(e)}function Ro(e,t){return e instanceof HTMLInputElement?!ai(e,t):Gd(e)}const Yd=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ai(e,t){return e.type==="checkbox"||e.type==="radio"?t===" ":Yd.has(e.type)}let $t=null,yr=new Set,jt=new Map,gt=!1,$r=!1;const Xd={Tab:!0,Escape:!0};function _n(e,t){for(let n of yr)n(e,t)}function qd(e){return!(e.metaKey||!st()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Cn(e){gt=!0,qd(e)&&($t="keyboard",_n("keyboard",e))}function Lt(e){$t="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(gt=!0,_n("pointer",e))}function si(e){Ll(e)&&(gt=!0,$t="virtual")}function ui(e){e.target===window||e.target===document||wn||!e.isTrusted||(!gt&&!$r&&($t="virtual",_n("virtual",e)),gt=!1,$r=!1)}function ci(){wn||(gt=!1,$r=!0)}function xr(e){if(typeof window>"u"||typeof document>"u"||jt.get(Be(e)))return;const t=Be(e),n=ae(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){gt=!0,r.apply(this,arguments)},n.addEventListener("keydown",Cn,!0),n.addEventListener("keyup",Cn,!0),n.addEventListener("click",si,!0),t.addEventListener("focus",ui,!0),t.addEventListener("blur",ci,!1),typeof PointerEvent<"u"&&(n.addEventListener("pointerdown",Lt,!0),n.addEventListener("pointermove",Lt,!0),n.addEventListener("pointerup",Lt,!0)),t.addEventListener("beforeunload",()=>{di(e)},{once:!0}),jt.set(t,{focus:r})}const di=(e,t)=>{const n=Be(e),r=ae(e);t&&r.removeEventListener("DOMContentLoaded",t),jt.has(n)&&(n.HTMLElement.prototype.focus=jt.get(n).focus,r.removeEventListener("keydown",Cn,!0),r.removeEventListener("keyup",Cn,!0),r.removeEventListener("click",si,!0),n.removeEventListener("focus",ui,!0),n.removeEventListener("blur",ci,!1),typeof PointerEvent<"u"&&(r.removeEventListener("pointerdown",Lt,!0),r.removeEventListener("pointermove",Lt,!0),r.removeEventListener("pointerup",Lt,!0)),jt.delete(n))};function Zd(e){const t=ae(e);let n;return t.readyState!=="loading"?xr(e):(n=()=>{xr(e)},t.addEventListener("DOMContentLoaded",n)),()=>di(e,n)}typeof document<"u"&&Zd();function En(){return $t!=="pointer"}function Sn(){return $t}function Jd(e){$t=e,_n(e,null)}const Qd=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ef(e,t,n){let r=ae(n?.target);const o=typeof window<"u"?Be(n?.target).HTMLInputElement:HTMLInputElement,l=typeof window<"u"?Be(n?.target).HTMLTextAreaElement:HTMLTextAreaElement,i=typeof window<"u"?Be(n?.target).HTMLElement:HTMLElement,a=typeof window<"u"?Be(n?.target).KeyboardEvent:KeyboardEvent;return e=e||r.activeElement instanceof o&&!Qd.has(r.activeElement.type)||r.activeElement instanceof l||r.activeElement instanceof i&&r.activeElement.isContentEditable,!(e&&t==="keyboard"&&n instanceof a&&!Xd[n.key])}function tf(e,t,n){xr(),d.useEffect(()=>{let r=(o,l)=>{ef(!!n?.isTextInput,o,l)&&e(En())};return yr.add(r),()=>{yr.delete(r)}},t)}function mt(e){const t=ae(e),n=ke(t);if(Sn()==="virtual"){let r=n;Al(()=>{ke(t)===r&&e.isConnected&&je(e)})}else je(e)}function fi(e){let{isDisabled:t,onFocus:n,onBlur:r,onFocusChange:o}=e;const l=d.useCallback(s=>{if(s.target===s.currentTarget)return r&&r(s),o&&o(!1),!0},[r,o]),i=li(l),a=d.useCallback(s=>{const u=ae(s.target),c=u?ke(u):ke();s.target===s.currentTarget&&c===ye(s.nativeEvent)&&(n&&n(s),o&&o(!0),i(s))},[o,n,i]);return{focusProps:{onFocus:!t&&(n||o||r)?a:void 0,onBlur:!t&&(r||o)?l:void 0}}}function Bo(e){if(!e)return;let t=!0;return n=>{let r={...n,preventDefault(){n.preventDefault()},isDefaultPrevented(){return n.isDefaultPrevented()},stopPropagation(){t=!0},continuePropagation(){t=!1},isPropagationStopped(){return t}};e(r),t&&n.stopPropagation()}}function nf(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:Bo(e.onKeyDown),onKeyUp:Bo(e.onKeyUp)}}}let rf=te.createContext(null);function of(e){let t=d.useContext(rf)||{};kl(t,e);let{ref:n,...r}=t;return r}function pi(e,t){let{focusProps:n}=fi(e),{keyboardProps:r}=nf(e),o=Se(n,r),l=of(t),i=e.isDisabled?{}:l,a=d.useRef(e.autoFocus);d.useEffect(()=>{a.current&&t.current&&mt(t.current),a.current=!1},[t]);let s=e.excludeFromTabOrder?-1:0;return e.isDisabled&&(s=void 0),{focusableProps:Se({...o,tabIndex:s},i)}}function lf({children:e}){let t=d.useMemo(()=>({register:()=>{}}),[]);return te.createElement(Yr.Provider,{value:t},e)}function Zt(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:r,onFocusWithinChange:o}=e,l=d.useRef({isFocusWithin:!1}),{addGlobalListener:i,removeAllGlobalListeners:a}=Kn(),s=d.useCallback(f=>{f.currentTarget.contains(f.target)&&l.current.isFocusWithin&&!f.currentTarget.contains(f.relatedTarget)&&(l.current.isFocusWithin=!1,a(),n&&n(f),o&&o(!1))},[n,o,l,a]),u=li(s),c=d.useCallback(f=>{if(!f.currentTarget.contains(f.target))return;const p=ae(f.target),h=ke(p);if(!l.current.isFocusWithin&&h===ye(f.nativeEvent)){r&&r(f),o&&o(!0),l.current.isFocusWithin=!0,u(f);let v=f.currentTarget;i(p,"focus",b=>{if(l.current.isFocusWithin&&!Ie(v,b.target)){let m=new p.defaultView.FocusEvent("blur",{relatedTarget:b.target});oi(m,v);let g=Gr(m);s(g)}},{capture:!0})}},[r,o,u,i,s]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:c,onBlur:s}}}let wr=!1,dn=0;function af(){wr=!0,setTimeout(()=>{wr=!1},50)}function Ko(e){e.pointerType==="touch"&&af()}function sf(){if(!(typeof document>"u"))return dn===0&&typeof PointerEvent<"u"&&document.addEventListener("pointerup",Ko),dn++,()=>{dn--,!(dn>0)&&typeof PointerEvent<"u"&&document.removeEventListener("pointerup",Ko)}}function Ut(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:r,isDisabled:o}=e,[l,i]=d.useState(!1),a=d.useRef({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;d.useEffect(sf,[]);let{addGlobalListener:s,removeAllGlobalListeners:u}=Kn(),{hoverProps:c,triggerHoverEnd:f}=d.useMemo(()=>{let p=(b,m)=>{if(a.pointerType=m,o||m==="touch"||a.isHovered||!b.currentTarget.contains(b.target))return;a.isHovered=!0;let g=b.currentTarget;a.target=g,s(ae(b.target),"pointerover",$=>{a.isHovered&&a.target&&!Ie(a.target,$.target)&&h($,$.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:g,pointerType:m}),n&&n(!0),i(!0)},h=(b,m)=>{let g=a.target;a.pointerType="",a.target=null,!(m==="touch"||!a.isHovered||!g)&&(a.isHovered=!1,u(),r&&r({type:"hoverend",target:g,pointerType:m}),n&&n(!1),i(!1))},v={};return typeof PointerEvent<"u"&&(v.onPointerEnter=b=>{wr&&b.pointerType==="mouse"||p(b,b.pointerType)},v.onPointerLeave=b=>{!o&&b.currentTarget.contains(b.target)&&h(b,b.pointerType)}),{hoverProps:v,triggerHoverEnd:h}},[t,n,r,o,a,s,u]);return d.useEffect(()=>{o&&f({currentTarget:a.target},a.pointerType)},[o]),{hoverProps:c,isHovered:l}}function uf(e){let{ref:t,onInteractOutside:n,isDisabled:r,onInteractOutsideStart:o}=e,l=d.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),i=Ee(s=>{n&&No(s,t)&&(o&&o(s),l.current.isPointerDown=!0)}),a=Ee(s=>{n&&n(s)});d.useEffect(()=>{let s=l.current;if(r)return;const u=t.current,c=ae(u);if(typeof PointerEvent<"u"){let f=p=>{s.isPointerDown&&No(p,t)&&a(p),s.isPointerDown=!1};return c.addEventListener("pointerdown",i,!0),c.addEventListener("click",f,!0),()=>{c.removeEventListener("pointerdown",i,!0),c.removeEventListener("click",f,!0)}}},[t,r,i,a])}function No(e,t){if(e.button>0)return!1;if(e.target){const n=e.target.ownerDocument;if(!n||!n.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current?!e.composedPath().includes(t.current):!1}const cf=500;function hi(e){let{isDisabled:t,onLongPressStart:n,onLongPressEnd:r,onLongPress:o,threshold:l=cf,accessibilityDescription:i}=e;const a=d.useRef(void 0);let{addGlobalListener:s,removeGlobalListener:u}=Kn(),{pressProps:c}=qt({isDisabled:t,onPressStart(p){if(p.continuePropagation(),(p.pointerType==="mouse"||p.pointerType==="touch")&&(n&&n({...p,type:"longpressstart"}),a.current=setTimeout(()=>{p.target.dispatchEvent(new PointerEvent("pointercancel",{bubbles:!0})),ae(p.target).activeElement!==p.target&&je(p.target),o&&o({...p,type:"longpress"}),a.current=void 0},l),p.pointerType==="touch")){let h=v=>{v.preventDefault()};s(p.target,"contextmenu",h,{once:!0}),s(window,"pointerup",()=>{setTimeout(()=>{u(p.target,"contextmenu",h)},30)},{once:!0})}},onPressEnd(p){a.current&&clearTimeout(a.current),r&&(p.pointerType==="mouse"||p.pointerType==="touch")&&r({...p,type:"longpressend"})}}),f=$c(o&&!t?i:void 0);return{longPressProps:Se(c,f)}}const Oo=te.createContext(null),Cr="react-aria-focus-scope-restore";let he=null;function df(e){let{children:t,contain:n,restoreFocus:r,autoFocus:o}=e,l=d.useRef(null),i=d.useRef(null),a=d.useRef([]),{parentNode:s}=d.useContext(Oo)||{},u=d.useMemo(()=>new Sr({scopeRef:a}),[a]);be(()=>{let p=s||Ce.root;if(Ce.getTreeNode(p.scopeRef)&&he&&!Pn(he,p.scopeRef)){let h=Ce.getTreeNode(he);h&&(p=h)}p.addChild(u),Ce.addNode(u)},[u,s]),be(()=>{let p=Ce.getTreeNode(a);p&&(p.contain=!!n)},[n]),be(()=>{var p;let h=(p=l.current)===null||p===void 0?void 0:p.nextSibling,v=[],b=m=>m.stopPropagation();for(;h&&h!==i.current;)v.push(h),h.addEventListener(Cr,b),h=h.nextSibling;return a.current=v,()=>{for(let m of v)m.removeEventListener(Cr,b)}},[t]),gf(a,r,n),hf(a,n),mf(a,r,n),vf(a,o),d.useEffect(()=>{const p=ke(ae(a.current?a.current[0]:void 0));let h=null;if(Ne(p,a.current)){for(let v of Ce.traverse())v.scopeRef&&Ne(p,v.scopeRef.current)&&(h=v);h===Ce.getTreeNode(a)&&(he=h.scopeRef)}},[a]),be(()=>()=>{var p,h,v;let b=(v=(h=Ce.getTreeNode(a))===null||h===void 0||(p=h.parent)===null||p===void 0?void 0:p.scopeRef)!==null&&v!==void 0?v:null;(a===he||Pn(a,he))&&(!b||Ce.getTreeNode(b))&&(he=b),Ce.removeTreeNode(a)},[a]);let c=d.useMemo(()=>ff(a),[]),f=d.useMemo(()=>({focusManager:c,parentNode:u}),[u,c]);return te.createElement(Oo.Provider,{value:f},te.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:l}),t,te.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:i}))}function ff(e){return{focusNext(t={}){let n=e.current,{from:r,tabbable:o,wrap:l,accept:i}=t;var a;let s=r||ke(ae((a=n[0])!==null&&a!==void 0?a:void 0)),u=n[0].previousElementSibling,c=dt(n),f=et(c,{tabbable:o,accept:i},n);f.currentNode=Ne(s,n)?s:u;let p=f.nextNode();return!p&&l&&(f.currentNode=u,p=f.nextNode()),p&&Qe(p,!0),p},focusPrevious(t={}){let n=e.current,{from:r,tabbable:o,wrap:l,accept:i}=t;var a;let s=r||ke(ae((a=n[0])!==null&&a!==void 0?a:void 0)),u=n[n.length-1].nextElementSibling,c=dt(n),f=et(c,{tabbable:o,accept:i},n);f.currentNode=Ne(s,n)?s:u;let p=f.previousNode();return!p&&l&&(f.currentNode=u,p=f.previousNode()),p&&Qe(p,!0),p},focusFirst(t={}){let n=e.current,{tabbable:r,accept:o}=t,l=dt(n),i=et(l,{tabbable:r,accept:o},n);i.currentNode=n[0].previousElementSibling;let a=i.nextNode();return a&&Qe(a,!0),a},focusLast(t={}){let n=e.current,{tabbable:r,accept:o}=t,l=dt(n),i=et(l,{tabbable:r,accept:o},n);i.currentNode=n[n.length-1].nextElementSibling;let a=i.previousNode();return a&&Qe(a,!0),a}}}function dt(e){return e[0].parentElement}function Vt(e){let t=Ce.getTreeNode(he);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function pf(e){if(e.checked)return!0;let t=[];if(!e.form)t=[...ae(e).querySelectorAll(`input[type="radio"][name="${CSS.escape(e.name)}"]`)].filter(l=>!l.form);else{var n,r;let l=(r=e.form)===null||r===void 0||(n=r.elements)===null||n===void 0?void 0:n.namedItem(e.name);t=[...l??[]]}return t?!t.some(l=>l.checked):!1}function hf(e,t){let n=d.useRef(void 0),r=d.useRef(void 0);be(()=>{let o=e.current;if(!t){r.current&&(cancelAnimationFrame(r.current),r.current=void 0);return}const l=ae(o?o[0]:void 0);let i=u=>{if(u.key!=="Tab"||u.altKey||u.ctrlKey||u.metaKey||!Vt(e)||u.isComposing)return;let c=ke(l),f=e.current;if(!f||!Ne(c,f))return;let p=dt(f),h=et(p,{tabbable:!0},f);if(!c)return;h.currentNode=c;let v=u.shiftKey?h.previousNode():h.nextNode();v||(h.currentNode=u.shiftKey?f[f.length-1].nextElementSibling:f[0].previousElementSibling,v=u.shiftKey?h.previousNode():h.nextNode()),u.preventDefault(),v&&Qe(v,!0)},a=u=>{(!he||Pn(he,e))&&Ne(ye(u),e.current)?(he=e,n.current=ye(u)):Vt(e)&&!it(ye(u),e)?n.current?n.current.focus():he&&he.current&&Er(he.current):Vt(e)&&(n.current=ye(u))},s=u=>{r.current&&cancelAnimationFrame(r.current),r.current=requestAnimationFrame(()=>{let c=Sn(),f=(c==="virtual"||c===null)&&Br()&&Tl(),p=ke(l);if(!f&&p&&Vt(e)&&!it(p,e)){he=e;let v=ye(u);if(v&&v.isConnected){var h;n.current=v,(h=n.current)===null||h===void 0||h.focus()}else he.current&&Er(he.current)}})};return l.addEventListener("keydown",i,!1),l.addEventListener("focusin",a,!1),o?.forEach(u=>u.addEventListener("focusin",a,!1)),o?.forEach(u=>u.addEventListener("focusout",s,!1)),()=>{l.removeEventListener("keydown",i,!1),l.removeEventListener("focusin",a,!1),o?.forEach(u=>u.removeEventListener("focusin",a,!1)),o?.forEach(u=>u.removeEventListener("focusout",s,!1))}},[e,t]),be(()=>()=>{r.current&&cancelAnimationFrame(r.current)},[r])}function bi(e){return it(e)}function Ne(e,t){return!e||!t?!1:t.some(n=>n.contains(e))}function it(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:n}of Ce.traverse(Ce.getTreeNode(t)))if(n&&Ne(e,n.current))return!0;return!1}function bf(e){return it(e,he)}function Pn(e,t){var n;let r=(n=Ce.getTreeNode(t))===null||n===void 0?void 0:n.parent;for(;r;){if(r.scopeRef===e)return!0;r=r.parent}return!1}function Qe(e,t=!1){if(e!=null&&!t)try{mt(e)}catch{}else if(e!=null)try{e.focus()}catch{}}function vi(e,t=!0){let n=e[0].previousElementSibling,r=dt(e),o=et(r,{tabbable:t},e);o.currentNode=n;let l=o.nextNode();return t&&!l&&(r=dt(e),o=et(r,{tabbable:!1},e),o.currentNode=n,l=o.nextNode()),l}function Er(e,t=!0){Qe(vi(e,t))}function vf(e,t){const n=te.useRef(t);d.useEffect(()=>{if(n.current){he=e;const r=ae(e.current?e.current[0]:void 0);!Ne(ke(r),he.current)&&e.current&&Er(e.current)}n.current=!1},[e])}function gf(e,t,n){be(()=>{if(t||n)return;let r=e.current;const o=ae(r?r[0]:void 0);let l=i=>{let a=ye(i);Ne(a,e.current)?he=e:bi(a)||(he=null)};return o.addEventListener("focusin",l,!1),r?.forEach(i=>i.addEventListener("focusin",l,!1)),()=>{o.removeEventListener("focusin",l,!1),r?.forEach(i=>i.removeEventListener("focusin",l,!1))}},[e,t,n])}function zo(e){let t=Ce.getTreeNode(he);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return t?.scopeRef===e}function mf(e,t,n){const r=d.useRef(typeof document<"u"?ke(ae(e.current?e.current[0]:void 0)):null);be(()=>{let o=e.current;const l=ae(o?o[0]:void 0);if(!t||n)return;let i=()=>{(!he||Pn(he,e))&&Ne(ke(l),e.current)&&(he=e)};return l.addEventListener("focusin",i,!1),o?.forEach(a=>a.addEventListener("focusin",i,!1)),()=>{l.removeEventListener("focusin",i,!1),o?.forEach(a=>a.removeEventListener("focusin",i,!1))}},[e,n]),be(()=>{const o=ae(e.current?e.current[0]:void 0);if(!t)return;let l=i=>{if(i.key!=="Tab"||i.altKey||i.ctrlKey||i.metaKey||!Vt(e)||i.isComposing)return;let a=o.activeElement;if(!it(a,e)||!zo(e))return;let s=Ce.getTreeNode(e);if(!s)return;let u=s.nodeToRestore,c=et(o.body,{tabbable:!0});c.currentNode=a;let f=i.shiftKey?c.previousNode():c.nextNode();if((!u||!u.isConnected||u===o.body)&&(u=void 0,s.nodeToRestore=void 0),(!f||!it(f,e))&&u){c.currentNode=u;do f=i.shiftKey?c.previousNode():c.nextNode();while(it(f,e));i.preventDefault(),i.stopPropagation(),f?Qe(f,!0):bi(u)?Qe(u,!0):a.blur()}};return n||o.addEventListener("keydown",l,!0),()=>{n||o.removeEventListener("keydown",l,!0)}},[e,t,n]),be(()=>{const o=ae(e.current?e.current[0]:void 0);if(!t)return;let l=Ce.getTreeNode(e);if(l){var i;return l.nodeToRestore=(i=r.current)!==null&&i!==void 0?i:void 0,()=>{let a=Ce.getTreeNode(e);if(!a)return;let s=a.nodeToRestore,u=ke(o);if(t&&s&&(u&&it(u,e)||u===o.body&&zo(e))){let c=Ce.clone();requestAnimationFrame(()=>{if(o.activeElement===o.body){let f=c.getTreeNode(e);for(;f;){if(f.nodeToRestore&&f.nodeToRestore.isConnected){_o(f.nodeToRestore);return}f=f.parent}for(f=c.getTreeNode(e);f;){if(f.scopeRef&&f.scopeRef.current&&Ce.getTreeNode(f.scopeRef)){let p=vi(f.scopeRef.current,!0);_o(p);return}f=f.parent}}})}}}},[e,t])}function _o(e){e.dispatchEvent(new CustomEvent(Cr,{bubbles:!0,cancelable:!0}))&&Qe(e)}function et(e,t,n){let r=t?.tabbable?Mc:Fl,o=e?.nodeType===Node.ELEMENT_NODE?e:null,l=ae(o),i=ec(l,e||l,NodeFilter.SHOW_ELEMENT,{acceptNode(a){var s;return!(t==null||(s=t.from)===null||s===void 0)&&s.contains(a)||t?.tabbable&&a.tagName==="INPUT"&&a.getAttribute("type")==="radio"&&(!pf(a)||i.currentNode.tagName==="INPUT"&&i.currentNode.type==="radio"&&i.currentNode.name===a.name)?NodeFilter.FILTER_REJECT:r(a)&&(!n||Ne(a,n))&&(!t?.accept||t.accept(a))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t?.from&&(i.currentNode=t.from),i}class qr{get size(){return this.fastMap.size}getTreeNode(t){return this.fastMap.get(t)}addTreeNode(t,n,r){let o=this.fastMap.get(n??null);if(!o)return;let l=new Sr({scopeRef:t});o.addChild(l),l.parent=o,this.fastMap.set(t,l),r&&(l.nodeToRestore=r)}addNode(t){this.fastMap.set(t.scopeRef,t)}removeTreeNode(t){if(t===null)return;let n=this.fastMap.get(t);if(!n)return;let r=n.parent;for(let l of this.traverse())l!==n&&n.nodeToRestore&&l.nodeToRestore&&n.scopeRef&&n.scopeRef.current&&Ne(l.nodeToRestore,n.scopeRef.current)&&(l.nodeToRestore=n.nodeToRestore);let o=n.children;r&&(r.removeChild(n),o.size>0&&o.forEach(l=>r&&r.addChild(l))),this.fastMap.delete(n.scopeRef)}*traverse(t=this.root){if(t.scopeRef!=null&&(yield t),t.children.size>0)for(let n of t.children)yield*this.traverse(n)}clone(){var t;let n=new qr;var r;for(let o of this.traverse())n.addTreeNode(o.scopeRef,(r=(t=o.parent)===null||t===void 0?void 0:t.scopeRef)!==null&&r!==void 0?r:null,o.nodeToRestore);return n}constructor(){this.fastMap=new Map,this.root=new Sr({scopeRef:null}),this.fastMap.set(null,this.root)}}class Sr{addChild(t){this.children.add(t),t.parent=this}removeChild(t){this.children.delete(t),t.parent=void 0}constructor(t){this.children=new Set,this.contain=!1,this.scopeRef=t.scopeRef}}let Ce=new qr;function Gt(e={}){let{autoFocus:t=!1,isTextInput:n,within:r}=e,o=d.useRef({isFocused:!1,isFocusVisible:t||En()}),[l,i]=d.useState(!1),[a,s]=d.useState(()=>o.current.isFocused&&o.current.isFocusVisible),u=d.useCallback(()=>s(o.current.isFocused&&o.current.isFocusVisible),[]),c=d.useCallback(h=>{o.current.isFocused=h,i(h),u()},[u]);tf(h=>{o.current.isFocusVisible=h,u()},[],{isTextInput:n});let{focusProps:f}=fi({isDisabled:r,onFocusChange:c}),{focusWithinProps:p}=Zt({isDisabled:!r,onFocusWithinChange:c});return{isFocused:l,isFocusVisible:a,focusProps:r?p:f}}function gi(e){let t=$f(ae(e));t!==e&&(t&&yf(t,e),e&&Zr(e,t))}function yf(e,t){e.dispatchEvent(new FocusEvent("blur",{relatedTarget:t})),e.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:t}))}function Zr(e,t){e.dispatchEvent(new FocusEvent("focus",{relatedTarget:t})),e.dispatchEvent(new FocusEvent("focusin",{bubbles:!0,relatedTarget:t}))}function $f(e){let t=ke(e),n=t?.getAttribute("aria-activedescendant");return n&&e.getElementById(n)||t}function mi(e,t,n){let{type:r}=e,{isOpen:o}=t;d.useEffect(()=>{n&&n.current&&ri.set(n.current,t.close)});let l;r==="menu"?l=!0:r==="listbox"&&(l="listbox");let i=qe();return{triggerProps:{"aria-haspopup":l,"aria-expanded":o,"aria-controls":o?i:void 0,onPress:t.toggle},overlayProps:{id:i}}}const nr=typeof document<"u"&&window.visualViewport,xf=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);let fn=0,rr;function wf(e={}){let{isDisabled:t}=e;be(()=>{if(!t)return fn++,fn===1&&(Rn()?rr=Ef():rr=Cf()),()=>{fn--,fn===0&&rr()}},[t])}function Cf(){let e=window.innerWidth-document.documentElement.clientWidth;return at(e>0&&("scrollbarGutter"in document.documentElement.style?ft(document.documentElement,"scrollbarGutter","stable"):ft(document.documentElement,"paddingRight",`${e}px`)),ft(document.documentElement,"overflow","hidden"))}function Ef(){let e,t,n=u=>{e=Ml(u.target,!0),!(e===document.documentElement&&e===document.body)&&e instanceof HTMLElement&&window.getComputedStyle(e).overscrollBehavior==="auto"&&(t=ft(e,"overscrollBehavior","contain"))},r=u=>{if(!e||e===document.documentElement||e===document.body){u.preventDefault();return}e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&u.preventDefault()},o=()=>{t&&t()},l=u=>{let c=u.target;Sf(c)&&(a(),c.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{c.style.transform="",nr&&(nr.height<window.innerHeight?requestAnimationFrame(()=>{Vo(c)}):nr.addEventListener("resize",()=>Vo(c),{once:!0}))}))},i=null,a=()=>{if(i)return;let u=()=>{window.scrollTo(0,0)},c=window.pageXOffset,f=window.pageYOffset;i=at(Bt(window,"scroll",u),ft(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),ft(document.documentElement,"overflow","hidden"),ft(document.body,"marginTop",`-${f}px`),()=>{window.scrollTo(c,f)}),window.scrollTo(0,0)},s=at(Bt(document,"touchstart",n,{passive:!1,capture:!0}),Bt(document,"touchmove",r,{passive:!1,capture:!0}),Bt(document,"touchend",o,{passive:!1,capture:!0}),Bt(document,"focus",l,!0));return()=>{t?.(),i?.(),s()}}function ft(e,t,n){let r=e.style[t];return e.style[t]=n,()=>{e.style[t]=r}}function Bt(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function Vo(e){let t=document.scrollingElement||document.documentElement,n=e;for(;n&&n!==t;){let r=Ml(n);if(r!==document.documentElement&&r!==document.body&&r!==n){let o=r.getBoundingClientRect().top,l=n.getBoundingClientRect().top;l>o+n.clientHeight&&(r.scrollTop+=l-o)}n=r.parentElement}}function Sf(e){return e instanceof HTMLInputElement&&!xf.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}const Pf=d.createContext({});function Tf(){var e;return(e=d.useContext(Pf))!==null&&e!==void 0?e:{}}var yi={};yi={dismiss:"تجاهل"};var $i={};$i={dismiss:"Отхвърляне"};var xi={};xi={dismiss:"Odstranit"};var wi={};wi={dismiss:"Luk"};var Ci={};Ci={dismiss:"Schließen"};var Ei={};Ei={dismiss:"Απόρριψη"};var Si={};Si={dismiss:"Dismiss"};var Pi={};Pi={dismiss:"Descartar"};var Ti={};Ti={dismiss:"Lõpeta"};var Ai={};Ai={dismiss:"Hylkää"};var ki={};ki={dismiss:"Rejeter"};var Mi={};Mi={dismiss:"התעלם"};var Di={};Di={dismiss:"Odbaci"};var Li={};Li={dismiss:"Elutasítás"};var Fi={};Fi={dismiss:"Ignora"};var Ii={};Ii={dismiss:"閉じる"};var Ri={};Ri={dismiss:"무시"};var Bi={};Bi={dismiss:"Atmesti"};var Ki={};Ki={dismiss:"Nerādīt"};var Ni={};Ni={dismiss:"Lukk"};var Oi={};Oi={dismiss:"Negeren"};var zi={};zi={dismiss:"Zignoruj"};var _i={};_i={dismiss:"Descartar"};var Vi={};Vi={dismiss:"Dispensar"};var ji={};ji={dismiss:"Revocare"};var Wi={};Wi={dismiss:"Пропустить"};var Hi={};Hi={dismiss:"Zrušiť"};var Ui={};Ui={dismiss:"Opusti"};var Gi={};Gi={dismiss:"Odbaci"};var Yi={};Yi={dismiss:"Avvisa"};var Xi={};Xi={dismiss:"Kapat"};var qi={};qi={dismiss:"Скасувати"};var Zi={};Zi={dismiss:"取消"};var Ji={};Ji={dismiss:"關閉"};var Qi={};Qi={"ar-AE":yi,"bg-BG":$i,"cs-CZ":xi,"da-DK":wi,"de-DE":Ci,"el-GR":Ei,"en-US":Si,"es-ES":Pi,"et-EE":Ti,"fi-FI":Ai,"fr-FR":ki,"he-IL":Mi,"hr-HR":Di,"hu-HU":Li,"it-IT":Fi,"ja-JP":Ii,"ko-KR":Ri,"lt-LT":Bi,"lv-LV":Ki,"nb-NO":Ni,"nl-NL":Oi,"pl-PL":zi,"pt-BR":_i,"pt-PT":Vi,"ro-RO":ji,"ru-RU":Wi,"sk-SK":Hi,"sl-SI":Ui,"sr-SP":Gi,"sv-SE":Yi,"tr-TR":Xi,"uk-UA":qi,"zh-CN":Zi,"zh-TW":Ji};const jo={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function Af(e={}){let{style:t,isFocusable:n}=e,[r,o]=d.useState(!1),{focusWithinProps:l}=Zt({isDisabled:!n,onFocusWithinChange:a=>o(a)}),i=d.useMemo(()=>r?t:t?{...jo,...t}:jo,[r]);return{visuallyHiddenProps:{...l,style:i}}}function kf(e){let{children:t,elementType:n="div",isFocusable:r,style:o,...l}=e,{visuallyHiddenProps:i}=Af(e);return te.createElement(n,Se(l,i),t)}function Mf(e){return e&&e.__esModule?e.default:e}function Wo(e){let{onDismiss:t,...n}=e,r=Fr(Mf(Qi),"@react-aria/overlays"),o=mn(n,r.format("dismiss")),l=()=>{t&&t()};return te.createElement(kf,null,te.createElement("button",{...o,tabIndex:-1,onClick:l,style:{width:1,height:1}}))}const Df=typeof HTMLElement<"u"&&"inert"in HTMLElement.prototype;let Kt=new WeakMap,Ke=[];function Lf(e,t){let n=Be(e?.[0]),r=t instanceof n.Element?{root:t}:t;var o;let l=(o=r?.root)!==null&&o!==void 0?o:document.body,i=r?.shouldUseInert&&Df,a=new Set(e),s=new Set,u=b=>i&&b instanceof n.HTMLElement?b.inert:b.getAttribute("aria-hidden")==="true",c=(b,m)=>{i&&b instanceof n.HTMLElement?b.inert=m:m?b.setAttribute("aria-hidden","true"):b.removeAttribute("aria-hidden")},f=b=>{for(let C of b.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))a.add(C);let m=C=>{if(s.has(C)||a.has(C)||C.parentElement&&s.has(C.parentElement)&&C.parentElement.getAttribute("role")!=="row")return NodeFilter.FILTER_REJECT;for(let w of a)if(C.contains(w))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},g=document.createTreeWalker(b,NodeFilter.SHOW_ELEMENT,{acceptNode:m}),$=m(b);if($===NodeFilter.FILTER_ACCEPT&&p(b),$!==NodeFilter.FILTER_REJECT){let C=g.nextNode();for(;C!=null;)p(C),C=g.nextNode()}},p=b=>{var m;let g=(m=Kt.get(b))!==null&&m!==void 0?m:0;u(b)&&g===0||(g===0&&c(b,!0),s.add(b),Kt.set(b,g+1))};Ke.length&&Ke[Ke.length-1].disconnect(),f(l);let h=new MutationObserver(b=>{for(let m of b)if(m.type==="childList"&&![...a,...s].some(g=>g.contains(m.target)))for(let g of m.addedNodes)(g instanceof HTMLElement||g instanceof SVGElement)&&(g.dataset.liveAnnouncer==="true"||g.dataset.reactAriaTopLayer==="true")?a.add(g):g instanceof Element&&f(g)});h.observe(l,{childList:!0,subtree:!0});let v={visibleNodes:a,hiddenNodes:s,observe(){h.observe(l,{childList:!0,subtree:!0})},disconnect(){h.disconnect()}};return Ke.push(v),()=>{h.disconnect();for(let b of s){let m=Kt.get(b);m!=null&&(m===1?(c(b,!1),Kt.delete(b)):Kt.set(b,m-1))}v===Ke[Ke.length-1]?(Ke.pop(),Ke.length&&Ke[Ke.length-1].observe()):Ke.splice(Ke.indexOf(v),1)}}const ea=te.createContext(null);function Ff(e){let t=Mn(),{portalContainer:n=t?null:document.body,isExiting:r}=e,[o,l]=d.useState(!1),i=d.useMemo(()=>({contain:o,setContain:l}),[o,l]),{getContainer:a}=Tf();if(!e.portalContainer&&a&&(n=a()),!n)return null;let s=e.children;return e.disableFocusManagement||(s=te.createElement(df,{restoreFocus:!0,contain:(e.shouldContainFocus||o)&&!r},s)),s=te.createElement(ea.Provider,{value:i},te.createElement(lf,null,s)),fu.createPortal(s,n)}function If(){let e=d.useContext(ea),t=e?.setContain;be(()=>{t?.(!0)},[t])}function db(e){const t=tt(),n=t?.labelPlacement;return d.useMemo(()=>{var r,o;const l=(o=(r=e.labelPlacement)!=null?r:n)!=null?o:"inside";return l==="inside"&&!e.label?"outside":l},[e.labelPlacement,n,e.label])}function Rf(e){const t=tt(),n=t?.labelPlacement;return d.useMemo(()=>{var r,o;const l=(o=(r=e.labelPlacement)!=null?r:n)!=null?o:"inside";return l==="inside"&&!e.label?"outside":l},[e.labelPlacement,n,e.label])}function Ze(e){return d.forwardRef(e)}var xt=(e,t,n=!0)=>{if(!t)return[e,{}];const r=t.reduce((o,l)=>l in e?{...o,[l]:e[l]}:o,{});return n?[Object.keys(e).filter(l=>!t.includes(l)).reduce((l,i)=>({...l,[i]:e[i]}),{}),r]:[e,r]},Bf={default:"bg-default text-default-foreground",primary:"bg-primary text-primary-foreground",secondary:"bg-secondary text-secondary-foreground",success:"bg-success text-success-foreground",warning:"bg-warning text-warning-foreground",danger:"bg-danger text-danger-foreground",foreground:"bg-foreground text-background"},Kf={default:"shadow-lg shadow-default/50 bg-default text-default-foreground",primary:"shadow-lg shadow-primary/40 bg-primary text-primary-foreground",secondary:"shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",success:"shadow-lg shadow-success/40 bg-success text-success-foreground",warning:"shadow-lg shadow-warning/40 bg-warning text-warning-foreground",danger:"shadow-lg shadow-danger/40 bg-danger text-danger-foreground"},Nf={default:"bg-transparent border-default text-foreground",primary:"bg-transparent border-primary text-primary",secondary:"bg-transparent border-secondary text-secondary",success:"bg-transparent border-success text-success",warning:"bg-transparent border-warning text-warning",danger:"bg-transparent border-danger text-danger"},Of={default:"bg-default/40 text-default-700",primary:"bg-primary/20 text-primary-600",secondary:"bg-secondary/20 text-secondary-600",success:"bg-success/20 text-success-700 dark:text-success",warning:"bg-warning/20 text-warning-700 dark:text-warning",danger:"bg-danger/20 text-danger-600 dark:text-danger-500"},zf={default:"border-default bg-default-100 text-default-foreground",primary:"border-default bg-default-100 text-primary",secondary:"border-default bg-default-100 text-secondary",success:"border-default bg-default-100 text-success",warning:"border-default bg-default-100 text-warning",danger:"border-default bg-default-100 text-danger"},_f={default:"bg-transparent text-default-foreground",primary:"bg-transparent text-primary",secondary:"bg-transparent text-secondary",success:"bg-transparent text-success",warning:"bg-transparent text-warning",danger:"bg-transparent text-danger"},Vf={default:"border-default text-default-foreground",primary:"border-primary text-primary",secondary:"border-secondary text-secondary",success:"border-success text-success",warning:"border-warning text-warning",danger:"border-danger text-danger"},G={solid:Bf,shadow:Kf,bordered:Nf,flat:Of,faded:zf,light:_f,ghost:Vf},jf={".spinner-bar-animation":{"animation-delay":"calc(-1.2s + (0.1s * var(--bar-index)))",transform:"rotate(calc(30deg * var(--bar-index)))translate(140%)"},".spinner-dot-animation":{"animation-delay":"calc(250ms * var(--dot-index))"},".spinner-dot-blink-animation":{"animation-delay":"calc(200ms * var(--dot-index))"}},Wf={".leading-inherit":{"line-height":"inherit"},".bg-img-inherit":{"background-image":"inherit"},".bg-clip-inherit":{"background-clip":"inherit"},".text-fill-inherit":{"-webkit-text-fill-color":"inherit"},".tap-highlight-transparent":{"-webkit-tap-highlight-color":"transparent"},".input-search-cancel-button-none":{"&::-webkit-search-cancel-button":{"-webkit-appearance":"none"}}},Hf={".scrollbar-hide":{"-ms-overflow-style":"none","scrollbar-width":"none","&::-webkit-scrollbar":{display:"none"}},".scrollbar-default":{"-ms-overflow-style":"auto","scrollbar-width":"auto","&::-webkit-scrollbar":{display:"block"}}},Uf={".text-tiny":{"font-size":"var(--heroui-font-size-tiny)","line-height":"var(--heroui-line-height-tiny)"},".text-small":{"font-size":"var(--heroui-font-size-small)","line-height":"var(--heroui-line-height-small)"},".text-medium":{"font-size":"var(--heroui-font-size-medium)","line-height":"var(--heroui-line-height-medium)"},".text-large":{"font-size":"var(--heroui-font-size-large)","line-height":"var(--heroui-line-height-large)"}},Ue="250ms",Gf={".transition-background":{"transition-property":"background","transition-timing-function":"ease","transition-duration":Ue},".transition-colors-opacity":{"transition-property":"color, background-color, border-color, text-decoration-color, fill, stroke, opacity","transition-timing-function":"ease","transition-duration":Ue},".transition-width":{"transition-property":"width","transition-timing-function":"ease","transition-duration":Ue},".transition-height":{"transition-property":"height","transition-timing-function":"ease","transition-duration":Ue},".transition-size":{"transition-property":"width, height","transition-timing-function":"ease","transition-duration":Ue},".transition-left":{"transition-property":"left","transition-timing-function":"ease","transition-duration":Ue},".transition-transform-opacity":{"transition-property":"transform, scale, opacity rotate","transition-timing-function":"ease","transition-duration":Ue},".transition-transform-background":{"transition-property":"transform, scale, background","transition-timing-function":"ease","transition-duration":Ue},".transition-transform-colors":{"transition-property":"transform, scale, color, background, background-color, border-color, text-decoration-color, fill, stroke","transition-timing-function":"ease","transition-duration":Ue},".transition-transform-colors-opacity":{"transition-property":"transform, scale, color, background, background-color, border-color, text-decoration-color, fill, stroke, opacity","transition-timing-function":"ease","transition-duration":Ue}},Yf={...Wf,...Gf,...Hf,...Uf,...jf},pn=["small","medium","large"],Ho={theme:{spacing:["divider"],radius:pn},classGroups:{shadow:[{shadow:pn}],opacity:[{opacity:["disabled"]}],"font-size":[{text:["tiny",...pn]}],"border-w":[{border:pn}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"],transition:Object.keys(Yf).filter(e=>e.includes(".transition")).map(e=>e.replace(".",""))}},Uo=e=>e===!1?"false":e===!0?"true":e===0?"0":e,Le=e=>{if(!e||typeof e!="object")return!0;for(let t in e)return!1;return!0},Xf=(e,t)=>{if(e===t)return!0;if(!e||!t)return!1;let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let o=0;o<n.length;o++){let l=n[o];if(!r.includes(l)||e[l]!==t[l])return!1}return!0};function ta(e,t){for(let n=0;n<e.length;n++){let r=e[n];Array.isArray(r)?ta(r,t):t.push(r)}}var na=(...e)=>{let t=[];ta(e,t);let n=[];for(let r=0;r<t.length;r++)t[r]&&n.push(t[r]);return n},ra=(e,t)=>{let n={};for(let r in e){let o=e[r];if(r in t){let l=t[r];Array.isArray(o)||Array.isArray(l)?n[r]=na(l,o):typeof o=="object"&&typeof l=="object"&&o&&l?n[r]=ra(o,l):n[r]=l+" "+o}else n[r]=o}for(let r in t)r in e||(n[r]=t[r]);return n},qf=/\s+/g,Go=e=>!e||typeof e!="string"?e:e.replace(qf," ").trim(),Zf=e=>Le(e)?cu:du({...e,extend:{theme:e.theme,classGroups:e.classGroups,conflictingClassGroupModifiers:e.conflictingClassGroupModifiers,conflictingClassGroups:e.conflictingClassGroups,...e.extend}}),Jf={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},Tn=(...e)=>{let t=[];oa(e,t);let n="";for(let r=0;r<t.length;r++)t[r]&&(n&&(n+=" "),n+=t[r]);return n||void 0};function oa(e,t){for(let n=0;n<e.length;n++){let r=e[n];Array.isArray(r)?oa(r,t):r&&t.push(r)}}var or=null,Pr={},Tr=!1,hn=(...e)=>t=>{let n=Tn(e);return!n||!t.twMerge?n:((!or||Tr)&&(Tr=!1,or=Zf(Pr)),or(n)||void 0)},Yo=(e,t)=>{for(let n in t)n in e?e[n]=Tn(e[n],t[n]):e[n]=t[n];return e},Qf=(e,t)=>{let{extend:n=null,slots:r={},variants:o={},compoundVariants:l=[],compoundSlots:i=[],defaultVariants:a={}}=e,s={...Jf,...t},u=n?.base?Tn(n.base,e?.base):e?.base,c=n?.variants&&!Le(n.variants)?ra(o,n.variants):o,f=n?.defaultVariants&&!Le(n.defaultVariants)?{...n.defaultVariants,...a}:a;!Le(s.twMergeConfig)&&!Xf(s.twMergeConfig,Pr)&&(Tr=!0,Pr=s.twMergeConfig);let p=Le(n?.slots),h=Le(r)?{}:{base:Tn(e?.base,p&&n?.base),...r},v=p?h:Yo({...n?.slots},Le(h)?{base:e?.base}:h),b=Le(n?.compoundVariants)?l:na(n?.compoundVariants,l),m=$=>{if(Le(c)&&Le(r)&&p)return hn(u,$?.class,$?.className)(s);if(b&&!Array.isArray(b))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof b}`);if(i&&!Array.isArray(i))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof i}`);let C=(P,R,E=[],L)=>{let T=E;if(typeof R=="string"){let y=Go(R).split(" ");for(let M=0;M<y.length;M++)T.push(`${P}:${y[M]}`)}else if(Array.isArray(R))for(let y=0;y<R.length;y++)T.push(`${P}:${R[y]}`);else if(typeof R=="object"&&typeof L=="string"&&L in R){let y=R[L];if(y&&typeof y=="string"){let M=Go(y).split(" "),S=[];for(let I=0;I<M.length;I++)S.push(`${P}:${M[I]}`);T[L]=T[L]?T[L].concat(S):S}else if(Array.isArray(y)&&y.length>0){let M=[];for(let S=0;S<y.length;S++)M.push(`${P}:${y[S]}`);T[L]=M}}return T},w=(P,R=c,E=null,L=null)=>{let T=R[P];if(!T||Le(T))return null;let y=L?.[P]??$?.[P];if(y===null)return null;let M=Uo(y),S=Array.isArray(s.responsiveVariants)&&s.responsiveVariants.length>0||s.responsiveVariants===!0,I=f?.[P],_=[];if(typeof M=="object"&&S)for(let[W,re]of Object.entries(M)){let Z=T[re];if(W==="initial"){I=re;continue}Array.isArray(s.responsiveVariants)&&!s.responsiveVariants.includes(W)||(_=C(W,Z,_,E))}let H=M!=null&&typeof M!="object"?M:Uo(I),V=T[H||"false"];return typeof _=="object"&&typeof E=="string"&&_[E]?Yo(_,V):_.length>0?(_.push(V),E==="base"?_.join(" "):_):V},A=()=>{if(!c)return null;let P=Object.keys(c),R=[];for(let E=0;E<P.length;E++){let L=w(P[E],c);L&&R.push(L)}return R},k=(P,R)=>{if(!c||typeof c!="object")return null;let E=[];for(let L in c){let T=w(L,c,P,R),y=P==="base"&&typeof T=="string"?T:T&&T[P];y&&E.push(y)}return E},K={};for(let P in $){let R=$[P];R!==void 0&&(K[P]=R)}let O=(P,R)=>{let E=typeof $?.[P]=="object"?{[P]:$[P]?.initial}:{};return{...f,...K,...E,...R}},z=(P=[],R)=>{let E=[],L=P.length;for(let T=0;T<L;T++){let{class:y,className:M,...S}=P[T],I=!0,_=O(null,R);for(let H in S){let V=S[H],W=_[H];if(Array.isArray(V)){if(!V.includes(W)){I=!1;break}}else{if((V==null||V===!1)&&(W==null||W===!1))continue;if(W!==V){I=!1;break}}}I&&(y&&E.push(y),M&&E.push(M))}return E},N=P=>{let R=z(b,P);if(!Array.isArray(R))return R;let E={},L=hn;for(let T=0;T<R.length;T++){let y=R[T];if(typeof y=="string")E.base=L(E.base,y)(s);else if(typeof y=="object")for(let M in y)E[M]=L(E[M],y[M])(s)}return E},x=P=>{if(i.length<1)return null;let R={},E=O(null,P);for(let L=0;L<i.length;L++){let{slots:T=[],class:y,className:M,...S}=i[L];if(!Le(S)){let I=!0;for(let _ in S){let H=E[_],V=S[_];if(H===void 0||(Array.isArray(V)?!V.includes(H):V!==H)){I=!1;break}}if(!I)continue}for(let I=0;I<T.length;I++){let _=T[I];R[_]||(R[_]=[]),R[_].push([y,M])}}return R};if(!Le(r)||!p){let P={};if(typeof v=="object"&&!Le(v)){let R=hn;for(let E in v)P[E]=L=>{let T=N(L),y=x(L);return R(v[E],k(E,L),T?T[E]:void 0,y?y[E]:void 0,L?.class,L?.className)(s)}}return P}return hn(u,A(),z(b),$?.class,$?.className)(s)},g=()=>{if(!(!c||typeof c!="object"))return Object.keys(c)};return m.variantKeys=g(),m.extend=n,m.base=u,m.slots=v,m.variants=c,m.defaultVariants=f,m.compoundSlots=i,m.compoundVariants=b,m},Oe=(e,t)=>{var n,r,o;return Qf(e,{...t,twMerge:(n=void 0)!=null?n:!0,twMergeConfig:{theme:{...(r=void 0)==null?void 0:r.theme,...Ho.theme},classGroups:{...(o=void 0)==null?void 0:o.classGroups,...Ho.classGroups}}})},Xo=Oe({slots:{base:"relative inline-flex flex-col gap-2 items-center justify-center",wrapper:"relative flex",label:"text-foreground dark:text-foreground-dark font-regular",circle1:"absolute w-full h-full rounded-full",circle2:"absolute w-full h-full rounded-full",dots:"relative rounded-full mx-auto",spinnerBars:["absolute","animate-fade-out","rounded-full","w-[25%]","h-[8%]","left-[calc(37.5%)]","top-[calc(46%)]","spinner-bar-animation"]},variants:{size:{sm:{wrapper:"w-5 h-5",circle1:"border-2",circle2:"border-2",dots:"size-1",label:"text-small"},md:{wrapper:"w-8 h-8",circle1:"border-3",circle2:"border-3",dots:"size-1.5",label:"text-medium"},lg:{wrapper:"w-10 h-10",circle1:"border-3",circle2:"border-3",dots:"size-2",label:"text-large"}},color:{current:{circle1:"border-b-current",circle2:"border-b-current",dots:"bg-current",spinnerBars:"bg-current"},white:{circle1:"border-b-white",circle2:"border-b-white",dots:"bg-white",spinnerBars:"bg-white"},default:{circle1:"border-b-default",circle2:"border-b-default",dots:"bg-default",spinnerBars:"bg-default"},primary:{circle1:"border-b-primary",circle2:"border-b-primary",dots:"bg-primary",spinnerBars:"bg-primary"},secondary:{circle1:"border-b-secondary",circle2:"border-b-secondary",dots:"bg-secondary",spinnerBars:"bg-secondary"},success:{circle1:"border-b-success",circle2:"border-b-success",dots:"bg-success",spinnerBars:"bg-success"},warning:{circle1:"border-b-warning",circle2:"border-b-warning",dots:"bg-warning",spinnerBars:"bg-warning"},danger:{circle1:"border-b-danger",circle2:"border-b-danger",dots:"bg-danger",spinnerBars:"bg-danger"}},labelColor:{foreground:{label:"text-foreground"},primary:{label:"text-primary"},secondary:{label:"text-secondary"},success:{label:"text-success"},warning:{label:"text-warning"},danger:{label:"text-danger"}},variant:{default:{circle1:["animate-spinner-ease-spin","border-solid","border-t-transparent","border-l-transparent","border-r-transparent"],circle2:["opacity-75","animate-spinner-linear-spin","border-dotted","border-t-transparent","border-l-transparent","border-r-transparent"]},gradient:{circle1:["border-0","bg-gradient-to-b","from-transparent","via-transparent","to-primary","animate-spinner-linear-spin","[animation-duration:1s]","[-webkit-mask:radial-gradient(closest-side,rgba(0,0,0,0.0)calc(100%-3px),rgba(0,0,0,1)calc(100%-3px))]"],circle2:["hidden"]},wave:{wrapper:"translate-y-3/4",dots:["animate-sway","spinner-dot-animation"]},dots:{wrapper:"translate-y-2/4",dots:["animate-blink","spinner-dot-blink-animation"]},spinner:{},simple:{wrapper:"text-foreground h-5 w-5 animate-spin",circle1:"opacity-25",circle2:"opacity-75"}}},defaultVariants:{size:"md",color:"primary",labelColor:"foreground",variant:"default"},compoundVariants:[{variant:"gradient",color:"current",class:{circle1:"to-current"}},{variant:"gradient",color:"white",class:{circle1:"to-white"}},{variant:"gradient",color:"default",class:{circle1:"to-default"}},{variant:"gradient",color:"primary",class:{circle1:"to-primary"}},{variant:"gradient",color:"secondary",class:{circle1:"to-secondary"}},{variant:"gradient",color:"success",class:{circle1:"to-success"}},{variant:"gradient",color:"warning",class:{circle1:"to-warning"}},{variant:"gradient",color:"danger",class:{circle1:"to-danger"}},{variant:"wave",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"wave",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"wave",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"dots",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"dots",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"dots",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"simple",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"simple",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",color:"current",class:{wrapper:"text-current"}},{variant:"simple",color:"white",class:{wrapper:"text-white"}},{variant:"simple",color:"default",class:{wrapper:"text-default"}},{variant:"simple",color:"primary",class:{wrapper:"text-primary"}},{variant:"simple",color:"secondary",class:{wrapper:"text-secondary"}},{variant:"simple",color:"success",class:{wrapper:"text-success"}},{variant:"simple",color:"warning",class:{wrapper:"text-warning"}},{variant:"simple",color:"danger",class:{wrapper:"text-danger"}}]}),Vn=["outline-solid outline-transparent","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],ep=["outline-solid outline-transparent","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],Pt={default:["[&+.border-medium.border-default]:ms-[calc(var(--heroui-border-width-medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(var(--heroui-border-width-medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(var(--heroui-border-width-medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(var(--heroui-border-width-medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(var(--heroui-border-width-medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(var(--heroui-border-width-medium)*-1)]"]},qo=Oe({slots:{base:["z-0","relative","bg-transparent","before:content-['']","before:hidden","before:z-[-1]","before:absolute","before:rotate-45","before:w-2.5","before:h-2.5","before:rounded-sm","data-[arrow=true]:before:block","data-[placement=top]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top]:before:left-1/2","data-[placement=top]:before:-translate-x-1/2","data-[placement=top-start]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-start]:before:left-3","data-[placement=top-end]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-end]:before:right-3","data-[placement=bottom]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom]:before:left-1/2","data-[placement=bottom]:before:-translate-x-1/2","data-[placement=bottom-start]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-start]:before:left-3","data-[placement=bottom-end]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-end]:before:right-3","data-[placement=left]:before:-right-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=left]:before:top-1/2","data-[placement=left]:before:-translate-y-1/2","data-[placement=left-start]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-start]:before:top-1/4","data-[placement=left-end]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-end]:before:bottom-1/4","data-[placement=right]:before:-left-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=right]:before:top-1/2","data-[placement=right]:before:-translate-y-1/2","data-[placement=right-start]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-start]:before:top-1/4","data-[placement=right-end]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-end]:before:bottom-1/4",...Vn],content:["z-10","px-2.5","py-1","w-full","inline-flex","flex-col","items-center","justify-center","box-border","subpixel-antialiased","outline-solid outline-transparent","box-border"],trigger:["z-10"],backdrop:["hidden"],arrow:[]},variants:{size:{sm:{content:"text-tiny"},md:{content:"text-small"},lg:{content:"text-medium"}},color:{default:{base:"before:bg-content1 before:shadow-small",content:"bg-content1"},foreground:{base:"before:bg-foreground",content:G.solid.foreground},primary:{base:"before:bg-primary",content:G.solid.primary},secondary:{base:"before:bg-secondary",content:G.solid.secondary},success:{base:"before:bg-success",content:G.solid.success},warning:{base:"before:bg-warning",content:G.solid.warning},danger:{base:"before:bg-danger",content:G.solid.danger}},radius:{none:{content:"rounded-none"},sm:{content:"rounded-small"},md:{content:"rounded-medium"},lg:{content:"rounded-large"},full:{content:"rounded-full"}},shadow:{none:{content:"shadow-none"},sm:{content:"shadow-small"},md:{content:"shadow-medium"},lg:{content:"shadow-large"}},backdrop:{transparent:{},opaque:{backdrop:"bg-overlay/50 backdrop-opacity-disabled"},blur:{backdrop:"backdrop-blur-sm backdrop-saturate-150 bg-overlay/30"}},triggerScaleOnOpen:{true:{trigger:["aria-expanded:scale-[0.97]","aria-expanded:opacity-70","subpixel-antialiased"]},false:{}},disableAnimation:{true:{base:"animate-none"}},isTriggerDisabled:{true:{trigger:"opacity-disabled pointer-events-none"},false:{}}},defaultVariants:{color:"default",radius:"lg",size:"md",shadow:"md",backdrop:"transparent",triggerScaleOnOpen:!0},compoundVariants:[{backdrop:["opaque","blur"],class:{backdrop:"block w-full h-full fixed inset-0 -z-30"}}]}),tp=["data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],np=["data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],An=Oe({base:[],variants:{orientation:{vertical:["overflow-y-auto",...tp],horizontal:["overflow-x-auto",...np]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}}),Zo=Oe({slots:{base:"group flex flex-col data-[hidden=true]:hidden",label:["absolute","z-10","pointer-events-none","origin-top-left","shrink-0","rtl:origin-top-right","subpixel-antialiased","block","text-small","text-foreground-500"],mainWrapper:"h-full",inputWrapper:"relative w-full inline-flex tap-highlight-transparent flex-row items-center shadow-xs px-3 gap-3",innerWrapper:"inline-flex w-full items-center h-full box-border",input:["w-full font-normal bg-transparent !outline-solid outline-transparent placeholder:text-foreground-500 focus-visible:outline-solid outline-transparent","data-[has-start-content=true]:ps-1.5","data-[has-end-content=true]:pe-1.5","data-[type=color]:rounded-none","file:cursor-pointer file:bg-transparent file:border-0","autofill:bg-transparent bg-clip-text"],clearButton:["p-2","-m-2","z-10","absolute","end-3","start-auto","pointer-events-none","appearance-none","outline-solid outline-transparent","select-none","opacity-0","cursor-pointer","active:!opacity-70","rounded-full",...Vn],helperWrapper:"hidden group-data-[has-helper=true]:flex p-1 relative flex-col gap-1.5",description:"text-tiny text-foreground-400",errorMessage:"text-tiny text-danger"},variants:{variant:{flat:{inputWrapper:["bg-default-100","data-[hover=true]:bg-default-200","group-data-[focus=true]:bg-default-100"]},faded:{inputWrapper:["bg-default-100","border-medium","border-default-200","data-[hover=true]:border-default-400 focus-within:border-default-400"],value:"group-data-[has-value=true]:text-default-foreground"},bordered:{inputWrapper:["border-medium","border-default-200","data-[hover=true]:border-default-400","group-data-[focus=true]:border-default-foreground"]},underlined:{inputWrapper:["!px-1","!pb-0","!gap-0","relative","box-border","border-b-medium","shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","border-default-200","!rounded-none","hover:border-default-300","after:content-['']","after:w-0","after:origin-center","after:bg-default-foreground","after:absolute","after:left-1/2","after:-translate-x-1/2","after:-bottom-[2px]","after:h-[2px]","group-data-[focus=true]:after:w-full"],innerWrapper:"pb-1",label:"group-data-[filled-within=true]:text-foreground"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{label:"text-tiny",inputWrapper:"h-8 min-h-8 px-2 rounded-small",input:"text-small",clearButton:"text-medium"},md:{inputWrapper:"h-10 min-h-10 rounded-medium",input:"text-small",clearButton:"text-large hover:!opacity-100"},lg:{label:"text-medium",inputWrapper:"h-12 min-h-12 rounded-large",input:"text-medium",clearButton:"text-large hover:!opacity-100"}},radius:{none:{inputWrapper:"rounded-none"},sm:{inputWrapper:"rounded-small"},md:{inputWrapper:"rounded-medium"},lg:{inputWrapper:"rounded-large"},full:{inputWrapper:"rounded-full"}},labelPlacement:{outside:{mainWrapper:"flex flex-col"},"outside-left":{base:"flex-row items-center flex-nowrap data-[has-helper=true]:items-start",inputWrapper:"flex-1",mainWrapper:"flex flex-col",label:"relative text-foreground pe-2 ps-2 pointer-events-auto"},"outside-top":{mainWrapper:"flex flex-col",label:"relative text-foreground pb-2 pointer-events-auto"},inside:{label:"cursor-text",inputWrapper:"flex-col items-start justify-center gap-0",innerWrapper:"group-data-[has-label=true]:items-end"}},fullWidth:{true:{base:"w-full"},false:{}},isClearable:{true:{input:"peer pe-6 input-search-cancel-button-none",clearButton:["peer-data-[filled=true]:pointer-events-auto","peer-data-[filled=true]:opacity-70 peer-data-[filled=true]:block","peer-data-[filled=true]:scale-100"]}},isDisabled:{true:{base:"opacity-disabled pointer-events-none",inputWrapper:"pointer-events-none",label:"pointer-events-none"}},isInvalid:{true:{label:"!text-danger",input:"!placeholder:text-danger !text-danger"}},isRequired:{true:{label:"after:content-['*'] after:text-danger after:ms-0.5"}},isMultiline:{true:{label:"relative",inputWrapper:"!h-auto",innerWrapper:"items-start group-data-[has-label=true]:items-start",input:"resize-none data-[hide-scroll=true]:scrollbar-hide",clearButton:"absolute top-2 right-2 rtl:right-auto rtl:left-2 z-10"}},disableAnimation:{true:{input:"transition-none",inputWrapper:"transition-none",label:"transition-none"},false:{inputWrapper:"transition-background motion-reduce:transition-none !duration-150",label:["will-change-auto","!duration-200","!ease-out","motion-reduce:transition-none","transition-[transform,color,left,opacity,translate,scale]"],clearButton:["scale-90","ease-out","duration-150","transition-[opacity,transform]","motion-reduce:transition-none","motion-reduce:scale-100"]}}},defaultVariants:{variant:"flat",color:"default",size:"md",fullWidth:!0,isDisabled:!1,isMultiline:!1},compoundVariants:[{variant:"flat",color:"default",class:{input:"group-data-[has-value=true]:text-default-foreground"}},{variant:"flat",color:"primary",class:{inputWrapper:["bg-primary-100","data-[hover=true]:bg-primary-50","text-primary","group-data-[focus=true]:bg-primary-50","placeholder:text-primary"],input:"placeholder:text-primary",label:"text-primary"}},{variant:"flat",color:"secondary",class:{inputWrapper:["bg-secondary-100","text-secondary","data-[hover=true]:bg-secondary-50","group-data-[focus=true]:bg-secondary-50","placeholder:text-secondary"],input:"placeholder:text-secondary",label:"text-secondary"}},{variant:"flat",color:"success",class:{inputWrapper:["bg-success-100","text-success-600","dark:text-success","placeholder:text-success-600","dark:placeholder:text-success","data-[hover=true]:bg-success-50","group-data-[focus=true]:bg-success-50"],input:"placeholder:text-success-600 dark:placeholder:text-success",label:"text-success-600 dark:text-success"}},{variant:"flat",color:"warning",class:{inputWrapper:["bg-warning-100","text-warning-600","dark:text-warning","placeholder:text-warning-600","dark:placeholder:text-warning","data-[hover=true]:bg-warning-50","group-data-[focus=true]:bg-warning-50"],input:"placeholder:text-warning-600 dark:placeholder:text-warning",label:"text-warning-600 dark:text-warning"}},{variant:"flat",color:"danger",class:{inputWrapper:["bg-danger-100","text-danger","dark:text-danger-500","placeholder:text-danger","dark:placeholder:text-danger-500","data-[hover=true]:bg-danger-50","group-data-[focus=true]:bg-danger-50"],input:"placeholder:text-danger dark:placeholder:text-danger-500",label:"text-danger dark:text-danger-500"}},{variant:"faded",color:"primary",class:{label:"text-primary",inputWrapper:"data-[hover=true]:border-primary focus-within:border-primary"}},{variant:"faded",color:"secondary",class:{label:"text-secondary",inputWrapper:"data-[hover=true]:border-secondary focus-within:border-secondary"}},{variant:"faded",color:"success",class:{label:"text-success",inputWrapper:"data-[hover=true]:border-success focus-within:border-success"}},{variant:"faded",color:"warning",class:{label:"text-warning",inputWrapper:"data-[hover=true]:border-warning focus-within:border-warning"}},{variant:"faded",color:"danger",class:{label:"text-danger",inputWrapper:"data-[hover=true]:border-danger focus-within:border-danger"}},{variant:"underlined",color:"default",class:{input:"group-data-[has-value=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{inputWrapper:"after:bg-primary",label:"text-primary"}},{variant:"underlined",color:"secondary",class:{inputWrapper:"after:bg-secondary",label:"text-secondary"}},{variant:"underlined",color:"success",class:{inputWrapper:"after:bg-success",label:"text-success"}},{variant:"underlined",color:"warning",class:{inputWrapper:"after:bg-warning",label:"text-warning"}},{variant:"underlined",color:"danger",class:{inputWrapper:"after:bg-danger",label:"text-danger"}},{variant:"bordered",color:"primary",class:{inputWrapper:"group-data-[focus=true]:border-primary",label:"text-primary"}},{variant:"bordered",color:"secondary",class:{inputWrapper:"group-data-[focus=true]:border-secondary",label:"text-secondary"}},{variant:"bordered",color:"success",class:{inputWrapper:"group-data-[focus=true]:border-success",label:"text-success"}},{variant:"bordered",color:"warning",class:{inputWrapper:"group-data-[focus=true]:border-warning",label:"text-warning"}},{variant:"bordered",color:"danger",class:{inputWrapper:"group-data-[focus=true]:border-danger",label:"text-danger"}},{labelPlacement:"inside",color:"default",class:{label:"group-data-[filled-within=true]:text-default-600"}},{labelPlacement:"outside",color:"default",class:{label:"group-data-[filled-within=true]:text-foreground"}},{radius:"full",size:["sm"],class:{inputWrapper:"px-3"}},{radius:"full",size:"md",class:{inputWrapper:"px-4"}},{radius:"full",size:"lg",class:{inputWrapper:"px-5"}},{disableAnimation:!1,variant:["faded","bordered"],class:{inputWrapper:"transition-colors motion-reduce:transition-none"}},{disableAnimation:!1,variant:"underlined",class:{inputWrapper:"after:transition-width motion-reduce:after:transition-none"}},{variant:["flat","faded"],class:{inputWrapper:[...ep]}},{isInvalid:!0,variant:"flat",class:{inputWrapper:["!bg-danger-50","data-[hover=true]:!bg-danger-100","group-data-[focus=true]:!bg-danger-50"]}},{isInvalid:!0,variant:"bordered",class:{inputWrapper:"!border-danger group-data-[focus=true]:!border-danger"}},{isInvalid:!0,variant:"underlined",class:{inputWrapper:"after:!bg-danger"}},{labelPlacement:"inside",size:"sm",class:{inputWrapper:"h-12 py-1.5 px-3"}},{labelPlacement:"inside",size:"md",class:{inputWrapper:"h-14 py-2"}},{labelPlacement:"inside",size:"lg",class:{inputWrapper:"h-16 py-2.5 gap-0"}},{labelPlacement:"inside",size:"sm",variant:["bordered","faded"],class:{inputWrapper:"py-1"}},{labelPlacement:["inside","outside"],class:{label:["group-data-[filled-within=true]:pointer-events-auto"]}},{labelPlacement:"outside",isMultiline:!1,class:{base:"relative justify-end",label:["pb-0","z-20","top-1/2","-translate-y-1/2","group-data-[filled-within=true]:start-0"]}},{labelPlacement:["inside"],class:{label:["group-data-[filled-within=true]:scale-85"]}},{labelPlacement:["inside"],variant:"flat",class:{innerWrapper:"pb-0.5"}},{variant:"underlined",size:"sm",class:{innerWrapper:"pb-1"}},{variant:"underlined",size:["md","lg"],class:{innerWrapper:"pb-1.5"}},{labelPlacement:"inside",size:["sm","md"],class:{label:"text-small"}},{labelPlacement:"inside",isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_8px)]"]}},{labelPlacement:"inside",isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_6px)]"]}},{labelPlacement:"inside",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_8px)]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_8px_-_var(--heroui-border-width-medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_6px_-_var(--heroui-border-width-medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_8px_-_var(--heroui-border-width-medium))]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"sm",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"md",class:{label:["group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_3.5px)]"]}},{labelPlacement:"inside",variant:"underlined",size:"lg",isMultiline:!1,class:{label:["text-medium","group-data-[filled-within=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_4px)]"]}},{labelPlacement:"outside",size:"sm",isMultiline:!1,class:{label:["start-2","text-tiny","group-data-[filled-within=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-tiny)/2_+_16px)]"],base:"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_8px)]"}},{labelPlacement:"outside",size:"md",isMultiline:!1,class:{label:["start-3","end-auto","text-small","group-data-[filled-within=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_20px)]"],base:"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_10px)]"}},{labelPlacement:"outside",size:"lg",isMultiline:!1,class:{label:["start-3","end-auto","text-medium","group-data-[filled-within=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_24px)]"],base:"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_12px)]"}},{labelPlacement:"outside-left",size:"sm",class:{label:"group-data-[has-helper=true]:pt-2"}},{labelPlacement:"outside-left",size:"md",class:{label:"group-data-[has-helper=true]:pt-3"}},{labelPlacement:"outside-left",size:"lg",class:{label:"group-data-[has-helper=true]:pt-4"}},{labelPlacement:["outside","outside-left"],isMultiline:!0,class:{inputWrapper:"py-2"}},{labelPlacement:"outside",isMultiline:!0,class:{label:"pb-1.5"}},{labelPlacement:"inside",isMultiline:!0,class:{label:"pb-0.5",input:"pt-0"}},{isMultiline:!0,disableAnimation:!1,class:{input:"transition-height !duration-100 motion-reduce:transition-none"}},{labelPlacement:["inside","outside"],class:{label:["pe-2","max-w-full","text-ellipsis","overflow-hidden"]}},{isMultiline:!0,radius:"full",class:{inputWrapper:"data-[has-multiple-rows=true]:rounded-large"}},{isClearable:!0,isMultiline:!0,class:{clearButton:["group-data-[has-value=true]:opacity-70 group-data-[has-value=true]:block","group-data-[has-value=true]:scale-100","group-data-[has-value=true]:pointer-events-auto"]}}]}),rp=Oe({slots:{base:"w-full relative flex flex-col gap-1 p-1 overflow-clip",list:"w-full flex flex-col gap-0.5 outline-solid outline-transparent",emptyContent:["h-10","px-2","py-1.5","w-full","h-full","text-foreground-400","text-start"]}}),Jo=Oe({slots:{base:["flex","group","gap-2","items-center","justify-between","relative","px-2","py-1.5","w-full","h-full","box-border","rounded-small","subpixel-antialiased","outline-solid outline-transparent","cursor-pointer","tap-highlight-transparent",...Vn,"data-[focus-visible=true]:dark:ring-offset-background-content1"],wrapper:"w-full flex flex-col items-start justify-center",title:"flex-1 text-small font-normal",description:["w-full","text-tiny","text-foreground-500","group-hover:text-current"],selectedIcon:["text-inherit","w-3","h-3","shrink-0"],shortcut:["px-1","py-0.5","rounded-sm","font-sans","text-foreground-500","text-tiny","border-small","border-default-300","group-hover:border-current"]},variants:{variant:{solid:{base:""},bordered:{base:"border-medium border-transparent bg-transparent"},light:{base:"bg-transparent"},faded:{base:["border-small border-transparent hover:border-default data-[hover=true]:bg-default-100","data-[selectable=true]:focus:border-default data-[selectable=true]:focus:bg-default-100"]},flat:{base:""},shadow:{base:"data-[hover=true]:shadow-lg"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},showDivider:{true:{base:["mb-1.5","after:content-['']","after:absolute","after:-bottom-1","after:left-0","after:right-0","after:h-divider","after:bg-divider"]},false:{}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{},false:{base:"data-[hover=true]:transition-colors"}},hasTitleTextChild:{true:{title:"truncate"}},hasDescriptionTextChild:{true:{description:"truncate"}}},defaultVariants:{variant:"solid",color:"default",showDivider:!1},compoundVariants:[{variant:"solid",color:"default",class:{base:["data-[hover=true]:bg-default","data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:bg-default","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"solid",color:"primary",class:{base:["data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground","data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"]}},{variant:"solid",color:"secondary",class:{base:["data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground","data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"]}},{variant:"solid",color:"success",class:{base:["data-[hover=true]:bg-success data-[hover=true]:text-success-foreground","data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"]}},{variant:"solid",color:"warning",class:{base:["data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground","data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"]}},{variant:"solid",color:"danger",class:{base:["data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground","data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"]}},{variant:"shadow",color:"default",class:{base:["data-[hover=true]:shadow-default/50 data-[hover=true]:bg-default data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:shadow-default/50 data-[selectable=true]:focus:bg-default data-[selectable=true]:focus:text-default-foreground"]}},{variant:"shadow",color:"primary",class:{base:["data-[hover=true]:shadow-primary/30 data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground","data-[selectable=true]:focus:shadow-primary/30 data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"]}},{variant:"shadow",color:"secondary",class:{base:["data-[hover=true]:shadow-secondary/30 data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground","data-[selectable=true]:focus:shadow-secondary/30 data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"]}},{variant:"shadow",color:"success",class:{base:["data-[hover=true]:shadow-success/30 data-[hover=true]:bg-success data-[hover=true]:text-success-foreground","data-[selectable=true]:focus:shadow-success/30 data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"]}},{variant:"shadow",color:"warning",class:{base:["data-[hover=true]:shadow-warning/30 data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground","data-[selectable=true]:focus:shadow-warning/30 data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"]}},{variant:"shadow",color:"danger",class:{base:["data-[hover=true]:shadow-danger/30 data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground","data-[selectable=true]:focus:shadow-danger/30 data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"]}},{variant:"bordered",color:"default",class:{base:["data-[hover=true]:border-default","data-[selectable=true]:focus:border-default"]}},{variant:"bordered",color:"primary",class:{base:["data-[hover=true]:border-primary data-[hover=true]:text-primary","data-[selectable=true]:focus:border-primary data-[selectable=true]:focus:text-primary"]}},{variant:"bordered",color:"secondary",class:{base:["data-[hover=true]:border-secondary data-[hover=true]:text-secondary","data-[selectable=true]:focus:border-secondary data-[selectable=true]:focus:text-secondary"]}},{variant:"bordered",color:"success",class:{base:["data-[hover=true]:border-success data-[hover=true]:text-success","data-[selectable=true]:focus:border-success data-[selectable=true]:focus:text-success"]}},{variant:"bordered",color:"warning",class:{base:["data-[hover=true]:border-warning data-[hover=true]:text-warning","data-[selectable=true]:focus:border-warning data-[selectable=true]:focus:text-warning"]}},{variant:"bordered",color:"danger",class:{base:["data-[hover=true]:border-danger data-[hover=true]:text-danger","data-[selectable=true]:focus:border-danger data-[selectable=true]:focus:text-danger"]}},{variant:"flat",color:"default",class:{base:["data-[hover=true]:bg-default/40","data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:bg-default/40","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"flat",color:"primary",class:{base:["data-[hover=true]:bg-primary/20 data-[hover=true]:text-primary","data-[selectable=true]:focus:bg-primary/20 data-[selectable=true]:focus:text-primary"]}},{variant:"flat",color:"secondary",class:{base:["data-[hover=true]:bg-secondary/20 data-[hover=true]:text-secondary","data-[selectable=true]:focus:bg-secondary/20 data-[selectable=true]:focus:text-secondary"]}},{variant:"flat",color:"success",class:{base:["data-[hover=true]:bg-success/20 data-[hover=true]:text-success","data-[selectable=true]:focus:bg-success/20 data-[selectable=true]:focus:text-success"]}},{variant:"flat",color:"warning",class:{base:["data-[hover=true]:bg-warning/20 data-[hover=true]:text-warning","data-[selectable=true]:focus:bg-warning/20 data-[selectable=true]:focus:text-warning"]}},{variant:"flat",color:"danger",class:{base:["data-[hover=true]:bg-danger/20 data-[hover=true]:text-danger","data-[selectable=true]:focus:bg-danger/20 data-[selectable=true]:focus:text-danger"]}},{variant:"faded",color:"default",class:{base:["data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"faded",color:"primary",class:{base:["data-[hover=true]:text-primary","data-[selectable=true]:focus:text-primary"]}},{variant:"faded",color:"secondary",class:{base:["data-[hover=true]:text-secondary","data-[selectable=true]:focus:text-secondary"]}},{variant:"faded",color:"success",class:{base:["data-[hover=true]:text-success","data-[selectable=true]:focus:text-success"]}},{variant:"faded",color:"warning",class:{base:["data-[hover=true]:text-warning","data-[selectable=true]:focus:text-warning"]}},{variant:"faded",color:"danger",class:{base:["data-[hover=true]:text-danger","data-[selectable=true]:focus:text-danger"]}},{variant:"light",color:"default",class:{base:["data-[hover=true]:text-default-500","data-[selectable=true]:focus:text-default-500"]}},{variant:"light",color:"primary",class:{base:["data-[hover=true]:text-primary","data-[selectable=true]:focus:text-primary"]}},{variant:"light",color:"secondary",class:{base:["data-[hover=true]:text-secondary","data-[selectable=true]:focus:text-secondary"]}},{variant:"light",color:"success",class:{base:["data-[hover=true]:text-success","data-[selectable=true]:focus:text-success"]}},{variant:"light",color:"warning",class:{base:["data-[hover=true]:text-warning","data-[selectable=true]:focus:text-warning"]}},{variant:"light",color:"danger",class:{base:["data-[hover=true]:text-danger","data-[selectable=true]:focus:text-danger"]}}]}),op=Oe({slots:{base:"relative mb-2",heading:"pl-1 text-tiny text-foreground-500",group:"data-[has-title=true]:pt-1",divider:"mt-2"}}),lp=Oe({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),ip=Oe({base:"flex flex-col gap-2 items-start"}),ap=Oe({base:["z-0","group","relative","inline-flex","items-center","justify-center","box-border","appearance-none","outline-solid outline-transparent","select-none","whitespace-nowrap","min-w-max","font-normal","subpixel-antialiased","overflow-hidden","tap-highlight-transparent","transform-gpu data-[pressed=true]:scale-[0.97]","cursor-pointer",...Vn],variants:{variant:{solid:"",bordered:"border-medium bg-transparent",light:"bg-transparent",flat:"",faded:"border-medium",shadow:"",ghost:"border-medium bg-transparent"},size:{sm:"px-3 min-w-16 h-8 text-tiny gap-2 rounded-small",md:"px-4 min-w-20 h-10 text-small gap-2 rounded-medium",lg:"px-6 min-w-24 h-12 text-medium gap-3 rounded-large"},color:{default:"",primary:"",secondary:"",success:"",warning:"",danger:""},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"},fullWidth:{true:"w-full"},isDisabled:{true:"opacity-disabled pointer-events-none"},isInGroup:{true:"[&:not(:first-child):not(:last-child)]:rounded-none"},isIconOnly:{true:"px-0 !gap-0",false:"[&>svg]:max-w-[theme(spacing.8)]"},disableAnimation:{true:"!transition-none data-[pressed=true]:scale-100",false:"transition-transform-colors-opacity motion-reduce:transition-none"}},defaultVariants:{size:"md",variant:"solid",color:"default",fullWidth:!1,isDisabled:!1,isInGroup:!1},compoundVariants:[{variant:"solid",color:"default",class:G.solid.default},{variant:"solid",color:"primary",class:G.solid.primary},{variant:"solid",color:"secondary",class:G.solid.secondary},{variant:"solid",color:"success",class:G.solid.success},{variant:"solid",color:"warning",class:G.solid.warning},{variant:"solid",color:"danger",class:G.solid.danger},{variant:"shadow",color:"default",class:G.shadow.default},{variant:"shadow",color:"primary",class:G.shadow.primary},{variant:"shadow",color:"secondary",class:G.shadow.secondary},{variant:"shadow",color:"success",class:G.shadow.success},{variant:"shadow",color:"warning",class:G.shadow.warning},{variant:"shadow",color:"danger",class:G.shadow.danger},{variant:"bordered",color:"default",class:G.bordered.default},{variant:"bordered",color:"primary",class:G.bordered.primary},{variant:"bordered",color:"secondary",class:G.bordered.secondary},{variant:"bordered",color:"success",class:G.bordered.success},{variant:"bordered",color:"warning",class:G.bordered.warning},{variant:"bordered",color:"danger",class:G.bordered.danger},{variant:"flat",color:"default",class:G.flat.default},{variant:"flat",color:"primary",class:G.flat.primary},{variant:"flat",color:"secondary",class:G.flat.secondary},{variant:"flat",color:"success",class:G.flat.success},{variant:"flat",color:"warning",class:G.flat.warning},{variant:"flat",color:"danger",class:G.flat.danger},{variant:"faded",color:"default",class:G.faded.default},{variant:"faded",color:"primary",class:G.faded.primary},{variant:"faded",color:"secondary",class:G.faded.secondary},{variant:"faded",color:"success",class:G.faded.success},{variant:"faded",color:"warning",class:G.faded.warning},{variant:"faded",color:"danger",class:G.faded.danger},{variant:"light",color:"default",class:[G.light.default,"data-[hover=true]:bg-default/40"]},{variant:"light",color:"primary",class:[G.light.primary,"data-[hover=true]:bg-primary/20"]},{variant:"light",color:"secondary",class:[G.light.secondary,"data-[hover=true]:bg-secondary/20"]},{variant:"light",color:"success",class:[G.light.success,"data-[hover=true]:bg-success/20"]},{variant:"light",color:"warning",class:[G.light.warning,"data-[hover=true]:bg-warning/20"]},{variant:"light",color:"danger",class:[G.light.danger,"data-[hover=true]:bg-danger/20"]},{variant:"ghost",color:"default",class:[G.ghost.default,"data-[hover=true]:!bg-default"]},{variant:"ghost",color:"primary",class:[G.ghost.primary,"data-[hover=true]:!bg-primary data-[hover=true]:!text-primary-foreground"]},{variant:"ghost",color:"secondary",class:[G.ghost.secondary,"data-[hover=true]:!bg-secondary data-[hover=true]:!text-secondary-foreground"]},{variant:"ghost",color:"success",class:[G.ghost.success,"data-[hover=true]:!bg-success data-[hover=true]:!text-success-foreground"]},{variant:"ghost",color:"warning",class:[G.ghost.warning,"data-[hover=true]:!bg-warning data-[hover=true]:!text-warning-foreground"]},{variant:"ghost",color:"danger",class:[G.ghost.danger,"data-[hover=true]:!bg-danger data-[hover=true]:!text-danger-foreground"]},{isInGroup:!0,class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,size:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,isRounded:!0,class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,radius:"none",class:"rounded-none first:rounded-s-none last:rounded-e-none"},{isInGroup:!0,radius:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,radius:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,radius:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,radius:"full",class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,variant:["ghost","bordered"],color:"default",className:Pt.default},{isInGroup:!0,variant:["ghost","bordered"],color:"primary",className:Pt.primary},{isInGroup:!0,variant:["ghost","bordered"],color:"secondary",className:Pt.secondary},{isInGroup:!0,variant:["ghost","bordered"],color:"success",className:Pt.success},{isInGroup:!0,variant:["ghost","bordered"],color:"warning",className:Pt.warning},{isInGroup:!0,variant:["ghost","bordered"],color:"danger",className:Pt.danger},{isIconOnly:!0,size:"sm",class:"min-w-8 w-8 h-8"},{isIconOnly:!0,size:"md",class:"min-w-10 w-10 h-10"},{isIconOnly:!0,size:"lg",class:"min-w-12 w-12 h-12"},{variant:["solid","faded","flat","bordered","shadow"],class:"data-[hover=true]:opacity-hover"}]});Oe({base:"inline-flex items-center justify-center h-auto",variants:{fullWidth:{true:"w-full"}},defaultVariants:{fullWidth:!1}});var Qo=Oe({slots:{base:"group inline-flex flex-column w-full",listboxWrapper:"scroll-py-6 w-full",listbox:"",popoverContent:"w-full p-1 overflow-hidden",endContentWrapper:"relative flex h-full items-center -mr-2",clearButton:["text-medium","translate-x-1","cursor-text","opacity-0","pointer-events-none","text-default-500","group-data-[invalid=true]:text-danger","data-[visible=true]:opacity-100","data-[visible=true]:pointer-events-auto","data-[visible=true]:cursor-pointer","sm:data-[visible=true]:opacity-0","sm:data-[visible=true]:pointer-events-none","sm:group-data-[hover=true]:data-[visible=true]:opacity-100","sm:group-data-[hover=true]:data-[visible=true]:pointer-events-auto"],selectorButton:"text-medium"},variants:{isClearable:{true:{},false:{clearButton:"hidden"}},disableAnimation:{true:{selectorButton:"transition-none"},false:{selectorButton:"transition-transform duration-150 ease motion-reduce:transition-none"}},disableSelectorIconRotation:{true:{},false:{selectorButton:"data-[open=true]:rotate-180"}}},defaultVariants:{isClearable:!0,disableSelectorIconRotation:!1}});function Ar(e){return bn()?e.altKey:e.ctrlKey}function gn(e,t){var n,r;let o=`[data-key="${CSS.escape(String(t))}"]`,l=(n=e.current)===null||n===void 0?void 0:n.dataset.collection;return l&&(o=`[data-collection="${CSS.escape(l)}"]${o}`),(r=e.current)===null||r===void 0?void 0:r.querySelector(o)}const la=new WeakMap;function sp(e){let t=qe();return la.set(e,t),t}function up(e){return la.get(e)}const cp=1e3;function dp(e){let{keyboardDelegate:t,selectionManager:n,onTypeSelect:r}=e,o=d.useRef({search:"",timeout:void 0}).current,l=i=>{let a=fp(i.key);if(!(!a||i.ctrlKey||i.metaKey||!i.currentTarget.contains(i.target)||o.search.length===0&&a===" ")){if(a===" "&&o.search.trim().length>0&&(i.preventDefault(),"continuePropagation"in i||i.stopPropagation()),o.search+=a,t.getKeyForSearch!=null){let s=t.getKeyForSearch(o.search,n.focusedKey);s==null&&(s=t.getKeyForSearch(o.search)),s!=null&&(n.setFocusedKey(s),r&&r(s))}clearTimeout(o.timeout),o.timeout=setTimeout(()=>{o.search=""},cp)}};return{typeSelectProps:{onKeyDownCapture:t.getKeyForSearch?l:void 0}}}function fp(e){return e.length===1||!/^[A-Z]/i.test(e)?e:""}function ia(e){let{selectionManager:t,keyboardDelegate:n,ref:r,autoFocus:o=!1,shouldFocusWrap:l=!1,disallowEmptySelection:i=!1,disallowSelectAll:a=!1,escapeKeyBehavior:s="clearSelection",selectOnFocus:u=t.selectionBehavior==="replace",disallowTypeAhead:c=!1,shouldUseVirtualFocus:f,allowsTabNavigation:p=!1,isVirtualized:h,scrollRef:v=r,linkBehavior:b="action"}=e,{direction:m}=Dn(),g=Bn(),$=y=>{var M;if(y.altKey&&y.key==="Tab"&&y.preventDefault(),!(!((M=r.current)===null||M===void 0)&&M.contains(y.target)))return;const S=(B,j)=>{if(B!=null){if(t.isLink(B)&&b==="selection"&&u&&!Ar(y)){bl.flushSync(()=>{t.setFocusedKey(B,j)});let oe=gn(r,B),le=t.getItemProps(B);oe&&g.open(oe,y,le.href,le.routerOptions);return}if(t.setFocusedKey(B,j),t.isLink(B)&&b==="override")return;y.shiftKey&&t.selectionMode==="multiple"?t.extendSelection(B):u&&!Ar(y)&&t.replaceSelection(B)}};switch(y.key){case"ArrowDown":if(n.getKeyBelow){var I,_,H;let B=t.focusedKey!=null?(I=n.getKeyBelow)===null||I===void 0?void 0:I.call(n,t.focusedKey):(_=n.getFirstKey)===null||_===void 0?void 0:_.call(n);B==null&&l&&(B=(H=n.getFirstKey)===null||H===void 0?void 0:H.call(n,t.focusedKey)),B!=null&&(y.preventDefault(),S(B))}break;case"ArrowUp":if(n.getKeyAbove){var V,W,re;let B=t.focusedKey!=null?(V=n.getKeyAbove)===null||V===void 0?void 0:V.call(n,t.focusedKey):(W=n.getLastKey)===null||W===void 0?void 0:W.call(n);B==null&&l&&(B=(re=n.getLastKey)===null||re===void 0?void 0:re.call(n,t.focusedKey)),B!=null&&(y.preventDefault(),S(B))}break;case"ArrowLeft":if(n.getKeyLeftOf){var Z,se,$e;let B=t.focusedKey!=null?(Z=n.getKeyLeftOf)===null||Z===void 0?void 0:Z.call(n,t.focusedKey):null;B==null&&l&&(B=m==="rtl"?(se=n.getFirstKey)===null||se===void 0?void 0:se.call(n,t.focusedKey):($e=n.getLastKey)===null||$e===void 0?void 0:$e.call(n,t.focusedKey)),B!=null&&(y.preventDefault(),S(B,m==="rtl"?"first":"last"))}break;case"ArrowRight":if(n.getKeyRightOf){var ce,ne,F;let B=t.focusedKey!=null?(ce=n.getKeyRightOf)===null||ce===void 0?void 0:ce.call(n,t.focusedKey):null;B==null&&l&&(B=m==="rtl"?(ne=n.getLastKey)===null||ne===void 0?void 0:ne.call(n,t.focusedKey):(F=n.getFirstKey)===null||F===void 0?void 0:F.call(n,t.focusedKey)),B!=null&&(y.preventDefault(),S(B,m==="rtl"?"last":"first"))}break;case"Home":if(n.getFirstKey){if(t.focusedKey===null&&y.shiftKey)return;y.preventDefault();let B=n.getFirstKey(t.focusedKey,At(y));t.setFocusedKey(B),B!=null&&(At(y)&&y.shiftKey&&t.selectionMode==="multiple"?t.extendSelection(B):u&&t.replaceSelection(B))}break;case"End":if(n.getLastKey){if(t.focusedKey===null&&y.shiftKey)return;y.preventDefault();let B=n.getLastKey(t.focusedKey,At(y));t.setFocusedKey(B),B!=null&&(At(y)&&y.shiftKey&&t.selectionMode==="multiple"?t.extendSelection(B):u&&t.replaceSelection(B))}break;case"PageDown":if(n.getKeyPageBelow&&t.focusedKey!=null){let B=n.getKeyPageBelow(t.focusedKey);B!=null&&(y.preventDefault(),S(B))}break;case"PageUp":if(n.getKeyPageAbove&&t.focusedKey!=null){let B=n.getKeyPageAbove(t.focusedKey);B!=null&&(y.preventDefault(),S(B))}break;case"a":At(y)&&t.selectionMode==="multiple"&&a!==!0&&(y.preventDefault(),t.selectAll());break;case"Escape":s==="clearSelection"&&!i&&t.selectedKeys.size!==0&&(y.stopPropagation(),y.preventDefault(),t.clearSelection());break;case"Tab":if(!p){if(y.shiftKey)r.current.focus();else{let B=et(r.current,{tabbable:!0}),j,oe;do oe=B.lastChild(),oe&&(j=oe);while(oe);j&&!j.contains(document.activeElement)&&je(j)}break}}},C=d.useRef({top:0,left:0});on(v,"scroll",h?void 0:()=>{var y,M,S,I;C.current={top:(S=(y=v.current)===null||y===void 0?void 0:y.scrollTop)!==null&&S!==void 0?S:0,left:(I=(M=v.current)===null||M===void 0?void 0:M.scrollLeft)!==null&&I!==void 0?I:0}});let w=y=>{if(t.isFocused){y.currentTarget.contains(y.target)||t.setFocused(!1);return}if(y.currentTarget.contains(y.target)){if(t.setFocused(!0),t.focusedKey==null){var M,S;let H=W=>{W!=null&&(t.setFocusedKey(W),u&&!t.isSelected(W)&&t.replaceSelection(W))},V=y.relatedTarget;var I,_;V&&y.currentTarget.compareDocumentPosition(V)&Node.DOCUMENT_POSITION_FOLLOWING?H((I=t.lastSelectedKey)!==null&&I!==void 0?I:(M=n.getLastKey)===null||M===void 0?void 0:M.call(n)):H((_=t.firstSelectedKey)!==null&&_!==void 0?_:(S=n.getFirstKey)===null||S===void 0?void 0:S.call(n))}else!h&&v.current&&(v.current.scrollTop=C.current.top,v.current.scrollLeft=C.current.left);if(t.focusedKey!=null&&v.current){let H=gn(r,t.focusedKey);H instanceof HTMLElement&&(!H.contains(document.activeElement)&&!f&&je(H),Sn()==="keyboard"&&go(H,{containingElement:r.current}))}}},A=y=>{y.currentTarget.contains(y.relatedTarget)||t.setFocused(!1)},k=d.useRef(!1);on(r,Ec,f?y=>{let{detail:M}=y;y.stopPropagation(),t.setFocused(!0),M?.focusStrategy==="first"&&(k.current=!0)}:void 0);let K=Ee(()=>{var y,M;let S=(M=(y=n.getFirstKey)===null||y===void 0?void 0:y.call(n))!==null&&M!==void 0?M:null;if(S==null){let I=ke();gi(r.current),Zr(I,null),t.collection.size>0&&(k.current=!1)}else t.setFocusedKey(S),k.current=!1});ho(()=>{k.current&&K()},[t.collection,K]);let O=Ee(()=>{t.collection.size>0&&(k.current=!1)});ho(()=>{O()},[t.focusedKey,O]),on(r,Cc,f?y=>{var M;y.stopPropagation(),t.setFocused(!1),!((M=y.detail)===null||M===void 0)&&M.clearFocusKey&&t.setFocusedKey(null)}:void 0);const z=d.useRef(o),N=d.useRef(!1);d.useEffect(()=>{if(z.current){var y,M;let _=null;var S;o==="first"&&(_=(S=(y=n.getFirstKey)===null||y===void 0?void 0:y.call(n))!==null&&S!==void 0?S:null);var I;o==="last"&&(_=(I=(M=n.getLastKey)===null||M===void 0?void 0:M.call(n))!==null&&I!==void 0?I:null);let H=t.selectedKeys;if(H.size){for(let V of H)if(t.canSelectItem(V)){_=V;break}}t.setFocused(!0),t.setFocusedKey(_),_==null&&!f&&r.current&&mt(r.current),t.collection.size>0&&(z.current=!1,N.current=!0)}});let x=d.useRef(t.focusedKey),P=d.useRef(null);d.useEffect(()=>{if(t.isFocused&&t.focusedKey!=null&&(t.focusedKey!==x.current||N.current)&&v.current&&r.current){let y=Sn(),M=gn(r,t.focusedKey);if(!(M instanceof HTMLElement))return;(y==="keyboard"||N.current)&&(P.current&&cancelAnimationFrame(P.current),P.current=requestAnimationFrame(()=>{v.current&&(Dl(v.current,M),y!=="virtual"&&go(M,{containingElement:r.current}))}))}!f&&t.isFocused&&t.focusedKey==null&&x.current!=null&&r.current&&mt(r.current),x.current=t.focusedKey,N.current=!1}),d.useEffect(()=>()=>{P.current&&cancelAnimationFrame(P.current)},[]),on(r,"react-aria-focus-scope-restore",y=>{y.preventDefault(),t.setFocused(!0)});let R={onKeyDown:$,onFocus:w,onBlur:A,onMouseDown(y){v.current===y.target&&y.preventDefault()}},{typeSelectProps:E}=dp({keyboardDelegate:n,selectionManager:t});c||(R=Se(E,R));let L;f||(L=t.focusedKey==null?0:-1);let T=sp(t.collection);return{collectionProps:Se(R,{tabIndex:L,"data-collection":T})}}function pp(e){let{id:t,selectionManager:n,key:r,ref:o,shouldSelectOnPressUp:l,shouldUseVirtualFocus:i,focus:a,isDisabled:s,onAction:u,allowsDifferentPressOrigin:c,linkBehavior:f="action"}=e,p=Bn();t=qe(t);let h=S=>{if(S.pointerType==="keyboard"&&Ar(S))n.toggleSelection(r);else{if(n.selectionMode==="none")return;if(n.isLink(r)){if(f==="selection"&&o.current){let I=n.getItemProps(r);p.open(o.current,S,I.href,I.routerOptions),n.setSelectedKeys(n.selectedKeys);return}else if(f==="override"||f==="none")return}n.selectionMode==="single"?n.isSelected(r)&&!n.disallowEmptySelection?n.toggleSelection(r):n.replaceSelection(r):S&&S.shiftKey?n.extendSelection(r):n.selectionBehavior==="toggle"||S&&(At(S)||S.pointerType==="touch"||S.pointerType==="virtual")?n.toggleSelection(r):n.replaceSelection(r)}};d.useEffect(()=>{r===n.focusedKey&&n.isFocused&&(i?gi(o.current):a?a():document.activeElement!==o.current&&o.current&&mt(o.current))},[o,r,n.focusedKey,n.childFocusStrategy,n.isFocused,i]),s=s||n.isDisabled(r);let v={};!i&&!s?v={tabIndex:r===n.focusedKey?0:-1,onFocus(S){S.target===o.current&&n.setFocusedKey(r)}}:s&&(v.onMouseDown=S=>{S.preventDefault()});let b=n.isLink(r)&&f==="override",m=n.isLink(r)&&f!=="selection"&&f!=="none",g=!s&&n.canSelectItem(r)&&!b,$=(u||m)&&!s,C=$&&(n.selectionBehavior==="replace"?!g:!g||n.isEmpty),w=$&&g&&n.selectionBehavior==="replace",A=C||w,k=d.useRef(null),K=A&&g,O=d.useRef(!1),z=d.useRef(!1),N=n.getItemProps(r),x=S=>{u&&u(),m&&o.current&&p.open(o.current,S,N.href,N.routerOptions)},P={ref:o};if(l?(P.onPressStart=S=>{k.current=S.pointerType,O.current=K,S.pointerType==="keyboard"&&(!A||tl())&&h(S)},c?(P.onPressUp=C?void 0:S=>{S.pointerType==="mouse"&&g&&h(S)},P.onPress=C?x:S=>{S.pointerType!=="keyboard"&&S.pointerType!=="mouse"&&g&&h(S)}):P.onPress=S=>{if(C||w&&S.pointerType!=="mouse"){if(S.pointerType==="keyboard"&&!el())return;x(S)}else S.pointerType!=="keyboard"&&g&&h(S)}):(P.onPressStart=S=>{k.current=S.pointerType,O.current=K,z.current=C,g&&(S.pointerType==="mouse"&&!C||S.pointerType==="keyboard"&&(!$||tl()))&&h(S)},P.onPress=S=>{(S.pointerType==="touch"||S.pointerType==="pen"||S.pointerType==="virtual"||S.pointerType==="keyboard"&&A&&el()||S.pointerType==="mouse"&&z.current)&&(A?x(S):g&&h(S))}),v["data-collection"]=up(n.collection),v["data-key"]=r,P.preventFocusOnPress=i,i&&(P=Se(P,{onPressStart(S){S.pointerType!=="touch"&&(n.setFocused(!0),n.setFocusedKey(r))},onPress(S){S.pointerType==="touch"&&(n.setFocused(!0),n.setFocusedKey(r))}})),N)for(let S of["onPressStart","onPressEnd","onPressChange","onPress","onPressUp","onClick"])N[S]&&(P[S]=at(P[S],N[S]));let{pressProps:R,isPressed:E}=qt(P),L=w?S=>{k.current==="mouse"&&(S.stopPropagation(),S.preventDefault(),x(S))}:void 0,{longPressProps:T}=hi({isDisabled:!K,onLongPress(S){S.pointerType==="touch"&&(h(S),n.setSelectionBehavior("toggle"))}}),y=S=>{k.current==="touch"&&O.current&&S.preventDefault()},M=f!=="none"&&n.isLink(r)?S=>{bt.isOpening||S.preventDefault()}:void 0;return{itemProps:Se(v,g||C||i&&!s?R:{},K?T:{},{onDoubleClick:L,onDragStartCapture:y,onClick:M,id:t},i?{onMouseDown:S=>S.preventDefault()}:void 0),isPressed:E,isSelected:n.isSelected(r),isFocused:n.isFocused&&n.focusedKey===r,isDisabled:s,allowsSelection:g,hasAction:A}}function el(){let e=window.event;return e?.key==="Enter"}function tl(){let e=window.event;return e?.key===" "||e?.code==="Space"}class nl{getItemRect(t){let n=this.ref.current;if(!n)return null;let r=t!=null?gn(this.ref,t):null;if(!r)return null;let o=n.getBoundingClientRect(),l=r.getBoundingClientRect();return{x:l.left-o.left+n.scrollLeft,y:l.top-o.top+n.scrollTop,width:l.width,height:l.height}}getContentSize(){let t=this.ref.current;var n,r;return{width:(n=t?.scrollWidth)!==null&&n!==void 0?n:0,height:(r=t?.scrollHeight)!==null&&r!==void 0?r:0}}getVisibleRect(){let t=this.ref.current;var n,r,o,l;return{x:(n=t?.scrollLeft)!==null&&n!==void 0?n:0,y:(r=t?.scrollTop)!==null&&r!==void 0?r:0,width:(o=t?.offsetWidth)!==null&&o!==void 0?o:0,height:(l=t?.offsetHeight)!==null&&l!==void 0?l:0}}constructor(t){this.ref=t}}class aa{isDisabled(t){var n;return this.disabledBehavior==="all"&&(((n=t.props)===null||n===void 0?void 0:n.isDisabled)||this.disabledKeys.has(t.key))}findNextNonDisabled(t,n){let r=t;for(;r!=null;){let o=this.collection.getItem(r);if(o?.type==="item"&&!this.isDisabled(o))return r;r=n(r)}return null}getNextKey(t){let n=t;return n=this.collection.getKeyAfter(n),this.findNextNonDisabled(n,r=>this.collection.getKeyAfter(r))}getPreviousKey(t){let n=t;return n=this.collection.getKeyBefore(n),this.findNextNonDisabled(n,r=>this.collection.getKeyBefore(r))}findKey(t,n,r){let o=t,l=this.layoutDelegate.getItemRect(o);if(!l||o==null)return null;let i=l;do{if(o=n(o),o==null)break;l=this.layoutDelegate.getItemRect(o)}while(l&&r(i,l)&&o!=null);return o}isSameRow(t,n){return t.y===n.y||t.x!==n.x}isSameColumn(t,n){return t.x===n.x||t.y!==n.y}getKeyBelow(t){return this.layout==="grid"&&this.orientation==="vertical"?this.findKey(t,n=>this.getNextKey(n),this.isSameRow):this.getNextKey(t)}getKeyAbove(t){return this.layout==="grid"&&this.orientation==="vertical"?this.findKey(t,n=>this.getPreviousKey(n),this.isSameRow):this.getPreviousKey(t)}getNextColumn(t,n){return n?this.getPreviousKey(t):this.getNextKey(t)}getKeyRightOf(t){let n=this.direction==="ltr"?"getKeyRightOf":"getKeyLeftOf";return this.layoutDelegate[n]?(t=this.layoutDelegate[n](t),this.findNextNonDisabled(t,r=>this.layoutDelegate[n](r))):this.layout==="grid"?this.orientation==="vertical"?this.getNextColumn(t,this.direction==="rtl"):this.findKey(t,r=>this.getNextColumn(r,this.direction==="rtl"),this.isSameColumn):this.orientation==="horizontal"?this.getNextColumn(t,this.direction==="rtl"):null}getKeyLeftOf(t){let n=this.direction==="ltr"?"getKeyLeftOf":"getKeyRightOf";return this.layoutDelegate[n]?(t=this.layoutDelegate[n](t),this.findNextNonDisabled(t,r=>this.layoutDelegate[n](r))):this.layout==="grid"?this.orientation==="vertical"?this.getNextColumn(t,this.direction==="ltr"):this.findKey(t,r=>this.getNextColumn(r,this.direction==="ltr"),this.isSameColumn):this.orientation==="horizontal"?this.getNextColumn(t,this.direction==="ltr"):null}getFirstKey(){let t=this.collection.getFirstKey();return this.findNextNonDisabled(t,n=>this.collection.getKeyAfter(n))}getLastKey(){let t=this.collection.getLastKey();return this.findNextNonDisabled(t,n=>this.collection.getKeyBefore(n))}getKeyPageAbove(t){let n=this.ref.current,r=this.layoutDelegate.getItemRect(t);if(!r)return null;if(n&&!Ht(n))return this.getFirstKey();let o=t;if(this.orientation==="horizontal"){let l=Math.max(0,r.x+r.width-this.layoutDelegate.getVisibleRect().width);for(;r&&r.x>l&&o!=null;)o=this.getKeyAbove(o),r=o==null?null:this.layoutDelegate.getItemRect(o)}else{let l=Math.max(0,r.y+r.height-this.layoutDelegate.getVisibleRect().height);for(;r&&r.y>l&&o!=null;)o=this.getKeyAbove(o),r=o==null?null:this.layoutDelegate.getItemRect(o)}return o??this.getFirstKey()}getKeyPageBelow(t){let n=this.ref.current,r=this.layoutDelegate.getItemRect(t);if(!r)return null;if(n&&!Ht(n))return this.getLastKey();let o=t;if(this.orientation==="horizontal"){let l=Math.min(this.layoutDelegate.getContentSize().width,r.y-r.width+this.layoutDelegate.getVisibleRect().width);for(;r&&r.x<l&&o!=null;)o=this.getKeyBelow(o),r=o==null?null:this.layoutDelegate.getItemRect(o)}else{let l=Math.min(this.layoutDelegate.getContentSize().height,r.y-r.height+this.layoutDelegate.getVisibleRect().height);for(;r&&r.y<l&&o!=null;)o=this.getKeyBelow(o),r=o==null?null:this.layoutDelegate.getItemRect(o)}return o??this.getLastKey()}getKeyForSearch(t,n){if(!this.collator)return null;let r=this.collection,o=n||this.getFirstKey();for(;o!=null;){let l=r.getItem(o);if(!l)return null;let i=l.textValue.slice(0,t.length);if(l.textValue&&this.collator.compare(i,t)===0)return o;o=this.getNextKey(o)}return null}constructor(...t){if(t.length===1){let n=t[0];this.collection=n.collection,this.ref=n.ref,this.collator=n.collator,this.disabledKeys=n.disabledKeys||new Set,this.disabledBehavior=n.disabledBehavior||"all",this.orientation=n.orientation||"vertical",this.direction=n.direction,this.layout=n.layout||"stack",this.layoutDelegate=n.layoutDelegate||new nl(n.ref)}else this.collection=t[0],this.disabledKeys=t[1],this.ref=t[2],this.collator=t[3],this.layout="stack",this.orientation="vertical",this.disabledBehavior="all",this.layoutDelegate=new nl(this.ref);this.layout==="stack"&&this.orientation==="vertical"&&(this.getKeyLeftOf=void 0,this.getKeyRightOf=void 0)}}function hp(e){let{selectionManager:t,collection:n,disabledKeys:r,ref:o,keyboardDelegate:l,layoutDelegate:i}=e,a=Rl({usage:"search",sensitivity:"base"}),s=t.disabledBehavior,u=d.useMemo(()=>l||new aa({collection:n,disabledKeys:r,disabledBehavior:s,ref:o,collator:a,layoutDelegate:i}),[l,i,n,r,o,a,s]),{collectionProps:c}=ia({...e,ref:o,selectionManager:t,keyboardDelegate:u});return{listProps:c}}var bp=e=>D.jsx("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:D.jsx("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})}),vp=e=>{const{isSelected:t,isIndeterminate:n,disableAnimation:r,...o}=e;return D.jsx("svg",{"aria-hidden":"true",className:"fill-current",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,viewBox:"0 0 24 24",width:"1em",...o,children:D.jsx("path",{d:"M18 6L6 18M6 6l12 12"})})},gp=({strokeWidth:e=1.5,...t})=>D.jsx("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:e,viewBox:"0 0 24 24",width:"1em",...t,children:D.jsx("path",{d:"m6 9 6 6 6-6"})}),rl={ease:[.36,.66,.4,1]},sa={scaleSpring:{enter:{transform:"scale(1)",opacity:1,transition:{type:"spring",bounce:0,duration:.2}},exit:{transform:"scale(0.85)",opacity:0,transition:{type:"easeOut",duration:.15}}},scaleSpringOpacity:{initial:{opacity:0,transform:"scale(0.8)"},enter:{opacity:1,transform:"scale(1)",transition:{type:"spring",bounce:0,duration:.3}},exit:{opacity:0,transform:"scale(0.96)",transition:{type:"easeOut",bounce:0,duration:.15}}},fade:{enter:{opacity:1,transition:{duration:.4,ease:rl.ease}},exit:{opacity:0,transition:{duration:.3,ease:rl.ease}}},collapse:{enter:{opacity:1,height:"auto",transition:{height:{type:"spring",bounce:0,duration:.3},opacity:{easings:"ease",duration:.4}}},exit:{opacity:0,height:0,transition:{easings:"ease",duration:.3}}}};class _e extends Set{constructor(t,n,r){super(t),t instanceof _e?(this.anchorKey=n??t.anchorKey,this.currentKey=r??t.currentKey):(this.anchorKey=n??null,this.currentKey=r??null)}}function mp(e,t){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}function yp(e){let{selectionMode:t="none",disallowEmptySelection:n=!1,allowDuplicateSelectionEvents:r,selectionBehavior:o="toggle",disabledBehavior:l="all"}=e,i=d.useRef(!1),[,a]=d.useState(!1),s=d.useRef(null),u=d.useRef(null),[,c]=d.useState(null),f=d.useMemo(()=>ol(e.selectedKeys),[e.selectedKeys]),p=d.useMemo(()=>ol(e.defaultSelectedKeys,new _e),[e.defaultSelectedKeys]),[h,v]=Ft(f,p,e.onSelectionChange),b=d.useMemo(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),[m,g]=d.useState(o);o==="replace"&&m==="toggle"&&typeof h=="object"&&h.size===0&&g("replace");let $=d.useRef(o);return d.useEffect(()=>{o!==$.current&&(g(o),$.current=o)},[o]),{selectionMode:t,disallowEmptySelection:n,selectionBehavior:m,setSelectionBehavior:g,get isFocused(){return i.current},setFocused(C){i.current=C,a(C)},get focusedKey(){return s.current},get childFocusStrategy(){return u.current},setFocusedKey(C,w="first"){s.current=C,u.current=w,c(C)},selectedKeys:h,setSelectedKeys(C){(r||!mp(C,h))&&v(C)},disabledKeys:b,disabledBehavior:l}}function ol(e,t){return e?e==="all"?"all":new _e(e):t}function ua(e){return null}ua.getCollectionNode=function*(t,n){let{childItems:r,title:o,children:l}=t,i=t.title||t.children,a=t.textValue||(typeof i=="string"?i:"")||t["aria-label"]||"";!a&&n?.suppressTextValueWarning,yield{type:"item",props:t,rendered:i,textValue:a,"aria-label":t["aria-label"],hasChildNodes:$p(t),*childNodes(){if(r)for(let s of r)yield{type:"item",value:s};else if(o){let s=[];te.Children.forEach(l,u=>{s.push({type:"item",element:u})}),yield*s}}}};function $p(e){return e.hasChildItems!=null?e.hasChildItems:!!(e.childItems||e.title&&te.Children.count(e.children)>0)}let xp=ua;class wp{build(t,n){return this.context=n,ll(()=>this.iterateCollection(t))}*iterateCollection(t){let{children:n,items:r}=t;if(te.isValidElement(n)&&n.type===te.Fragment)yield*this.iterateCollection({children:n.props.children,items:r});else if(typeof n=="function"){if(!r)throw new Error("props.children was a function but props.items is missing");let o=0;for(let l of r)yield*this.getFullNode({value:l,index:o},{renderer:n}),o++}else{let o=[];te.Children.forEach(n,i=>{i&&o.push(i)});let l=0;for(let i of o){let a=this.getFullNode({element:i,index:l},{});for(let s of a)l++,yield s}}}getKey(t,n,r,o){if(t.key!=null)return t.key;if(n.type==="cell"&&n.key!=null)return`${o}${n.key}`;let l=n.value;if(l!=null){var i;let a=(i=l.key)!==null&&i!==void 0?i:l.id;if(a==null)throw new Error("No key found for item");return a}return o?`${o}.${n.index}`:`$.${n.index}`}getChildState(t,n){return{renderer:n.renderer||t.renderer}}*getFullNode(t,n,r,o){if(te.isValidElement(t.element)&&t.element.type===te.Fragment){let m=[];te.Children.forEach(t.element.props.children,$=>{m.push($)});var l;let g=(l=t.index)!==null&&l!==void 0?l:0;for(const $ of m)yield*this.getFullNode({element:$,index:g++},n,r,o);return}let i=t.element;if(!i&&t.value&&n&&n.renderer){let m=this.cache.get(t.value);if(m&&(!m.shouldInvalidate||!m.shouldInvalidate(this.context))){m.index=t.index,m.parentKey=o?o.key:null,yield m;return}i=n.renderer(t.value)}if(te.isValidElement(i)){let m=i.type;if(typeof m!="function"&&typeof m.getCollectionNode!="function"){let w=i.type;throw new Error(`Unknown element <${w}> in collection.`)}let g=m.getCollectionNode(i.props,this.context);var a;let $=(a=t.index)!==null&&a!==void 0?a:0,C=g.next();for(;!C.done&&C.value;){let w=C.value;t.index=$;var s;let A=(s=w.key)!==null&&s!==void 0?s:null;A==null&&(A=w.element?null:this.getKey(i,t,n,r));let K=[...this.getFullNode({...w,key:A,index:$,wrapper:Cp(t.wrapper,w.wrapper)},this.getChildState(n,w),r?`${r}${i.key}`:i.key,o)];for(let O of K){var u,c;O.value=(c=(u=w.value)!==null&&u!==void 0?u:t.value)!==null&&c!==void 0?c:null,O.value&&this.cache.set(O.value,O);var f;if(t.type&&O.type!==t.type)throw new Error(`Unsupported type <${lr(O.type)}> in <${lr((f=o?.type)!==null&&f!==void 0?f:"unknown parent type")}>. Only <${lr(t.type)}> is supported.`);$++,yield O}C=g.next(K)}return}if(t.key==null||t.type==null)return;let p=this;var h,v;let b={type:t.type,props:t.props,key:t.key,parentKey:o?o.key:null,value:(h=t.value)!==null&&h!==void 0?h:null,level:o?o.level+1:0,index:t.index,rendered:t.rendered,textValue:(v=t.textValue)!==null&&v!==void 0?v:"","aria-label":t["aria-label"],wrapper:t.wrapper,shouldInvalidate:t.shouldInvalidate,hasChildNodes:t.hasChildNodes||!1,childNodes:ll(function*(){if(!t.hasChildNodes||!t.childNodes)return;let m=0;for(let g of t.childNodes()){g.key!=null&&(g.key=`${b.key}${g.key}`);let $=p.getFullNode({...g,index:m},p.getChildState(n,g),b.key,b);for(let C of $)m++,yield C}})};yield b}constructor(){this.cache=new WeakMap}}function ll(e){let t=[],n=null;return{*[Symbol.iterator](){for(let r of t)yield r;n||(n=e());for(let r of n)t.push(r),yield r}}}function Cp(e,t){if(e&&t)return n=>e(t(n));if(e)return e;if(t)return t}function lr(e){return e[0].toUpperCase()+e.slice(1)}function Ep(e,t,n){let r=d.useMemo(()=>new wp,[]),{children:o,items:l,collection:i}=e;return d.useMemo(()=>{if(i)return i;let s=r.build({children:o,items:l},n);return t(s)},[r,o,l,i,n,t])}function jn(e,t){return typeof t.getChildren=="function"?t.getChildren(e.key):e.childNodes}function Sp(e){return Pp(e)}function Pp(e,t){for(let n of e)return n}function ir(e,t,n){if(t.parentKey===n.parentKey)return t.index-n.index;let r=[...il(e,t),t],o=[...il(e,n),n],l=r.slice(0,o.length).findIndex((i,a)=>i!==o[a]);return l!==-1?(t=r[l],n=o[l],t.index-n.index):r.findIndex(i=>i===n)>=0?1:(o.findIndex(i=>i===t)>=0,-1)}function il(e,t){let n=[],r=t;for(;r?.parentKey!=null;)r=e.getItem(r.parentKey),r&&n.unshift(r);return n}const al=new WeakMap;function ca(e){let t=al.get(e);if(t!=null)return t;let n=0,r=o=>{for(let l of o)l.type==="section"?r(jn(l,e)):l.type==="item"&&n++};return r(e),al.set(e,n),n}class Jr{get selectionMode(){return this.state.selectionMode}get disallowEmptySelection(){return this.state.disallowEmptySelection}get selectionBehavior(){return this.state.selectionBehavior}setSelectionBehavior(t){this.state.setSelectionBehavior(t)}get isFocused(){return this.state.isFocused}setFocused(t){this.state.setFocused(t)}get focusedKey(){return this.state.focusedKey}get childFocusStrategy(){return this.state.childFocusStrategy}setFocusedKey(t,n){(t==null||this.collection.getItem(t))&&this.state.setFocusedKey(t,n)}get selectedKeys(){return this.state.selectedKeys==="all"?new Set(this.getSelectAllKeys()):this.state.selectedKeys}get rawSelection(){return this.state.selectedKeys}isSelected(t){if(this.state.selectionMode==="none")return!1;let n=this.getKey(t);return n==null?!1:this.state.selectedKeys==="all"?this.canSelectItem(n):this.state.selectedKeys.has(n)}get isEmpty(){return this.state.selectedKeys!=="all"&&this.state.selectedKeys.size===0}get isSelectAll(){if(this.isEmpty)return!1;if(this.state.selectedKeys==="all")return!0;if(this._isSelectAll!=null)return this._isSelectAll;let t=this.getSelectAllKeys(),n=this.state.selectedKeys;return this._isSelectAll=t.every(r=>n.has(r)),this._isSelectAll}get firstSelectedKey(){let t=null;for(let r of this.state.selectedKeys){let o=this.collection.getItem(r);(!t||o&&ir(this.collection,o,t)<0)&&(t=o)}var n;return(n=t?.key)!==null&&n!==void 0?n:null}get lastSelectedKey(){let t=null;for(let r of this.state.selectedKeys){let o=this.collection.getItem(r);(!t||o&&ir(this.collection,o,t)>0)&&(t=o)}var n;return(n=t?.key)!==null&&n!==void 0?n:null}get disabledKeys(){return this.state.disabledKeys}get disabledBehavior(){return this.state.disabledBehavior}extendSelection(t){if(this.selectionMode==="none")return;if(this.selectionMode==="single"){this.replaceSelection(t);return}let n=this.getKey(t);if(n==null)return;let r;if(this.state.selectedKeys==="all")r=new _e([n],n,n);else{let i=this.state.selectedKeys;var o;let a=(o=i.anchorKey)!==null&&o!==void 0?o:n;r=new _e(i,a,n);var l;for(let s of this.getKeyRange(a,(l=i.currentKey)!==null&&l!==void 0?l:n))r.delete(s);for(let s of this.getKeyRange(n,a))this.canSelectItem(s)&&r.add(s)}this.state.setSelectedKeys(r)}getKeyRange(t,n){let r=this.collection.getItem(t),o=this.collection.getItem(n);return r&&o?ir(this.collection,r,o)<=0?this.getKeyRangeInternal(t,n):this.getKeyRangeInternal(n,t):[]}getKeyRangeInternal(t,n){var r;if(!((r=this.layoutDelegate)===null||r===void 0)&&r.getKeyRange)return this.layoutDelegate.getKeyRange(t,n);let o=[],l=t;for(;l!=null;){let i=this.collection.getItem(l);if(i&&(i.type==="item"||i.type==="cell"&&this.allowsCellSelection)&&o.push(l),l===n)return o;l=this.collection.getKeyAfter(l)}return[]}getKey(t){let n=this.collection.getItem(t);if(!n||n.type==="cell"&&this.allowsCellSelection)return t;for(;n&&n.type!=="item"&&n.parentKey!=null;)n=this.collection.getItem(n.parentKey);return!n||n.type!=="item"?null:n.key}toggleSelection(t){if(this.selectionMode==="none")return;if(this.selectionMode==="single"&&!this.isSelected(t)){this.replaceSelection(t);return}let n=this.getKey(t);if(n==null)return;let r=new _e(this.state.selectedKeys==="all"?this.getSelectAllKeys():this.state.selectedKeys);r.has(n)?r.delete(n):this.canSelectItem(n)&&(r.add(n),r.anchorKey=n,r.currentKey=n),!(this.disallowEmptySelection&&r.size===0)&&this.state.setSelectedKeys(r)}replaceSelection(t){if(this.selectionMode==="none")return;let n=this.getKey(t);if(n==null)return;let r=this.canSelectItem(n)?new _e([n],n,n):new _e;this.state.setSelectedKeys(r)}setSelectedKeys(t){if(this.selectionMode==="none")return;let n=new _e;for(let r of t){let o=this.getKey(r);if(o!=null&&(n.add(o),this.selectionMode==="single"))break}this.state.setSelectedKeys(n)}getSelectAllKeys(){let t=[],n=r=>{for(;r!=null;){if(this.canSelectItem(r)){var o;let i=this.collection.getItem(r);i?.type==="item"&&t.push(r);var l;i?.hasChildNodes&&(this.allowsCellSelection||i.type!=="item")&&n((l=(o=Sp(jn(i,this.collection)))===null||o===void 0?void 0:o.key)!==null&&l!==void 0?l:null)}r=this.collection.getKeyAfter(r)}};return n(this.collection.getFirstKey()),t}selectAll(){!this.isSelectAll&&this.selectionMode==="multiple"&&this.state.setSelectedKeys("all")}clearSelection(){!this.disallowEmptySelection&&(this.state.selectedKeys==="all"||this.state.selectedKeys.size>0)&&this.state.setSelectedKeys(new _e)}toggleSelectAll(){this.isSelectAll?this.clearSelection():this.selectAll()}select(t,n){this.selectionMode!=="none"&&(this.selectionMode==="single"?this.isSelected(t)&&!this.disallowEmptySelection?this.toggleSelection(t):this.replaceSelection(t):this.selectionBehavior==="toggle"||n&&(n.pointerType==="touch"||n.pointerType==="virtual")?this.toggleSelection(t):this.replaceSelection(t))}isSelectionEqual(t){if(t===this.state.selectedKeys)return!0;let n=this.selectedKeys;if(t.size!==n.size)return!1;for(let r of t)if(!n.has(r))return!1;for(let r of n)if(!t.has(r))return!1;return!0}canSelectItem(t){var n;if(this.state.selectionMode==="none"||this.state.disabledKeys.has(t))return!1;let r=this.collection.getItem(t);return!(!r||!(r==null||(n=r.props)===null||n===void 0)&&n.isDisabled||r.type==="cell"&&!this.allowsCellSelection)}isDisabled(t){var n,r;return this.state.disabledBehavior==="all"&&(this.state.disabledKeys.has(t)||!!(!((r=this.collection.getItem(t))===null||r===void 0||(n=r.props)===null||n===void 0)&&n.isDisabled))}isLink(t){var n,r;return!!(!((r=this.collection.getItem(t))===null||r===void 0||(n=r.props)===null||n===void 0)&&n.href)}getItemProps(t){var n;return(n=this.collection.getItem(t))===null||n===void 0?void 0:n.props}withCollection(t){return new Jr(t,this.state,{allowsCellSelection:this.allowsCellSelection,layoutDelegate:this.layoutDelegate||void 0})}constructor(t,n,r){this.collection=t,this.state=n;var o;this.allowsCellSelection=(o=r?.allowsCellSelection)!==null&&o!==void 0?o:!1,this._isSelectAll=null,this.layoutDelegate=r?.layoutDelegate||null}}function Tp(e){let t=ht(e,{enabled:typeof e.elementType=="string"}),n;return e.orientation==="vertical"&&(n="vertical"),e.elementType!=="hr"?{separatorProps:{...t,role:"separator","aria-orientation":n}}:{separatorProps:t}}function Ap(e){const{as:t,className:n,orientation:r,...o}=e;let l=t||"hr";l==="hr"&&r==="vertical"&&(l="div");const{separatorProps:i}=Tp({elementType:typeof l=="string"?l:"hr",orientation:r}),a=d.useMemo(()=>lp({orientation:r,className:n}),[r,n]),s=d.useCallback((u={})=>({className:a,role:"separator","data-orientation":r,...i,...o,...u}),[a,r,i,o]);return{Component:l,getDividerProps:s}}var da=Ze((e,t)=>{const{Component:n,getDividerProps:r}=Ap({...e});return D.jsx(n,{ref:t,...r()})});da.displayName="HeroUI.Divider";var kp=da,Mp=e=>{const t={top:{originY:1},bottom:{originY:0},left:{originX:1},right:{originX:0},"top-start":{originX:0,originY:1},"top-end":{originX:1,originY:1},"bottom-start":{originX:0,originY:0},"bottom-end":{originX:1,originY:0},"right-start":{originX:0,originY:0},"right-end":{originX:0,originY:1},"left-start":{originX:1,originY:0},"left-end":{originX:1,originY:1}};return t?.[e]||{}},Dp=e=>({top:"top",bottom:"bottom",left:"left",right:"right","top-start":"top start","top-end":"top end","bottom-start":"bottom start","bottom-end":"bottom end","left-start":"left top","left-end":"left bottom","right-start":"right top","right-end":"right bottom"})[e],Lp=(e,t)=>{if(t.includes("-")){const[n]=t.split("-");if(n.includes(e))return!1}return!0},sl=(e,t)=>{if(t.includes("-")){const[,n]=t.split("-");return`${e}-${n}`}return e},Nt=new WeakMap,Fe=[];function Fp(e,t=document.body){let n=new Set(e),r=new Set,o=s=>{for(let p of s.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))n.add(p);let u=p=>{if(n.has(p)||p.parentElement&&r.has(p.parentElement)&&p.parentElement.getAttribute("role")!=="row")return NodeFilter.FILTER_REJECT;for(let h of n)if(p.contains(h))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},c=document.createTreeWalker(s,NodeFilter.SHOW_ELEMENT,{acceptNode:u}),f=u(s);if(f===NodeFilter.FILTER_ACCEPT&&l(s),f!==NodeFilter.FILTER_REJECT){let p=c.nextNode();for(;p!=null;)l(p),p=c.nextNode()}},l=s=>{var u;let c=(u=Nt.get(s))!=null?u:0;s.getAttribute("aria-hidden")==="true"&&c===0||(c===0&&s.setAttribute("aria-hidden","true"),r.add(s),Nt.set(s,c+1))};Fe.length&&Fe[Fe.length-1].disconnect(),o(t);let i=new MutationObserver(s=>{for(let u of s)if(!(u.type!=="childList"||u.addedNodes.length===0)&&![...n,...r].some(c=>c.contains(u.target))){for(let c of u.removedNodes)c instanceof Element&&(n.delete(c),r.delete(c));for(let c of u.addedNodes)(c instanceof HTMLElement||c instanceof SVGElement)&&(c.dataset.liveAnnouncer==="true"||c.dataset.reactAriaTopLayer==="true")?n.add(c):c instanceof Element&&o(c)}});i.observe(t,{childList:!0,subtree:!0});let a={visibleNodes:n,hiddenNodes:r,observe(){i.observe(t,{childList:!0,subtree:!0})},disconnect(){i.disconnect()}};return Fe.push(a),()=>{i.disconnect();for(let s of r){let u=Nt.get(s);u!=null&&(u===1?(s.removeAttribute("aria-hidden"),Nt.delete(s)):Nt.set(s,u-1))}a===Fe[Fe.length-1]?(Fe.pop(),Fe.length&&Fe[Fe.length-1].observe()):Fe.splice(Fe.indexOf(a),1)}}function Ip(e){let t=Fe[Fe.length-1];if(t&&!t.visibleNodes.has(e))return t.visibleNodes.add(e),()=>{t.visibleNodes.delete(e)}}var Qr=globalThis?.document?d.useLayoutEffect:d.useEffect,[fb,Rp]=gl({name:"ButtonGroupContext",strict:!1});function Bp(e,t){let{elementType:n="button",isDisabled:r,onPress:o,onPressStart:l,onPressEnd:i,onPressUp:a,onPressChange:s,preventFocusOnPress:u,allowFocusWhenDisabled:c,onClick:f,href:p,target:h,rel:v,type:b="button",allowTextSelectionOnPress:m}=e,g;n==="button"?g={type:b,disabled:r}:g={role:"button",href:n==="a"&&!r?p:void 0,target:n==="a"?h:void 0,type:n==="input"?b:void 0,disabled:n==="input"?r:void 0,"aria-disabled":!r||n==="input"?void 0:r,rel:n==="a"?v:void 0};let{pressProps:$,isPressed:C}=qt({onClick:f,onPressStart:l,onPressEnd:i,onPressUp:a,onPressChange:s,onPress:o,isDisabled:r,preventFocusOnPress:u,allowTextSelectionOnPress:m,ref:t}),{focusableProps:w}=pi(e,t);c&&(w.tabIndex=r?-1:w.tabIndex);let A=Se(w,$,Yt(e,{labelable:!0}));return{isPressed:C,buttonProps:Se(g,A,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}var Kp=()=>vl(()=>import("./index-D_FgxOY2.js"),__vite__mapDeps([0,1,2,3,4,5])).then(e=>e.default),fa=e=>{const{ripples:t=[],motionProps:n,color:r="currentColor",style:o,onClear:l}=e;return D.jsx(D.Fragment,{children:t.map(i=>{const a=mu(.01*i.size,.2,i.size>100?.75:.5);return D.jsx(jr,{features:Kp,children:D.jsx(Wl,{mode:"popLayout",children:D.jsx(Ur.span,{animate:{transform:"scale(2)",opacity:0},className:"heroui-ripple",exit:{opacity:0},initial:{transform:"scale(0)",opacity:.35},style:{position:"absolute",backgroundColor:r,borderRadius:"100%",transformOrigin:"center",pointerEvents:"none",overflow:"hidden",inset:0,zIndex:0,top:i.y,left:i.x,width:`${i.size}px`,height:`${i.size}px`,...o},transition:{duration:a},onAnimationComplete:()=>{l(i.key)},...n})})},i.key)})})};fa.displayName="HeroUI.Ripple";var Np=fa;function Op(e={}){const[t,n]=d.useState([]),r=d.useCallback(l=>{const i=l.target,a=Math.max(i.clientWidth,i.clientHeight);n(s=>[...s,{key:vu(s.length.toString()),size:a,x:l.x-a/2,y:l.y-a/2}])},[]),o=d.useCallback(l=>{n(i=>i.filter(a=>a.key!==l))},[]);return{ripples:t,onClear:o,onPress:r,...e}}function zp(e){var t,n,r,o,l,i,a,s,u;const c=Rp(),f=tt(),p=!!c,{ref:h,as:v,children:b,startContent:m,endContent:g,autoFocus:$,className:C,spinner:w,isLoading:A=!1,disableRipple:k=!1,fullWidth:K=(t=c?.fullWidth)!=null?t:!1,radius:O=c?.radius,size:z=(n=c?.size)!=null?n:"md",color:N=(r=c?.color)!=null?r:"default",variant:x=(o=c?.variant)!=null?o:"solid",disableAnimation:P=(i=(l=c?.disableAnimation)!=null?l:f?.disableAnimation)!=null?i:!1,isDisabled:R=(a=c?.isDisabled)!=null?a:!1,isIconOnly:E=(s=c?.isIconOnly)!=null?s:!1,spinnerPlacement:L="start",onPress:T,onClick:y,...M}=e,S=v||"button",I=typeof S=="string",_=Ve(h),H=(u=k||f?.disableRipple)!=null?u:P,{isFocusVisible:V,isFocused:W,focusProps:re}=Gt({autoFocus:$}),Z=R||A,se=d.useMemo(()=>ap({size:z,color:N,variant:x,radius:O,fullWidth:K,isDisabled:Z,isInGroup:p,disableAnimation:P,isIconOnly:E,className:C}),[z,N,x,O,K,Z,p,E,P,C]),{onPress:$e,onClear:ce,ripples:ne}=Op(),F=d.useCallback(ee=>{H||Z||P||_.current&&$e(ee)},[H,Z,P,_,$e]),{buttonProps:B,isPressed:j}=Bp({elementType:v,isDisabled:Z,onPress:pt(T,F),onClick:y,...M},_),{isHovered:oe,hoverProps:le}=Ut({isDisabled:Z}),fe=d.useCallback((ee={})=>({"data-disabled":U(Z),"data-focus":U(W),"data-pressed":U(j),"data-focus-visible":U(V),"data-hover":U(oe),"data-loading":U(A),...ie(B,re,le,ht(M,{enabled:I}),ht(ee)),className:se}),[A,Z,W,j,I,V,oe,B,re,le,M,se]),Ae=ee=>d.isValidElement(ee)?d.cloneElement(ee,{"aria-hidden":!0,focusable:!1}):null,Me=Ae(m),xe=Ae(g),ve=d.useMemo(()=>({sm:"sm",md:"sm",lg:"md"})[z],[z]),J=d.useCallback(()=>({ripples:ne,onClear:ce}),[ne,ce]);return{Component:S,children:b,domRef:_,spinner:w,styles:se,startContent:Me,endContent:xe,isLoading:A,spinnerPlacement:L,spinnerSize:ve,disableRipple:H,getButtonProps:fe,getRippleProps:J,isIconOnly:E}}function _p(e){var t,n;const[r,o]=xt(e,Xo.variantKeys),l=tt(),i=(n=(t=e?.variant)!=null?t:l?.spinnerVariant)!=null?n:"default",{children:a,className:s,classNames:u,label:c,...f}=r,p=d.useMemo(()=>Xo({...o}),[yt(o)]),h=pe(u?.base,s),v=c||a,b=d.useMemo(()=>v&&typeof v=="string"?v:f["aria-label"]?"":"Loading",[a,v,f["aria-label"]]),m=d.useCallback(()=>({"aria-label":b,className:p.base({class:h}),...f}),[b,p,h,f]);return{label:v,slots:p,classNames:u,variant:i,getSpinnerProps:m}}var pa=Ze((e,t)=>{const{slots:n,classNames:r,label:o,variant:l,getSpinnerProps:i}=_p({...e});return l==="wave"||l==="dots"?D.jsxs("div",{ref:t,...i(),children:[D.jsx("div",{className:n.wrapper({class:r?.wrapper}),children:[...new Array(3)].map((a,s)=>D.jsx("i",{className:n.dots({class:r?.dots}),style:{"--dot-index":s}},`dot-${s}`))}),o&&D.jsx("span",{className:n.label({class:r?.label}),children:o})]}):l==="simple"?D.jsxs("div",{ref:t,...i(),children:[D.jsxs("svg",{className:n.wrapper({class:r?.wrapper}),fill:"none",viewBox:"0 0 24 24",children:[D.jsx("circle",{className:n.circle1({class:r?.circle1}),cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),D.jsx("path",{className:n.circle2({class:r?.circle2}),d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",fill:"currentColor"})]}),o&&D.jsx("span",{className:n.label({class:r?.label}),children:o})]}):l==="spinner"?D.jsxs("div",{ref:t,...i(),children:[D.jsx("div",{className:n.wrapper({class:r?.wrapper}),children:[...new Array(12)].map((a,s)=>D.jsx("i",{className:n.spinnerBars({class:r?.spinnerBars}),style:{"--bar-index":s}},`star-${s}`))}),o&&D.jsx("span",{className:n.label({class:r?.label}),children:o})]}):D.jsxs("div",{ref:t,...i(),children:[D.jsxs("div",{className:n.wrapper({class:r?.wrapper}),children:[D.jsx("i",{className:n.circle1({class:r?.circle1})}),D.jsx("i",{className:n.circle2({class:r?.circle2})})]}),o&&D.jsx("span",{className:n.label({class:r?.label}),children:o})]})});pa.displayName="HeroUI.Spinner";var Vp=pa,ha=Ze((e,t)=>{const{Component:n,domRef:r,children:o,spinnerSize:l,spinner:i=D.jsx(Vp,{color:"current",size:l}),spinnerPlacement:a,startContent:s,endContent:u,isLoading:c,disableRipple:f,getButtonProps:p,getRippleProps:h,isIconOnly:v}=zp({...e,ref:t});return D.jsxs(n,{ref:r,...p(),children:[s,c&&a==="start"&&i,c&&v?null:o,c&&a==="end"&&i,u,!f&&D.jsx(Np,{...h()})]})});ha.displayName="HeroUI.Button";var ul=ha;const ba={badInput:!1,customError:!1,patternMismatch:!1,rangeOverflow:!1,rangeUnderflow:!1,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!0},va={...ba,customError:!0,valid:!1},Ot={isInvalid:!1,validationDetails:ba,validationErrors:[]},ga=d.createContext({}),kr="__formValidationState"+Date.now();function ma(e){if(e[kr]){let{realtimeValidation:t,displayValidation:n,updateValidation:r,resetValidation:o,commitValidation:l}=e[kr];return{realtimeValidation:t,displayValidation:n,updateValidation:r,resetValidation:o,commitValidation:l}}return jp(e)}function jp(e){let{isInvalid:t,validationState:n,name:r,value:o,builtinValidation:l,validate:i,validationBehavior:a="aria"}=e;n&&(t||(t=n==="invalid"));let s=t!==void 0?{isInvalid:t,validationErrors:[],validationDetails:va}:null,u=d.useMemo(()=>{if(!i||o==null)return null;let N=Wp(i,o);return cl(N)},[i,o]);l?.validationDetails.valid&&(l=void 0);let c=d.useContext(ga),f=d.useMemo(()=>r?Array.isArray(r)?r.flatMap(N=>Mr(c[N])):Mr(c[r]):[],[c,r]),[p,h]=d.useState(c),[v,b]=d.useState(!1);c!==p&&(h(c),b(!1));let m=d.useMemo(()=>cl(v?[]:f),[v,f]),g=d.useRef(Ot),[$,C]=d.useState(Ot),w=d.useRef(Ot),A=()=>{if(!k)return;K(!1);let N=u||l||g.current;ar(N,w.current)||(w.current=N,C(N))},[k,K]=d.useState(!1);return d.useEffect(A),{realtimeValidation:s||m||u||l||Ot,displayValidation:a==="native"?s||m||$:s||m||u||l||$,updateValidation(N){a==="aria"&&!ar($,N)?C(N):g.current=N},resetValidation(){let N=Ot;ar(N,w.current)||(w.current=N,C(N)),a==="native"&&K(!1),b(!0)},commitValidation(){a==="native"&&K(!0),b(!0)}}}function Mr(e){return e?Array.isArray(e)?e:[e]:[]}function Wp(e,t){if(typeof e=="function"){let n=e(t);if(n&&typeof n!="boolean")return Mr(n)}return[]}function cl(e){return e.length?{isInvalid:!0,validationErrors:e,validationDetails:va}:null}function ar(e,t){return e===t?!0:!!e&&!!t&&e.isInvalid===t.isInvalid&&e.validationErrors.length===t.validationErrors.length&&e.validationErrors.every((n,r)=>n===t.validationErrors[r])&&Object.entries(e.validationDetails).every(([n,r])=>t.validationDetails[n]===r)}function Hp(e,t,n){let{validationBehavior:r,focus:o}=e;be(()=>{if(r==="native"&&n?.current&&!n.current.disabled){let u=t.realtimeValidation.isInvalid?t.realtimeValidation.validationErrors.join(" ")||"Invalid value.":"";n.current.setCustomValidity(u),n.current.hasAttribute("title")||(n.current.title=""),t.realtimeValidation.isInvalid||t.updateValidation(Gp(n.current))}});let l=d.useRef(!1),i=Ee(()=>{l.current||t.resetValidation()}),a=Ee(u=>{var c;t.displayValidation.isInvalid||t.commitValidation();let f=n==null||(c=n.current)===null||c===void 0?void 0:c.form;if(!u.defaultPrevented&&n&&f&&Yp(f)===n.current){var p;o?o():(p=n.current)===null||p===void 0||p.focus(),Jd("keyboard")}u.preventDefault()}),s=Ee(()=>{t.commitValidation()});d.useEffect(()=>{let u=n?.current;if(!u)return;let c=u.form,f=c?.reset;return c&&(c.reset=()=>{l.current=!window.event||window.event.type==="message"&&window.event.target instanceof MessagePort,f?.call(c),l.current=!1}),u.addEventListener("invalid",a),u.addEventListener("change",s),c?.addEventListener("reset",i),()=>{u.removeEventListener("invalid",a),u.removeEventListener("change",s),c?.removeEventListener("reset",i),c&&(c.reset=f)}},[n,a,s,i,r])}function Up(e){let t=e.validity;return{badInput:t.badInput,customError:t.customError,patternMismatch:t.patternMismatch,rangeOverflow:t.rangeOverflow,rangeUnderflow:t.rangeUnderflow,stepMismatch:t.stepMismatch,tooLong:t.tooLong,tooShort:t.tooShort,typeMismatch:t.typeMismatch,valueMissing:t.valueMissing,valid:t.valid}}function Gp(e){return{isInvalid:!e.validity.valid,validationDetails:Up(e),validationErrors:e.validationMessage?[e.validationMessage]:[]}}function Yp(e){for(let t=0;t<e.elements.length;t++){let n=e.elements[t];if(!n.validity.valid)return n}return null}function ya(e){let{id:t,label:n,"aria-labelledby":r,"aria-label":o,labelElementType:l="label"}=e;t=qe(t);let i=qe(),a={};n&&(r=r?`${i} ${r}`:i,a={id:i,htmlFor:l==="label"?t:void 0});let s=mn({id:t,"aria-label":o,"aria-labelledby":r});return{labelProps:a,fieldProps:s}}function Xp(e){let{description:t,errorMessage:n,isInvalid:r,validationState:o}=e,{labelProps:l,fieldProps:i}=ya(e),a=Wt([!!t,!!n,r,o]),s=Wt([!!t,!!n,r,o]);return i=Se(i,{"aria-describedby":[a,s,e["aria-describedby"]].filter(Boolean).join(" ")||void 0}),{labelProps:l,fieldProps:i,descriptionProps:{id:a},errorMessageProps:{id:s}}}var dl=Symbol("default");function qp(e){const t=d.useRef(null),n=d.useRef(void 0),r=d.useCallback(o=>{if(typeof e=="function"){const l=e,i=l(o);return()=>{typeof i=="function"?i():l(null)}}else if(e)return e.current=o,()=>{e.current=null}},[e]);return d.useMemo(()=>({get current(){return t.current},set current(o){t.current=o,n.current&&(n.current(),n.current=void 0),o!=null&&(n.current=r(o))}}),[r])}function eo(e,t){let n=d.useContext(e);if(t===null)return null;if(n&&typeof n=="object"&&"slots"in n&&n.slots){let r=new Intl.ListFormat().format(Object.keys(n.slots).map(l=>`"${l}"`));if(!t&&!n.slots[dl])throw new Error(`A slot prop is required. Valid slot names are ${r}.`);let o=t||dl;if(!n.slots[o])throw new Error(`Invalid slot "${t}". Valid slot names are ${r}.`);return n.slots[o]}return n}function Zp(e,t,n){let r=eo(n,e.slot)||{},{ref:o,...l}=r,i=qp(d.useMemo(()=>wl(t,o),[t,o])),a=ie(l,e);return"style"in l&&l.style&&"style"in e&&e.style&&(typeof l.style=="function"||typeof e.style=="function"?a.style=s=>{let u=typeof l.style=="function"?l.style(s):l.style,c={...s.defaultStyle,...u},f=typeof e.style=="function"?e.style({...s,defaultStyle:c}):e.style;return{...c,...f}}:a.style={...l.style,...e.style}),[a,i]}var kn=d.createContext(null);d.forwardRef(function(t,n){[t,n]=Zp(t,n,kn);let{validationErrors:r,validationBehavior:o="native",children:l,className:i,...a}=t;const s=d.useMemo(()=>ip({className:i}),[i]);return D.jsx("form",{noValidate:o!=="native",...a,ref:n,className:s,children:D.jsx(kn.Provider,{value:{...t,validationBehavior:o},children:D.jsx(ga.Provider,{value:r??{},children:l})})})});function $a(e){let[t,n]=Ft(e.isOpen,e.defaultOpen||!1,e.onOpenChange);const r=d.useCallback(()=>{n(!0)},[n]),o=d.useCallback(()=>{n(!1)},[n]),l=d.useCallback(()=>{n(!t)},[n,t]);return{isOpen:t,setOpen:n,open:r,close:o,toggle:l}}var Ge=[];function Jp(e,t){const{disableOutsideEvents:n=!0,isDismissable:r=!1,isKeyboardDismissDisabled:o=!1,isOpen:l,onClose:i,shouldCloseOnBlur:a,shouldCloseOnInteractOutside:s}=e;d.useEffect(()=>{if(l&&!Ge.includes(t))return Ge.push(t),()=>{let b=Ge.indexOf(t);b>=0&&Ge.splice(b,1)}},[l,t]);const u=()=>{Ge[Ge.length-1]===t&&i&&i()},c=b=>{(!s||s(b.target))&&(Ge[Ge.length-1]===t&&n&&(b.stopPropagation(),b.preventDefault()),u())},f=b=>{(!s||s(b.target))&&(Ge[Ge.length-1]===t&&n&&(b.stopPropagation(),b.preventDefault()),u())},p=b=>{b.key==="Escape"&&!o&&!b.nativeEvent.isComposing&&(b.stopPropagation(),b.preventDefault(),u())};uf({isDisabled:!(r&&l),onInteractOutside:r&&l?f:void 0,onInteractOutsideStart:c,ref:t});const{focusWithinProps:h}=Zt({isDisabled:!a,onBlurWithin:b=>{!b.relatedTarget||bf(b.relatedTarget)||(!s||s(b.relatedTarget))&&u()}}),v=b=>{b.target===b.currentTarget&&b.preventDefault()};return{overlayProps:{onKeyDown:p,...h},underlayProps:{onPointerDown:v}}}function xa(e,t){let{inputElementType:n="input",isDisabled:r=!1,isRequired:o=!1,isReadOnly:l=!1,type:i="text",validationBehavior:a="aria"}=e,[s,u]=Ft(e.value,e.defaultValue||"",e.onChange),{focusableProps:c}=pi(e,t),f=ma({...e,value:s}),{isInvalid:p,validationErrors:h,validationDetails:v}=f.displayValidation,{labelProps:b,fieldProps:m,descriptionProps:g,errorMessageProps:$}=Xp({...e,isInvalid:p,errorMessage:e.errorMessage||h}),C=Yt(e,{labelable:!0});const w={type:i,pattern:e.pattern};let[A]=d.useState(s);var k;return wc(t,(k=e.defaultValue)!==null&&k!==void 0?k:A,u),Hp(e,f,t),d.useEffect(()=>{if(t.current instanceof Be(t.current).HTMLTextAreaElement){let K=t.current;Object.defineProperty(K,"defaultValue",{get:()=>K.value,set:()=>{},configurable:!0})}},[t]),{labelProps:b,inputProps:Se(C,n==="input"?w:void 0,{disabled:r,readOnly:l,required:o&&a==="native","aria-required":o&&a==="aria"||void 0,"aria-invalid":p||void 0,"aria-errormessage":e["aria-errormessage"],"aria-activedescendant":e["aria-activedescendant"],"aria-autocomplete":e["aria-autocomplete"],"aria-haspopup":e["aria-haspopup"],"aria-controls":e["aria-controls"],value:s,onChange:K=>u(K.target.value),autoComplete:e.autoComplete,autoCapitalize:e.autoCapitalize,maxLength:e.maxLength,minLength:e.minLength,name:e.name,form:e.form,placeholder:e.placeholder,inputMode:e.inputMode,autoCorrect:e.autoCorrect,spellCheck:e.spellCheck,[parseInt(te.version,10)>=17?"enterKeyHint":"enterkeyhint"]:e.enterKeyHint,onCopy:e.onCopy,onCut:e.onCut,onPaste:e.onPaste,onCompositionEnd:e.onCompositionEnd,onCompositionStart:e.onCompositionStart,onCompositionUpdate:e.onCompositionUpdate,onSelect:e.onSelect,onBeforeInput:e.onBeforeInput,onInput:e.onInput,...c,...m}),descriptionProps:g,errorMessageProps:$,isInvalid:p,validationErrors:h,validationDetails:v}}function Qp(e){var t,n,r,o,l,i,a;const s=tt(),{validationBehavior:u}=eo(kn)||{},[c,f]=xt(e,Zo.variantKeys),{ref:p,as:h,type:v,label:b,baseRef:m,wrapperRef:g,description:$,className:C,classNames:w,autoFocus:A,startContent:k,endContent:K,onClear:O,onChange:z,validationState:N,validationBehavior:x=(t=u??s?.validationBehavior)!=null?t:"native",innerWrapperRef:P,onValueChange:R=()=>{},...E}=c,L=d.useCallback(Q=>{R(Q??"")},[R]),[T,y]=d.useState(!1),M=h||"div",S=(r=(n=e.disableAnimation)!=null?n:s?.disableAnimation)!=null?r:!1,I=Ve(p),_=Ve(m),H=Ve(g),V=Ve(P),[W,re]=Ft(c.value,(o=c.defaultValue)!=null?o:"",L),Z=v==="file",se=((a=(i=(l=I?.current)==null?void 0:l.files)==null?void 0:i.length)!=null?a:0)>0,$e=["date","time","month","week","range"].includes(v),ce=!yl(W)||$e||se,ne=ce||T,F=v==="hidden",B=e.isMultiline,j=pe(w?.base,C,ce?"is-filled":""),oe=d.useCallback(()=>{var Q;Z?I.current.value="":re(""),O?.(),(Q=I.current)==null||Q.focus()},[re,O,Z]);Qr(()=>{I.current&&re(I.current.value)},[I.current]);const{labelProps:le,inputProps:fe,isInvalid:Ae,validationErrors:Me,validationDetails:xe,descriptionProps:ve,errorMessageProps:J}=xa({...e,validationBehavior:x,autoCapitalize:e.autoCapitalize,value:W,"aria-label":e.label?e["aria-label"]:bu(e["aria-label"],e.placeholder),inputElementType:B?"textarea":"input",onChange:re},I);Z&&(delete fe.value,delete fe.onChange);const{isFocusVisible:ee,isFocused:ue,focusProps:Re}=Gt({autoFocus:A,isTextInput:!0}),{isHovered:ge,hoverProps:It}=Ut({isDisabled:!!e?.isDisabled}),{isHovered:de,hoverProps:We}=Ut({isDisabled:!!e?.isDisabled}),{focusProps:De,isFocusVisible:wt}=Gt(),{focusWithinProps:Jt}=Zt({onFocusWithinChange:y}),{pressProps:Qt}=qt({isDisabled:!!e?.isDisabled||!!e?.isReadOnly,onPress:oe}),rt=N==="invalid"||Ae,He=Rf({labelPlacement:e.labelPlacement,label:b}),en=typeof c.errorMessage=="function"?c.errorMessage({isInvalid:rt,validationErrors:Me,validationDetails:xe}):c.errorMessage||Me?.join(" "),Ct=!!O||e.isClearable,Hn=!!b||!!$||!!en,ot=!!c.placeholder,Un=!!b,q=!!$||!!en,Y=He==="outside-left",Pe=He==="outside-top",ze=He==="outside"||Y||Pe,Et=He==="inside",tn=I.current?(!I.current.value||I.current.value===""||!W||W==="")&&ot:!1,Je=!!k,Zs=ze?Y||Pe||ot||He==="outside"&&Je:!1,Js=He==="outside"&&!ot&&!Je,we=d.useMemo(()=>Zo({...f,isInvalid:rt,labelPlacement:He,isClearable:Ct,disableAnimation:S}),[yt(f),rt,He,Ct,Je,S]),Qs=d.useCallback((Q={})=>({ref:_,className:we.base({class:j}),"data-slot":"base","data-filled":U(ce||ot||Je||tn||Z),"data-filled-within":U(ne||ot||Je||tn||Z),"data-focus-within":U(T),"data-focus-visible":U(ee),"data-readonly":U(e.isReadOnly),"data-focus":U(ue),"data-hover":U(ge||de),"data-required":U(e.isRequired),"data-invalid":U(rt),"data-disabled":U(e.isDisabled),"data-has-elements":U(Hn),"data-has-helper":U(q),"data-has-label":U(Un),"data-has-value":U(!tn),"data-hidden":U(F),...Jt,...Q}),[we,j,ce,ue,ge,de,rt,q,Un,Hn,tn,Je,T,ee,ne,ot,Jt,F,e.isReadOnly,e.isRequired,e.isDisabled]),eu=d.useCallback((Q={})=>({"data-slot":"label",className:we.label({class:w?.label}),...ie(le,We,Q)}),[we,de,le,w?.label]),no=d.useCallback(Q=>{Q.key==="Escape"&&W&&(Ct||O)&&!e.isReadOnly&&(re(""),O?.())},[W,re,O,Ct,e.isReadOnly]),tu=d.useCallback((Q={})=>({"data-slot":"input","data-filled":U(ce),"data-filled-within":U(ne),"data-has-start-content":U(Je),"data-has-end-content":U(!!K),"data-type":v,className:we.input({class:pe(w?.input,ce?"is-filled":"",B?"pe-0":"",v==="password"?"[&::-ms-reveal]:hidden":"")}),...ie(Re,fe,ht(E,{enabled:!0,labelable:!0,omitEventNames:new Set(Object.keys(fe))}),Q),"aria-readonly":U(e.isReadOnly),onChange:pt(fe.onChange,z),onKeyDown:pt(fe.onKeyDown,Q.onKeyDown,no),ref:I}),[we,W,Re,fe,E,ce,ne,Je,K,w?.input,e.isReadOnly,e.isRequired,z,no]),nu=d.useCallback((Q={})=>({ref:H,"data-slot":"input-wrapper","data-hover":U(ge||de),"data-focus-visible":U(ee),"data-focus":U(ue),className:we.inputWrapper({class:pe(w?.inputWrapper,ce?"is-filled":"")}),...ie(Q,It),onClick:Rt=>{I.current&&Rt.currentTarget===Rt.target&&I.current.focus()},style:{cursor:"text",...Q.style}}),[we,ge,de,ee,ue,W,w?.inputWrapper]),ru=d.useCallback((Q={})=>({...Q,ref:V,"data-slot":"inner-wrapper",onClick:Rt=>{I.current&&Rt.currentTarget===Rt.target&&I.current.focus()},className:we.innerWrapper({class:pe(w?.innerWrapper,Q?.className)})}),[we,w?.innerWrapper]),ou=d.useCallback((Q={})=>({...Q,"data-slot":"main-wrapper",className:we.mainWrapper({class:pe(w?.mainWrapper,Q?.className)})}),[we,w?.mainWrapper]),lu=d.useCallback((Q={})=>({...Q,"data-slot":"helper-wrapper",className:we.helperWrapper({class:pe(w?.helperWrapper,Q?.className)})}),[we,w?.helperWrapper]),iu=d.useCallback((Q={})=>({...Q,...ve,"data-slot":"description",className:we.description({class:pe(w?.description,Q?.className)})}),[we,w?.description]),au=d.useCallback((Q={})=>({...Q,...J,"data-slot":"error-message",className:we.errorMessage({class:pe(w?.errorMessage,Q?.className)})}),[we,J,w?.errorMessage]),su=d.useCallback((Q={})=>({...Q,type:"button",tabIndex:-1,disabled:e.isDisabled,"aria-label":"clear input","data-slot":"clear-button","data-focus-visible":U(wt),className:we.clearButton({class:pe(w?.clearButton,Q?.className)}),...ie(Qt,De)}),[we,wt,Qt,De,w?.clearButton]);return{Component:M,classNames:w,domRef:I,label:b,description:$,startContent:k,endContent:K,labelPlacement:He,isClearable:Ct,hasHelper:q,hasStartContent:Je,isLabelOutside:Zs,isOutsideLeft:Y,isOutsideTop:Pe,isLabelOutsideAsPlaceholder:Js,shouldLabelBeOutside:ze,shouldLabelBeInside:Et,hasPlaceholder:ot,isInvalid:rt,errorMessage:en,getBaseProps:Qs,getLabelProps:eu,getInputProps:tu,getMainWrapperProps:ou,getInputWrapperProps:nu,getInnerWrapperProps:ru,getHelperWrapperProps:lu,getDescriptionProps:iu,getErrorMessageProps:au,getClearButtonProps:su}}var wa=Ze((e,t)=>{const{Component:n,label:r,description:o,isClearable:l,startContent:i,endContent:a,labelPlacement:s,hasHelper:u,isOutsideLeft:c,isOutsideTop:f,shouldLabelBeOutside:p,errorMessage:h,isInvalid:v,getBaseProps:b,getLabelProps:m,getInputProps:g,getInnerWrapperProps:$,getInputWrapperProps:C,getMainWrapperProps:w,getHelperWrapperProps:A,getDescriptionProps:k,getErrorMessageProps:K,getClearButtonProps:O}=Qp({...e,ref:t}),z=r?D.jsx("label",{...m(),children:r}):null,N=d.useMemo(()=>l?D.jsx("button",{...O(),children:a||D.jsx(bp,{})}):a,[l,O]),x=d.useMemo(()=>{const E=v&&h;return!u||!(E||o)?null:D.jsx("div",{...A(),children:E?D.jsx("div",{...K(),children:h}):D.jsx("div",{...k(),children:o})})},[u,v,h,o,A,K,k]),P=d.useMemo(()=>D.jsxs("div",{...$(),children:[i,D.jsx("input",{...g()}),N]}),[i,N,g,$]),R=d.useMemo(()=>p?D.jsxs("div",{...w(),children:[D.jsxs("div",{...C(),children:[!c&&!f?z:null,P]}),x]}):D.jsxs(D.Fragment,{children:[D.jsxs("div",{...C(),children:[z,P]}),x]}),[s,x,p,z,P,h,o,w,C,K,k]);return D.jsxs(n,{...b(),children:[c||f?z:null,R]})});wa.displayName="HeroUI.Input";var eh=wa;function th(e,t){const{groupRef:n,triggerRef:r,popoverRef:o,showArrow:l,offset:i=7,crossOffset:a=0,scrollRef:s,shouldFlip:u,boundaryElement:c,isDismissable:f=!0,shouldCloseOnBlur:p=!0,shouldCloseOnScroll:h=!0,placement:v="top",containerPadding:b,shouldCloseOnInteractOutside:m,isNonModal:g,isKeyboardDismissDisabled:$,updatePositionDeps:C=[],...w}=e,A=g??!0,k=w.trigger==="SubmenuTrigger",{overlayProps:K,underlayProps:O}=Jp({isOpen:t.isOpen,onClose:t.close,shouldCloseOnBlur:p,isDismissable:f||k,isKeyboardDismissDisabled:$,shouldCloseOnInteractOutside:m||(R=>{var E;return!((E=r.current)!=null&&E.contains(R))}),disableOutsideEvents:!A},o),{overlayProps:z,arrowProps:N,placement:x,updatePosition:P}=Nd({...w,shouldFlip:u,crossOffset:a,targetRef:r,overlayRef:o,isOpen:t.isOpen,scrollRef:s,boundaryElement:c,containerPadding:b,placement:Dp(v),offset:l?i+3:i,onClose:A&&!k&&h?t.close:()=>{}});return Qr(()=>{C.length&&P()},C),d.useEffect(()=>{var R,E;if(t.isOpen&&o.current)return A?Ip((R=n?.current)!=null?R:o.current):Fp([(E=n?.current)!=null?E:o.current])},[A,t.isOpen,o,n]),{popoverProps:ie(K,z),arrowProps:N,underlayProps:O,placement:x}}var nh="top";function rh(e){var t,n,r;const o=tt(),[l,i]=xt(e,qo.variantKeys),{as:a,ref:s,children:u,state:c,triggerRef:f,scrollRef:p,defaultOpen:h,onOpenChange:v,isOpen:b,isNonModal:m=!0,shouldFlip:g=!0,containerPadding:$=12,shouldBlockScroll:C=!1,isDismissable:w=!0,shouldCloseOnBlur:A,portalContainer:k,updatePositionDeps:K,dialogProps:O,placement:z=nh,triggerType:N="dialog",showArrow:x=!1,offset:P=7,crossOffset:R=0,boundaryElement:E,isKeyboardDismissDisabled:L,shouldCloseOnInteractOutside:T,shouldCloseOnScroll:y,motionProps:M,className:S,classNames:I,onClose:_,...H}=l,V=a||"div",W=Ve(s),re=d.useRef(null),Z=d.useRef(!1),se=f||re,$e=(n=(t=e.disableAnimation)!=null?t:o?.disableAnimation)!=null?n:!1,ce=$a({isOpen:b,defaultOpen:h,onOpenChange:de=>{v?.(de),de||_?.()}}),ne=c||ce,{popoverProps:F,underlayProps:B,placement:j}=th({triggerRef:se,isNonModal:m,popoverRef:W,placement:z,offset:P,scrollRef:p,isDismissable:w,shouldCloseOnBlur:A,boundaryElement:E,crossOffset:R,shouldFlip:g,containerPadding:$,updatePositionDeps:K,isKeyboardDismissDisabled:L,shouldCloseOnScroll:y,shouldCloseOnInteractOutside:T},ne),oe=d.useMemo(()=>j?Lp(j,z)?j:z:null,[j,z]),{triggerProps:le}=mi({type:N},ne,se),{isFocusVisible:fe,isFocused:Ae,focusProps:Me}=Gt(),xe=d.useMemo(()=>qo({...i}),[yt(i)]),ve=pe(I?.base,S);wf({isDisabled:!(C&&ne.isOpen)});const J=(de={})=>({ref:W,...ie(F,H,de),style:ie(F.style,H.style,de.style)}),ee=(de={})=>({"data-slot":"base","data-open":U(ne.isOpen),"data-focus":U(Ae),"data-arrow":U(x),"data-focus-visible":U(fe),"data-placement":j?sl(j,z):void 0,...ie(Me,O,de),className:xe.base({class:pe(ve)}),style:{outline:"none"}}),ue=d.useCallback((de={})=>({"data-slot":"content","data-open":U(ne.isOpen),"data-arrow":U(x),"data-placement":j?sl(j,z):void 0,className:xe.content({class:pe(I?.content,de.className)})}),[xe,ne.isOpen,x,oe,z,I,j]),Re=d.useCallback(de=>{var We;let De;return de.pointerType==="touch"&&(e?.backdrop==="blur"||e?.backdrop==="opaque")?De=setTimeout(()=>{Z.current=!0},100):Z.current=!0,(We=le.onPress)==null||We.call(le,de),()=>{clearTimeout(De)}},[le?.onPress]),ge=d.useCallback((de={},We=null)=>{const{isDisabled:De,...wt}=de;return{"data-slot":"trigger",...ie({"aria-haspopup":"dialog"},le,wt),onPress:Re,isDisabled:De,className:xe.trigger({class:pe(I?.trigger,de.className),isTriggerDisabled:De}),ref:wl(We,se)}},[ne,le,Re,se]),It=d.useCallback((de={})=>({"data-slot":"backdrop",className:xe.backdrop({class:I?.backdrop}),onClick:We=>{if(!Z.current){We.preventDefault();return}ne.close(),Z.current=!1},...B,...de}),[xe,ne.isOpen,I,B]);return{state:ne,Component:V,children:u,classNames:I,showArrow:x,triggerRef:se,placement:oe,isNonModal:m,popoverRef:W,portalContainer:k,isOpen:ne.isOpen,onClose:ne.close,disableAnimation:$e,shouldBlockScroll:C,backdrop:(r=e.backdrop)!=null?r:"transparent",motionProps:M,getBackdropProps:It,getPopoverProps:J,getTriggerProps:ge,getDialogProps:ee,getContentProps:ue}}function oh(e,t){let{role:n="dialog"}=e,r=Wt();r=e["aria-label"]?void 0:r;let o=d.useRef(!1);return d.useEffect(()=>{if(t.current&&!t.current.contains(document.activeElement)){mt(t.current);let l=setTimeout(()=>{(document.activeElement===t.current||document.activeElement===document.body)&&(o.current=!0,t.current&&(t.current.blur(),mt(t.current)),o.current=!1)},500);return()=>{clearTimeout(l)}}},[t]),If(),{dialogProps:{...Yt(e,{labelable:!0}),role:n,tabIndex:-1,"aria-labelledby":e["aria-labelledby"]||r,onBlur:l=>{o.current&&l.stopPropagation()}},titleProps:{id:r}}}var Ca=()=>vl(()=>import("./index-D_FgxOY2.js"),__vite__mapDeps([0,1,2,3,4,5])).then(e=>e.default),Ea=Ze(({children:e,motionProps:t,placement:n,disableAnimation:r,style:o={},transformOrigin:l={},...i},a)=>{let s=o;return l.originX!==void 0||l.originY!==void 0?s={...s,transformOrigin:l}:n&&(s={...s,...Mp(n==="center"?"top":n)}),r?D.jsx("div",{...i,ref:a,children:e}):D.jsx(jr,{features:Ca,children:D.jsx(Ur.div,{ref:a,animate:"enter",exit:"exit",initial:"initial",style:s,variants:sa.scaleSpringOpacity,...ie(i,t),children:e})})});Ea.displayName="HeroUI.FreeSoloPopoverWrapper";var Sa=Ze(({children:e,transformOrigin:t,disableDialogFocus:n=!1,...r},o)=>{const{Component:l,state:i,placement:a,backdrop:s,portalContainer:u,disableAnimation:c,motionProps:f,isNonModal:p,getPopoverProps:h,getBackdropProps:v,getDialogProps:b,getContentProps:m}=rh({...r,ref:o}),g=d.useRef(null),{dialogProps:$,titleProps:C}=oh({},g),w=b({...!n&&{ref:g},...$}),A=d.useMemo(()=>s==="transparent"?null:c?D.jsx("div",{...v()}):D.jsx(jr,{features:Ca,children:D.jsx(Ur.div,{animate:"enter",exit:"exit",initial:"exit",variants:sa.fade,...v()})}),[s,c,v]);return D.jsxs(Ff,{portalContainer:u,children:[!p&&A,D.jsx(l,{...h(),children:D.jsxs(Ea,{disableAnimation:c,motionProps:f,placement:a,tabIndex:-1,transformOrigin:t,...w,children:[!p&&D.jsx(Wo,{onDismiss:i.close}),D.jsx("div",{...m(),children:typeof e=="function"?e(C):e}),D.jsx(Wo,{onDismiss:i.close})]})})]})});Sa.displayName="HeroUI.FreeSoloPopover";var lh=Sa,Pa={};Pa={longPressMessage:"اضغط مطولاً أو اضغط على Alt + السهم لأسفل لفتح القائمة"};var Ta={};Ta={longPressMessage:"Натиснете продължително или натиснете Alt+ стрелка надолу, за да отворите менюто"};var Aa={};Aa={longPressMessage:"Dlouhým stiskem nebo stisknutím kláves Alt + šipka dolů otevřete nabídku"};var ka={};ka={longPressMessage:"Langt tryk eller tryk på Alt + pil ned for at åbne menuen"};var Ma={};Ma={longPressMessage:"Drücken Sie lange oder drücken Sie Alt + Nach-unten, um das Menü zu öffnen"};var Da={};Da={longPressMessage:"Πιέστε παρατεταμένα ή πατήστε Alt + κάτω βέλος για να ανοίξετε το μενού"};var La={};La={longPressMessage:"Long press or press Alt + ArrowDown to open menu"};var Fa={};Fa={longPressMessage:"Mantenga pulsado o pulse Alt + flecha abajo para abrir el menú"};var Ia={};Ia={longPressMessage:"Menüü avamiseks vajutage pikalt või vajutage klahve Alt + allanool"};var Ra={};Ra={longPressMessage:"Avaa valikko painamalla pohjassa tai näppäinyhdistelmällä Alt + Alanuoli"};var Ba={};Ba={longPressMessage:"Appuyez de manière prolongée ou appuyez sur Alt + Flèche vers le bas pour ouvrir le menu."};var Ka={};Ka={longPressMessage:"לחץ לחיצה ארוכה או הקש Alt + ArrowDown כדי לפתוח את התפריט"};var Na={};Na={longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dolje za otvaranje izbornika"};var Oa={};Oa={longPressMessage:"Nyomja meg hosszan, vagy nyomja meg az Alt + lefele nyíl gombot a menü megnyitásához"};var za={};za={longPressMessage:"Premere a lungo o premere Alt + Freccia giù per aprire il menu"};var _a={};_a={longPressMessage:"長押しまたは Alt+下矢印キーでメニューを開く"};var Va={};Va={longPressMessage:"길게 누르거나 Alt + 아래쪽 화살표를 눌러 메뉴 열기"};var ja={};ja={longPressMessage:"Norėdami atidaryti meniu, nuspaudę palaikykite arba paspauskite „Alt + ArrowDown“."};var Wa={};Wa={longPressMessage:"Lai atvērtu izvēlni, turiet nospiestu vai nospiediet taustiņu kombināciju Alt + lejupvērstā bultiņa"};var Ha={};Ha={longPressMessage:"Langt trykk eller trykk Alt + PilNed for å åpne menyen"};var Ua={};Ua={longPressMessage:"Druk lang op Alt + pijl-omlaag of druk op Alt om het menu te openen"};var Ga={};Ga={longPressMessage:"Naciśnij i przytrzymaj lub naciśnij klawisze Alt + Strzałka w dół, aby otworzyć menu"};var Ya={};Ya={longPressMessage:"Pressione e segure ou pressione Alt + Seta para baixo para abrir o menu"};var Xa={};Xa={longPressMessage:"Prima continuamente ou prima Alt + Seta Para Baixo para abrir o menu"};var qa={};qa={longPressMessage:"Apăsați lung sau apăsați pe Alt + săgeată în jos pentru a deschide meniul"};var Za={};Za={longPressMessage:"Нажмите и удерживайте или нажмите Alt + Стрелка вниз, чтобы открыть меню"};var Ja={};Ja={longPressMessage:"Ponuku otvoríte dlhým stlačením alebo stlačením klávesu Alt + klávesu so šípkou nadol"};var Qa={};Qa={longPressMessage:"Za odprtje menija pritisnite in držite gumb ali pritisnite Alt+puščica navzdol"};var es={};es={longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dole da otvorite meni"};var ts={};ts={longPressMessage:"Håll nedtryckt eller tryck på Alt + pil nedåt för att öppna menyn"};var ns={};ns={longPressMessage:"Menüyü açmak için uzun basın veya Alt + Aşağı Ok tuşuna basın"};var rs={};rs={longPressMessage:"Довго або звичайно натисніть комбінацію клавіш Alt і стрілка вниз, щоб відкрити меню"};var os={};os={longPressMessage:"长按或按 Alt + 向下方向键以打开菜单"};var ls={};ls={longPressMessage:"長按或按 Alt+向下鍵以開啟功能表"};var is={};is={"ar-AE":Pa,"bg-BG":Ta,"cs-CZ":Aa,"da-DK":ka,"de-DE":Ma,"el-GR":Da,"en-US":La,"es-ES":Fa,"et-EE":Ia,"fi-FI":Ra,"fr-FR":Ba,"he-IL":Ka,"hr-HR":Na,"hu-HU":Oa,"it-IT":za,"ja-JP":_a,"ko-KR":Va,"lt-LT":ja,"lv-LV":Wa,"nb-NO":Ha,"nl-NL":Ua,"pl-PL":Ga,"pt-BR":Ya,"pt-PT":Xa,"ro-RO":qa,"ru-RU":Za,"sk-SK":Ja,"sl-SI":Qa,"sr-SP":es,"sv-SE":ts,"tr-TR":ns,"uk-UA":rs,"zh-CN":os,"zh-TW":ls};function ih(e){return e&&e.__esModule?e.default:e}function ah(e,t,n){let{type:r="menu",isDisabled:o,trigger:l="press"}=e,i=qe(),{triggerProps:a,overlayProps:s}=mi({type:r},t,n),u=h=>{if(!o&&!(l==="longPress"&&!h.altKey)&&n&&n.current)switch(h.key){case"Enter":case" ":if(l==="longPress"||h.isDefaultPrevented())return;case"ArrowDown":"continuePropagation"in h||h.stopPropagation(),h.preventDefault(),t.toggle("first");break;case"ArrowUp":"continuePropagation"in h||h.stopPropagation(),h.preventDefault(),t.toggle("last");break;default:"continuePropagation"in h&&h.continuePropagation()}},c=Fr(ih(is),"@react-aria/menu"),{longPressProps:f}=hi({isDisabled:o||l!=="longPress",accessibilityDescription:c.format("longPressMessage"),onLongPressStart(){t.close()},onLongPress(){t.open("first")}}),p={preventFocusOnPress:!0,onPressStart(h){h.pointerType!=="touch"&&h.pointerType!=="keyboard"&&!o&&(je(h.target),t.open(h.pointerType==="virtual"?"first":null))},onPress(h){h.pointerType==="touch"&&!o&&(je(h.target),t.toggle())}};return delete a.onPress,{menuTriggerProps:{...a,...l==="press"?p:f,id:i,onKeyDown:u},menuProps:{...s,"aria-labelledby":i,autoFocus:t.focusStrategy||!0,onClose:t.close}}}var sh=700;function uh(){return Mn()||typeof window>"u"?!1:window.screen.width<=sh}const as=7e3;let Ye=null;function sr(e,t="assertive",n=as){Ye?Ye.announce(e,t,n):(Ye=new ch,(typeof IS_REACT_ACT_ENVIRONMENT=="boolean"?IS_REACT_ACT_ENVIRONMENT:typeof jest<"u")?Ye.announce(e,t,n):setTimeout(()=>{Ye?.isAttached()&&Ye?.announce(e,t,n)},100))}class ch{isAttached(){var t;return(t=this.node)===null||t===void 0?void 0:t.isConnected}createLog(t){let n=document.createElement("div");return n.setAttribute("role","log"),n.setAttribute("aria-live",t),n.setAttribute("aria-relevant","additions"),n}destroy(){this.node&&(document.body.removeChild(this.node),this.node=null)}announce(t,n="assertive",r=as){var o,l;if(!this.node)return;let i=document.createElement("div");typeof t=="object"?(i.setAttribute("role","img"),i.setAttribute("aria-labelledby",t["aria-labelledby"])):i.textContent=t,n==="assertive"?(o=this.assertiveLog)===null||o===void 0||o.appendChild(i):(l=this.politeLog)===null||l===void 0||l.appendChild(i),t!==""&&setTimeout(()=>{i.remove()},r)}clear(t){this.node&&((!t||t==="assertive")&&this.assertiveLog&&(this.assertiveLog.innerHTML=""),(!t||t==="polite")&&this.politeLog&&(this.politeLog.innerHTML=""))}constructor(){this.node=null,this.assertiveLog=null,this.politeLog=null,typeof document<"u"&&(this.node=document.createElement("div"),this.node.dataset.liveAnnouncer="true",Object.assign(this.node.style,{border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"}),this.assertiveLog=this.createLog("assertive"),this.node.appendChild(this.assertiveLog),this.politeLog=this.createLog("polite"),this.node.appendChild(this.politeLog),document.body.prepend(this.node))}}function Tt(e,t,n){let r=n.initialDeps??[],o;return()=>{var l,i,a,s;let u;n.key&&((l=n.debug)!=null&&l.call(n))&&(u=Date.now());const c=e();if(!(c.length!==r.length||c.some((h,v)=>r[v]!==h)))return o;r=c;let p;if(n.key&&((i=n.debug)!=null&&i.call(n))&&(p=Date.now()),o=t(...c),n.key&&((a=n.debug)!=null&&a.call(n))){const h=Math.round((Date.now()-u)*100)/100,v=Math.round((Date.now()-p)*100)/100,b=v/16,m=(g,$)=>{for(g=String(g);g.length<$;)g=" "+g;return g};console.info(`%c⏱ ${m(v,5)} /${m(h,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*b,120))}deg 100% 31%);`,n?.key)}return(s=n?.onChange)==null||s.call(n,o),o}}function ur(e,t){if(e===void 0)throw new Error("Unexpected undefined");return e}const dh=(e,t)=>Math.abs(e-t)<1,fh=(e,t,n)=>{let r;return function(...o){e.clearTimeout(r),r=e.setTimeout(()=>t.apply(this,o),n)}},ph=e=>e,hh=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),r=[];for(let o=t;o<=n;o++)r.push(o);return r},bh=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;const o=i=>{const{width:a,height:s}=i;t({width:Math.round(a),height:Math.round(s)})};if(o(n.getBoundingClientRect()),!r.ResizeObserver)return()=>{};const l=new r.ResizeObserver(i=>{const a=i[0];if(a?.borderBoxSize){const s=a.borderBoxSize[0];if(s){o({width:s.inlineSize,height:s.blockSize});return}}o(n.getBoundingClientRect())});return l.observe(n,{box:"border-box"}),()=>{l.unobserve(n)}},fl={passive:!0},vh=typeof window>"u"?!0:"onscrollend"in window,gh=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;let o=0;const l=e.options.useScrollendEvent&&vh?()=>{}:fh(r,()=>{t(o,!1)},e.options.isScrollingResetDelay),i=u=>()=>{const{horizontal:c,isRtl:f}=e.options;o=c?n.scrollLeft*(f&&-1||1):n.scrollTop,l(),t(o,u)},a=i(!0),s=i(!1);return s(),n.addEventListener("scroll",a,fl),n.addEventListener("scrollend",s,fl),()=>{n.removeEventListener("scroll",a),n.removeEventListener("scrollend",s)}},mh=(e,t,n)=>{if(t?.borderBoxSize){const r=t.borderBoxSize[0];if(r)return Math.round(r[n.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[n.options.horizontal?"width":"height"])},yh=(e,{adjustments:t=0,behavior:n},r)=>{var o,l;const i=e+t;(l=(o=r.scrollElement)==null?void 0:o.scrollTo)==null||l.call(o,{[r.options.horizontal?"left":"top"]:i,behavior:n})};class $h{constructor(t){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let n=null;const r=()=>n||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:n=new this.targetWindow.ResizeObserver(o=>{o.forEach(l=>{this._measureElement(l.target,l)})}));return{disconnect:()=>{var o;(o=r())==null||o.disconnect(),n=null},observe:o=>{var l;return(l=r())==null?void 0:l.observe(o,{box:"border-box"})},unobserve:o=>{var l;return(l=r())==null?void 0:l.unobserve(o)}}})(),this.range=null,this.setOptions=n=>{Object.entries(n).forEach(([r,o])=>{typeof o>"u"&&delete n[r]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:ph,rangeExtractor:hh,onChange:()=>{},measureElement:mh,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,...n}},this.notify=n=>{var r,o;(o=(r=this.options).onChange)==null||o.call(r,this,n)},this.maybeNotify=Tt(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),n=>{this.notify(n)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(n=>n()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var n;const r=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==r){if(this.cleanup(),!r){this.maybeNotify();return}this.scrollElement=r,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=((n=this.scrollElement)==null?void 0:n.window)??null,this.elementsCache.forEach(o=>{this.observer.observe(o)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,o=>{this.scrollRect=o,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(o,l)=>{this.scrollAdjustments=0,this.scrollDirection=l?this.getScrollOffset()<o?"forward":"backward":null,this.scrollOffset=o,this.isScrolling=l,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??(typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(n,r)=>{const o=new Map,l=new Map;for(let i=r-1;i>=0;i--){const a=n[i];if(o.has(a.lane))continue;const s=l.get(a.lane);if(s==null||a.end>s.end?l.set(a.lane,a):a.end<s.end&&o.set(a.lane,!0),o.size===this.options.lanes)break}return l.size===this.options.lanes?Array.from(l.values()).sort((i,a)=>i.end===a.end?i.index-a.index:i.end-a.end)[0]:void 0},this.getMeasurementOptions=Tt(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(n,r,o,l,i)=>(this.pendingMeasuredCacheIndexes=[],{count:n,paddingStart:r,scrollMargin:o,getItemKey:l,enabled:i}),{key:!1}),this.getMeasurements=Tt(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:n,paddingStart:r,scrollMargin:o,getItemKey:l,enabled:i},a)=>{if(!i)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(c=>{this.itemSizeCache.set(c.key,c.size)}));const s=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const u=this.measurementsCache.slice(0,s);for(let c=s;c<n;c++){const f=l(c),p=this.options.lanes===1?u[c-1]:this.getFurthestMeasurement(u,c),h=p?p.end+this.options.gap:r+o,v=a.get(f),b=typeof v=="number"?v:this.options.estimateSize(c),m=h+b,g=p?p.lane:c%this.options.lanes;u[c]={index:c,start:h,size:b,end:m,key:f,lane:g}}return this.measurementsCache=u,u},{key:!1,debug:()=>this.options.debug}),this.calculateRange=Tt(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(n,r,o)=>this.range=n.length>0&&r>0?xh({measurements:n,outerSize:r,scrollOffset:o}):null,{key:!1,debug:()=>this.options.debug}),this.getIndexes=Tt(()=>{let n=null,r=null;const o=this.calculateRange();return o&&(n=o.startIndex,r=o.endIndex),[this.options.rangeExtractor,this.options.overscan,this.options.count,n,r]},(n,r,o,l,i)=>l===null||i===null?[]:n({startIndex:l,endIndex:i,overscan:r,count:o}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=n=>{const r=this.options.indexAttribute,o=n.getAttribute(r);return o?parseInt(o,10):(console.warn(`Missing attribute name '${r}={index}' on measured element.`),-1)},this._measureElement=(n,r)=>{const o=this.indexFromElement(n),l=this.measurementsCache[o];if(!l)return;const i=l.key,a=this.elementsCache.get(i);a!==n&&(a&&this.observer.unobserve(a),this.observer.observe(n),this.elementsCache.set(i,n)),n.isConnected&&this.resizeItem(o,this.options.measureElement(n,r,this))},this.resizeItem=(n,r)=>{const o=this.measurementsCache[n];if(!o)return;const l=this.itemSizeCache.get(o.key)??o.size,i=r-l;i!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(o,i,this):o.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(o.index),this.itemSizeCache=new Map(this.itemSizeCache.set(o.key,r)),this.notify(!1))},this.measureElement=n=>{if(!n){this.elementsCache.forEach((r,o)=>{r.isConnected||(this.observer.unobserve(r),this.elementsCache.delete(o))});return}this._measureElement(n,void 0)},this.getVirtualItems=Tt(()=>[this.getIndexes(),this.getMeasurements()],(n,r)=>{const o=[];for(let l=0,i=n.length;l<i;l++){const a=n[l],s=r[a];o.push(s)}return o},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=n=>{const r=this.getMeasurements();if(r.length!==0)return ur(r[ss(0,r.length-1,o=>ur(r[o]).start,n)])},this.getOffsetForAlignment=(n,r)=>{const o=this.getSize(),l=this.getScrollOffset();r==="auto"&&n>=l+o&&(r="end"),r==="end"&&(n-=o);const i=this.options.horizontal?"scrollWidth":"scrollHeight",s=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[i]:this.scrollElement[i]:0)-o;return Math.max(Math.min(s,n),0)},this.getOffsetForIndex=(n,r="auto")=>{n=Math.max(0,Math.min(n,this.options.count-1));const o=this.measurementsCache[n];if(!o)return;const l=this.getSize(),i=this.getScrollOffset();if(r==="auto")if(o.end>=i+l-this.options.scrollPaddingEnd)r="end";else if(o.start<=i+this.options.scrollPaddingStart)r="start";else return[i,r];const a=o.start-this.options.scrollPaddingStart+(o.size-l)/2;switch(r){case"center":return[this.getOffsetForAlignment(a,r),r];case"end":return[this.getOffsetForAlignment(o.end+this.options.scrollPaddingEnd,r),r];default:return[this.getOffsetForAlignment(o.start-this.options.scrollPaddingStart,r),r]}},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(n,{align:r="start",behavior:o}={})=>{this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(n,r),{adjustments:void 0,behavior:o})},this.scrollToIndex=(n,{align:r="auto",behavior:o}={})=>{n=Math.max(0,Math.min(n,this.options.count-1)),this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const l=this.getOffsetForIndex(n,r);if(!l)return;const[i,a]=l;this._scrollToOffset(i,{adjustments:void 0,behavior:o}),o!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(n))){const[u]=ur(this.getOffsetForIndex(n,a));dh(u,this.getScrollOffset())||this.scrollToIndex(n,{align:a,behavior:o})}else this.scrollToIndex(n,{align:a,behavior:o})}))},this.scrollBy=(n,{behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+n,{adjustments:void 0,behavior:r})},this.getTotalSize=()=>{var n;const r=this.getMeasurements();let o;return r.length===0?o=this.options.paddingStart:o=this.options.lanes===1?((n=r[r.length-1])==null?void 0:n.end)??0:Math.max(...r.slice(-this.options.lanes).map(l=>l.end)),Math.max(o-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(n,{adjustments:r,behavior:o})=>{this.options.scrollToFn(n,{behavior:o,adjustments:r},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(t)}}const ss=(e,t,n,r)=>{for(;e<=t;){const o=(e+t)/2|0,l=n(o);if(l<r)e=o+1;else if(l>r)t=o-1;else return o}return e>0?e-1:0};function xh({measurements:e,outerSize:t,scrollOffset:n}){const r=e.length-1,l=ss(0,r,a=>e[a].start,n);let i=l;for(;i<r&&e[i].end<n+t;)i++;return{startIndex:l,endIndex:i}}const pl=typeof document<"u"?d.useLayoutEffect:d.useEffect;function wh(e){const t=d.useReducer(()=>({}),{})[1],n={...e,onChange:(o,l)=>{var i;l?bl.flushSync(t):t(),(i=e.onChange)==null||i.call(e,o,l)}},[r]=d.useState(()=>new $h(n));return r.setOptions(n),pl(()=>r._didMount(),[]),pl(()=>r._willUpdate()),r}function Ch(e){return wh({observeElementRect:bh,observeElementOffset:gh,scrollToFn:yh,...e})}class Dr{*[Symbol.iterator](){yield*this.iterable}get size(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(t){let n=this.keyMap.get(t);var r;return n&&(r=n.prevKey)!==null&&r!==void 0?r:null}getKeyAfter(t){let n=this.keyMap.get(t);var r;return n&&(r=n.nextKey)!==null&&r!==void 0?r:null}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(t){var n;return(n=this.keyMap.get(t))!==null&&n!==void 0?n:null}at(t){const n=[...this.getKeys()];return this.getItem(n[t])}getChildren(t){let n=this.keyMap.get(t);return n?.childNodes||[]}constructor(t){this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.iterable=t;let n=i=>{if(this.keyMap.set(i.key,i),i.childNodes&&i.type==="section")for(let a of i.childNodes)n(a)};for(let i of t)n(i);let r=null,o=0;for(let[i,a]of this.keyMap)r?(r.nextKey=i,a.prevKey=r.key):(this.firstKey=i,a.prevKey=void 0),a.type==="item"&&(a.index=o++),r=a,r.nextKey=void 0;var l;this.lastKey=(l=r?.key)!==null&&l!==void 0?l:null}}function us(e){let{filter:t,layoutDelegate:n}=e,r=yp(e),o=d.useMemo(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),l=d.useCallback(u=>t?new Dr(t(u)):new Dr(u),[t]),i=d.useMemo(()=>({suppressTextValueWarning:e.suppressTextValueWarning}),[e.suppressTextValueWarning]),a=Ep(e,l,i),s=d.useMemo(()=>new Jr(a,r,{layoutDelegate:n}),[a,r,n]);return Eh(a,s),{collection:a,disabledKeys:o,selectionManager:s}}function Eh(e,t){const n=d.useRef(null);d.useEffect(()=>{if(t.focusedKey!=null&&!e.getItem(t.focusedKey)&&n.current){const c=n.current.getItem(t.focusedKey),f=[...n.current.getKeys()].map(g=>{const $=n.current.getItem(g);return $?.type==="item"?$:null}).filter(g=>g!==null),p=[...e.getKeys()].map(g=>{const $=e.getItem(g);return $?.type==="item"?$:null}).filter(g=>g!==null);var r,o;const h=((r=f?.length)!==null&&r!==void 0?r:0)-((o=p?.length)!==null&&o!==void 0?o:0);var l,i,a;let v=Math.min(h>1?Math.max(((l=c?.index)!==null&&l!==void 0?l:0)-h+1,0):(i=c?.index)!==null&&i!==void 0?i:0,((a=p?.length)!==null&&a!==void 0?a:0)-1),b=null,m=!1;for(;v>=0;){if(!t.isDisabled(p[v].key)){b=p[v];break}if(v<p.length-1&&!m)v++;else{m=!0;var s,u;v>((s=c?.index)!==null&&s!==void 0?s:0)&&(v=(u=c?.index)!==null&&u!==void 0?u:0),v--}}t.setFocusedKey(b?b.key:null)}n.current=e},[e,t])}function Sh(e){var t;let[n,r]=Ft(e.selectedKey,(t=e.defaultSelectedKey)!==null&&t!==void 0?t:null,e.onSelectionChange),o=d.useMemo(()=>n!=null?[n]:[],[n]),{collection:l,disabledKeys:i,selectionManager:a}=us({...e,selectionMode:"single",disallowEmptySelection:!0,allowDuplicateSelectionEvents:!0,selectedKeys:o,onSelectionChange:u=>{if(u==="all")return;var c;let f=(c=u.values().next().value)!==null&&c!==void 0?c:null;f===n&&e.onSelectionChange&&e.onSelectionChange(f),r(f)}}),s=n!=null?l.getItem(n):null;return{collection:l,disabledKeys:i,selectionManager:a,selectedKey:n,setSelectedKey:r,selectedItem:s}}function Ph(e={}){const{domRef:t,isEnabled:n=!0,overflowCheck:r="vertical",visibility:o="auto",offset:l=0,onVisibilityChange:i,updateDeps:a=[]}=e,s=d.useRef(o);d.useEffect(()=>{const u=t?.current;if(!u||!n)return;const c=(h,v,b,m,g)=>{if(o==="auto"){const $=`${m}${xl(g)}Scroll`;v&&b?(u.dataset[$]="true",u.removeAttribute(`data-${m}-scroll`),u.removeAttribute(`data-${g}-scroll`)):(u.dataset[`${m}Scroll`]=v.toString(),u.dataset[`${g}Scroll`]=b.toString(),u.removeAttribute(`data-${m}-${g}-scroll`))}else{const $=v&&b?"both":v?m:b?g:"none";$!==s.current&&(i?.($),s.current=$)}},f=()=>{var h,v;const b=[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}],m=u.querySelector('ul[data-slot="list"]'),g=+((h=m?.getAttribute("data-virtual-scroll-height"))!=null?h:u.scrollHeight),$=+((v=m?.getAttribute("data-virtual-scroll-top"))!=null?v:u.scrollTop);for(const{type:C,prefix:w,suffix:A}of b)if(r===C||r==="both"){const k=C==="vertical"?$>l:u.scrollLeft>l,K=C==="vertical"?$+u.clientHeight+l<g:u.scrollLeft+u.clientWidth+l<u.scrollWidth;c(C,k,K,w,A)}},p=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(h=>{u.removeAttribute(`data-${h}-scroll`)})};return f(),u.addEventListener("scroll",f,!0),o!=="auto"&&(p(),o==="both"?(u.dataset.topBottomScroll=String(r==="vertical"),u.dataset.leftRightScroll=String(r==="horizontal")):(u.dataset.topBottomScroll="false",u.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(h=>{u.dataset[`${h}Scroll`]=String(o===h)}))),()=>{u.removeEventListener("scroll",f,!0),p()}},[...a,n,o,r,i,t])}function Th(e){var t;const[n,r]=xt(e,An.variantKeys),{ref:o,as:l,children:i,className:a,style:s,size:u=40,offset:c=0,visibility:f="auto",isEnabled:p=!0,onVisibilityChange:h,...v}=n,b=l||"div",m=Ve(o);Ph({domRef:m,offset:c,visibility:f,isEnabled:p,onVisibilityChange:h,updateDeps:[i],overflowCheck:(t=e.orientation)!=null?t:"vertical"});const g=d.useMemo(()=>An({...r,className:a}),[yt(r),a]);return{Component:b,styles:g,domRef:m,children:i,getBaseProps:(C={})=>{var w;return{ref:m,className:g,"data-orientation":(w=e.orientation)!=null?w:"vertical",style:{"--scroll-shadow-size":`${u}px`,...s,...C.style},...v,...C}}}}var cs=Ze((e,t)=>{const{Component:n,children:r,getBaseProps:o}=Th({...e,ref:t});return D.jsx(n,{...o(),children:r})});cs.displayName="HeroUI.ScrollShadow";var Ah=cs,kh=xp,pb=kh;const Wn=new WeakMap;function Mh(e){return typeof e=="string"?e.replace(/\s*/g,""):""+e}function ds(e,t){let n=Wn.get(e);if(!n)throw new Error("Unknown list");return`${n.id}-option-${Mh(t)}`}function Dh(e,t,n){let r=Yt(e,{labelable:!0}),o=e.selectionBehavior||"toggle",l=e.linkBehavior||(o==="replace"?"action":"override");o==="toggle"&&l==="action"&&(l="override");let{listProps:i}=hp({...e,ref:n,selectionManager:t.selectionManager,collection:t.collection,disabledKeys:t.disabledKeys,linkBehavior:l}),{focusWithinProps:a}=Zt({onFocusWithin:e.onFocus,onBlurWithin:e.onBlur,onFocusWithinChange:e.onFocusChange}),s=qe(e.id);Wn.set(t,{id:s,shouldUseVirtualFocus:e.shouldUseVirtualFocus,shouldSelectOnPressUp:e.shouldSelectOnPressUp,shouldFocusOnHover:e.shouldFocusOnHover,isVirtualized:e.isVirtualized,onAction:e.onAction,linkBehavior:l});let{labelProps:u,fieldProps:c}=ya({...e,id:s,labelElementType:"span"});return{labelProps:u,listBoxProps:Se(r,a,t.selectionManager.selectionMode==="multiple"?{"aria-multiselectable":"true"}:{},{role:"listbox",...Se(c,i)})}}function Lh(e,t,n){var r,o;let{key:l}=e,i=Wn.get(t);var a;let s=(a=e.isDisabled)!==null&&a!==void 0?a:t.selectionManager.isDisabled(l);var u;let c=(u=e.isSelected)!==null&&u!==void 0?u:t.selectionManager.isSelected(l);var f;let p=(f=e.shouldSelectOnPressUp)!==null&&f!==void 0?f:i?.shouldSelectOnPressUp;var h;let v=(h=e.shouldFocusOnHover)!==null&&h!==void 0?h:i?.shouldFocusOnHover;var b;let m=(b=e.shouldUseVirtualFocus)!==null&&b!==void 0?b:i?.shouldUseVirtualFocus;var g;let $=(g=e.isVirtualized)!==null&&g!==void 0?g:i?.isVirtualized,C=Wt(),w=Wt(),A={role:"option","aria-disabled":s||void 0,"aria-selected":t.selectionManager.selectionMode!=="none"?c:void 0};st()&&Rr()||(A["aria-label"]=e["aria-label"],A["aria-labelledby"]=C,A["aria-describedby"]=w);let k=t.collection.getItem(l);if($){let y=Number(k?.index);A["aria-posinset"]=Number.isNaN(y)?void 0:y+1,A["aria-setsize"]=ca(t.collection)}let K=i?.onAction?()=>{var y;return i==null||(y=i.onAction)===null||y===void 0?void 0:y.call(i,l)}:void 0,O=ds(t,l),{itemProps:z,isPressed:N,isFocused:x,hasAction:P,allowsSelection:R}=pp({selectionManager:t.selectionManager,key:l,ref:n,shouldSelectOnPressUp:p,allowsDifferentPressOrigin:p&&v,isVirtualized:$,shouldUseVirtualFocus:m,isDisabled:s,onAction:K||!(k==null||(r=k.props)===null||r===void 0)&&r.onAction?at(k==null||(o=k.props)===null||o===void 0?void 0:o.onAction,K):void 0,linkBehavior:i?.linkBehavior,id:O}),{hoverProps:E}=Ut({isDisabled:s||!v,onHoverStart(){En()||(t.selectionManager.setFocused(!0),t.selectionManager.setFocusedKey(l))}}),L=Yt(k?.props);delete L.id;let T=hc(k?.props);return{optionProps:{...A,...Se(L,z,E,T),id:O},labelProps:{id:C},descriptionProps:{id:w},isFocused:x,isFocusVisible:x&&t.selectionManager.isFocused&&En(),isSelected:c,isDisabled:s,isPressed:N,allowsSelection:R,hasAction:P}}function Fh(e){let{heading:t,"aria-label":n}=e,r=qe();return{itemProps:{role:"presentation"},headingProps:t?{id:r,role:"presentation"}:{},groupProps:{role:"group","aria-label":n,"aria-labelledby":t?r:void 0}}}function Ih(e){var t;const n=tt(),{ref:r,as:o,state:l,variant:i,color:a,onAction:s,children:u,onSelectionChange:c,disableAnimation:f=(t=n?.disableAnimation)!=null?t:!1,itemClasses:p,className:h,topContent:v,bottomContent:b,emptyContent:m="No items.",hideSelectedIcon:g=!1,hideEmptyContent:$=!1,shouldHighlightOnFocus:C=!1,classNames:w,...A}=e,k=o||"ul",K=typeof k=="string",O=Ve(r),z=us({...e,children:u,onSelectionChange:c}),N=l||z,{listBoxProps:x}=Dh({...e,onAction:s},N,O),P=d.useMemo(()=>rp(),[]),R=pe(w?.base,h);return{Component:k,state:N,variant:i,color:a,slots:P,classNames:w,topContent:v,bottomContent:b,emptyContent:m,hideEmptyContent:$,shouldHighlightOnFocus:C,hideSelectedIcon:g,disableAnimation:f,className:h,itemClasses:p,getBaseProps:(y={})=>({ref:O,"data-slot":"base",className:P.base({class:R}),...ht(A,{enabled:K}),...y}),getListProps:(y={})=>({"data-slot":"list",className:P.list({class:w?.list}),...x,...y}),getEmptyContentProps:(y={})=>({"data-slot":"empty-content",children:m,className:P.emptyContent({class:w?.emptyContent}),...y})}}function Rh(e){const{isSelected:t,disableAnimation:n,...r}=e;return D.jsx("svg",{"aria-hidden":"true","data-selected":t,role:"presentation",viewBox:"0 0 17 18",...r,children:D.jsx("polyline",{fill:"none",points:"1 9 7 14 15 4",stroke:"currentColor",strokeDasharray:22,strokeDashoffset:t?44:66,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,style:n?{}:{transition:"stroke-dashoffset 200ms ease"}})})}function Bh(e){var t,n;const r=tt(),[o,l]=xt(e,Jo.variantKeys),{as:i,item:a,state:s,description:u,startContent:c,endContent:f,isVirtualized:p,selectedIcon:h,className:v,classNames:b,autoFocus:m,onPress:g,onPressUp:$,onPressStart:C,onPressEnd:w,onPressChange:A,onClick:k,shouldHighlightOnFocus:K,hideSelectedIcon:O=!1,isReadOnly:z=!1,...N}=o,x=(n=(t=e.disableAnimation)!=null?t:r?.disableAnimation)!=null?n:!1,P=d.useRef(null),R=i||(e.href?"a":"li"),E=typeof R=="string",{rendered:L,key:T}=a,y=s.disabledKeys.has(T)||e.isDisabled,M=s.selectionManager.selectionMode!=="none",S=uh(),{pressProps:I,isPressed:_}=qt({ref:P,isDisabled:y,onClick:k,onPress:g,onPressUp:$,onPressStart:C,onPressEnd:w,onPressChange:A}),{isHovered:H,hoverProps:V}=Ut({isDisabled:y}),{isFocusVisible:W,focusProps:re}=Gt({autoFocus:m}),{isFocused:Z,isSelected:se,optionProps:$e,labelProps:ce,descriptionProps:ne}=Lh({key:T,isDisabled:y,"aria-label":o["aria-label"],isVirtualized:p},s,P);let F=$e;const B=d.useMemo(()=>Jo({...l,isDisabled:y,disableAnimation:x,hasTitleTextChild:typeof L=="string",hasDescriptionTextChild:typeof u=="string"}),[yt(l),y,x,L,u]),j=pe(b?.base,v);z&&(F=gu(F));const oe=K&&Z||(S?H||_:H||Z&&!W),le=(ve={})=>({ref:P,...ie(F,z?{}:ie(re,I),V,ht(N,{enabled:E}),ve),"data-selectable":U(M),"data-focus":U(Z),"data-hover":U(oe),"data-disabled":U(y),"data-selected":U(se),"data-pressed":U(_),"data-focus-visible":U(W),className:B.base({class:pe(j,ve.className)})}),fe=(ve={})=>({...ie(ce,ve),"data-label":U(!0),className:B.title({class:b?.title})}),Ae=(ve={})=>({...ie(ne,ve),className:B.description({class:b?.description})}),Me=(ve={})=>({...ie(ve),className:B.wrapper({class:b?.wrapper})}),xe=d.useCallback((ve={})=>({"aria-hidden":U(!0),"data-disabled":U(y),className:B.selectedIcon({class:b?.selectedIcon}),...ve}),[y,B,b]);return{Component:R,domRef:P,slots:B,classNames:b,isSelectable:M,isSelected:se,isDisabled:y,rendered:L,description:u,startContent:c,endContent:f,selectedIcon:h,hideSelectedIcon:O,disableAnimation:x,getItemProps:le,getLabelProps:fe,getWrapperProps:Me,getDescriptionProps:Ae,getSelectedIconProps:xe}}var fs=e=>{const{Component:t,rendered:n,description:r,isSelectable:o,isSelected:l,isDisabled:i,selectedIcon:a,startContent:s,endContent:u,hideSelectedIcon:c,disableAnimation:f,getItemProps:p,getLabelProps:h,getWrapperProps:v,getDescriptionProps:b,getSelectedIconProps:m}=Bh(e),g=d.useMemo(()=>{const $=D.jsx(Rh,{disableAnimation:f,isSelected:l});return typeof a=="function"?a({icon:$,isSelected:l,isDisabled:i}):a||$},[a,l,i,f]);return D.jsxs(t,{...p(),children:[s,r?D.jsxs("div",{...v(),children:[D.jsx("span",{...h(),children:n}),D.jsx("span",{...b(),children:r})]}):D.jsx("span",{...h(),children:n}),o&&!c&&D.jsx("span",{...m(),children:g}),u]})};fs.displayName="HeroUI.ListboxItem";var to=fs,ps=Ze(({item:e,state:t,as:n,variant:r,color:o,disableAnimation:l,className:i,classNames:a,hideSelectedIcon:s,showDivider:u=!1,dividerProps:c={},itemClasses:f,title:p,items:h,...v},b)=>{const m=n||"li",g=d.useMemo(()=>op(),[]),$=pe(a?.base,i),C=pe(a?.divider,c?.className),{itemProps:w,headingProps:A,groupProps:k}=Fh({heading:e.rendered,"aria-label":e["aria-label"]});return D.jsxs(m,{"data-slot":"base",...ie(w,v),className:g.base({class:$}),children:[e.rendered&&D.jsx("span",{...A,className:g.heading({class:a?.heading}),"data-slot":"heading",children:e.rendered}),D.jsxs("ul",{...k,className:g.group({class:a?.group}),"data-has-title":!!e.rendered,"data-slot":"group",children:[[...e.childNodes].map(K=>{const{key:O,props:z}=K;let N=D.jsx(to,{classNames:f,color:o,disableAnimation:l,hideSelectedIcon:s,item:K,state:t,variant:r,...z},O);return K.wrapper&&(N=K.wrapper(N)),N}),u&&D.jsx(kp,{as:"li",className:g.divider({class:C}),...c})]})]},e.key)});ps.displayName="HeroUI.ListboxSection";var hs=ps;function Kh(e={}){const{domRef:t,isEnabled:n=!0,overflowCheck:r="vertical",visibility:o="auto",offset:l=0,onVisibilityChange:i,updateDeps:a=[]}=e,s=d.useRef(o);d.useEffect(()=>{const u=t?.current;if(!u||!n)return;const c=(h,v,b,m,g)=>{if(o==="auto"){const $=`${m}${xl(g)}Scroll`;v&&b?(u.dataset[$]="true",u.removeAttribute(`data-${m}-scroll`),u.removeAttribute(`data-${g}-scroll`)):(u.dataset[`${m}Scroll`]=v.toString(),u.dataset[`${g}Scroll`]=b.toString(),u.removeAttribute(`data-${m}-${g}-scroll`))}else{const $=v&&b?"both":v?m:b?g:"none";$!==s.current&&(i?.($),s.current=$)}},f=()=>{var h,v;const b=[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}],m=u.querySelector('ul[data-slot="list"]'),g=+((h=m?.getAttribute("data-virtual-scroll-height"))!=null?h:u.scrollHeight),$=+((v=m?.getAttribute("data-virtual-scroll-top"))!=null?v:u.scrollTop);for(const{type:C,prefix:w,suffix:A}of b)if(r===C||r==="both"){const k=C==="vertical"?$>l:u.scrollLeft>l,K=C==="vertical"?$+u.clientHeight+l<g:u.scrollLeft+u.clientWidth+l<u.scrollWidth;c(C,k,K,w,A)}},p=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(h=>{u.removeAttribute(`data-${h}-scroll`)})};return f(),u.addEventListener("scroll",f,!0),o!=="auto"&&(p(),o==="both"?(u.dataset.topBottomScroll=String(r==="vertical"),u.dataset.leftRightScroll=String(r==="horizontal")):(u.dataset.topBottomScroll="false",u.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(h=>{u.dataset[`${h}Scroll`]=String(o===h)}))),()=>{u.removeEventListener("scroll",f,!0),p()}},[...a,n,o,r,i,t])}function Nh(e){var t;const[n,r]=xt(e,An.variantKeys),{ref:o,as:l,children:i,className:a,style:s,size:u=40,offset:c=0,visibility:f="auto",isEnabled:p=!0,onVisibilityChange:h,...v}=n,b=l||"div",m=Ve(o);Kh({domRef:m,offset:c,visibility:f,isEnabled:p,onVisibilityChange:h,updateDeps:[i],overflowCheck:(t=e.orientation)!=null?t:"vertical"});const g=d.useMemo(()=>An({...r,className:a}),[yt(r),a]);return{Component:b,styles:g,domRef:m,children:i,getBaseProps:(C={})=>{var w;return{ref:m,className:g,"data-orientation":(w=e.orientation)!=null?w:"vertical",style:{"--scroll-shadow-size":`${u}px`,...s,...C.style},...v,...C}}}}var Oh=(e,t)=>{const n=[];for(const r of e)r.type==="section"?n.push(([...r.childNodes].length+1)*t):n.push(t);return n},zh=e=>{if(!e||e.scrollTop===void 0||e.clientHeight===void 0||e.scrollHeight===void 0)return{isTop:!1,isBottom:!1,isMiddle:!1};const t=e.scrollTop===0,n=Math.ceil(e.scrollTop+e.clientHeight)>=e.scrollHeight;return{isTop:t,isBottom:n,isMiddle:!t&&!n}},_h=e=>{var t;const{Component:n,state:r,color:o,variant:l,itemClasses:i,getBaseProps:a,topContent:s,bottomContent:u,hideEmptyContent:c,hideSelectedIcon:f,shouldHighlightOnFocus:p,disableAnimation:h,getEmptyContentProps:v,getListProps:b,scrollShadowProps:m}=e,{virtualization:g}=e;if(!g||!yl(g)&&!g.maxListboxHeight&&!g.itemHeight)throw new Error("You are using a virtualized listbox. VirtualizedListbox requires 'virtualization' props with 'maxListboxHeight' and 'itemHeight' properties. This error might have originated from autocomplete components that use VirtualizedListbox. Please provide these props to use the virtualized listbox.");const{maxListboxHeight:$,itemHeight:C}=g,w=Math.min($,C*r.collection.size),A=d.useRef(null),k=d.useMemo(()=>Oh([...r.collection],C),[r.collection,C]),K=Ch({count:[...r.collection].length,getScrollElement:()=>A.current,estimateSize:L=>k[L]}),O=K.getVirtualItems(),z=K.getTotalSize(),{getBaseProps:N}=Nh({...m}),x=L=>{var T;const y=[...r.collection][L.index];if(!y)return null;const M={color:o,item:y,state:r,variant:l,disableAnimation:h,hideSelectedIcon:f,...y.props},S={position:"absolute",top:0,left:0,width:"100%",height:`${L.size}px`,transform:`translateY(${L.start}px)`};if(y.type==="section")return D.jsx(hs,{...M,itemClasses:i,style:{...S,...M.style}},y.key);let I=D.jsx(to,{...M,classNames:ie(i,(T=y.props)==null?void 0:T.classNames),shouldHighlightOnFocus:p,style:{...S,...M.style}},y.key);return y.wrapper&&(I=y.wrapper(I)),I},[P,R]=d.useState({isTop:!1,isBottom:!0,isMiddle:!1}),E=D.jsxs(n,{...b(),"data-virtual-scroll-height":z,"data-virtual-scroll-top":(t=A?.current)==null?void 0:t.scrollTop,children:[!r.collection.size&&!c&&D.jsx("li",{children:D.jsx("div",{...v()})}),D.jsx("div",{...ht(N()),ref:A,style:{height:$,overflow:"auto"},onScroll:L=>{R(zh(L.target))},children:w>0&&C>0&&D.jsx("div",{style:{height:`${z}px`,width:"100%",position:"relative"},children:O.map(L=>x(L))})})]});return D.jsxs("div",{...a(),children:[s,E,u]})},Vh=_h,jh=Ze(function(t,n){const{isVirtualized:r,...o}=t,l=Ih({...o,ref:n}),{Component:i,state:a,color:s,variant:u,itemClasses:c,getBaseProps:f,topContent:p,bottomContent:h,hideEmptyContent:v,hideSelectedIcon:b,shouldHighlightOnFocus:m,disableAnimation:g,getEmptyContentProps:$,getListProps:C}=l;if(r)return D.jsx(Vh,{...t,...l});const w=D.jsxs(i,{...C(),children:[!a.collection.size&&!v&&D.jsx("li",{children:D.jsx("div",{...$()})}),[...a.collection].map(A=>{var k;const K={color:s,item:A,state:a,variant:u,disableAnimation:g,hideSelectedIcon:b,...A.props};if(A.type==="section")return D.jsx(hs,{...K,itemClasses:c},A.key);let O=D.jsx(to,{...K,classNames:ie(c,(k=A.props)==null?void 0:k.classNames),shouldHighlightOnFocus:m},A.key);return A.wrapper&&(O=A.wrapper(O)),O})]});return D.jsxs("div",{...f(),children:[p,w,h]})}),Wh=jh;function Hh(e){var t;let{defaultFilter:n,menuTrigger:r="input",allowsEmptyCollection:o=!1,allowsCustomValue:l,shouldCloseOnBlur:i=!0}=e,[a,s]=d.useState(!1),[u,c]=d.useState(!1),[f,p]=d.useState(null),h=J=>{e.onSelectionChange&&e.onSelectionChange(J),J===g&&(V(),I())};var v;let{collection:b,selectionManager:m,selectedKey:g,setSelectedKey:$,selectedItem:C,disabledKeys:w}=Sh({...e,onSelectionChange:h,items:(v=e.items)!==null&&v!==void 0?v:e.defaultItems}),[A,k]=Ft(e.inputValue,hl(e.defaultInputValue,g,b)||"",e.onInputChange),[K]=d.useState(g),[O]=d.useState(A),z=b,N=d.useMemo(()=>e.items!=null||!n?b:Uh(b,A,n),[b,A,n,e.items]),[x,P]=d.useState(N),R=d.useRef("focus"),L=$a({...e,onOpenChange:J=>{e.onOpenChange&&e.onOpenChange(J,J?R.current:void 0),m.setFocused(J),J||m.setFocusedKey(null)},isOpen:void 0,defaultOpen:void 0}),T=(J=null,ee)=>{let ue=ee==="manual"||ee==="focus"&&r==="focus";(o||N.size>0||ue&&z.size>0||e.items)&&(ue&&!L.isOpen&&e.items===void 0&&s(!0),R.current=ee,p(J),L.open())},y=(J=null,ee)=>{let ue=ee==="manual"||ee==="focus"&&r==="focus";!(o||N.size>0||ue&&z.size>0||e.items)&&!L.isOpen||(ue&&!L.isOpen&&e.items===void 0&&s(!0),L.isOpen||(R.current=ee),S(J))},M=d.useCallback(()=>{P(a?z:N)},[a,z,N]),S=d.useCallback((J=null)=>{L.isOpen&&M(),p(J),L.toggle()},[L,M]),I=d.useCallback(()=>{L.isOpen&&(M(),L.close())},[L,M]),[_,H]=d.useState(A),V=()=>{var J,ee;let ue=g!=null&&(ee=(J=b.getItem(g))===null||J===void 0?void 0:J.textValue)!==null&&ee!==void 0?ee:"";H(ue),k(ue)};var W,re;let Z=d.useRef((re=(W=e.selectedKey)!==null&&W!==void 0?W:e.defaultSelectedKey)!==null&&re!==void 0?re:null);var se;let $e=d.useRef(g!=null&&(se=(t=b.getItem(g))===null||t===void 0?void 0:t.textValue)!==null&&se!==void 0?se:"");d.useEffect(()=>{var J;u&&(N.size>0||o)&&!L.isOpen&&A!==_&&r!=="manual"&&T(null,"input"),!a&&!o&&L.isOpen&&N.size===0&&I(),g!=null&&g!==Z.current&&I(),A!==_&&(m.setFocusedKey(null),s(!1),A===""&&(e.inputValue===void 0||e.selectedKey===void 0)&&$(null)),g!==Z.current&&(e.inputValue===void 0||e.selectedKey===void 0)?V():_!==A&&H(A);var ee;let ue=g!=null&&(ee=(J=b.getItem(g))===null||J===void 0?void 0:J.textValue)!==null&&ee!==void 0?ee:"";!u&&g!=null&&e.inputValue===void 0&&g===Z.current&&$e.current!==ue&&(H(ue),k(ue)),Z.current=g,$e.current=ue});let ce=ma({...e,value:d.useMemo(()=>({inputValue:A,selectedKey:g}),[A,g])}),ne=()=>{l&&g==null?F():B()},F=()=>{Z.current=null,$(null),I()},B=()=>{if(e.selectedKey!==void 0&&e.inputValue!==void 0){var J,ee;(J=e.onSelectionChange)===null||J===void 0||J.call(e,g);var ue;let Re=g!=null&&(ue=(ee=b.getItem(g))===null||ee===void 0?void 0:ee.textValue)!==null&&ue!==void 0?ue:"";H(Re),I()}else V(),I()};const j=()=>{if(l){var J,ee;const ue=g!=null&&(ee=(J=b.getItem(g))===null||J===void 0?void 0:J.textValue)!==null&&ee!==void 0?ee:"";A===ue?B():F()}else B()};let oe=()=>{L.isOpen&&m.focusedKey!=null?g===m.focusedKey?B():$(m.focusedKey):j()},le=d.useRef(A),fe=J=>{J?(le.current=A,r==="focus"&&!e.isReadOnly&&T(null,"focus")):(i&&j(),A!==le.current&&ce.commitValidation()),c(J)},Ae=d.useMemo(()=>L.isOpen?a?z:N:x,[L.isOpen,z,N,a,x]);var Me;let xe=(Me=e.defaultSelectedKey)!==null&&Me!==void 0?Me:K;var ve;return{...ce,...L,focusStrategy:f,toggle:y,open:T,close:j,selectionManager:m,selectedKey:g,defaultSelectedKey:xe,setSelectedKey:$,disabledKeys:w,isFocused:u,setFocused:fe,selectedItem:C,collection:Ae,inputValue:A,defaultInputValue:(ve=hl(e.defaultInputValue,xe,b))!==null&&ve!==void 0?ve:O,setInputValue:k,commit:oe,revert:ne}}function Uh(e,t,n){return new Dr(bs(e,e,t,n))}function bs(e,t,n,r){let o=[];for(let l of t)if(l.type==="section"&&l.hasChildNodes){let i=bs(e,jn(l,e),n,r);[...i].some(a=>a.type==="item")&&o.push({...l,childNodes:i})}else l.type==="item"&&r(l.textValue,n)?o.push({...l}):l.type!=="item"&&o.push({...l});return o}function hl(e,t,n){if(e==null){var r,o;if(t!=null)return(o=(r=n.getItem(t))===null||r===void 0?void 0:r.textValue)!==null&&o!==void 0?o:""}return e}var vs={};vs={buttonLabel:"عرض المقترحات",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} خيار`,other:()=>`${t.number(e.optionCount)} خيارات`})} متاحة.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`المجموعة المدخلة ${e.groupTitle}, مع ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} خيار`,other:()=>`${t.number(e.groupCount)} خيارات`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", محدد",other:""},e.isSelected)}`,listboxLabel:"مقترحات",selectedAnnouncement:e=>`${e.optionText}، محدد`};var gs={};gs={buttonLabel:"Покажи предложения",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} опция`,other:()=>`${t.number(e.optionCount)} опции`})} на разположение.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Въведена група ${e.groupTitle}, с ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} опция`,other:()=>`${t.number(e.groupCount)} опции`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", избрани",other:""},e.isSelected)}`,listboxLabel:"Предложения",selectedAnnouncement:e=>`${e.optionText}, избрани`};var ms={};ms={buttonLabel:"Zobrazit doporučení",countAnnouncement:(e,t)=>`K dispozici ${t.plural(e.optionCount,{one:()=>`je ${t.number(e.optionCount)} možnost`,other:()=>`jsou/je ${t.number(e.optionCount)} možnosti/-í`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Zadaná skupina „${e.groupTitle}“ ${t.plural(e.groupCount,{one:()=>`s ${t.number(e.groupCount)} možností`,other:()=>`se ${t.number(e.groupCount)} možnostmi`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:" (vybráno)",other:""},e.isSelected)}`,listboxLabel:"Návrhy",selectedAnnouncement:e=>`${e.optionText}, vybráno`};var ys={};ys={buttonLabel:"Vis forslag",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} mulighed tilgængelig`,other:()=>`${t.number(e.optionCount)} muligheder tilgængelige`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Angivet gruppe ${e.groupTitle}, med ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} mulighed`,other:()=>`${t.number(e.groupCount)} muligheder`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", valgt",other:""},e.isSelected)}`,listboxLabel:"Forslag",selectedAnnouncement:e=>`${e.optionText}, valgt`};var $s={};$s={buttonLabel:"Empfehlungen anzeigen",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} Option`,other:()=>`${t.number(e.optionCount)} Optionen`})} verfügbar.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Eingetretene Gruppe ${e.groupTitle}, mit ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} Option`,other:()=>`${t.number(e.groupCount)} Optionen`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", ausgewählt",other:""},e.isSelected)}`,listboxLabel:"Empfehlungen",selectedAnnouncement:e=>`${e.optionText}, ausgewählt`};var xs={};xs={buttonLabel:"Προβολή προτάσεων",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} επιλογή`,other:()=>`${t.number(e.optionCount)} επιλογές `})} διαθέσιμες.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Εισαγμένη ομάδα ${e.groupTitle}, με ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} επιλογή`,other:()=>`${t.number(e.groupCount)} επιλογές`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", επιλεγμένο",other:""},e.isSelected)}`,listboxLabel:"Προτάσεις",selectedAnnouncement:e=>`${e.optionText}, επιλέχθηκε`};var ws={};ws={focusAnnouncement:(e,t)=>`${t.select({true:()=>`Entered group ${e.groupTitle}, with ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} option`,other:()=>`${t.number(e.groupCount)} options`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", selected",other:""},e.isSelected)}`,countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} option`,other:()=>`${t.number(e.optionCount)} options`})} available.`,selectedAnnouncement:e=>`${e.optionText}, selected`,buttonLabel:"Show suggestions",listboxLabel:"Suggestions"};var Cs={};Cs={buttonLabel:"Mostrar sugerencias",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opción`,other:()=>`${t.number(e.optionCount)} opciones`})} disponible(s).`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Se ha unido al grupo ${e.groupTitle}, con ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opción`,other:()=>`${t.number(e.groupCount)} opciones`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", seleccionado",other:""},e.isSelected)}`,listboxLabel:"Sugerencias",selectedAnnouncement:e=>`${e.optionText}, seleccionado`};var Es={};Es={buttonLabel:"Kuva soovitused",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} valik`,other:()=>`${t.number(e.optionCount)} valikud`})} saadaval.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Sisestatud rühm ${e.groupTitle}, valikuga ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} valik`,other:()=>`${t.number(e.groupCount)} valikud`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", valitud",other:""},e.isSelected)}`,listboxLabel:"Soovitused",selectedAnnouncement:e=>`${e.optionText}, valitud`};var Ss={};Ss={buttonLabel:"Näytä ehdotukset",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} vaihtoehto`,other:()=>`${t.number(e.optionCount)} vaihtoehdot`})} saatavilla.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Mentiin ryhmään ${e.groupTitle}, ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} vaihtoehdon`,other:()=>`${t.number(e.groupCount)} vaihtoehdon`})} kanssa.`,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", valittu",other:""},e.isSelected)}`,listboxLabel:"Ehdotukset",selectedAnnouncement:e=>`${e.optionText}, valittu`};var Ps={};Ps={buttonLabel:"Afficher les suggestions",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} option`,other:()=>`${t.number(e.optionCount)} options`})} disponible(s).`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Groupe ${e.groupTitle} rejoint, avec ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} option`,other:()=>`${t.number(e.groupCount)} options`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", sélectionné(s)",other:""},e.isSelected)}`,listboxLabel:"Suggestions",selectedAnnouncement:e=>`${e.optionText}, sélectionné`};var Ts={};Ts={buttonLabel:"הצג הצעות",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`אפשרות ${t.number(e.optionCount)}`,other:()=>`${t.number(e.optionCount)} אפשרויות`})} במצב זמין.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`נכנס לקבוצה ${e.groupTitle}, עם ${t.plural(e.groupCount,{one:()=>`אפשרות ${t.number(e.groupCount)}`,other:()=>`${t.number(e.groupCount)} אפשרויות`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", נבחר",other:""},e.isSelected)}`,listboxLabel:"הצעות",selectedAnnouncement:e=>`${e.optionText}, נבחר`};var As={};As={buttonLabel:"Prikaži prijedloge",countAnnouncement:(e,t)=>`Dostupno još: ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opcija`,other:()=>`${t.number(e.optionCount)} opcije/a`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Unesena skupina ${e.groupTitle}, s ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opcijom`,other:()=>`${t.number(e.groupCount)} opcije/a`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", odabranih",other:""},e.isSelected)}`,listboxLabel:"Prijedlozi",selectedAnnouncement:e=>`${e.optionText}, odabrano`};var ks={};ks={buttonLabel:"Javaslatok megjelenítése",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} lehetőség`,other:()=>`${t.number(e.optionCount)} lehetőség`})} áll rendelkezésre.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Belépett a(z) ${e.groupTitle} csoportba, amely ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} lehetőséget`,other:()=>`${t.number(e.groupCount)} lehetőséget`})} tartalmaz. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", kijelölve",other:""},e.isSelected)}`,listboxLabel:"Javaslatok",selectedAnnouncement:e=>`${e.optionText}, kijelölve`};var Ms={};Ms={buttonLabel:"Mostra suggerimenti",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opzione disponibile`,other:()=>`${t.number(e.optionCount)} opzioni disponibili`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Ingresso nel gruppo ${e.groupTitle}, con ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opzione`,other:()=>`${t.number(e.groupCount)} opzioni`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", selezionato",other:""},e.isSelected)}`,listboxLabel:"Suggerimenti",selectedAnnouncement:e=>`${e.optionText}, selezionato`};var Ds={};Ds={buttonLabel:"候補を表示",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} 個のオプション`,other:()=>`${t.number(e.optionCount)} 個のオプション`})}を利用できます。`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`入力されたグループ ${e.groupTitle}、${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} 個のオプション`,other:()=>`${t.number(e.groupCount)} 個のオプション`})}を含む。`,other:""},e.isGroupChange)}${e.optionText}${t.select({true:"、選択済み",other:""},e.isSelected)}`,listboxLabel:"候補",selectedAnnouncement:e=>`${e.optionText}、選択済み`};var Ls={};Ls={buttonLabel:"제안 사항 표시",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)}개 옵션`,other:()=>`${t.number(e.optionCount)}개 옵션`})}을 사용할 수 있습니다.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`입력한 그룹 ${e.groupTitle}, ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)}개 옵션`,other:()=>`${t.number(e.groupCount)}개 옵션`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", 선택됨",other:""},e.isSelected)}`,listboxLabel:"제안",selectedAnnouncement:e=>`${e.optionText}, 선택됨`};var Fs={};Fs={buttonLabel:"Rodyti pasiūlymus",countAnnouncement:(e,t)=>`Yra ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} parinktis`,other:()=>`${t.number(e.optionCount)} parinktys (-ių)`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Įvesta grupė ${e.groupTitle}, su ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} parinktimi`,other:()=>`${t.number(e.groupCount)} parinktimis (-ių)`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", pasirinkta",other:""},e.isSelected)}`,listboxLabel:"Pasiūlymai",selectedAnnouncement:e=>`${e.optionText}, pasirinkta`};var Is={};Is={buttonLabel:"Rādīt ieteikumus",countAnnouncement:(e,t)=>`Pieejamo opciju skaits: ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opcija`,other:()=>`${t.number(e.optionCount)} opcijas`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Ievadīta grupa ${e.groupTitle}, ar ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opciju`,other:()=>`${t.number(e.groupCount)} opcijām`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", atlasīta",other:""},e.isSelected)}`,listboxLabel:"Ieteikumi",selectedAnnouncement:e=>`${e.optionText}, atlasīta`};var Rs={};Rs={buttonLabel:"Vis forslag",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} alternativ`,other:()=>`${t.number(e.optionCount)} alternativer`})} finnes.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Angitt gruppe ${e.groupTitle}, med ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} alternativ`,other:()=>`${t.number(e.groupCount)} alternativer`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", valgt",other:""},e.isSelected)}`,listboxLabel:"Forslag",selectedAnnouncement:e=>`${e.optionText}, valgt`};var Bs={};Bs={buttonLabel:"Suggesties weergeven",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} optie`,other:()=>`${t.number(e.optionCount)} opties`})} beschikbaar.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Groep ${e.groupTitle} ingevoerd met ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} optie`,other:()=>`${t.number(e.groupCount)} opties`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", geselecteerd",other:""},e.isSelected)}`,listboxLabel:"Suggesties",selectedAnnouncement:e=>`${e.optionText}, geselecteerd`};var Ks={};Ks={buttonLabel:"Wyświetlaj sugestie",countAnnouncement:(e,t)=>`dostępna/dostępne(-nych) ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opcja`,other:()=>`${t.number(e.optionCount)} opcje(-i)`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Dołączono do grupy ${e.groupTitle}, z ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opcją`,other:()=>`${t.number(e.groupCount)} opcjami`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", wybrano",other:""},e.isSelected)}`,listboxLabel:"Sugestie",selectedAnnouncement:e=>`${e.optionText}, wybrano`};var Ns={};Ns={buttonLabel:"Mostrar sugestões",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opção`,other:()=>`${t.number(e.optionCount)} opções`})} disponível.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Grupo inserido ${e.groupTitle}, com ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opção`,other:()=>`${t.number(e.groupCount)} opções`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", selecionado",other:""},e.isSelected)}`,listboxLabel:"Sugestões",selectedAnnouncement:e=>`${e.optionText}, selecionado`};var Os={};Os={buttonLabel:"Apresentar sugestões",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opção`,other:()=>`${t.number(e.optionCount)} opções`})} disponível.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Grupo introduzido ${e.groupTitle}, com ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opção`,other:()=>`${t.number(e.groupCount)} opções`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", selecionado",other:""},e.isSelected)}`,listboxLabel:"Sugestões",selectedAnnouncement:e=>`${e.optionText}, selecionado`};var zs={};zs={buttonLabel:"Afișare sugestii",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opțiune`,other:()=>`${t.number(e.optionCount)} opțiuni`})} disponibile.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Grup ${e.groupTitle} introdus, cu ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opțiune`,other:()=>`${t.number(e.groupCount)} opțiuni`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", selectat",other:""},e.isSelected)}`,listboxLabel:"Sugestii",selectedAnnouncement:e=>`${e.optionText}, selectat`};var _s={};_s={buttonLabel:"Показать предложения",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} параметр`,other:()=>`${t.number(e.optionCount)} параметров`})} доступно.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Введенная группа ${e.groupTitle}, с ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} параметром`,other:()=>`${t.number(e.groupCount)} параметрами`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", выбранными",other:""},e.isSelected)}`,listboxLabel:"Предложения",selectedAnnouncement:e=>`${e.optionText}, выбрано`};var Vs={};Vs={buttonLabel:"Zobraziť návrhy",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} možnosť`,other:()=>`${t.number(e.optionCount)} možnosti/-í`})} k dispozícii.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Zadaná skupina ${e.groupTitle}, s ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} možnosťou`,other:()=>`${t.number(e.groupCount)} možnosťami`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", vybraté",other:""},e.isSelected)}`,listboxLabel:"Návrhy",selectedAnnouncement:e=>`${e.optionText}, vybraté`};var js={};js={buttonLabel:"Prikaži predloge",countAnnouncement:(e,t)=>`Na voljo je ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opcija`,other:()=>`${t.number(e.optionCount)} opcije`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Vnesena skupina ${e.groupTitle}, z ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opcija`,other:()=>`${t.number(e.groupCount)} opcije`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", izbrano",other:""},e.isSelected)}`,listboxLabel:"Predlogi",selectedAnnouncement:e=>`${e.optionText}, izbrano`};var Ws={};Ws={buttonLabel:"Prikaži predloge",countAnnouncement:(e,t)=>`Dostupno još: ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} opcija`,other:()=>`${t.number(e.optionCount)} opcije/a`})}.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Unesena grupa ${e.groupTitle}, s ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} opcijom`,other:()=>`${t.number(e.groupCount)} optione/a`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", izabranih",other:""},e.isSelected)}`,listboxLabel:"Predlozi",selectedAnnouncement:e=>`${e.optionText}, izabrano`};var Hs={};Hs={buttonLabel:"Visa förslag",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} alternativ`,other:()=>`${t.number(e.optionCount)} alternativ`})} tillgängliga.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Ingick i gruppen ${e.groupTitle} med ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} alternativ`,other:()=>`${t.number(e.groupCount)} alternativ`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", valda",other:""},e.isSelected)}`,listboxLabel:"Förslag",selectedAnnouncement:e=>`${e.optionText}, valda`};var Us={};Us={buttonLabel:"Önerileri göster",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} seçenek`,other:()=>`${t.number(e.optionCount)} seçenekler`})} kullanılabilir.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Girilen grup ${e.groupTitle}, ile ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} seçenek`,other:()=>`${t.number(e.groupCount)} seçenekler`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", seçildi",other:""},e.isSelected)}`,listboxLabel:"Öneriler",selectedAnnouncement:e=>`${e.optionText}, seçildi`};var Gs={};Gs={buttonLabel:"Показати пропозиції",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} параметр`,other:()=>`${t.number(e.optionCount)} параметри(-ів)`})} доступно.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Введена група ${e.groupTitle}, з ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} параметр`,other:()=>`${t.number(e.groupCount)} параметри(-ів)`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", вибрано",other:""},e.isSelected)}`,listboxLabel:"Пропозиції",selectedAnnouncement:e=>`${e.optionText}, вибрано`};var Ys={};Ys={buttonLabel:"显示建议",countAnnouncement:(e,t)=>`有 ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} 个选项`,other:()=>`${t.number(e.optionCount)} 个选项`})}可用。`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`进入了 ${e.groupTitle} 组，其中有 ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} 个选项`,other:()=>`${t.number(e.groupCount)} 个选项`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", 已选择",other:""},e.isSelected)}`,listboxLabel:"建议",selectedAnnouncement:e=>`${e.optionText}, 已选择`};var Xs={};Xs={buttonLabel:"顯示建議",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} 選項`,other:()=>`${t.number(e.optionCount)} 選項`})} 可用。`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`輸入的群組 ${e.groupTitle}, 有 ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} 選項`,other:()=>`${t.number(e.groupCount)} 選項`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", 已選取",other:""},e.isSelected)}`,listboxLabel:"建議",selectedAnnouncement:e=>`${e.optionText}, 已選取`};var qs={};qs={"ar-AE":vs,"bg-BG":gs,"cs-CZ":ms,"da-DK":ys,"de-DE":$s,"el-GR":xs,"en-US":ws,"es-ES":Cs,"et-EE":Es,"fi-FI":Ss,"fr-FR":Ps,"he-IL":Ts,"hr-HR":As,"hu-HU":ks,"it-IT":Ms,"ja-JP":Ds,"ko-KR":Ls,"lt-LT":Fs,"lv-LV":Is,"nb-NO":Rs,"nl-NL":Bs,"pl-PL":Ks,"pt-BR":Ns,"pt-PT":Os,"ro-RO":zs,"ru-RU":_s,"sk-SK":Vs,"sl-SI":js,"sr-SP":Ws,"sv-SE":Hs,"tr-TR":Us,"uk-UA":Gs,"zh-CN":Ys,"zh-TW":Xs};function Gh(e){return e&&e.__esModule?e.default:e}function Yh(e,t){let{buttonRef:n,popoverRef:r,inputRef:o,listBoxRef:l,keyboardDelegate:i,layoutDelegate:a,shouldFocusWrap:s,isReadOnly:u,isDisabled:c}=e,f=d.useRef(null);n=n??f;let p=Fr(Gh(qs),"@react-aria/combobox"),{menuTriggerProps:h,menuProps:v}=ah({type:"listbox",isDisabled:c||u},t,n);Wn.set(t,{id:v.id});let{collection:b}=t,{disabledKeys:m}=t.selectionManager,g=d.useMemo(()=>i||new aa({collection:b,disabledKeys:m,ref:l,layoutDelegate:a}),[i,a,b,m,l]),{collectionProps:$}=ia({selectionManager:t.selectionManager,keyboardDelegate:g,disallowTypeAhead:!0,disallowEmptySelection:!0,shouldFocusWrap:s,ref:o,isVirtualized:!0}),C=Bn(),w=F=>{if(!F.nativeEvent.isComposing)switch(F.key){case"Enter":case"Tab":if(t.isOpen&&F.key==="Enter"&&F.preventDefault(),t.isOpen&&l.current&&t.selectionManager.focusedKey!=null&&t.selectionManager.isLink(t.selectionManager.focusedKey)){let B=l.current.querySelector(`[data-key="${CSS.escape(t.selectionManager.focusedKey.toString())}"]`);if(F.key==="Enter"&&B instanceof HTMLAnchorElement){let j=t.collection.getItem(t.selectionManager.focusedKey);j&&C.open(B,F,j.props.href,j.props.routerOptions)}t.close()}else t.commit();break;case"Escape":(t.selectedKey!==null||t.inputValue===""||e.allowsCustomValue)&&F.continuePropagation(),t.revert();break;case"ArrowDown":t.open("first","manual");break;case"ArrowUp":t.open("last","manual");break;case"ArrowLeft":case"ArrowRight":t.selectionManager.setFocusedKey(null);break}},A=F=>{var B;let j=n?.current&&n.current===F.relatedTarget,oe=(B=r.current)===null||B===void 0?void 0:B.contains(F.relatedTarget);j||oe||(e.onBlur&&e.onBlur(F),t.setFocused(!1))},k=F=>{t.isFocused||(e.onFocus&&e.onFocus(F),t.setFocused(!0))},{isInvalid:K,validationErrors:O,validationDetails:z}=t.displayValidation,{labelProps:N,inputProps:x,descriptionProps:P,errorMessageProps:R}=xa({...e,onChange:t.setInputValue,onKeyDown:u?e.onKeyDown:at(t.isOpen&&$.onKeyDown,w,e.onKeyDown),onBlur:A,value:t.inputValue,defaultValue:t.defaultInputValue,onFocus:k,autoComplete:"off",validate:void 0,[kr]:t},o),E=F=>{if(F.pointerType==="touch"){var B;(B=o.current)===null||B===void 0||B.focus(),t.toggle(null,"manual")}},L=F=>{if(F.pointerType!=="touch"){var B;(B=o.current)===null||B===void 0||B.focus(),t.toggle(F.pointerType==="keyboard"||F.pointerType==="virtual"?"first":null,"manual")}},T=mn({id:h.id,"aria-label":p.format("buttonLabel"),"aria-labelledby":e["aria-labelledby"]||N.id}),y=mn({id:v.id,"aria-label":p.format("listboxLabel"),"aria-labelledby":e["aria-labelledby"]||N.id}),M=d.useRef(0),S=F=>{if(c||u)return;if(F.timeStamp-M.current<500){var B;F.preventDefault(),(B=o.current)===null||B===void 0||B.focus();return}let j=F.target.getBoundingClientRect(),oe=F.changedTouches[0],le=Math.ceil(j.left+.5*j.width),fe=Math.ceil(j.top+.5*j.height);if(oe.clientX===le&&oe.clientY===fe){var Ae;F.preventDefault(),(Ae=o.current)===null||Ae===void 0||Ae.focus(),t.toggle(null,"manual"),M.current=F.timeStamp}},I=t.selectionManager.focusedKey!=null&&t.isOpen?t.collection.getItem(t.selectionManager.focusedKey):void 0;var _;let H=(_=I?.parentKey)!==null&&_!==void 0?_:null;var V;let W=(V=t.selectionManager.focusedKey)!==null&&V!==void 0?V:null,re=d.useRef(H),Z=d.useRef(W);d.useEffect(()=>{if(bn()&&I!=null&&W!=null&&W!==Z.current){let B=t.selectionManager.isSelected(W),j=H!=null?t.collection.getItem(H):null,oe=j?.["aria-label"]||(typeof j?.rendered=="string"?j.rendered:"")||"";var F;let le=p.format("focusAnnouncement",{isGroupChange:(F=j&&H!==re.current)!==null&&F!==void 0?F:!1,groupTitle:oe,groupCount:j?[...jn(j,t.collection)].length:0,optionText:I["aria-label"]||I.textValue||"",isSelected:B});sr(le)}re.current=H,Z.current=W});let se=ca(t.collection),$e=d.useRef(se),ce=d.useRef(t.isOpen);d.useEffect(()=>{let F=t.isOpen!==ce.current&&(t.selectionManager.focusedKey==null||bn());if(t.isOpen&&(F||se!==$e.current)){let B=p.format("countAnnouncement",{optionCount:se});sr(B)}$e.current=se,ce.current=t.isOpen});let ne=d.useRef(t.selectedKey);return d.useEffect(()=>{if(bn()&&t.isFocused&&t.selectedItem&&t.selectedKey!==ne.current){let F=t.selectedItem["aria-label"]||t.selectedItem.textValue||"",B=p.format("selectedAnnouncement",{optionText:F});sr(B)}ne.current=t.selectedKey}),d.useEffect(()=>{if(t.isOpen)return Lf([o.current,r.current].filter(F=>F!=null))},[t.isOpen,o,r]),vc(()=>{!I&&o.current&&ke(ae(o.current))===o.current&&Zr(o.current,null)},[I]),{labelProps:N,buttonProps:{...h,...T,excludeFromTabOrder:!0,preventFocusOnPress:!0,onPress:E,onPressStart:L,isDisabled:c||u},inputProps:Se(x,{role:"combobox","aria-expanded":h["aria-expanded"],"aria-controls":t.isOpen?v.id:void 0,"aria-autocomplete":"list","aria-activedescendant":I?ds(t,I.key):void 0,onTouchEnd:S,autoCorrect:"off",spellCheck:"false"}),listBoxProps:Se(v,y,{autoFocus:t.focusStrategy||!0,shouldUseVirtualFocus:!0,shouldSelectOnPressUp:!0,shouldFocusOnHover:!0,linkBehavior:"selection"}),descriptionProps:P,errorMessageProps:R,isInvalid:K,validationErrors:O,validationDetails:z}}function Xh(e){var t,n,r,o,l;const i=tt(),{validationBehavior:a}=eo(kn)||{},[s,u]=xt(e,Qo.variantKeys),c=(n=(t=e.disableAnimation)!=null?t:i?.disableAnimation)!=null?n:!1,f=e.disableClearable!==void 0?!e.disableClearable:e.isReadOnly?!1:e.isClearable,{ref:p,as:h,label:v,isLoading:b,menuTrigger:m="focus",filterOptions:g={sensitivity:"base"},children:$,selectorIcon:C,clearIcon:w,scrollRef:A,defaultFilter:k,endContent:K,allowsEmptyCollection:O=!0,shouldCloseOnBlur:z=!0,popoverProps:N={},inputProps:x={},scrollShadowProps:P={},listboxProps:R={},selectorButtonProps:E={},clearButtonProps:L={},showScrollIndicators:T=!0,allowsCustomValue:y=!1,isVirtualized:M,maxListboxHeight:S=256,itemHeight:I=32,validationBehavior:_=(r=a??i?.validationBehavior)!=null?r:"native",className:H,classNames:V,errorMessage:W,onOpenChange:re,onClose:Z,onClear:se,isReadOnly:$e=!1,...ce}=s,{contains:ne}=Dc(g);let F=Hh({...e,children:$,menuTrigger:m,validationBehavior:_,shouldCloseOnBlur:z,allowsEmptyCollection:O,defaultFilter:k&&typeof k=="function"?k:ne,onOpenChange:(q,Y)=>{re?.(q,Y),q||Z?.()}});F={...F,...$e&&{disabledKeys:new Set([...F.collection.getKeys()])}};const B=d.useRef(null),j=d.useRef(null),oe=d.useRef(null),le=d.useRef(null),fe=Ve(p),Ae=Ve(A),{buttonProps:Me,inputProps:xe,listBoxProps:ve,isInvalid:J,validationDetails:ee,validationErrors:ue}=Yh({validationBehavior:_,...e,inputRef:fe,buttonRef:B,listBoxRef:oe,popoverRef:le},F),Re=e.isInvalid||J,ge={inputProps:ie({label:v,ref:fe,wrapperRef:j,onClick:()=>{!F.isOpen&&F.selectedItem&&F.open()},isClearable:!1,disableAnimation:c},x),popoverProps:ie({offset:5,placement:"bottom",triggerScaleOnOpen:!1,disableAnimation:c},N),scrollShadowProps:ie({ref:Ae,isEnabled:(o=T&&F.collection.size>5)!=null?o:!0,hideScrollBar:!0,offset:15},P),listboxProps:ie({hideEmptyContent:y,emptyContent:"No results found.",disableAnimation:c},R),selectorButtonProps:ie({isLoading:b,size:"sm",variant:"light",radius:"full",color:Re?"danger":e?.color,isIconOnly:!0,disableAnimation:c},E),clearButtonProps:ie({size:"sm",variant:"light",radius:"full",color:Re?"danger":e?.color,isIconOnly:!0,disableAnimation:c},L)},It=pe(V?.base,H),de=(l=ge.listboxProps)!=null&&l.hideEmptyContent?F.isOpen&&!!F.collection.size:F.isOpen;if(Qr(()=>{if(!fe.current)return;const q=fe.current.value,Y=F.collection.getItem(q);Y&&F.inputValue!==Y.textValue&&(F.setSelectedKey(q),F.setInputValue(Y.textValue))},[fe.current]),d.useEffect(()=>{let q;if(F.selectedKey!==null&&F.collection.getItem(F.selectedKey)&&!F.disabledKeys.has(F.selectedKey))q=F.selectedKey;else{let Y=F.collection.getFirstKey();for(;Y&&F.disabledKeys.has(Y);)Y=F.collection.getKeyAfter(Y);q=Y}F.selectionManager.setFocusedKey(q)},[F.collection,F.disabledKeys,F.selectedKey]),d.useEffect(()=>{if(F.isOpen&&le.current&&oe.current){let q=oe.current.querySelector("[aria-selected=true] [data-label=true]"),Y=Ae.current;if(q&&Y&&q.parentElement){let ze=(Y?.getBoundingClientRect()).height;Y.scrollTop=q.parentElement.offsetTop-ze/2+q.parentElement.clientHeight/2,F.selectionManager.setFocusedKey(F.selectedKey)}}},[F.isOpen,c]),d.useEffect(()=>{if(de&&le.current&&j.current){let q=j.current.getBoundingClientRect(),Y=le.current;Y.style.width=q.width+"px"}},[de]),xe.onKeyDown){const q=xe.onKeyDown;xe.onKeyDown=Y=>("continuePropagation"in Y&&(Y.stopPropagation=()=>{}),q(Y))}const We=h||"div",De=d.useMemo(()=>Qo({...u,isClearable:f,disableAnimation:c}),[yt(u),f,c]),wt=()=>({"data-invalid":U(Re),"data-open":U(F.isOpen),className:De.base({class:It})}),Jt=()=>{var q;return{ref:B,...ie(Me,ge.selectorButtonProps),"data-open":U(F.isOpen),className:De.selectorButton({class:pe(V?.selectorButton,(q=ge.selectorButtonProps)==null?void 0:q.className)})}},Qt=()=>{var q,Y;return{...ie(Me,ge.clearButtonProps),onPressStart:()=>{var Pe;(Pe=fe.current)==null||Pe.focus()},onPress:Pe=>{var ze,Et;(Et=(ze=ge.clearButtonProps)==null?void 0:ze.onPress)==null||Et.call(ze,Pe),F.selectedItem&&F.setSelectedKey(null),F.setInputValue(""),F.open(),se?.()},"data-visible":!!F.selectedItem||((q=F.inputValue)==null?void 0:q.length)>0,className:De.clearButton({class:pe(V?.clearButton,(Y=ge.clearButtonProps)==null?void 0:Y.className)})}},rt=_==="native"&&F.displayValidation.isInvalid===!1&&F.realtimeValidation.isInvalid===!0;return{Component:We,inputRef:fe,label:v,state:F,slots:De,classNames:V,isLoading:b,clearIcon:w,isOpen:de,endContent:K,isClearable:f,disableAnimation:c,allowsCustomValue:y,selectorIcon:C,getBaseProps:wt,getInputProps:()=>({...ce,...xe,...ge.inputProps,isInvalid:rt?void 0:Re,validationBehavior:_,errorMessage:typeof W=="function"?W({isInvalid:Re,validationErrors:ue,validationDetails:ee}):W||ue?.join(" "),onClick:pt(ge.inputProps.onClick,ce.onClick)}),getListBoxProps:()=>{const q=M??F.collection.size>50;return{state:F,ref:oe,isVirtualized:q,virtualization:q?{maxListboxHeight:S,itemHeight:I}:void 0,scrollShadowProps:ge.scrollShadowProps,...ie(ge.listboxProps,ve,{shouldHighlightOnFocus:!0})}},getPopoverProps:(q={})=>{var Y,Pe,ze;const Et=ie(ge.popoverProps,q);return{state:F,ref:le,triggerRef:j,scrollRef:oe,triggerType:"listbox",...Et,classNames:{...(Y=ge.popoverProps)==null?void 0:Y.classNames,content:De.popoverContent({class:pe(V?.popoverContent,(ze=(Pe=ge.popoverProps)==null?void 0:Pe.classNames)==null?void 0:ze.content,q.className)})},disableDialogFocus:!0}},getEmptyPopoverProps:()=>({ref:le,className:"hidden"}),getClearButtonProps:Qt,getSelectorButtonProps:Jt,getListBoxWrapperProps:(q={})=>{var Y,Pe;return{...ie(ge.scrollShadowProps,q),className:De.listboxWrapper({class:pe(V?.listboxWrapper,(Y=ge.scrollShadowProps)==null?void 0:Y.className,q?.className)}),style:{maxHeight:(Pe=e.maxListboxHeight)!=null?Pe:256}}},getEndContentWrapperProps:(q={})=>({className:De.endContentWrapper({class:pe(V?.endContentWrapper,q?.className)}),onPointerDown:pt(q.onPointerDown,Y=>{var Pe;Y.button===0&&Y.currentTarget===Y.target&&((Pe=fe.current)==null||Pe.focus())}),onMouseDown:pt(q.onMouseDown,Y=>{Y.button===0&&Y.currentTarget===Y.target&&Y.preventDefault()})})}}var qh=Ze(function(t,n){var r;const{Component:o,isOpen:l,disableAnimation:i,selectorIcon:a=D.jsx(gp,{}),clearIcon:s=D.jsx(vp,{}),endContent:u,getBaseProps:c,getSelectorButtonProps:f,getInputProps:p,getListBoxProps:h,getPopoverProps:v,getEmptyPopoverProps:b,getClearButtonProps:m,getListBoxWrapperProps:g,getEndContentWrapperProps:$}=Xh({...t,ref:n}),C=h(),w=l?D.jsx(lh,{...v(),children:D.jsx(Ah,{...g(),children:D.jsx(Wh,{...C})})}):((r=C.state)==null?void 0:r.collection.size)===0?D.jsx("div",{...b()}):null;return D.jsxs(o,{...c(),children:[D.jsx(eh,{...p(),endContent:D.jsxs("div",{...$(),children:[u||D.jsx(ul,{...m(),children:s}),a&&D.jsx(ul,{...f(),children:a})]})}),i?w:D.jsx(Wl,{children:w})]})}),hb=qh;export{Mn as $,Ze as A,lb as B,Ur as C,Mp as D,Wl as E,Oe as F,Vn as G,gl as H,Ve as I,rl as J,oh as K,jr as L,vp as M,pt as N,Wo as O,wf as P,If as Q,Lf as R,Bp as S,sa as T,Gt as U,wl as V,Ff as W,ul as X,Fc as Y,Nl as Z,Bl as _,hb as a,Wr as a$,Lc as a0,ei as a1,Zn as a2,X as a3,Vc as a4,Cd as a5,cb as a6,vt as a7,fd as a8,vr as a9,Rl as aA,aa as aB,ah as aC,dp as aD,Xp as aE,Jd as aF,eo as aG,kn as aH,db as aI,Af as aJ,Hp as aK,bp as aL,lh as aM,Ah as aN,Wh as aO,kf as aP,gp as aQ,zr as aR,pr as aS,ab as aT,On as aU,ut as aV,ub as aW,sb as aX,Vl as aY,Kc as aZ,Kl as a_,_l as aa,Or as ab,be as ac,et as ad,G as ae,qt as af,mt as ag,hp as ah,ht as ai,ob as aj,Ft as ak,yp as al,Ep as am,Jr as an,kp as ao,xp as ap,pp as aq,hc as ar,mn as as,Dn as at,ia as au,nb as av,Sh as aw,at as ax,us as ay,ma as az,Tf as b,Yc as b0,yn as b1,dd as b2,zl as b3,Yl as b4,Ql as b5,$d as b6,vd as b7,id as b8,ad as b9,Zl as ba,wd as bb,br as bc,Gc as bd,Ul as be,Uc as bf,$a as c,Yt as d,Ut as e,Se as f,qe as g,pi as h,rb as i,Sn as j,En as k,pb as l,xt as m,tb as n,ie as o,Nd as p,Qr as q,Jp as r,Vp as s,qo as t,tt as u,yt as v,sl as w,U as x,pe as y,Dp as z};
