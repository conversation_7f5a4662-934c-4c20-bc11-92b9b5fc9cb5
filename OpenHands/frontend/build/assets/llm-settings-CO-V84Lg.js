import{j as t,w as te,R as c}from"./chunk-C37GKA54-CBbYr_fP.js";import{o as j,e as se,u as ae,M as ne,D as g,H as E}from"./help-link-CHYsvCwj.js";import{D as S,u as le}from"./use-settings-CSlhfPqo.js";import{u as ie}from"./use-save-settings-Bb4p0cGE.js";import{S as x,a as M}from"./switch-skeleton-BbiAGd10.js";import{I as n}from"./declaration-xyc84-tJ.js";import{S as p}from"./settings-input-i4i_IemM.js";import{B as oe}from"./brand-button-3Z8FN4qR.js";import{a as re,d as de}from"./custom-toast-handlers-CR9P-jKI.js";import{r as ce}from"./retrieve-axios-error-message-CYr77e_f.js";import{S as C}from"./settings-dropdown-input-Did5iUTK.js";import{u as Ee}from"./use-config-jdwF3W4-.js";import{I as A}from"./input-skeleton-FOAHYnV9.js";import{S as pe}from"./subtext-skeleton-DyZMS3uA.js";import{K as T}from"./key-status-icon-DbpY1ViG.js";import{g as Se}from"./map-provider-g8SgAMsv.js";import{u as Ie}from"./useTranslation-BG59QWH_.js";import"./useQuery-Cu2nkJ8V.js";import"./open-hands-axios-CtirLpss.js";import"./open-hands-Ce72Fmtl.js";import"./chunk-S6H5EOGR-Bwn62IP6.js";import"./utils-KsbccAr1.js";import"./index-yKbcr7Pf.js";import"./preload-helper-BXl3LOEh.js";import"./module-5laXsVNO.js";import"./optional-tag-e1gRgM9y.js";import"./mutation-B9dSlWD-.js";import"./index-cxP66Ws3.js";import"./i18nInstance-DBIXdvxg.js";const _e=s=>Object.keys(s).length>0&&(!!s.LLM_BASE_URL||s.AGENT!==S.AGENT||s.CONFIRMATION_MODE||!!s.SECURITY_ANALYZER),me=(s,I)=>{if(!I)return!1;const d=j(s),{provider:r,model:a}=se(I);return!(r in d&&d[r].models.includes(a))};function ue(){return t.jsxs("div",{"data-testid":"app-settings-skeleton",className:"px-11 py-9 flex flex-col gap-6",children:[t.jsx(x,{}),t.jsx(A,{}),t.jsx(A,{}),t.jsx(A,{}),t.jsx(pe,{}),t.jsx(x,{}),t.jsx(x,{}),t.jsx(A,{})]})}function Ae(){const{t:s}=Ie(),{mutate:I,isPending:d}=ie(),{data:r}=ae(),{data:a,isLoading:O,isFetching:R}=le(),{data:G}=Ee(),[_,N]=c.useState("basic"),[v,f]=c.useState(!1),[w,o]=c.useState({model:!1,apiKey:!1,searchApiKey:!1,baseUrl:!1,agent:!1,confirmationMode:!1,enableDefaultCondenser:!1,securityAnalyzer:!1}),[D,h]=c.useState(null),k=j(r?.models||[]);c.useEffect(()=>{const l=r&&a?me(r.models,a.LLM_MODEL)||_e({...a}):!1;a&&f(a.CONFIRMATION_MODE),N(l?"advanced":"basic")},[a,r]),c.useEffect(()=>{a?.LLM_MODEL&&h(a.LLM_MODEL)},[a?.LLM_MODEL]);const K=()=>{de(s(n.SETTINGS$SAVED_WARNING)),o({model:!1,apiKey:!1,searchApiKey:!1,baseUrl:!1,agent:!1,confirmationMode:!1,enableDefaultCondenser:!1,securityAnalyzer:!1})},P=e=>{const l=ce(e);re(l||s(n.ERROR$GENERIC))},U=e=>{const l=e.get("llm-provider-input")?.toString(),i=l?Se(l):void 0,m=e.get("llm-model-input")?.toString(),y=e.get("llm-api-key-input")?.toString(),L=e.get("search-api-key-input")?.toString(),u=i&&m&&`${i}/${m}`;I({LLM_MODEL:u,llm_api_key:y||null,SEARCH_API_KEY:L||"",LLM_BASE_URL:S.LLM_BASE_URL,AGENT:S.AGENT,CONFIRMATION_MODE:S.CONFIRMATION_MODE,SECURITY_ANALYZER:S.SECURITY_ANALYZER,ENABLE_DEFAULT_CONDENSER:S.ENABLE_DEFAULT_CONDENSER},{onSuccess:K,onError:P})},$=e=>{const l=e.get("llm-custom-model-input")?.toString(),i=e.get("base-url-input")?.toString(),m=e.get("llm-api-key-input")?.toString(),y=e.get("search-api-key-input")?.toString(),L=e.get("agent-input")?.toString(),u=e.get("enable-confirmation-mode-switch")?.toString()==="on",Q=e.get("enable-memory-condenser-switch")?.toString()==="on",ee=e.get("security-analyzer-input")?.toString();I({LLM_MODEL:l,LLM_BASE_URL:i,llm_api_key:m||null,SEARCH_API_KEY:y||"",AGENT:L,CONFIRMATION_MODE:u,ENABLE_DEFAULT_CONDENSER:Q,SECURITY_ANALYZER:u?ee:void 0},{onSuccess:K,onError:P})},F=e=>{_==="basic"?U(e):$(e)},H=e=>{f(!!a?.CONFIRMATION_MODE),N(e?"advanced":"basic"),o({model:!1,apiKey:!1,searchApiKey:!1,baseUrl:!1,agent:!1,confirmationMode:!1,enableDefaultCondenser:!1,securityAnalyzer:!1})},B=e=>{const l=e!==a?.LLM_MODEL.replace("openai/","");o(i=>({...i,model:l})),h(e)},b=e=>{const l=e!=="";o(i=>({...i,apiKey:l}))},Y=e=>{const l=e!==a?.SEARCH_API_KEY;o(i=>({...i,searchApiKey:l}))},V=e=>{const l=e!==a?.LLM_MODEL&&e!=="";o(i=>({...i,model:l})),h(e)},z=e=>{const l=e!==a?.LLM_BASE_URL;o(i=>({...i,baseUrl:l}))},W=e=>{const l=e!==a?.AGENT&&e!=="";o(i=>({...i,agent:l}))},Z=e=>{f(e);const l=e!==a?.CONFIRMATION_MODE;o(i=>({...i,confirmationMode:l}))},X=e=>{const l=e!==a?.ENABLE_DEFAULT_CONDENSER;o(i=>({...i,enableDefaultCondenser:l}))},q=e=>{const l=e!==a?.SECURITY_ANALYZER;o(i=>({...i,securityAnalyzer:l}))},J=Object.values(w).some(e=>e);return!a||R?t.jsx(ue,{}):t.jsx("div",{"data-testid":"llm-settings-screen",className:"h-full",children:t.jsxs("form",{action:F,className:"flex flex-col h-full justify-between",children:[t.jsxs("div",{className:"p-9 flex flex-col gap-6",children:[t.jsx(M,{testId:"advanced-settings-switch",defaultIsToggled:_==="advanced",onToggle:H,isToggled:_==="advanced",children:s(n.SETTINGS$ADVANCED)}),_==="basic"&&t.jsxs("div",{"data-testid":"llm-settings-form-basic",className:"flex flex-col gap-6",children:[!O&&!R&&t.jsxs(t.Fragment,{children:[t.jsx(ne,{models:k,currentModel:a.LLM_MODEL||g,onChange:B}),(a.LLM_MODEL?.startsWith("openhands/")||D?.startsWith("openhands/"))&&t.jsx(E,{testId:"openhands-api-key-help",text:s(n.SETTINGS$OPENHANDS_API_KEY_HELP_TEXT),linkText:s(n.SETTINGS$NAV_API_KEYS),href:"https://app.all-hands.dev/settings/api-keys",suffix:s(n.SETTINGS$OPENHANDS_API_KEY_HELP_SUFFIX)})]}),t.jsx(p,{testId:"llm-api-key-input",name:"llm-api-key-input",label:s(n.SETTINGS_FORM$API_KEY),type:"password",className:"w-full max-w-[680px]",placeholder:a.LLM_API_KEY_SET?"<hidden>":"",onChange:b,startContent:a.LLM_API_KEY_SET&&t.jsx(T,{isSet:a.LLM_API_KEY_SET})}),t.jsx(E,{testId:"llm-api-key-help-anchor",text:s(n.SETTINGS$DONT_KNOW_API_KEY),linkText:s(n.SETTINGS$CLICK_FOR_INSTRUCTIONS),href:"https://docs.all-hands.dev/usage/local-setup#getting-an-api-key"}),t.jsx(p,{testId:"search-api-key-input",name:"search-api-key-input",label:s(n.SETTINGS$SEARCH_API_KEY),type:"password",className:"w-full max-w-[680px]",defaultValue:a.SEARCH_API_KEY||"",onChange:Y,placeholder:s(n.API$TAVILY_KEY_EXAMPLE),startContent:a.SEARCH_API_KEY_SET&&t.jsx(T,{isSet:a.SEARCH_API_KEY_SET})}),t.jsx(E,{testId:"search-api-key-help-anchor",text:s(n.SETTINGS$SEARCH_API_KEY_OPTIONAL),linkText:s(n.SETTINGS$SEARCH_API_KEY_INSTRUCTIONS),href:"https://tavily.com/"})]}),_==="advanced"&&t.jsxs("div",{"data-testid":"llm-settings-form-advanced",className:"flex flex-col gap-6",children:[t.jsx(p,{testId:"llm-custom-model-input",name:"llm-custom-model-input",label:s(n.SETTINGS$CUSTOM_MODEL),defaultValue:a.LLM_MODEL||g,placeholder:g,type:"text",className:"w-full max-w-[680px]",onChange:V}),(a.LLM_MODEL?.startsWith("openhands/")||D?.startsWith("openhands/"))&&t.jsx(E,{testId:"openhands-api-key-help-2",text:s(n.SETTINGS$OPENHANDS_API_KEY_HELP_TEXT),linkText:s(n.SETTINGS$NAV_API_KEYS),href:"https://app.all-hands.dev/settings/api-keys",suffix:s(n.SETTINGS$OPENHANDS_API_KEY_HELP_SUFFIX)}),t.jsx(p,{testId:"base-url-input",name:"base-url-input",label:s(n.SETTINGS$BASE_URL),defaultValue:a.LLM_BASE_URL,placeholder:"https://api.openai.com",type:"text",className:"w-full max-w-[680px]",onChange:z}),t.jsx(p,{testId:"llm-api-key-input",name:"llm-api-key-input",label:s(n.SETTINGS_FORM$API_KEY),type:"password",className:"w-full max-w-[680px]",placeholder:a.LLM_API_KEY_SET?"<hidden>":"",onChange:b,startContent:a.LLM_API_KEY_SET&&t.jsx(T,{isSet:a.LLM_API_KEY_SET})}),t.jsx(E,{testId:"llm-api-key-help-anchor-advanced",text:s(n.SETTINGS$DONT_KNOW_API_KEY),linkText:s(n.SETTINGS$CLICK_FOR_INSTRUCTIONS),href:"https://docs.all-hands.dev/usage/local-setup#getting-an-api-key"}),t.jsx(p,{testId:"search-api-key-input",name:"search-api-key-input",label:s(n.SETTINGS$SEARCH_API_KEY),type:"password",className:"w-full max-w-[680px]",defaultValue:a.SEARCH_API_KEY||"",onChange:Y,placeholder:s(n.API$TVLY_KEY_EXAMPLE),startContent:a.SEARCH_API_KEY_SET&&t.jsx(T,{isSet:a.SEARCH_API_KEY_SET})}),t.jsx(E,{testId:"search-api-key-help-anchor",text:s(n.SETTINGS$SEARCH_API_KEY_OPTIONAL),linkText:s(n.SETTINGS$SEARCH_API_KEY_INSTRUCTIONS),href:"https://tavily.com/"}),t.jsx(C,{testId:"agent-input",name:"agent-input",label:s(n.SETTINGS$AGENT),items:r?.agents.map(e=>({key:e,label:e}))||[],defaultSelectedKey:a.AGENT,isClearable:!1,onInputChange:W,wrapperClassName:"w-full max-w-[680px]"}),G?.APP_MODE==="saas"&&t.jsx(C,{testId:"runtime-settings-input",name:"runtime-settings-input",label:t.jsxs(t.Fragment,{children:[s(n.SETTINGS$RUNTIME_SETTINGS),t.jsx("a",{href:"mailto:<EMAIL>",children:s(n.SETTINGS$GET_IN_TOUCH)})]}),items:[],isDisabled:!0,wrapperClassName:"w-full max-w-[680px]"}),t.jsx(M,{testId:"enable-memory-condenser-switch",name:"enable-memory-condenser-switch",defaultIsToggled:a.ENABLE_DEFAULT_CONDENSER,onToggle:X,children:s(n.SETTINGS$ENABLE_MEMORY_CONDENSATION)}),t.jsx(M,{testId:"enable-confirmation-mode-switch",name:"enable-confirmation-mode-switch",onToggle:Z,defaultIsToggled:a.CONFIRMATION_MODE,isBeta:!0,children:s(n.SETTINGS$CONFIRMATION_MODE)}),v&&t.jsx(C,{testId:"security-analyzer-input",name:"security-analyzer-input",label:s(n.SETTINGS$SECURITY_ANALYZER),items:r?.securityAnalyzers.map(e=>({key:e,label:e}))||[],placeholder:s(n.SETTINGS$SECURITY_ANALYZER_PLACEHOLDER),defaultSelectedKey:a.SECURITY_ANALYZER,isClearable:!0,showOptionalTag:!0,onInputChange:q,wrapperClassName:"w-full max-w-[680px]"})]})]}),t.jsx("div",{className:"flex gap-6 p-6 justify-end border-t border-t-tertiary",children:t.jsxs(oe,{testId:"submit-button",type:"submit",variant:"primary",isDisabled:!J||d,children:[!d&&s("SETTINGS$SAVE_CHANGES"),d&&s("SETTINGS$SAVING")]})})]})})}const We=te(Ae);export{We as default};
