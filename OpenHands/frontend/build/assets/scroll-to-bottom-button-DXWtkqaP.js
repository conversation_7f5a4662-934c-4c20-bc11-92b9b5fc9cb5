import{r as o,j as a}from"./chunk-C37GKA54-CBbYr_fP.js";const b=e=>o.createElement("svg",{width:12,height:17,viewBox:"0 0 12 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5304 6.46978L10.4697 7.53044L6.75006 3.81077L6.75006 17.0001H5.25006L5.25006 3.81077L1.53039 7.53044L0.469727 6.46978L6.00006 0.939453L11.5304 6.46978Z",fill:"white"}));function S(e){const[c,r]=o.useState(!0),[m,s]=o.useState(!0),i=o.useRef(0),u=o.useCallback(t=>t.scrollTop+t.clientHeight>=t.scrollHeight-20,[]),h=o.useCallback(t=>{const l=u(t);s(l);const n=t.scrollTop,d=n<i.current;i.current=n,d&&r(!1),l&&r(!0)},[u]),p=o.useCallback(()=>{const t=e.current;t&&requestAnimationFrame(()=>{r(!0),s(!0),t.scrollTo({top:t.scrollHeight,behavior:"smooth"})})},[e]);return o.useEffect(()=>{if(c){const t=e.current;t&&requestAnimationFrame(()=>{t.scrollTo({top:t.scrollHeight,behavior:"smooth"})})}}),{scrollRef:e,autoScroll:c,setAutoScroll:r,scrollDomToBottom:p,hitBottom:m,setHitBottom:s,onChatBodyScroll:h}}function g({onClick:e}){return a.jsx("button",{type:"button",onClick:e,"data-testid":"scroll-to-bottom",className:"button-base p-1 hover:bg-neutral-500 rotate-180",children:a.jsx(b,{width:15,height:15})})}export{b as S,g as a,S as u};
