import{aR as _e,aS as Bt,a0 as z,a2 as ve,aT as Ue,a3 as Lt,aU as _t,aV as Xn,aW as Yn,aX as qn,aY as Zn,aZ as Jn,a_ as Qn,a7 as K,a$ as es,b0 as ts,aa as he,b1 as it,b2 as Ut,b3 as ns,b4 as ss,b5 as is,b6 as jt,b7 as rs,b8 as as,b9 as os,ba as ls,bb as us,bc as Gt,bd as Wt,be as $t,bf as Ht}from"./chunk-S6H5EOGR-Bwn62IP6.js";import{r as cs}from"./chunk-C37GKA54-CBbYr_fP.js";function hs(t,e){t.indexOf(e)===-1&&t.push(e)}function fs(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}let je=()=>{};const N={},zt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Xt=t=>/^0[^.\s]+$/u.test(t);function Ge(t){let e;return()=>(e===void 0&&(e=t()),e)}const X=t=>t,ds=(t,e)=>n=>e(t(n)),fe=(...t)=>t.reduce(ds),Yt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class qt{constructor(){this.subscriptions=[]}add(e){return hs(this.subscriptions,e),()=>fs(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const a=this.subscriptions[r];a&&a(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const O=t=>t*1e3,R=t=>t/1e3;function Zt(t,e){return e?t*(1e3/e):0}const Jt=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,ps=1e-7,ms=12;function gs(t,e,n,s,i){let r,a,o=0;do a=e+(n-e)/2,r=Jt(a,s,i)-t,r>0?n=a:e=a;while(Math.abs(r)>ps&&++o<ms);return a}function te(t,e,n,s){if(t===e&&n===s)return X;const i=r=>gs(r,0,1,t,n);return r=>r===0||r===1?r:Jt(i(r),e,s)}const Qt=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,en=t=>e=>1-t(1-e),tn=te(.33,1.53,.69,.99),We=en(tn),nn=Qt(We),sn=t=>(t*=2)<1?.5*We(t):.5*(2-Math.pow(2,-10*(t-1))),$e=t=>1-Math.sin(Math.acos(t)),ys=en($e),rn=Qt($e),bs=te(.42,0,1,1),Ts=te(0,0,.58,1),an=te(.42,0,.58,1),vs=t=>Array.isArray(t)&&typeof t[0]!="number",on=t=>Array.isArray(t)&&typeof t[0]=="number",Vs={linear:X,easeIn:bs,easeInOut:an,easeOut:Ts,circIn:$e,circInOut:rn,circOut:ys,backIn:We,backInOut:nn,backOut:tn,anticipate:sn},As=t=>typeof t=="string",rt=t=>{if(on(t)){je(t.length===4);const[e,n,s,i]=t;return te(e,n,s,i)}else if(As(t))return Vs[t];return t},se=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function xs(t,e){let n=new Set,s=new Set,i=!1,r=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function l(u){a.has(u)&&(c.schedule(u),t()),u(o)}const c={schedule:(u,h=!1,f=!1)=>{const T=f&&i?n:s;return h&&a.add(u),T.has(u)||T.add(u),u},cancel:u=>{s.delete(u),a.delete(u)},process:u=>{if(o=u,i){r=!0;return}i=!0,[n,s]=[s,n],n.forEach(l),n.clear(),i=!1,r&&(r=!1,c.process(u))}};return c}const ws=40;function ln(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,a=se.reduce((g,x)=>(g[x]=xs(r),g),{}),{setup:o,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:f,render:d,postRender:T}=a,v=()=>{const g=N.useManualTiming?i.timestamp:performance.now();n=!1,N.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(g-i.timestamp,ws),1)),i.timestamp=g,i.isProcessing=!0,o.process(i),l.process(i),c.process(i),u.process(i),h.process(i),f.process(i),d.process(i),T.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(v))},b=()=>{n=!0,s=!0,i.isProcessing||t(v)};return{schedule:se.reduce((g,x)=>{const m=a[x];return g[x]=(A,M=!1,y=!1)=>(n||b(),m.schedule(A,M,y)),g},{}),cancel:g=>{for(let x=0;x<se.length;x++)a[se[x]].cancel(g)},state:i,steps:a}}const{schedule:I,cancel:Ve,state:ae,steps:oa}=ln(typeof requestAnimationFrame<"u"?requestAnimationFrame:X,!0);let ie;function Ss(){ie=void 0}const E={now:()=>(ie===void 0&&E.set(ae.isProcessing||N.useManualTiming?ae.timestamp:performance.now()),ie),set:t=>{ie=t,queueMicrotask(Ss)}},J=t=>Math.round(t*1e5)/1e5,He=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Ms(t){return t==null}const Cs=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ze=(t,e)=>n=>!!(typeof n=="string"&&Cs.test(n)&&n.startsWith(t)||e&&!Ms(n)&&Object.prototype.hasOwnProperty.call(n,e)),un=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,a,o]=s.match(He);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(a),alpha:o!==void 0?parseFloat(o):1}},Ps=t=>z(0,255,t),de={..._e,transform:t=>Math.round(Ps(t))},L={test:ze("rgb","red"),parse:un("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+de.transform(t)+", "+de.transform(e)+", "+de.transform(n)+", "+J(Bt.transform(s))+")"};function Fs(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Ae={test:ze("#"),parse:Fs,transform:L.transform},$={test:ze("hsl","hue"),parse:un("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ve.transform(J(e))+", "+ve.transform(J(n))+", "+J(Bt.transform(s))+")"},S={test:t=>L.test(t)||Ae.test(t)||$.test(t),parse:t=>L.test(t)?L.parse(t):$.test(t)?$.parse(t):Ae.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?L.transform(t):$.transform(t),getAnimatableNone:t=>{const e=S.parse(t);return e.alpha=0,S.transform(e)}},Ds=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Es(t){return isNaN(t)&&typeof t=="string"&&(t.match(He)?.length||0)+(t.match(Ds)?.length||0)>0}const cn="number",hn="color",Is="var",Os="var(",at="${}",Rs=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ee(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const o=e.replace(Rs,l=>(S.test(l)?(s.color.push(r),i.push(hn),n.push(S.parse(l))):l.startsWith(Os)?(s.var.push(r),i.push(Is),n.push(l)):(s.number.push(r),i.push(cn),n.push(parseFloat(l))),++r,at)).split(at);return{values:n,split:o,indexes:s,types:i}}function fn(t){return ee(t).values}function dn(t){const{split:e,types:n}=ee(t),s=e.length;return i=>{let r="";for(let a=0;a<s;a++)if(r+=e[a],i[a]!==void 0){const o=n[a];o===cn?r+=J(i[a]):o===hn?r+=S.transform(i[a]):r+=i[a]}return r}}const Ns=t=>typeof t=="number"?0:S.test(t)?S.getAnimatableNone(t):t;function Ks(t){const e=fn(t);return dn(t)(e.map(Ns))}const Y={test:Es,parse:fn,createTransformer:dn,getAnimatableNone:Ks};function pe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function ks({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,a=0;if(!e)i=r=a=n;else{const o=n<.5?n*(1+e):n+e-n*e,l=2*n-o;i=pe(l,o,t+1/3),r=pe(l,o,t),a=pe(l,o,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(a*255),alpha:s}}function oe(t,e){return n=>n>0?e:t}const ne=(t,e,n)=>t+(e-t)*n,me=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Bs=[Ae,L,$],Ls=t=>Bs.find(e=>e.test(t));function ot(t){const e=Ls(t);if(!e)return!1;let n=e.parse(t);return e===$&&(n=ks(n)),n}const lt=(t,e)=>{const n=ot(t),s=ot(e);if(!n||!s)return oe(t,e);const i={...n};return r=>(i.red=me(n.red,s.red,r),i.green=me(n.green,s.green,r),i.blue=me(n.blue,s.blue,r),i.alpha=ne(n.alpha,s.alpha,r),L.transform(i))},xe=new Set(["none","hidden"]);function _s(t,e){return xe.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Us(t,e){return n=>ne(t,e,n)}function Xe(t){return typeof t=="number"?Us:typeof t=="string"?Ue(t)?oe:S.test(t)?lt:Ws:Array.isArray(t)?pn:typeof t=="object"?S.test(t)?lt:js:oe}function pn(t,e){const n=[...t],s=n.length,i=t.map((r,a)=>Xe(r)(r,e[a]));return r=>{for(let a=0;a<s;a++)n[a]=i[a](r);return n}}function js(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Xe(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function Gs(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],a=t.indexes[r][s[r]],o=t.values[a]??0;n[i]=o,s[r]++}return n}const Ws=(t,e)=>{const n=Y.createTransformer(e),s=ee(t),i=ee(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?xe.has(t)&&!i.values.length||xe.has(e)&&!s.values.length?_s(t,e):fe(pn(Gs(s,i),i.values),n):oe(t,e)};function mn(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?ne(t,e,n):Xe(t)(t,e)}const $s=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>I.update(e,n),stop:()=>Ve(e),now:()=>ae.isProcessing?ae.timestamp:E.now()}},gn=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=Math.round(t(r/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},le=2e4;function Ye(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<le;)e+=n,s=t.next(e);return e>=le?1/0:e}function Hs(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Ye(s),le);return{type:"keyframes",ease:r=>s.next(i*r).value/e,duration:R(i)}}const zs=5;function yn(t,e,n){const s=Math.max(e-zs,0);return Zt(n-t(s),e-s)}const w={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ge=.001;function Xs({duration:t=w.duration,bounce:e=w.bounce,velocity:n=w.velocity,mass:s=w.mass}){let i,r,a=1-e;a=z(w.minDamping,w.maxDamping,a),t=z(w.minDuration,w.maxDuration,R(t)),a<1?(i=c=>{const u=c*a,h=u*t,f=u-n,d=we(c,a),T=Math.exp(-h);return ge-f/d*T},r=c=>{const h=c*a*t,f=h*n+n,d=Math.pow(a,2)*Math.pow(c,2)*t,T=Math.exp(-h),v=we(Math.pow(c,2),a);return(-i(c)+ge>0?-1:1)*((f-d)*T)/v}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-ge+u*h},r=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const o=5/t,l=qs(i,r,o);if(t=O(t),isNaN(l))return{stiffness:w.stiffness,damping:w.damping,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:a*2*Math.sqrt(s*c),duration:t}}}const Ys=12;function qs(t,e,n){let s=n;for(let i=1;i<Ys;i++)s=s-t(s)/e(s);return s}function we(t,e){return t*Math.sqrt(1-e*e)}const Zs=["duration","bounce"],Js=["stiffness","damping","mass"];function ut(t,e){return e.some(n=>t[n]!==void 0)}function Qs(t){let e={velocity:w.velocity,stiffness:w.stiffness,damping:w.damping,mass:w.mass,isResolvedFromDuration:!1,...t};if(!ut(t,Js)&&ut(t,Zs))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*z(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:w.mass,stiffness:i,damping:r}}else{const n=Xs(t);e={...e,...n,mass:w.mass},e.isResolvedFromDuration=!0}return e}function ue(t=w.visualDuration,e=w.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],o={done:!1,value:r},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:d}=Qs({...n,velocity:-R(n.velocity||0)}),T=f||0,v=c/(2*Math.sqrt(l*u)),b=a-r,p=R(Math.sqrt(l/u)),V=Math.abs(b)<5;s||(s=V?w.restSpeed.granular:w.restSpeed.default),i||(i=V?w.restDelta.granular:w.restDelta.default);let g;if(v<1){const m=we(p,v);g=A=>{const M=Math.exp(-v*p*A);return a-M*((T+v*p*b)/m*Math.sin(m*A)+b*Math.cos(m*A))}}else if(v===1)g=m=>a-Math.exp(-p*m)*(b+(T+p*b)*m);else{const m=p*Math.sqrt(v*v-1);g=A=>{const M=Math.exp(-v*p*A),y=Math.min(m*A,300);return a-M*((T+v*p*b)*Math.sinh(y)+m*b*Math.cosh(y))/m}}const x={calculatedDuration:d&&h||null,next:m=>{const A=g(m);if(d)o.done=m>=h;else{let M=m===0?T:0;v<1&&(M=m===0?O(T):yn(g,m,A));const y=Math.abs(M)<=s,P=Math.abs(a-A)<=i;o.done=y&&P}return o.value=o.done?a:A,o},toString:()=>{const m=Math.min(Ye(x),le),A=gn(M=>x.next(m*M).value,m,30);return m+"ms "+A},toTransition:()=>{}};return x}ue.applyToOptions=t=>{const e=Hs(t,100,ue);return t.ease=e.ease,t.duration=O(e.duration),t.type="keyframes",t};function Se({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=y=>o!==void 0&&y<o||l!==void 0&&y>l,T=y=>o===void 0?l:l===void 0||Math.abs(o-y)<Math.abs(l-y)?o:l;let v=n*e;const b=h+v,p=a===void 0?b:a(b);p!==b&&(v=p-h);const V=y=>-v*Math.exp(-y/s),g=y=>p+V(y),x=y=>{const P=V(y),F=g(y);f.done=Math.abs(P)<=c,f.value=f.done?p:F};let m,A;const M=y=>{d(f.value)&&(m=y,A=ue({keyframes:[f.value,T(f.value)],velocity:yn(g,y,f.value),damping:i,stiffness:r,restDelta:c,restSpeed:u}))};return M(0),{calculatedDuration:null,next:y=>{let P=!1;return!A&&m===void 0&&(P=!0,x(y),M(y)),m!==void 0&&y>=m?A.next(y-m):(!P&&x(y),f)}}}function ei(t,e,n){const s=[],i=n||N.mix||mn,r=t.length-1;for(let a=0;a<r;a++){let o=i(t[a],t[a+1]);if(e){const l=Array.isArray(e)?e[a]||X:e;o=fe(l,o)}s.push(o)}return s}function ti(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(je(r===e.length),r===1)return()=>e[0];if(r===2&&e[0]===e[1])return()=>e[1];const a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=ei(e,s,i),l=o.length,c=u=>{if(a&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const f=Yt(t[h],t[h+1],u);return o[h](f)};return n?u=>c(z(t[0],t[r-1],u)):c}function ni(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Yt(0,e,s);t.push(ne(n,1,i))}}function si(t){const e=[0];return ni(e,t.length-1),e}function ii(t,e){return t.map(n=>n*e)}function ri(t,e){return t.map(()=>e||an).splice(0,t.length-1)}function Q({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=vs(s)?s.map(rt):rt(s),r={done:!1,value:e[0]},a=ii(n&&n.length===e.length?n:si(e),t),o=ti(a,e,{ease:Array.isArray(i)?i:ri(e,i)});return{calculatedDuration:t,next:l=>(r.value=o(l),r.done=l>=t,r)}}const ai=t=>t!==null;function qe(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(ai),o=i<0||e&&n!=="loop"&&e%2===1?0:r.length-1;return!o||s===void 0?r[o]:s}const oi={decay:Se,inertia:Se,tween:Q,keyframes:Q,spring:ue};function bn(t){typeof t.type=="string"&&(t.type=oi[t.type])}class Ze{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const li=t=>t/100;class Je extends Ze{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==E.now()&&this.tick(E.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;bn(e);const{type:n=Q,repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:a=0}=e;let{keyframes:o}=e;const l=n||Q;l!==Q&&typeof o[0]!="number"&&(this.mixKeyframes=fe(li,mn(o[0],o[1])),o=[0,100]);const c=l({...e,keyframes:o});r==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...o].reverse(),velocity:-a})),c.calculatedDuration===null&&(c.calculatedDuration=Ye(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:f,repeatDelay:d,type:T,onUpdate:v,finalKeyframe:b}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const p=this.currentTime-c*(this.playbackSpeed>=0?1:-1),V=this.playbackSpeed>=0?p<0:p>i;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let g=this.currentTime,x=s;if(h){const y=Math.min(this.currentTime,i)/o;let P=Math.floor(y),F=y%1;!F&&y>=1&&(F=1),F===1&&P--,P=Math.min(P,h+1),!!(P%2)&&(f==="reverse"?(F=1-F,d&&(F-=d/o)):f==="mirror"&&(x=a)),g=z(0,1,F)*o}const m=V?{done:!1,value:u[0]}:x.next(g);r&&(m.value=r(m.value));let{done:A}=m;!V&&l!==null&&(A=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const M=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return M&&T!==Se&&(m.value=qe(u,this.options,b,this.speed)),v&&v(m.value),M&&this.finish(),m}then(e,n){return this.finished.then(e,n)}get duration(){return R(this.calculatedDuration)}get time(){return R(this.currentTime)}set time(e){e=O(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(E.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=R(this.currentTime))}play(){if(this.isStopped)return;const{driver:e=$s,startTime:n}=this.options;this.driver||(this.driver=e(i=>this.tick(i))),this.options.onPlay?.();const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(E.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function ui(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const _=t=>t*180/Math.PI,Me=t=>{const e=_(Math.atan2(t[1],t[0]));return Ce(e)},ci={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Me,rotateZ:Me,skewX:t=>_(Math.atan(t[1])),skewY:t=>_(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ce=t=>(t=t%360,t<0&&(t+=360),t),ct=Me,ht=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ft=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),hi={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ht,scaleY:ft,scale:t=>(ht(t)+ft(t))/2,rotateX:t=>Ce(_(Math.atan2(t[6],t[5]))),rotateY:t=>Ce(_(Math.atan2(-t[2],t[0]))),rotateZ:ct,rotate:ct,skewX:t=>_(Math.atan(t[4])),skewY:t=>_(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Pe(t){return t.includes("scale")?1:0}function Fe(t,e){if(!t||t==="none")return Pe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=hi,i=n;else{const o=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=ci,i=o}if(!i)return Pe(e);const r=s[e],a=i[1].split(",").map(di);return typeof r=="function"?r(a):a[r]}const fi=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Fe(n,e)};function di(t){return parseFloat(t.trim())}const dt=t=>t===_e||t===Lt,pi=new Set(["x","y","z"]),mi=_t.filter(t=>!pi.has(t));function gi(t){const e=[];return mi.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const U={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Fe(e,"x"),y:(t,{transform:e})=>Fe(e,"y")};U.translateX=U.x;U.translateY=U.y;const j=new Set;let De=!1,Ee=!1,Ie=!1;function Tn(){if(Ee){const t=Array.from(j).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=gi(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,a])=>{s.getValue(r)?.set(a)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Ee=!1,De=!1,j.forEach(t=>t.complete(Ie)),j.clear()}function vn(){j.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ee=!0)})}function yi(){Ie=!0,vn(),Tn(),Ie=!1}class Qe{constructor(e,n,s,i,r,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(j.add(this),De||(De=!0,I.read(vn),I.resolveKeyframes(Tn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const r=i?.get(),a=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const o=s.readValue(n,a);o!=null&&(e[0]=o)}e[0]===void 0&&(e[0]=a),i&&r===void 0&&i.set(e[0])}ui(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),j.delete(this)}cancel(){this.state==="scheduled"&&(j.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const bi=t=>t.startsWith("--");function Ti(t,e,n){bi(e)?t.style.setProperty(e,n):t.style[e]=n}const vi=Ge(()=>window.ScrollTimeline!==void 0),Vi={};function Ai(t,e){const n=Ge(t);return()=>Vi[e]??n()}const Vn=Ai(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Z=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,pt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Z([0,.65,.55,1]),circOut:Z([.55,0,1,.45]),backIn:Z([.31,.01,.66,-.59]),backOut:Z([.33,1.53,.69,.99])};function An(t,e){if(t)return typeof t=="function"?Vn()?gn(t,e):"ease-out":on(t)?Z(t):Array.isArray(t)?t.map(n=>An(n,e)||pt.easeOut):pt[t]}function xi(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=An(o,i);Array.isArray(h)&&(u.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:a==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),t.animate(u,f)}function xn(t){return typeof t=="function"&&"applyToOptions"in t}function wi({type:t,...e}){return xn(t)&&Vn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Si extends Ze{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:r,allowFlatten:a=!1,finalKeyframe:o,onComplete:l}=e;this.isPseudoElement=!!r,this.allowFlatten=a,this.options=e,je(typeof e.type!="string");const c=wi(e);this.animation=xi(n,s,i,c,r),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const u=qe(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(u):Ti(n,s,u),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const e=this.animation.effect?.getComputedTiming?.().duration||0;return R(Number(e))}get time(){return R(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=O(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&vi()?(this.animation.timeline=e,X):n(this)}}const wn={anticipate:sn,backInOut:nn,circInOut:rn};function Mi(t){return t in wn}function Ci(t){typeof t.ease=="string"&&Mi(t.ease)&&(t.ease=wn[t.ease])}const mt=10;class Pi extends Si{constructor(e){Ci(e),bn(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:r,...a}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const o=new Je({...a,autoplay:!1}),l=O(this.finishedTime??this.time);n.setWithVelocity(o.sample(l-mt).value,o.sample(l).value,mt),o.stop()}}const gt=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Y.test(t)||t==="0")&&!t.startsWith("url("));function Fi(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function Di(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],a=gt(i,e),o=gt(r,e);return!a||!o?!1:Fi(t)||(n==="spring"||xn(n))&&s}function Oe(t){t.duration=0,t.type}const Ei=new Set(["opacity","clipPath","filter","transform"]),Ii=Ge(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Oi(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:a}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:c}=e.owner.getProps();return Ii()&&n&&Ei.has(n)&&(n!=="transform"||!c)&&!l&&!s&&i!=="mirror"&&r!==0&&a!=="inertia"}const Ri=40;class Ni extends Ze{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:a="loop",keyframes:o,name:l,motionValue:c,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:a,name:l,motionValue:c,element:u,...h},d=u?.KeyframeResolver||Qe;this.keyframeResolver=new d(o,(T,v,b)=>this.onKeyframesResolved(T,v,f,!b),l,c,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:r,type:a,velocity:o,delay:l,isHandoff:c,onUpdate:u}=s;this.resolvedAt=E.now(),Di(e,r,a,o)||((N.instantAnimations||!l)&&u?.(qe(e,s,n)),e[0]=e[e.length-1],Oe(s),s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Ri?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!c&&Oi(f)?new Pi({...f,element:f.motionValue.owner.current}):new Je(f);d.finished.then(()=>this.notifyFinished()).catch(X),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),yi()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Ki=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ki(t){const e=Ki.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Sn(t,e,n=1){const[s,i]=ki(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const a=r.trim();return zt(a)?parseFloat(a):a}return Ue(i)?Sn(i,e,n+1):i}function Mn(t,e){return t?.[e]??t?.default??t}const Cn=new Set(["width","height","top","left","right","bottom",..._t]),Bi={test:t=>t==="auto",parse:t=>t},Pn=t=>e=>e.test(t),Fn=[_e,Lt,ve,Xn,Yn,qn,Bi],yt=t=>Fn.find(Pn(t));function Li(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Xt(t):!0}const _i=new Set(["brightness","contrast","saturate","opacity"]);function Ui(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(He)||[];if(!s)return t;const i=n.replace(s,"");let r=_i.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const ji=/\b([a-z-]*)\(.*?\)/gu,Re={...Y,getAnimatableNone:t=>{const e=t.match(ji);return e?e.map(Ui).join(" "):t}},Gi={...Zn,color:S,backgroundColor:S,outlineColor:S,fill:S,stroke:S,borderColor:S,borderTopColor:S,borderRightColor:S,borderBottomColor:S,borderLeftColor:S,filter:Re,WebkitFilter:Re},Dn=t=>Gi[t];function En(t,e){let n=Dn(t);return n!==Re&&(n=Y),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Wi=new Set(["auto","none","0"]);function $i(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!Wi.has(r)&&ee(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=En(n,i)}class Hi extends Qe{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Ue(c))){const u=Sn(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!Cn.has(s)||e.length!==2)return;const[i,r]=e,a=yt(i),o=yt(r);if(a!==o)if(dt(a)&&dt(o))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else U[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Li(e[i]))&&s.push(i);s.length&&$i(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=U[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const r=s.length-1,a=s[r];s[r]=U[n](e.measureViewportBox(),window.getComputedStyle(e.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([o,l])=>{e.getValue(o).set(l)}),this.resolveNoneKeyframes()}}function zi(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const bt=30,Xi=t=>!isNaN(parseFloat(t));class Yi{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=s=>{const i=E.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const r of this.dependents)r.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=E.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Xi(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new qt);const s=this.events[e].add(n);return e==="change"?()=>{s(),I.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=E.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>bt)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,bt);return Zt(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ce(t,e){return new Yi(t,e)}const{schedule:qi}=ln(queueMicrotask,!1),Tt={x:!1,y:!1};function In(){return Tt.x||Tt.y}function On(t,e){const n=zi(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function vt(t){return!(t.pointerType==="touch"||In())}function Zi(t,e,n={}){const[s,i,r]=On(t,n),a=o=>{if(!vt(o))return;const{target:l}=o,c=e(l,o);if(typeof c!="function"||!l)return;const u=h=>{vt(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,i)};return s.forEach(o=>{o.addEventListener("pointerenter",a,i)}),r}const Rn=(t,e)=>e?t===e?!0:Rn(t,e.parentElement):!1,Nn=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,Ji=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Qi(t){return Ji.has(t.tagName)||t.tabIndex!==-1}const re=new WeakSet;function Vt(t){return e=>{e.key==="Enter"&&t(e)}}function ye(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const er=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=Vt(()=>{if(re.has(n))return;ye(n,"down");const i=Vt(()=>{ye(n,"up")}),r=()=>ye(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function At(t){return Nn(t)&&!In()}function tr(t,e,n={}){const[s,i,r]=On(t,n),a=o=>{const l=o.currentTarget;if(!At(o))return;re.add(l);const c=e(l,o),u=(d,T)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),re.has(l)&&re.delete(l),At(d)&&typeof c=="function"&&c(d,{success:T})},h=d=>{u(d,l===window||l===document||n.useGlobalTarget||Rn(l,d.target))},f=d=>{u(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(o=>{(n.useGlobalTarget?window:o).addEventListener("pointerdown",a,i),Jn(o)&&(o.addEventListener("focus",c=>er(c,i)),!Qi(o)&&!o.hasAttribute("tabindex")&&(o.tabIndex=0))}),r}const nr=[...Fn,S,Y],sr=t=>nr.find(Pn(t));function ir({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function la({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function rr(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function be(t){return t===void 0||t===1}function ar({scale:t,scaleX:e,scaleY:n}){return!be(t)||!be(e)||!be(n)}function or(t){return ar(t)||lr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function lr(t){return xt(t.x)||xt(t.y)}function xt(t){return t&&t!=="0%"}function wt(t,e,n){const s=t-n,i=e*s;return n+i}function St(t,e,n,s,i){return i!==void 0&&(t=wt(t,i,s)),wt(t,n,s)+e}function Ne(t,e=0,n=1,s,i){t.min=St(t.min,e,n,s,i),t.max=St(t.max,e,n,s,i)}function ur(t,{x:e,y:n}){Ne(t.x,e.translate,e.scale,e.originPoint),Ne(t.y,n.translate,n.scale,n.originPoint)}const Mt=.999999999999,Ct=1.0000000000001;function ua(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,a;for(let o=0;o<i;o++){r=n[o],a=r.projectionDelta;const{visualElement:l}=r.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&Dt(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,ur(t,a)),s&&or(r.latestValues)&&Dt(t,r.latestValues))}e.x<Ct&&e.x>Mt&&(e.x=1),e.y<Ct&&e.y>Mt&&(e.y=1)}function Pt(t,e){t.min=t.min+e,t.max=t.max+e}function Ft(t,e,n,s,i=.5){const r=ne(t.min,t.max,i);Ne(t,e,n,r,s)}function Dt(t,e){Ft(t.x,e.x,e.scaleX,e.scale,e.originX),Ft(t.y,e.y,e.scaleY,e.scale,e.originY)}function Kn(t,e){return ir(rr(t.getBoundingClientRect(),e))}function ca(t,e,n){const s=Kn(t,n),{scroll:i}=e;return i&&(Pt(s.x,i.offset.x),Pt(s.y,i.offset.y)),s}const Et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ha=()=>({x:Et(),y:Et()}),It=()=>({min:0,max:0}),kn=()=>({x:It(),y:It()}),Ke={current:null},Bn={current:!1};function cr(){if(Bn.current=!0,!!Qn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ke.current=t.matches;t.addEventListener("change",e),e()}else Ke.current=!1}const hr=new WeakMap;function fr(t,e,n){for(const s in e){const i=e[s],r=n[s];if(K(i))t.addValue(s,i);else if(K(r))t.addValue(s,ce(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const a=t.getValue(s);a.liveStyle===!0?a.jump(i):a.hasAnimated||a.set(i)}else{const a=t.getStaticValue(s);t.addValue(s,ce(a!==void 0?a:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Ot=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class dr{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Qe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=E.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,I.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=a;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=es(n),this.isVariantNode=ts(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&K(d)&&d.set(l[f])}}mount(e){this.current=e,hr.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Bn.current||cr(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ke.current,this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ve(this.notifyUpdate),Ve(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=he.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&I.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in it){const n=it[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):kn()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Ot.length;s++){const i=Ot[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,a=e[r];a&&(this.propEventSubscriptions[i]=this.on(i,a))}this.prevMotionValues=fr(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ce(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(zt(s)||Xt(s))?s=parseFloat(s):!sr(s)&&Y.test(n)&&(s=En(e,n)),this.setBaseTarget(e,K(s)?s.get():s)),K(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=Ut(this.props,n,this.presenceContext?.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!K(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new qt),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}scheduleRenderMicrotask(){qi.render(this.render)}}class Ln extends dr{constructor(){super(...arguments),this.KeyframeResolver=Hi}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;K(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function _n(t,{style:e,vars:n},s,i){const r=t.style;let a;for(a in e)r[a]=e[a];i?.applyProjectionStyles(r,s);for(a in n)r.setProperty(a,n[a])}function pr(t){return window.getComputedStyle(t)}class mr extends Ln{constructor(){super(...arguments),this.type="html",this.renderInstance=_n}readValueFromInstance(e,n){if(he.has(n))return this.projection?.isProjecting?Pe(n):fi(e,n);{const s=pr(e),i=(ns(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Kn(e,n)}build(e,n,s){ss(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return is(e,n,s)}}const Un=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function gr(t,e,n,s){_n(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Un.has(i)?i:jt(i),e.attrs[i])}class yr extends Ln{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=kn}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(he.has(n)){const s=Dn(n);return s&&s.default||0}return n=Un.has(n)?n:jt(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return rs(e,n,s)}build(e,n,s){as(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){gr(e,n,s,i)}mount(e){this.isSVGTag=os(e.tagName),super.mount(e)}}const br=(t,e)=>ls(t)?new yr(e):new mr(e,{allowProjection:t!==cs.Fragment});function H(t,e,n){const s=t.getProps();return Ut(s,e,n!==void 0?n:s.custom,t)}const ke=t=>Array.isArray(t);function Tr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ce(n))}function vr(t){return ke(t)?t[t.length-1]||0:t}function Vr(t,e){const n=H(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const a in r){const o=vr(r[a]);Tr(t,a,o)}}function Ar(t){return!!(K(t)&&t.add)}function xr(t,e){const n=t.getValue("willChange");if(Ar(n))return n.add(e);if(!n&&N.WillChange){const s=new N.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function wr(t){return t.props[us]}const Sr=t=>t!==null;function Mr(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Sr),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[r]}const Cr={type:"spring",stiffness:500,damping:25,restSpeed:10},Pr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Fr={type:"keyframes",duration:.8},Dr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Er=(t,{keyframes:e})=>e.length>2?Fr:he.has(t)?t.startsWith("scale")?Pr(e[1]):Cr:Dr;function Ir({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const Or=(t,e,n,s={},i,r)=>a=>{const o=Mn(s,t)||{},l=o.delay||s.delay||0;let{elapsed:c=0}=s;c=c-O(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-c,onUpdate:f=>{e.set(f),o.onUpdate&&o.onUpdate(f)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:i};Ir(o)||Object.assign(u,Er(t,u)),u.duration&&(u.duration=O(u.duration)),u.repeatDelay&&(u.repeatDelay=O(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(Oe(u),u.delay===0&&(h=!0)),(N.instantAnimations||N.skipAnimations)&&(h=!0,Oe(u),u.delay=0),u.allowFlatten=!o.type&&!o.ease,h&&!r&&e.get()!==void 0){const f=Mr(u.keyframes,o);if(f!==void 0){I.update(()=>{u.onUpdate(f),u.onComplete()});return}}return o.isSync?new Je(u):new Ni(u)};function Rr({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function jn(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);const l=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const u in o){const h=t.getValue(u,t.latestValues[u]??null),f=o[u];if(f===void 0||c&&Rr(c,u))continue;const d={delay:n,...Mn(r||{},u)},T=h.get();if(T!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===T&&!d.velocity)continue;let v=!1;if(window.MotionHandoffAnimation){const p=wr(t);if(p){const V=window.MotionHandoffAnimation(p,u,I);V!==null&&(d.startTime=V,v=!0)}}xr(t,u),h.start(Or(u,h,f,t.shouldReduceMotion&&Cn.has(u)?{type:!1}:d,t,v));const b=h.animation;b&&l.push(b)}return a&&Promise.all(l).then(()=>{I.update(()=>{a&&Vr(t,a)})}),l}function Gn(t,e,n,s=0,i=1){const r=Array.from(t).sort((c,u)=>c.sortNodePosition(u)).indexOf(e),a=t.size,o=(a-1)*s;return typeof n=="function"?n(r,a):i===1?r*s:o-r*s}function Be(t,e,n={}){const s=H(t,e,n.type==="exit"?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(jn(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(l=0)=>{const{delayChildren:c=0,staggerChildren:u,staggerDirection:h}=i;return Nr(t,e,l,c,u,h,n)}:()=>Promise.resolve(),{when:o}=i;if(o){const[l,c]=o==="beforeChildren"?[r,a]:[a,r];return l().then(()=>c())}else return Promise.all([r(),a(n.delay)])}function Nr(t,e,n=0,s=0,i=0,r=1,a){const o=[];for(const l of t.variantChildren)l.notify("AnimationStart",e),o.push(Be(l,e,{...a,delay:n+(typeof s=="function"?0:s)+Gn(t.variantChildren,l,s,i,r)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(o)}function Kr(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Be(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Be(t,e,n);else{const i=typeof e=="function"?H(t,e,n.custom):e;s=Promise.all(jn(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Wn(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const kr=Wt.length;function $n(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?$n(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<kr;n++){const s=Wt[n],i=t.props[s];(Gt(i)||i===!1)&&(e[s]=i)}return e}const Br=[...Ht].reverse(),Lr=Ht.length;function _r(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Kr(t,n,s)))}function Ur(t){let e=_r(t),n=Rt(),s=!0;const i=l=>(c,u)=>{const h=H(t,u,l==="exit"?t.presenceContext?.custom:void 0);if(h){const{transition:f,transitionEnd:d,...T}=h;c={...c,...T,...d}}return c};function r(l){e=l(t)}function a(l){const{props:c}=t,u=$n(t.parent)||{},h=[],f=new Set;let d={},T=1/0;for(let b=0;b<Lr;b++){const p=Br[b],V=n[p],g=c[p]!==void 0?c[p]:u[p],x=Gt(g),m=p===l?V.isActive:null;m===!1&&(T=b);let A=g===u[p]&&g!==c[p]&&x;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),V.protectedKeys={...d},!V.isActive&&m===null||!g&&!V.prevProp||$t(g)||typeof g=="boolean")continue;const M=jr(V.prevProp,g);let y=M||p===l&&V.isActive&&!A&&x||b>T&&x,P=!1;const F=Array.isArray(g)?g:[g];let G=F.reduce(i(p),{});m===!1&&(G={});const{prevResolvedValues:tt={}}=V,Hn={...tt,...G},nt=C=>{y=!0,f.has(C)&&(P=!0,f.delete(C)),V.needsAnimating[C]=!0;const D=t.getValue(C);D&&(D.liveStyle=!1)};for(const C in Hn){const D=G[C],k=tt[C];if(d.hasOwnProperty(C))continue;let W=!1;ke(D)&&ke(k)?W=!Wn(D,k):W=D!==k,W?D!=null?nt(C):f.add(C):D!==void 0&&f.has(C)?nt(C):V.protectedKeys[C]=!0}V.prevProp=g,V.prevResolvedValues=G,V.isActive&&(d={...d,...G}),s&&t.blockInitialAnimation&&(y=!1);const st=A&&M;y&&(!st||P)&&h.push(...F.map(C=>{const D={type:p};if(typeof C=="string"&&s&&!st&&t.manuallyAnimateOnMount&&t.parent){const{parent:k}=t,W=H(k,C);if(k.enteringChildren&&W){const{delayChildren:zn}=W.transition||{};D.delay=Gn(k.enteringChildren,t,zn)}}return{animation:C,options:D}}))}if(f.size){const b={};if(typeof c.initial!="boolean"){const p=H(t,Array.isArray(c.initial)?c.initial[0]:c.initial);p&&p.transition&&(b.transition=p.transition)}f.forEach(p=>{const V=t.getBaseTarget(p),g=t.getValue(p);g&&(g.liveStyle=!0),b[p]=V??null}),h.push({animation:b})}let v=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(v=!1),s=!1,v?e(h):Promise.resolve()}function o(l,c){if(n[l].isActive===c)return Promise.resolve();t.variantChildren?.forEach(h=>h.animationState?.setActive(l,c)),n[l].isActive=c;const u=a(l);for(const h in n)n[h].protectedKeys={};return u}return{animateChanges:a,setActive:o,setAnimateFunction:r,getState:()=>n,reset:()=>{n=Rt(),s=!0}}}function jr(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Wn(e,t):!1}function B(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Rt(){return{animate:B(!0),whileInView:B(),whileHover:B(),whileTap:B(),whileDrag:B(),whileFocus:B(),exit:B()}}class q{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Gr extends q{constructor(e){super(e),e.animationState||(e.animationState=Ur(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();$t(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Wr=0;class $r extends q{constructor(){super(...arguments),this.id=Wr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Hr={animation:{Feature:Gr},exit:{Feature:$r}};function Nt(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function et(t){return{point:{x:t.pageX,y:t.pageY}}}const fa=t=>e=>Nn(e)&&t(e,et(e));function Kt(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,r=s[i];r&&I.postRender(()=>r(e,et(e)))}class zr extends q{mount(){const{current:e}=this.node;e&&(this.unmount=Zi(e,(n,s)=>(Kt(this.node,s,"Start"),i=>Kt(this.node,i,"End"))))}unmount(){}}class Xr extends q{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fe(Nt(this.node.current,"focus",()=>this.onFocus()),Nt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function kt(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),r=s[i];r&&I.postRender(()=>r(e,et(e)))}class Yr extends q{mount(){const{current:e}=this.node;e&&(this.unmount=tr(e,(n,s)=>(kt(this.node,s,"Start"),(i,{success:r})=>kt(this.node,i,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Le=new WeakMap,Te=new WeakMap,qr=t=>{const e=Le.get(t.target);e&&e(t)},Zr=t=>{t.forEach(qr)};function Jr({root:t,...e}){const n=t||document;Te.has(n)||Te.set(n,{});const s=Te.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Zr,{root:t,...e})),s[i]}function Qr(t,e,n){const s=Jr(e);return Le.set(t,n),s.observe(t),()=>{Le.delete(t),s.unobserve(t)}}const ea={some:0,all:1};class ta extends q{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,a={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:ea[i]},o=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,r&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return Qr(this.node.current,a,o)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(na(e,n))&&this.startObserver()}unmount(){}}function na({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const sa={inView:{Feature:ta},tap:{Feature:Yr},focus:{Feature:Xr},hover:{Feature:zr}},da={renderer:br,...Hr,...sa};export{E as A,ys as B,wt as C,Mn as D,oa as E,q as F,or as G,Pt as H,Dt as I,ar as J,ur as K,lr as L,ua as M,ha as N,wr as O,Yi as P,qt as S,Nt as a,fa as b,ae as c,da as d,Nn as e,I as f,et as g,Ve as h,Tt as i,R as j,Yt as k,kn as l,ne as m,ca as n,la as o,fe as p,ir as q,xr as r,O as s,Or as t,X as u,Y as v,qi as w,ce as x,hs as y,fs as z};
