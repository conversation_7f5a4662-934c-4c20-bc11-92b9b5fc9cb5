import{j as s,r as m,w as u}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as h,a as d}from"./react-redux-B5osdedR.js";import{I as o}from"./declaration-xyc84-tJ.js";import{u as a}from"./useTranslation-BG59QWH_.js";import{G as f}from"./iconBase-2PDVWRGH.js";import{u as p}from"./use-conversation-id-0JHAicdF.js";import{e as x,i as c,s as z}from"./browser-slice-DabBaamq.js";import"./i18nInstance-DBIXdvxg.js";function w({src:t}){const{t:r}=a();return s.jsx("img",{src:t,style:{objectFit:"contain",width:"100%",height:"auto"},className:"rounded-xl",alt:r(o.BROWSER$SCREENSHOT_ALT)})}function j(t){return f({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 48h-.7c-55.4.2-107.4 21.9-146.6 61.1C69.6 148.4 48 200.5 48 256s21.6 107.6 60.8 146.9c39.1 39.2 91.2 60.9 146.6 61.1h.7c114.7 0 208-93.3 208-208S370.7 48 256 48zm180.2 194h-77.6c-.9-26.7-4.2-52.2-9.8-76.2 17.1-5.5 33.7-12.5 49.7-21 22 28.2 35 61.6 37.7 97.2zM242 242h-61.8c.8-24.5 3.8-47.7 8.8-69.1 17.4 3.9 35.1 6.3 53 7.1v62zm0 28v61.9c-17.8.8-35.6 3.2-53 7.1-5-21.4-8-44.6-8.8-69H242zm28 0h61.3c-.8 24.4-3.8 47.6-8.8 68.9-17.2-3.9-34.8-6.2-52.5-7V270zm0-28v-62c17.8-.8 35.4-3.2 52.5-7 5 21.4 8 44.5 8.8 69H270zm109.4-117.9c-12.3 6.1-25 11.3-38 15.5-7.1-21.4-16.1-39.9-26.5-54.5 24 8.3 45.9 21.6 64.5 39zM315 146.8c-14.7 3.2-29.8 5.2-45 6V79.4c17 9.2 33.6 33.9 45 67.4zM242 79v73.7c-15.4-.8-30.6-2.8-45.5-6.1 11.6-33.8 28.4-58.5 45.5-67.6zm-45.6 6.4c-10.3 14.5-19.2 32.9-26.3 54.1-12.8-4.2-25.4-9.4-37.5-15.4 18.4-17.3 40.1-30.5 63.8-38.7zm-82.9 59.5c15.8 8.4 32.3 15.4 49.2 20.8-5.7 23.9-9 49.5-9.8 76.2h-77c2.6-35.4 15.6-68.8 37.6-97zM75.8 270h77c.9 26.7 4.2 52.3 9.8 76.2-16.9 5.5-33.4 12.5-49.2 20.8-21.9-28.1-34.9-61.5-37.6-97zm56.7 117.9c12.1-6 24.7-11.2 37.6-15.4 7.1 21.3 16 39.6 26.3 54.2-23.7-8.4-45.4-21.5-63.9-38.8zm64-22.6c14.9-3.3 30.2-5.3 45.5-6.1V433c-17.2-9.1-33.9-33.9-45.5-67.7zm73.5 67.3v-73.5c15.2.8 30.3 2.8 45 6-11.4 33.6-28 58.3-45 67.5zm45-5.7c10.4-14.6 19.4-33.1 26.5-54.5 13 4.2 25.8 9.5 38 15.6-18.6 17.3-40.6 30.6-64.5 38.9zm83.5-59.8c-16-8.5-32.6-15.5-49.7-21 5.6-23.9 8.9-49.4 9.8-76.1h77.6c-2.7 35.5-15.6 68.9-37.7 97.1z"},child:[]}]})(t)}function v(){const{t}=a();return s.jsxs("div",{className:"flex flex-col items-center h-full justify-center",children:[s.jsx(j,{size:100}),t(o.BROWSER$NO_PAGE_LOADED)]})}function S(){const{url:t,screenshotSrc:r}=h(l=>l.browser),{conversationId:n}=p(),e=d();m.useEffect(()=>{e(x(c.url)),e(z(c.screenshotSrc))},[n]);const i=r&&r.startsWith("data:image/png;base64,")?r:`data:image/png;base64,${r||""}`;return s.jsxs("div",{className:"h-full w-full flex flex-col text-neutral-400",children:[s.jsx("div",{className:"w-full p-2 truncate border-b border-neutral-600",children:t}),s.jsx("div",{className:"overflow-y-auto grow scrollbar-hide rounded-xl",children:r?s.jsx(w,{src:i}):s.jsx(v,{})})]})}function b(){return s.jsx(S,{})}const O=u(b);export{O as default};
