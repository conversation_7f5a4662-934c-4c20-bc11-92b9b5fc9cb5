function _(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var ve=typeof Symbol=="function"&&Symbol.observable||"@@observable",ue=ve,Q=()=>Math.random().toString(36).substring(7).split("").join("."),Me={INIT:`@@redux/INIT${Q()}`,REPLACE:`@@redux/REPLACE${Q()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${Q()}`},B=Me;function ie(e){if(typeof e!="object"||e===null)return!1;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||Object.getPrototypeOf(e)===null}function we(e,t,r){if(typeof e!="function")throw new Error(_(2));if(typeof t=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(_(0));if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(_(1));return r(we)(e,t)}let n=e,i=t,o=new Map,c=o,s=0,u=!1;function a(){c===o&&(c=new Map,o.forEach((h,S)=>{c.set(S,h)}))}function f(){if(u)throw new Error(_(3));return i}function d(h){if(typeof h!="function")throw new Error(_(4));if(u)throw new Error(_(5));let S=!0;a();const O=s++;return c.set(O,h),function(){if(S){if(u)throw new Error(_(6));S=!1,a(),c.delete(O),o=null}}}function p(h){if(!ie(h))throw new Error(_(7));if(typeof h.type>"u")throw new Error(_(8));if(typeof h.type!="string")throw new Error(_(17));if(u)throw new Error(_(9));try{u=!0,i=n(i,h)}finally{u=!1}return(o=c).forEach(O=>{O()}),h}function E(h){if(typeof h!="function")throw new Error(_(10));n=h,p({type:B.REPLACE})}function C(){const h=d;return{subscribe(S){if(typeof S!="object"||S===null)throw new Error(_(11));function O(){const y=S;y.next&&y.next(f())}return O(),{unsubscribe:h(O)}},[ue](){return this}}}return p({type:B.INIT}),{dispatch:p,subscribe:d,getState:f,replaceReducer:E,[ue]:C}}function ze(e){Object.keys(e).forEach(t=>{const r=e[t];if(typeof r(void 0,{type:B.INIT})>"u")throw new Error(_(12));if(typeof r(void 0,{type:B.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(_(13))})}function Ne(e){const t=Object.keys(e),r={};for(let o=0;o<t.length;o++){const c=t[o];typeof e[c]=="function"&&(r[c]=e[c])}const n=Object.keys(r);let i;try{ze(r)}catch(o){i=o}return function(c={},s){if(i)throw i;let u=!1;const a={};for(let f=0;f<n.length;f++){const d=n[f],p=r[d],E=c[d],C=p(E,s);if(typeof C>"u")throw s&&s.type,new Error(_(14));a[d]=C,u=u||C!==E}return u=u||n.length!==Object.keys(c).length,u?a:c}}function $(...e){return e.length===0?t=>t:e.length===1?e[0]:e.reduce((t,r)=>(...n)=>t(r(...n)))}function Ie(...e){return t=>(r,n)=>{const i=t(r,n);let o=()=>{throw new Error(_(15))};const c={getState:i.getState,dispatch:(u,...a)=>o(u,...a)},s=e.map(u=>u(c));return o=$(...s)(i.dispatch),{...i,dispatch:o}}}function xe(e){return ie(e)&&"type"in e&&typeof e.type=="string"}var me=Symbol.for("immer-nothing"),fe=Symbol.for("immer-draftable"),g=Symbol.for("immer-state");function P(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var z=Object.getPrototypeOf;function v(e){return!!e&&!!e[g]}function A(e){return e?ge(e)||Array.isArray(e)||!!e[fe]||!!e.constructor?.[fe]||q(e)||H(e):!1}var Fe=Object.prototype.constructor.toString();function ge(e){if(!e||typeof e!="object")return!1;const t=z(e);if(t===null)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===Fe}function K(e,t){L(e)===0?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function L(e){const t=e[g];return t?t.type_:Array.isArray(e)?1:q(e)?2:H(e)?3:0}function Z(e,t){return L(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function be(e,t,r){const n=L(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function je(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function q(e){return e instanceof Map}function H(e){return e instanceof Set}function k(e){return e.copy_||e.base_}function V(e,t){if(q(e))return new Map(e);if(H(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=ge(e);if(t===!0||t==="class_only"&&!r){const n=Object.getOwnPropertyDescriptors(e);delete n[g];let i=Reflect.ownKeys(n);for(let o=0;o<i.length;o++){const c=i[o],s=n[c];s.writable===!1&&(s.writable=!0,s.configurable=!0),(s.get||s.set)&&(n[c]={configurable:!0,writable:!0,enumerable:s.enumerable,value:e[c]})}return Object.create(z(e),n)}else{const n=z(e);if(n!==null&&r)return{...e};const i=Object.create(n);return Object.assign(i,e)}}function oe(e,t=!1){return X(e)||v(e)||!A(e)||(L(e)>1&&(e.set=e.add=e.clear=e.delete=Be),Object.freeze(e),t&&Object.entries(e).forEach(([r,n])=>oe(n,!0))),e}function Be(){P(2)}function X(e){return Object.isFrozen(e)}var $e={};function M(e){const t=$e[e];return t||P(0,e),t}var I;function Ee(){return I}function Ke(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ae(e,t){t&&(M("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function ee(e){te(e),e.drafts_.forEach(Ue),e.drafts_=null}function te(e){e===I&&(I=e.parent_)}function le(e){return I=Ke(I,e)}function Ue(e){const t=e[g];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function de(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return e!==void 0&&e!==r?(r[g].modified_&&(ee(t),P(4)),A(e)&&(e=U(t,e),t.parent_||W(t,e)),t.patches_&&M("Patches").generateReplacementPatches_(r[g].base_,e,t.patches_,t.inversePatches_)):e=U(t,r,[]),ee(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==me?e:void 0}function U(e,t,r){if(X(t))return t;const n=t[g];if(!n)return K(t,(i,o)=>ye(e,n,t,i,o,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return W(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const i=n.copy_;let o=i,c=!1;n.type_===3&&(o=new Set(i),i.clear(),c=!0),K(o,(s,u)=>ye(e,n,i,s,u,r,c)),W(e,i,!1),r&&e.patches_&&M("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function ye(e,t,r,n,i,o,c){if(v(i)){const s=o&&t&&t.type_!==3&&!Z(t.assigned_,n)?o.concat(n):void 0,u=U(e,i,s);if(be(r,n,u),v(u))e.canAutoFreeze_=!1;else return}else c&&r.add(i);if(A(i)&&!X(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;U(e,i),(!t||!t.scope_.parent_)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,n)&&W(e,i)}}function W(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&oe(t,r)}function We(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:Ee(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,o=ce;r&&(i=[n],o=x);const{revoke:c,proxy:s}=Proxy.revocable(i,o);return n.draft_=s,n.revoke_=c,s}var ce={get(e,t){if(t===g)return e;const r=k(e);if(!Z(r,t))return Le(e,r,t);const n=r[t];return e.finalized_||!A(n)?n:n===J(e.base_,t)?(Y(e),e.copy_[t]=ne(n,e)):n},has(e,t){return t in k(e)},ownKeys(e){return Reflect.ownKeys(k(e))},set(e,t,r){const n=Ce(k(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const i=J(k(e),t),o=i?.[g];if(o&&o.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(je(r,i)&&(r!==void 0||Z(e.base_,t)))return!0;Y(e),re(e)}return e.copy_[t]===r&&(r!==void 0||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty(e,t){return J(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,Y(e),re(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const r=k(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty(){P(11)},getPrototypeOf(e){return z(e.base_)},setPrototypeOf(){P(12)}},x={};K(ce,(e,t)=>{x[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});x.deleteProperty=function(e,t){return x.set.call(this,e,t,void 0)};x.set=function(e,t,r){return ce.set.call(this,e[0],t,r,e[0])};function J(e,t){const r=e[g];return(r?k(r):e)[t]}function Le(e,t,r){const n=Ce(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}function Ce(e,t){if(!(t in e))return;let r=z(e);for(;r;){const n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=z(r)}}function re(e){e.modified_||(e.modified_=!0,e.parent_&&re(e.parent_))}function Y(e){e.copy_||(e.copy_=V(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var qe=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,r,n)=>{if(typeof t=="function"&&typeof r!="function"){const o=r;r=t;const c=this;return function(u=o,...a){return c.produce(u,f=>r.call(this,f,...a))}}typeof r!="function"&&P(6),n!==void 0&&typeof n!="function"&&P(7);let i;if(A(t)){const o=le(this),c=ne(t,void 0);let s=!0;try{i=r(c),s=!1}finally{s?ee(o):te(o)}return ae(o,n),de(i,o)}else if(!t||typeof t!="object"){if(i=r(t),i===void 0&&(i=t),i===me&&(i=void 0),this.autoFreeze_&&oe(i,!0),n){const o=[],c=[];M("Patches").generateReplacementPatches_(t,i,o,c),n(o,c)}return i}else P(1,t)},this.produceWithPatches=(t,r)=>{if(typeof t=="function")return(c,...s)=>this.produceWithPatches(c,u=>t(u,...s));let n,i;return[this.produce(t,r,(c,s)=>{n=c,i=s}),n,i]},typeof e?.autoFreeze=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof e?.useStrictShallowCopy=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){A(e)||P(8),v(e)&&(e=He(e));const t=le(this),r=ne(e,void 0);return r[g].isManual_=!0,te(t),r}finishDraft(e,t){const r=e&&e[g];(!r||!r.isManual_)&&P(9);const{scope_:n}=r;return ae(n,t),de(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const i=t[r];if(i.path.length===0&&i.op==="replace"){e=i.value;break}}r>-1&&(t=t.slice(r+1));const n=M("Patches").applyPatches_;return v(e)?n(e,t):this.produce(e,i=>n(i,t))}};function ne(e,t){const r=q(e)?M("MapSet").proxyMap_(e,t):H(e)?M("MapSet").proxySet_(e,t):We(e,t);return(t?t.scope_:Ee()).drafts_.push(r),r}function He(e){return v(e)||P(10,e),Se(e)}function Se(e){if(!A(e)||X(e))return e;const t=e[g];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=V(e,t.scope_.immer_.useStrictShallowCopy_)}else r=V(e,!0);return K(r,(n,i)=>{be(r,n,Se(i))}),t&&(t.finalized_=!1),r}var b=new qe,Pe=b.produce;b.produceWithPatches.bind(b);b.setAutoFreeze.bind(b);b.setUseStrictShallowCopy.bind(b);b.applyPatches.bind(b);b.createDraft.bind(b);b.finishDraft.bind(b);function Oe(e){return({dispatch:r,getState:n})=>i=>o=>typeof o=="function"?o(r,n,e):i(o)}var Xe=Oe(),Ge=Oe,Qe=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?$:$.apply(null,arguments)};function he(e,t){function r(...n){if(t){let i=t(...n);if(!i)throw new Error(T(0));return{type:e,payload:i.payload,..."meta"in i&&{meta:i.meta},..."error"in i&&{error:i.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>xe(n)&&n.type===e,r}var Re=class N extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,N.prototype)}static get[Symbol.species](){return N}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new N(...t[0].concat(this)):new N(...t.concat(this))}};function pe(e){return A(e)?Pe(e,()=>{}):e}function F(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function Je(e){return typeof e=="boolean"}var Ye=()=>function(t){const{thunk:r=!0,immutableCheck:n=!0,serializableCheck:i=!0,actionCreatorCheck:o=!0}=t??{};let c=new Re;return r&&(Je(r)?c.push(Xe):c.push(Ge(r.extraArgument))),c},Ze="RTK_autoBatch",_e=e=>t=>{setTimeout(t,e)},Ve=(e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let i=!0,o=!1,c=!1;const s=new Set,u=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:_e(10):e.type==="callback"?e.queueNotification:_e(e.timeout),a=()=>{c=!1,o&&(o=!1,s.forEach(f=>f()))};return Object.assign({},n,{subscribe(f){const d=()=>i&&f(),p=n.subscribe(d);return s.add(f),()=>{p(),s.delete(f)}},dispatch(f){try{return i=!f?.meta?.[Ze],o=!i,o&&(c||(c=!0,u(a))),n.dispatch(f)}finally{i=!0}}})},et=e=>function(r){const{autoBatch:n=!0}=r??{};let i=new Re(e);return n&&i.push(Ve(typeof n=="object"?n:void 0)),i};function ht(e){const t=Ye(),{reducer:r=void 0,middleware:n,devTools:i=!0,preloadedState:o=void 0,enhancers:c=void 0}=e||{};let s;if(typeof r=="function")s=r;else if(ie(r))s=Ne(r);else throw new Error(T(1));let u;typeof n=="function"?u=n(t):u=t();let a=$;i&&(a=Qe({trace:!1,...typeof i=="object"&&i}));const f=Ie(...u),d=et(f);let p=typeof c=="function"?c(d):d();const E=a(...p);return we(s,o,E)}function Te(e){const t={},r=[];let n;const i={addCase(o,c){const s=typeof o=="string"?o:o.type;if(!s)throw new Error(T(28));if(s in t)throw new Error(T(29));return t[s]=c,i},addMatcher(o,c){return r.push({matcher:o,reducer:c}),i},addDefaultCase(o){return n=o,i}};return e(i),[t,r,n]}function tt(e){return typeof e=="function"}function rt(e,t){let[r,n,i]=Te(t),o;if(tt(e))o=()=>pe(e());else{const s=pe(e);o=()=>s}function c(s=o(),u){let a=[r[u.type],...n.filter(({matcher:f})=>f(u)).map(({reducer:f})=>f)];return a.filter(f=>!!f).length===0&&(a=[i]),a.reduce((f,d)=>{if(d)if(v(f)){const E=d(f,u);return E===void 0?f:E}else{if(A(f))return Pe(f,p=>d(p,u));{const p=d(f,u);if(p===void 0){if(f===null)return f;throw Error("A case reducer on a non-draftable value must not return undefined")}return p}}return f},s)}return c.getInitialState=o,c}var nt=Symbol.for("rtk-slice-createasyncthunk");function it(e,t){return`${e}/${t}`}function ot({creators:e}={}){const t=e?.asyncThunk?.[nt];return function(n){const{name:i,reducerPath:o=i}=n;if(!i)throw new Error(T(11));const c=(typeof n.reducers=="function"?n.reducers(ut()):n.reducers)||{},s=Object.keys(c),u={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},a={addCase(l,y){const w=typeof l=="string"?l:l.type;if(!w)throw new Error(T(12));if(w in u.sliceCaseReducersByType)throw new Error(T(13));return u.sliceCaseReducersByType[w]=y,a},addMatcher(l,y){return u.sliceMatchers.push({matcher:l,reducer:y}),a},exposeAction(l,y){return u.actionCreators[l]=y,a},exposeCaseReducer(l,y){return u.sliceCaseReducersByName[l]=y,a}};s.forEach(l=>{const y=c[l],w={reducerName:l,type:it(i,l),createNotation:typeof n.reducers=="function"};at(y)?dt(w,y,a,t):ft(w,y,a)});function f(){const[l={},y=[],w=void 0]=typeof n.extraReducers=="function"?Te(n.extraReducers):[n.extraReducers],D={...l,...u.sliceCaseReducersByType};return rt(n.initialState,R=>{for(let m in D)R.addCase(m,D[m]);for(let m of u.sliceMatchers)R.addMatcher(m.matcher,m.reducer);for(let m of y)R.addMatcher(m.matcher,m.reducer);w&&R.addDefaultCase(w)})}const d=l=>l,p=new Map,E=new WeakMap;let C;function G(l,y){return C||(C=f()),C(l,y)}function h(){return C||(C=f()),C.getInitialState()}function S(l,y=!1){function w(R){let m=R[l];return typeof m>"u"&&y&&(m=F(E,w,h)),m}function D(R=d){const m=F(p,y,()=>new WeakMap);return F(m,R,()=>{const se={};for(const[De,ke]of Object.entries(n.selectors??{}))se[De]=ct(ke,R,()=>F(E,R,h),y);return se})}return{reducerPath:l,getSelectors:D,get selectors(){return D(w)},selectSlice:w}}const O={name:i,reducer:G,actions:u.actionCreators,caseReducers:u.sliceCaseReducersByName,getInitialState:h,...S(o),injectInto(l,{reducerPath:y,...w}={}){const D=y??o;return l.inject({reducerPath:D,reducer:G},w),{...O,...S(D,!0)}}};return O}}function ct(e,t,r,n){function i(o,...c){let s=t(o);return typeof s>"u"&&n&&(s=r()),e(s,...c)}return i.unwrapped=e,i}var st=ot();function ut(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function ft({type:e,reducerName:t,createNotation:r},n,i){let o,c;if("reducer"in n){if(r&&!lt(n))throw new Error(T(17));o=n.reducer,c=n.prepare}else o=n;i.addCase(e,o).exposeCaseReducer(t,o).exposeAction(t,c?he(e,c):he(e))}function at(e){return e._reducerDefinitionType==="asyncThunk"}function lt(e){return e._reducerDefinitionType==="reducerWithPrepare"}function dt({type:e,reducerName:t},r,n,i){if(!i)throw new Error(T(18));const{payloadCreator:o,fulfilled:c,pending:s,rejected:u,settled:a,options:f}=r,d=i(e,o,f);n.exposeAction(t,d),c&&n.addCase(d.fulfilled,c),s&&n.addCase(d.pending,s),u&&n.addCase(d.rejected,u),a&&n.addMatcher(d.settled,a),n.exposeCaseReducer(t,{fulfilled:c||j,pending:s||j,rejected:u||j,settled:a||j})}function j(){}function T(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}const yt={url:"https://github.com/All-Hands-AI/OpenHands",screenshotSrc:""},Ae=st({name:"browser",initialState:yt,reducers:{setUrl:(e,t)=>{e.url=t.payload},setScreenshotSrc:(e,t)=>{e.screenshotSrc=t.payload}}}),{setUrl:pt,setScreenshotSrc:_t}=Ae.actions,wt=Ae.reducer;export{Ne as a,ht as b,st as c,wt as d,pt as e,yt as i,_t as s};
