const d=o=>typeof o=="string",K=()=>{let o,e;const t=new Promise((i,s)=>{o=i,e=s});return t.resolve=o,t.reject=e,t},ee=o=>o==null?"":""+o,ye=(o,e,t)=>{o.forEach(i=>{e[i]&&(t[i]=e[i])})},Se=/###/g,te=o=>o&&o.indexOf("###")>-1?o.replace(Se,"."):o,se=o=>!o||d(o),A=(o,e,t)=>{const i=d(e)?e.split("."):e;let s=0;for(;s<i.length-1;){if(se(o))return{};const n=te(i[s]);!o[n]&&t&&(o[n]=new t),Object.prototype.hasOwnProperty.call(o,n)?o=o[n]:o={},++s}return se(o)?{}:{obj:o,k:te(i[s])}},ie=(o,e,t)=>{const{obj:i,k:s}=A(o,e,Object);if(i!==void 0||e.length===1){i[s]=t;return}let n=e[e.length-1],r=e.slice(0,e.length-1),a=A(o,r,Object);for(;a.obj===void 0&&r.length;)n=`${r[r.length-1]}.${n}`,r=r.slice(0,r.length-1),a=A(o,r,Object),a?.obj&&typeof a.obj[`${a.k}.${n}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${n}`]=t},Oe=(o,e,t,i)=>{const{obj:s,k:n}=A(o,e,Object);s[n]=s[n]||[],s[n].push(t)},z=(o,e)=>{const{obj:t,k:i}=A(o,e);if(t&&Object.prototype.hasOwnProperty.call(t,i))return t[i]},Le=(o,e,t)=>{const i=z(o,t);return i!==void 0?i:z(e,t)},ge=(o,e,t)=>{for(const i in e)i!=="__proto__"&&i!=="constructor"&&(i in o?d(o[i])||o[i]instanceof String||d(e[i])||e[i]instanceof String?t&&(o[i]=e[i]):ge(o[i],e[i],t):o[i]=e[i]);return o},T=o=>o.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var be={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const we=o=>d(o)?o.replace(/[&<>"'\/]/g,e=>be[e]):o;class ve{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const i=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,i),this.regExpQueue.push(e),i}}const Ce=[" ",",","?","!",";"],Re=new ve(20),$e=(o,e,t)=>{e=e||"",t=t||"";const i=Ce.filter(r=>e.indexOf(r)<0&&t.indexOf(r)<0);if(i.length===0)return!0;const s=Re.getRegExp(`(${i.map(r=>r==="?"?"\\?":r).join("|")})`);let n=!s.test(o);if(!n){const r=o.indexOf(t);r>0&&!s.test(o.substring(0,r))&&(n=!0)}return n},_=(o,e,t=".")=>{if(!o)return;if(o[e])return Object.prototype.hasOwnProperty.call(o,e)?o[e]:void 0;const i=e.split(t);let s=o;for(let n=0;n<i.length;){if(!s||typeof s!="object")return;let r,a="";for(let l=n;l<i.length;++l)if(l!==n&&(a+=t),a+=i[l],r=s[a],r!==void 0){if(["string","number","boolean"].indexOf(typeof r)>-1&&l<i.length-1)continue;n+=l-n+1;break}s=r}return s},U=o=>o?.replace("_","-"),Pe={type:"logger",log(o){this.output("log",o)},warn(o){this.output("warn",o)},error(o){this.output("error",o)},output(o,e){console?.[o]?.apply?.(console,e)}};class J{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||Pe,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,i,s){return s&&!this.debug?null:(d(e[0])&&(e[0]=`${i}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new J(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new J(this.logger,e)}}var $=new J;class Q{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(i=>{this.observers[i]||(this.observers[i]=new Map);const s=this.observers[i].get(t)||0;this.observers[i].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([s,n])=>{for(let r=0;r<n;r++)s(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([s,n])=>{for(let r=0;r<n;r++)s.apply(s,[e,...t])})}}class ne extends Q{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,i,s={}){const n=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,r=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],i&&(Array.isArray(i)?a.push(...i):d(i)&&n?a.push(...i.split(n)):a.push(i)));const l=z(this.data,a);return!l&&!t&&!i&&e.indexOf(".")>-1&&(e=a[0],t=a[1],i=a.slice(2).join(".")),l||!r||!d(i)?l:_(this.data?.[e]?.[t],i,n)}addResource(e,t,i,s,n={silent:!1}){const r=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let a=[e,t];i&&(a=a.concat(r?i.split(r):i)),e.indexOf(".")>-1&&(a=e.split("."),s=t,t=a[1]),this.addNamespaces(t),ie(this.data,a,s),n.silent||this.emit("added",e,t,i,s)}addResources(e,t,i,s={silent:!1}){for(const n in i)(d(i[n])||Array.isArray(i[n]))&&this.addResource(e,t,n,i[n],{silent:!0});s.silent||this.emit("added",e,t,i)}addResourceBundle(e,t,i,s,n,r={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),s=i,i=t,t=a[1]),this.addNamespaces(t);let l=z(this.data,a)||{};r.skipCopy||(i=JSON.parse(JSON.stringify(i))),s?ge(l,i,n):l={...l,...i},ie(this.data,a,l),r.silent||this.emit("added",e,t,i)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(s=>t[s]&&Object.keys(t[s]).length>0)}toJSON(){return this.data}}var pe={processors:{},addPostProcessor(o){this.processors[o.name]=o},handle(o,e,t,i,s){return o.forEach(n=>{e=this.processors[n]?.process(e,t,i,s)??e}),e}};const re={},ae=o=>!d(o)&&typeof o!="boolean"&&typeof o!="number";class W extends Q{constructor(e,t={}){super(),ye(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=$.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const i={...t};return e==null?!1:this.resolve(e,i)?.res!==void 0}extractFromKey(e,t){let i=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;i===void 0&&(i=":");const s=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let n=t.ns||this.options.defaultNS||[];const r=i&&e.indexOf(i)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!$e(e,i,s);if(r&&!a){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:d(n)?[n]:n};const u=e.split(i);(i!==s||i===s&&this.options.ns.indexOf(u[0])>-1)&&(n=u.shift()),e=u.join(s)}return{key:e,namespaces:d(n)?[n]:n}}translate(e,t,i){let s=typeof t=="object"?{...t}:t;if(typeof s!="object"&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(s={...s}),s||(s={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const n=s.returnDetails!==void 0?s.returnDetails:this.options.returnDetails,r=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(e[e.length-1],s),u=l[l.length-1];let h=s.nsSeparator!==void 0?s.nsSeparator:this.options.nsSeparator;h===void 0&&(h=":");const f=s.lng||this.language,p=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(f?.toLowerCase()==="cimode")return p?n?{res:`${u}${h}${a}`,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(s)}:`${u}${h}${a}`:n?{res:a,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(s)}:a;const g=this.resolve(e,s);let c=g?.res;const m=g?.usedKey||a,S=g?.exactUsedKey||a,b=["[object Number]","[object Function]","[object RegExp]"],x=s.joinArrays!==void 0?s.joinArrays:this.options.joinArrays,F=!this.i18nFormat||this.i18nFormat.handleAsObject,L=s.count!==void 0&&!d(s.count),N=W.hasDefaultValue(s),j=L?this.pluralResolver.getSuffix(f,s.count,s):"",I=s.ordinal&&L?this.pluralResolver.getSuffix(f,s.count,{ordinal:!1}):"",G=L&&!s.ordinal&&s.count===0,k=G&&s[`defaultValue${this.options.pluralSeparator}zero`]||s[`defaultValue${j}`]||s[`defaultValue${I}`]||s.defaultValue;let v=c;F&&!c&&N&&(v=k);const me=ae(v),xe=Object.prototype.toString.apply(v);if(F&&v&&me&&b.indexOf(xe)<0&&!(d(x)&&Array.isArray(v))){if(!s.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const C=this.options.returnedObjectHandler?this.options.returnedObjectHandler(m,v,{...s,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return n?(g.res=C,g.usedParams=this.getUsedParamsDetails(s),g):C}if(r){const C=Array.isArray(v),w=C?[]:{},Z=C?S:m;for(const R in v)if(Object.prototype.hasOwnProperty.call(v,R)){const P=`${Z}${r}${R}`;N&&!c?w[R]=this.translate(P,{...s,defaultValue:ae(k)?k[R]:void 0,joinArrays:!1,ns:l}):w[R]=this.translate(P,{...s,joinArrays:!1,ns:l}),w[R]===P&&(w[R]=v[R])}c=w}}else if(F&&d(x)&&Array.isArray(c))c=c.join(x),c&&(c=this.extendTranslation(c,e,s,i));else{let C=!1,w=!1;!this.isValidLookup(c)&&N&&(C=!0,c=k),this.isValidLookup(c)||(w=!0,c=a);const R=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&w?void 0:c,P=N&&k!==c&&this.options.updateMissing;if(w||C||P){if(this.logger.log(P?"updateKey":"missingKey",f,u,a,P?k:c),r){const O=this.resolve(a,{...s,keySeparator:!1});O&&O.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let D=[];const H=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if(this.options.saveMissingTo==="fallback"&&H&&H[0])for(let O=0;O<H.length;O++)D.push(H[O]);else this.options.saveMissingTo==="all"?D=this.languageUtils.toResolveHierarchy(s.lng||this.language):D.push(s.lng||this.language);const X=(O,E,V)=>{const q=N&&V!==c?V:R;this.options.missingKeyHandler?this.options.missingKeyHandler(O,u,E,q,P,s):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(O,u,E,q,P,s),this.emit("missingKey",O,u,E,c)};this.options.saveMissing&&(this.options.saveMissingPlurals&&L?D.forEach(O=>{const E=this.pluralResolver.getSuffixes(O,s);G&&s[`defaultValue${this.options.pluralSeparator}zero`]&&E.indexOf(`${this.options.pluralSeparator}zero`)<0&&E.push(`${this.options.pluralSeparator}zero`),E.forEach(V=>{X([O],a+V,s[`defaultValue${V}`]||k)})}):X(D,a,k))}c=this.extendTranslation(c,e,s,g,i),w&&c===a&&this.options.appendNamespaceToMissingKey&&(c=`${u}${h}${a}`),(w||C)&&this.options.parseMissingKeyHandler&&(c=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${h}${a}`:a,C?c:void 0,s))}return n?(g.res=c,g.usedParams=this.getUsedParamsDetails(s),g):c}extendTranslation(e,t,i,s,n){if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!i.skipInterpolation){i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});const l=d(e)&&(i?.interpolation?.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let u;if(l){const f=e.match(this.interpolator.nestingRegexp);u=f&&f.length}let h=i.replace&&!d(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,i.lng||this.language||s.usedLng,i),l){const f=e.match(this.interpolator.nestingRegexp),p=f&&f.length;u<p&&(i.nest=!1)}!i.lng&&s&&s.res&&(i.lng=this.language||s.usedLng),i.nest!==!1&&(e=this.interpolator.nest(e,(...f)=>n?.[0]===f[0]&&!i.context?(this.logger.warn(`It seems you are nesting recursively key: ${f[0]} in key: ${t[0]}`),null):this.translate(...f,t),i)),i.interpolation&&this.interpolator.reset()}const r=i.postProcess||this.options.postProcess,a=d(r)?[r]:r;return e!=null&&a?.length&&i.applyPostProcessor!==!1&&(e=pe.handle(a,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),e}resolve(e,t={}){let i,s,n,r,a;return d(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(i))return;const u=this.extractFromKey(l,t),h=u.key;s=h;let f=u.namespaces;this.options.fallbackNS&&(f=f.concat(this.options.fallbackNS));const p=t.count!==void 0&&!d(t.count),g=p&&!t.ordinal&&t.count===0,c=t.context!==void 0&&(d(t.context)||typeof t.context=="number")&&t.context!=="",m=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);f.forEach(S=>{this.isValidLookup(i)||(a=S,!re[`${m[0]}-${S}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(a)&&(re[`${m[0]}-${S}`]=!0,this.logger.warn(`key "${s}" for languages "${m.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),m.forEach(b=>{if(this.isValidLookup(i))return;r=b;const x=[h];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(x,h,b,S,t);else{let L;p&&(L=this.pluralResolver.getSuffix(b,t.count,t));const N=`${this.options.pluralSeparator}zero`,j=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(p&&(x.push(h+L),t.ordinal&&L.indexOf(j)===0&&x.push(h+L.replace(j,this.options.pluralSeparator)),g&&x.push(h+N)),c){const I=`${h}${this.options.contextSeparator}${t.context}`;x.push(I),p&&(x.push(I+L),t.ordinal&&L.indexOf(j)===0&&x.push(I+L.replace(j,this.options.pluralSeparator)),g&&x.push(I+N))}}let F;for(;F=x.pop();)this.isValidLookup(i)||(n=F,i=this.getResource(b,S,F,t))}))})}),{res:i,usedKey:s,exactUsedKey:n,usedLng:r,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,i,s={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,i,s):this.resourceStore.getResource(e,t,i,s)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],i=e.replace&&!d(e.replace);let s=i?e.replace:e;if(i&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!i){s={...s};for(const n of t)delete s[n]}return s}static hasDefaultValue(e){const t="defaultValue";for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&t===i.substring(0,t.length)&&e[i]!==void 0)return!0;return!1}}class oe{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=$.create("languageUtils")}getScriptPartFromCode(e){if(e=U(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=U(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(d(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(i=>{if(t)return;const s=this.formatLanguageCode(i);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(i=>{if(t)return;const s=this.getScriptPartFromCode(i);if(this.isSupportedCode(s))return t=s;const n=this.getLanguagePartFromCode(i);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find(r=>{if(r===n)return r;if(!(r.indexOf("-")<0&&n.indexOf("-")<0)&&(r.indexOf("-")>0&&n.indexOf("-")<0&&r.substring(0,r.indexOf("-"))===n||r.indexOf(n)===0&&n.length>1))return r})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),d(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let i=e[t];return i||(i=e[this.getScriptPartFromCode(t)]),i||(i=e[this.formatLanguageCode(t)]),i||(i=e[this.getLanguagePartFromCode(t)]),i||(i=e.default),i||[]}toResolveHierarchy(e,t){const i=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),s=[],n=r=>{r&&(this.isSupportedCode(r)?s.push(r):this.logger.warn(`rejecting language code not found in supportedLngs: ${r}`))};return d(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&n(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&n(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&n(this.getLanguagePartFromCode(e))):d(e)&&n(this.formatLanguageCode(e)),i.forEach(r=>{s.indexOf(r)<0&&n(this.formatLanguageCode(r))}),s}}const le={zero:0,one:1,two:2,few:3,many:4,other:5},ue={select:o=>o===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Ne{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=$.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const i=U(e==="dev"?"en":e),s=t.ordinal?"ordinal":"cardinal",n=JSON.stringify({cleanedCode:i,type:s});if(n in this.pluralRulesCache)return this.pluralRulesCache[n];let r;try{r=new Intl.PluralRules(i,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),ue;if(!e.match(/-|_/))return ue;const l=this.languageUtils.getLanguagePartFromCode(e);r=this.getRule(l,t)}return this.pluralRulesCache[n]=r,r}needsPlural(e,t={}){let i=this.getRule(e,t);return i||(i=this.getRule("dev",t)),i?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t,i={}){return this.getSuffixes(e,i).map(s=>`${t}${s}`)}getSuffixes(e,t={}){let i=this.getRule(e,t);return i||(i=this.getRule("dev",t)),i?i.resolvedOptions().pluralCategories.sort((s,n)=>le[s]-le[n]).map(s=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(e,t,i={}){const s=this.getRule(e,i);return s?`${this.options.prepend}${i.ordinal?`ordinal${this.options.prepend}`:""}${s.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,i))}}const fe=(o,e,t,i=".",s=!0)=>{let n=Le(o,e,t);return!n&&s&&d(t)&&(n=_(o,t,i),n===void 0&&(n=_(e,t,i))),n},Y=o=>o.replace(/\$/g,"$$$$");class ke{constructor(e={}){this.logger=$.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(t=>t),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:i,useRawValueToEscape:s,prefix:n,prefixEscaped:r,suffix:a,suffixEscaped:l,formatSeparator:u,unescapeSuffix:h,unescapePrefix:f,nestingPrefix:p,nestingPrefixEscaped:g,nestingSuffix:c,nestingSuffixEscaped:m,nestingOptionsSeparator:S,maxReplaces:b,alwaysFormat:x}=e.interpolation;this.escape=t!==void 0?t:we,this.escapeValue=i!==void 0?i:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=n?T(n):r||"{{",this.suffix=a?T(a):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=h?"":f||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=p?T(p):g||T("$t("),this.nestingSuffix=c?T(c):m||T(")"),this.nestingOptionsSeparator=S||",",this.maxReplaces=b||1e3,this.alwaysFormat=x!==void 0?x:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,i)=>t?.source===i?(t.lastIndex=0,t):new RegExp(i,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,i,s){let n,r,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=g=>{if(g.indexOf(this.formatSeparator)<0){const b=fe(t,l,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(b,void 0,i,{...s,...t,interpolationkey:g}):b}const c=g.split(this.formatSeparator),m=c.shift().trim(),S=c.join(this.formatSeparator).trim();return this.format(fe(t,l,m,this.options.keySeparator,this.options.ignoreJSONStructure),S,i,{...s,...t,interpolationkey:m})};this.resetRegExp();const h=s?.missingInterpolationHandler||this.options.missingInterpolationHandler,f=s?.interpolation?.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>Y(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?Y(this.escape(g)):Y(g)}].forEach(g=>{for(a=0;n=g.regex.exec(e);){const c=n[1].trim();if(r=u(c),r===void 0)if(typeof h=="function"){const S=h(e,n,s);r=d(S)?S:""}else if(s&&Object.prototype.hasOwnProperty.call(s,c))r="";else if(f){r=n[0];continue}else this.logger.warn(`missed to pass in variable ${c} for interpolating ${e}`),r="";else!d(r)&&!this.useRawValueToEscape&&(r=ee(r));const m=g.safeValue(r);if(e=e.replace(n[0],m),f?(g.regex.lastIndex+=r.length,g.regex.lastIndex-=n[0].length):g.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t,i={}){let s,n,r;const a=(l,u)=>{const h=this.nestingOptionsSeparator;if(l.indexOf(h)<0)return l;const f=l.split(new RegExp(`${h}[ ]*{`));let p=`{${f[1]}`;l=f[0],p=this.interpolate(p,r);const g=p.match(/'/g),c=p.match(/"/g);((g?.length??0)%2===0&&!c||c.length%2!==0)&&(p=p.replace(/'/g,'"'));try{r=JSON.parse(p),u&&(r={...u,...r})}catch(m){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,m),`${l}${h}${p}`}return r.defaultValue&&r.defaultValue.indexOf(this.prefix)>-1&&delete r.defaultValue,l};for(;s=this.nestingRegexp.exec(e);){let l=[];r={...i},r=r.replace&&!d(r.replace)?r.replace:r,r.applyPostProcessor=!1,delete r.defaultValue;const u=/{.*}/.test(s[1])?s[1].lastIndexOf("}")+1:s[1].indexOf(this.formatSeparator);if(u!==-1&&(l=s[1].slice(u).split(this.formatSeparator).map(h=>h.trim()).filter(Boolean),s[1]=s[1].slice(0,u)),n=t(a.call(this,s[1].trim(),r),r),n&&s[0]===e&&!d(n))return n;d(n)||(n=ee(n)),n||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),n=""),l.length&&(n=l.reduce((h,f)=>this.format(h,f,i.lng,{...i,interpolationkey:s[1].trim()}),n.trim())),e=e.replace(s[0],n),this.regexp.lastIndex=0}return e}}const Fe=o=>{let e=o.toLowerCase().trim();const t={};if(o.indexOf("(")>-1){const i=o.split("(");e=i[0].toLowerCase().trim();const s=i[1].substring(0,i[1].length-1);e==="currency"&&s.indexOf(":")<0?t.currency||(t.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?t.range||(t.range=s.trim()):s.split(";").forEach(r=>{if(r){const[a,...l]=r.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),h=a.trim();t[h]||(t[h]=u),u==="false"&&(t[h]=!1),u==="true"&&(t[h]=!0),isNaN(u)||(t[h]=parseInt(u,10))}})}return{formatName:e,formatOptions:t}},he=o=>{const e={};return(t,i,s)=>{let n=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(n={...n,[s.interpolationkey]:void 0});const r=i+JSON.stringify(n);let a=e[r];return a||(a=o(U(i),s),e[r]=a),a(t)}},Ee=o=>(e,t,i)=>o(U(t),i)(e);class je{constructor(e={}){this.logger=$.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const i=t.cacheInBuiltFormats?he:Ee;this.formats={number:i((s,n)=>{const r=new Intl.NumberFormat(s,{...n});return a=>r.format(a)}),currency:i((s,n)=>{const r=new Intl.NumberFormat(s,{...n,style:"currency"});return a=>r.format(a)}),datetime:i((s,n)=>{const r=new Intl.DateTimeFormat(s,{...n});return a=>r.format(a)}),relativetime:i((s,n)=>{const r=new Intl.RelativeTimeFormat(s,{...n});return a=>r.format(a,n.range||"day")}),list:i((s,n)=>{const r=new Intl.ListFormat(s,{...n});return a=>r.format(a)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=he(t)}format(e,t,i,s={}){const n=t.split(this.formatSeparator);if(n.length>1&&n[0].indexOf("(")>1&&n[0].indexOf(")")<0&&n.find(a=>a.indexOf(")")>-1)){const a=n.findIndex(l=>l.indexOf(")")>-1);n[0]=[n[0],...n.splice(1,a)].join(this.formatSeparator)}return n.reduce((a,l)=>{const{formatName:u,formatOptions:h}=Fe(l);if(this.formats[u]){let f=a;try{const p=s?.formatParams?.[s.interpolationkey]||{},g=p.locale||p.lng||s.locale||s.lng||i;f=this.formats[u](a,g,{...h,...s,...p})}catch(p){this.logger.warn(p)}return f}else this.logger.warn(`there was no format function for ${u}`);return a},e)}}const Ie=(o,e)=>{o.pending[e]!==void 0&&(delete o.pending[e],o.pendingCount--)};class Te extends Q{constructor(e,t,i,s={}){super(),this.backend=e,this.store=t,this.services=i,this.languageUtils=i.languageUtils,this.options=s,this.logger=$.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(i,s.backend,s)}queueLoad(e,t,i,s){const n={},r={},a={},l={};return e.forEach(u=>{let h=!0;t.forEach(f=>{const p=`${u}|${f}`;!i.reload&&this.store.hasResourceBundle(u,f)?this.state[p]=2:this.state[p]<0||(this.state[p]===1?r[p]===void 0&&(r[p]=!0):(this.state[p]=1,h=!1,r[p]===void 0&&(r[p]=!0),n[p]===void 0&&(n[p]=!0),l[f]===void 0&&(l[f]=!0)))}),h||(a[u]=!0)}),(Object.keys(n).length||Object.keys(r).length)&&this.queue.push({pending:r,pendingCount:Object.keys(r).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(n),pending:Object.keys(r),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(e,t,i){const s=e.split("|"),n=s[0],r=s[1];t&&this.emit("failedLoading",n,r,t),!t&&i&&this.store.addResourceBundle(n,r,i,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&i&&(this.state[e]=0);const a={};this.queue.forEach(l=>{Oe(l.loaded,[n],r),Ie(l,e),t&&l.errors.push(t),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{a[u]||(a[u]={});const h=l.loaded[u];h.length&&h.forEach(f=>{a[u][f]===void 0&&(a[u][f]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(e,t,i,s=0,n=this.retryTimeout,r){if(!e.length)return r(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:i,tried:s,wait:n,callback:r});return}this.readingCalls++;const a=(u,h)=>{if(this.readingCalls--,this.waitingReads.length>0){const f=this.waitingReads.shift();this.read(f.lng,f.ns,f.fcName,f.tried,f.wait,f.callback)}if(u&&h&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,i,s+1,n*2,r)},n);return}r(u,h)},l=this.backend[i].bind(this.backend);if(l.length===2){try{const u=l(e,t);u&&typeof u.then=="function"?u.then(h=>a(null,h)).catch(a):a(null,u)}catch(u){a(u)}return}return l(e,t,a)}prepareLoading(e,t,i={},s){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();d(e)&&(e=this.languageUtils.toResolveHierarchy(e)),d(t)&&(t=[t]);const n=this.queueLoad(e,t,i,s);if(!n.toLoad.length)return n.pending.length||s(),null;n.toLoad.forEach(r=>{this.loadOne(r)})}load(e,t,i){this.prepareLoading(e,t,{},i)}reload(e,t,i){this.prepareLoading(e,t,{reload:!0},i)}loadOne(e,t=""){const i=e.split("|"),s=i[0],n=i[1];this.read(s,n,"read",void 0,void 0,(r,a)=>{r&&this.logger.warn(`${t}loading namespace ${n} for language ${s} failed`,r),!r&&a&&this.logger.log(`${t}loaded namespace ${n} for language ${s}`,a),this.loaded(e,r,a)})}saveMissing(e,t,i,s,n,r={},a=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${i}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(i==null||i==="")){if(this.backend?.create){const l={...r,isUpdate:n},u=this.backend.create.bind(this.backend);if(u.length<6)try{let h;u.length===5?h=u(e,t,i,s,l):h=u(e,t,i,s),h&&typeof h.then=="function"?h.then(f=>a(null,f)).catch(a):a(null,h)}catch(h){a(h)}else u(e,t,i,s,a,l)}!e||!e[0]||this.store.addResource(e[0],t,i,s)}}}const ce=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:o=>{let e={};if(typeof o[1]=="object"&&(e=o[1]),d(o[1])&&(e.defaultValue=o[1]),d(o[2])&&(e.tDescription=o[2]),typeof o[2]=="object"||typeof o[3]=="object"){const t=o[3]||o[2];Object.keys(t).forEach(i=>{e[i]=t[i]})}return e},interpolation:{escapeValue:!0,format:o=>o,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),de=o=>(d(o.ns)&&(o.ns=[o.ns]),d(o.fallbackLng)&&(o.fallbackLng=[o.fallbackLng]),d(o.fallbackNS)&&(o.fallbackNS=[o.fallbackNS]),o.supportedLngs?.indexOf?.("cimode")<0&&(o.supportedLngs=o.supportedLngs.concat(["cimode"])),typeof o.initImmediate=="boolean"&&(o.initAsync=o.initImmediate),o),B=()=>{},De=o=>{Object.getOwnPropertyNames(Object.getPrototypeOf(o)).forEach(t=>{typeof o[t]=="function"&&(o[t]=o[t].bind(o))})};class M extends Q{constructor(e={},t){if(super(),this.options=de(e),this.services={},this.logger=$,this.modules={external:[]},De(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(d(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const i=ce();this.options={...i,...this.options,...de(e)},this.options.interpolation={...i.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const s=u=>u?typeof u=="function"?new u:u:null;if(!this.options.isClone){this.modules.logger?$.init(s(this.modules.logger),this.options):$.init(null,this.options);let u;this.modules.formatter?u=this.modules.formatter:u=je;const h=new oe(this.options);this.store=new ne(this.options.resources,this.options);const f=this.services;f.logger=$,f.resourceStore=this.store,f.languageUtils=h,f.pluralResolver=new Ne(h,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==i.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),u&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(f.formatter=s(u),f.formatter.init&&f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new ke(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new Te(s(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",(g,...c)=>{this.emit(g,...c)}),this.modules.languageDetector&&(f.languageDetector=s(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=s(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new W(this.services,this.options),this.translator.on("*",(g,...c)=>{this.emit(g,...c)}),this.modules.external.forEach(g=>{g.init&&g.init(this)})}if(this.format=this.options.interpolation.format,t||(t=B),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const u=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);u.length>0&&u[0]!=="dev"&&(this.options.lng=u[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(u=>{this[u]=(...h)=>this.store[u](...h)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(u=>{this[u]=(...h)=>(this.store[u](...h),this)});const a=K(),l=()=>{const u=(h,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(f),t(h,f)};if(this.languages&&!this.isInitialized)return u(null,this.t.bind(this));this.changeLanguage(this.options.lng,u)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),a}loadResources(e,t=B){let i=t;const s=d(e)?e:this.language;if(typeof e=="function"&&(i=e),!this.options.resources||this.options.partialBundledLanguages){if(s?.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return i();const n=[],r=a=>{if(!a||a==="cimode")return;this.services.languageUtils.toResolveHierarchy(a).forEach(u=>{u!=="cimode"&&n.indexOf(u)<0&&n.push(u)})};s?r(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>r(l)),this.options.preload?.forEach?.(a=>r(a)),this.services.backendConnector.load(n,this.options.ns,a=>{!a&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),i(a)})}else i(null)}reloadResources(e,t,i){const s=K();return typeof e=="function"&&(i=e,e=void 0),typeof t=="function"&&(i=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),i||(i=B),this.services.backendConnector.reload(e,t,n=>{s.resolve(),i(n)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&pe.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const i=this.languages[t];if(!(["cimode","dev"].indexOf(i)>-1)&&this.store.hasLanguageSomeTranslations(i)){this.resolvedLanguage=i;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const i=K();this.emit("languageChanging",e);const s=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},n=(a,l)=>{l?this.isLanguageChangingTo===e&&(s(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,i.resolve((...u)=>this.t(...u)),t&&t(a,(...u)=>this.t(...u))},r=a=>{!e&&!a&&this.services.languageDetector&&(a=[]);const l=d(a)?a:a&&a[0],u=this.store.hasLanguageSomeTranslations(l)?l:this.services.languageUtils.getBestMatchFromCodes(d(a)?[a]:a);u&&(this.language||s(u),this.translator.language||this.translator.changeLanguage(u),this.services.languageDetector?.cacheUserLanguage?.(u)),this.loadResources(u,h=>{n(h,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?r(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(r):this.services.languageDetector.detect(r):r(e),i}getFixedT(e,t,i){const s=(n,r,...a)=>{let l;typeof r!="object"?l=this.options.overloadTranslationOptionHandler([n,r].concat(a)):l={...r},l.lng=l.lng||s.lng,l.lngs=l.lngs||s.lngs,l.ns=l.ns||s.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||i||s.keyPrefix);const u=this.options.keySeparator||".";let h;return l.keyPrefix&&Array.isArray(n)?h=n.map(f=>`${l.keyPrefix}${u}${f}`):h=l.keyPrefix?`${l.keyPrefix}${u}${n}`:n,this.t(h,l)};return d(e)?s.lng=e:s.lngs=e,s.ns=t,s.keyPrefix=i,s}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const i=t.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,n=this.languages[this.languages.length-1];if(i.toLowerCase()==="cimode")return!0;const r=(a,l)=>{const u=this.services.backendConnector.state[`${a}|${l}`];return u===-1||u===0||u===2};if(t.precheck){const a=t.precheck(this,r);if(a!==void 0)return a}return!!(this.hasResourceBundle(i,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||r(i,e)&&(!s||r(n,e)))}loadNamespaces(e,t){const i=K();return this.options.ns?(d(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{i.resolve(),t&&t(s)}),i):(t&&t(),Promise.resolve())}loadLanguages(e,t){const i=K();d(e)&&(e=[e]);const s=this.options.preload||[],n=e.filter(r=>s.indexOf(r)<0&&this.services.languageUtils.isSupportedCode(r));return n.length?(this.options.preload=s.concat(n),this.loadResources(r=>{i.resolve(),t&&t(r)}),i):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!e)return"rtl";try{const s=new Intl.Locale(e);if(s&&s.getTextInfo){const n=s.getTextInfo();if(n&&n.direction)return n.direction}}catch{}const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],i=this.services?.languageUtils||new oe(ce());return e.toLowerCase().indexOf("-latn")>1?"ltr":t.indexOf(i.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new M(e,t)}cloneInstance(e={},t=B){const i=e.forkResourceStore;i&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},n=new M(s);if((e.debug!==void 0||e.prefix!==void 0)&&(n.logger=n.logger.clone(e)),["store","services","language"].forEach(a=>{n[a]=this[a]}),n.services={...this.services},n.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},i){const a=Object.keys(this.store.data).reduce((l,u)=>(l[u]={...this.store.data[u]},l[u]=Object.keys(l[u]).reduce((h,f)=>(h[f]={...l[u][f]},h),l[u]),l),{});n.store=new ne(a,s),n.services.resourceStore=n.store}return n.translator=new W(n.services,s),n.translator.on("*",(a,...l)=>{n.emit(a,...l)}),n.init(s,t),n.translator.options=s,n.translator.backendConnector.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},n}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const y=M.createInstance();y.createInstance=M.createInstance;y.createInstance;y.dir;y.init;y.loadResources;y.reloadResources;y.use;y.changeLanguage;y.getFixedT;y.t;y.exists;y.setDefaultNamespace;y.hasLoadedNamespace;y.loadNamespaces;y.loadLanguages;export{y as i};
