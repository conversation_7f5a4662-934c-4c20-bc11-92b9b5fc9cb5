import{R as g,r as v,a as y,b as f,s as C,d as m,t as b,c as w,n as S,i as d,e as U,f as F,o as i,A as R}from"./open-hands-axios-CtirLpss.js";var D=class extends g{#n;#a;#e;#i;#t;#o;#r;constructor(s){super(),this.#r=!1,this.#o=s.defaultOptions,this.setOptions(s.options),this.observers=[],this.#i=s.client,this.#e=this.#i.getQueryCache(),this.queryKey=s.queryKey,this.queryHash=s.queryHash,this.#n=A(this.options),this.state=s.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#t?.promise}setOptions(s){this.options={...this.#o,...s},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#e.remove(this)}setData(s,t){const e=v(this.state.data,s,this.options);return this.#s({data:e,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),e}setState(s,t){this.#s({type:"setState",state:s,setStateOptions:t})}cancel(s){const t=this.#t?.promise;return this.#t?.cancel(s),t?t.then(y).catch(y):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(s=>f(s.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===C||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(s=>m(s.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(s=>s.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(s=0){return this.state.data===void 0?!0:s==="static"?!1:this.state.isInvalidated?!0:!b(this.state.dataUpdatedAt,s)}onFocus(){this.observers.find(t=>t.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#t?.continue()}onOnline(){this.observers.find(t=>t.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#t?.continue()}addObserver(s){this.observers.includes(s)||(this.observers.push(s),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",query:this,observer:s}))}removeObserver(s){this.observers.includes(s)&&(this.observers=this.observers.filter(t=>t!==s),this.observers.length||(this.#t&&(this.#r?this.#t.cancel({revert:!0}):this.#t.cancelRetry()),this.scheduleGc()),this.#e.notify({type:"observerRemoved",query:this,observer:s}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#s({type:"invalidate"})}fetch(s,t){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#t)return this.#t.continueRetry(),this.#t.promise}if(s&&this.setOptions(s),!this.options.queryFn){const r=this.observers.find(c=>c.options.queryFn);r&&this.setOptions(r.options)}const e=new AbortController,a=r=>{Object.defineProperty(r,"signal",{enumerable:!0,get:()=>(this.#r=!0,e.signal)})},n=()=>{const r=U(this.options,t),l=(()=>{const p={client:this.#i,queryKey:this.queryKey,meta:this.meta};return a(p),p})();return this.#r=!1,this.options.persister?this.options.persister(r,l,this):r(l)},o=(()=>{const r={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#i,state:this.state,fetchFn:n};return a(r),r})();this.options.behavior?.onFetch(o,this),this.#a=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==o.fetchOptions?.meta)&&this.#s({type:"fetch",meta:o.fetchOptions?.meta});const u=r=>{d(r)&&r.silent||this.#s({type:"error",error:r}),d(r)||(this.#e.config.onError?.(r,this),this.#e.config.onSettled?.(this.state.data,r,this)),this.scheduleGc()};return this.#t=w({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:e.abort.bind(e),onSuccess:r=>{if(r===void 0){u(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(r)}catch(c){u(c);return}this.#e.config.onSuccess?.(r,this),this.#e.config.onSettled?.(r,this.state.error,this),this.scheduleGc()},onError:u,onFail:(r,c)=>{this.#s({type:"failed",failureCount:r,error:c})},onPause:()=>{this.#s({type:"pause"})},onContinue:()=>{this.#s({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#t.start()}#s(s){const t=e=>{switch(s.type){case"failed":return{...e,fetchFailureCount:s.failureCount,fetchFailureReason:s.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...k(e.data,this.options),fetchMeta:s.meta??null};case"success":return this.#a=void 0,{...e,data:s.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:s.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!s.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const a=s.error;return d(a)&&a.revert&&this.#a?{...this.#a,fetchStatus:"idle"}:{...e,error:a,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...s.state}}};this.state=t(this.state),S.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#e.notify({query:this,type:"updated",action:s})})}};function k(s,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:F(t.networkMode)?"fetching":"paused",...s===void 0&&{error:null,status:"pending"}}}function A(s){const t=typeof s.initialData=="function"?s.initialData():s.initialData,e=t!==void 0,a=e?typeof s.initialDataUpdatedAt=="function"?s.initialDataUpdatedAt():s.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:e?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:e?"success":"pending",fetchStatus:"idle"}}class q{static currentConversation=null;static getCurrentConversation(){return this.currentConversation}static setCurrentConversation(t){this.currentConversation=t}static getConversationUrl(t){return this.currentConversation?.conversation_id===t&&this.currentConversation.url?this.currentConversation.url:`/api/conversations/${t}`}static async getModels(){const{data:t}=await i.get("/api/options/models");return t}static async getAgents(){const{data:t}=await i.get("/api/options/agents");return t}static async getSecurityAnalyzers(){const{data:t}=await i.get("/api/options/security-analyzers");return t}static async getConfig(){const{data:t}=await i.get("/api/options/config");return t}static getConversationHeaders(){const t=new R,e=this.currentConversation?.session_api_key;return e&&t.set("X-Session-API-Key",e),t}static async submitFeedback(t,e){const a=`/api/conversations/${t}/submit-feedback`,{data:n}=await i.post(a,e);return n}static async submitConversationFeedback(t,e,a,n){const h="/feedback/conversation",o={conversation_id:t,event_id:a,rating:e,reason:n,metadata:{source:"likert-scale"}},{data:u}=await i.post(h,o);return u}static async checkFeedbackExists(t,e){try{const a=`/feedback/conversation/${t}/${e}`,{data:n}=await i.get(a);return n}catch{return{exists:!1}}}static async authenticate(t){return t==="oss"||await i.post("/api/authenticate"),!0}static async getWorkspaceZip(t){const e=`${this.getConversationUrl(t)}/zip-directory`;return(await i.get(e,{responseType:"blob",headers:this.getConversationHeaders()})).data}static async getWebHosts(t){const e=`${this.getConversationUrl(t)}/web-hosts`,a=await i.get(e,{headers:this.getConversationHeaders()});return Object.keys(a.data.hosts)}static async getGitHubAccessToken(t){const{data:e}=await i.post("/api/keycloak/callback",{code:t});return e}static async getVSCodeUrl(t){const e=`${this.getConversationUrl(t)}/vscode-url`,{data:a}=await i.get(e,{headers:this.getConversationHeaders()});return a}static async getRuntimeId(t){const e=`${this.getConversationUrl(t)}/config`,{data:a}=await i.get(e,{headers:this.getConversationHeaders()});return a}static async getUserConversations(){const{data:t}=await i.get("/api/conversations?limit=20");return t.results}static async searchConversations(t,e,a=20){const n=new URLSearchParams;n.append("limit",a.toString()),t&&n.append("selected_repository",t),e&&n.append("conversation_trigger",e);const{data:h}=await i.get(`/api/conversations?${n.toString()}`);return h.results}static async deleteUserConversation(t){await i.delete(`/api/conversations/${t}`)}static async createConversation(t,e,a,n,h,o,u){const r={repository:t,git_provider:e,selected_branch:h,initial_user_msg:a,suggested_task:n,conversation_instructions:o,create_microagent:u},{data:c}=await i.post("/api/conversations",r);return c}static async getConversation(t){const{data:e}=await i.get(`/api/conversations/${t}`);return e}static async startConversation(t,e){const{data:a}=await i.post(`/api/conversations/${t}/start`,e?{providers_set:e}:{});return a}static async stopConversation(t){const{data:e}=await i.post(`/api/conversations/${t}/stop`);return e}static async getSettings(){const{data:t}=await i.get("/api/settings");return t}static async saveSettings(t){return(await i.post("/api/settings",t)).status===200}static async createCheckoutSession(t){const{data:e}=await i.post("/api/billing/create-checkout-session",{amount:t});return e.redirect_url}static async createBillingSessionResponse(){const{data:t}=await i.post("/api/billing/create-customer-setup-session");return t.redirect_url}static async getBalance(){const{data:t}=await i.get("/api/billing/credits");return t.credits}static async getGitUser(){const t=await i.get("/api/user/info"),{data:e}=t;return{id:e.id,login:e.login,avatar_url:e.avatar_url,company:e.company,name:e.name,email:e.email}}static async searchGitRepositories(t,e=5){return(await i.get("/api/user/search/repositories",{params:{query:t,per_page:e}})).data}static async getTrajectory(t){const e=`${this.getConversationUrl(t)}/trajectory`,{data:a}=await i.get(e,{headers:this.getConversationHeaders()});return a}static async logout(t){const e=t==="saas"?"/api/logout":"/api/unset-provider-tokens";await i.post(e)}static async getGitChanges(t){const e=`${this.getConversationUrl(t)}/git/changes`,{data:a}=await i.get(e,{headers:this.getConversationHeaders()});return a}static async getGitChangeDiff(t,e){const a=`${this.getConversationUrl(t)}/git/diff`,{data:n}=await i.get(a,{params:{path:e},headers:this.getConversationHeaders()});return n}static async retrieveUserGitRepositories(){const{data:t}=await i.get("/api/user/repositories",{params:{sort:"pushed"}});return t}static async getRepositoryBranches(t){const{data:e}=await i.get(`/api/user/repository/branches?repository=${encodeURIComponent(t)}`);return e}static async getMicroagents(t){const e=`${this.getConversationUrl(t)}/microagents`,{data:a}=await i.get(e,{headers:this.getConversationHeaders()});return a}static async getRepositoryMicroagents(t,e){const{data:a}=await i.get(`/api/user/repository/${t}/${e}/microagents`);return a}static async getMicroagentPrompt(t,e){const{data:a}=await i.get(`/api/conversations/${t}/remember_prompt`,{params:{event_id:e}});return a.prompt}static async updateConversation(t,e){const{data:a}=await i.patch(`/api/conversations/${t}`,e);return a}}export{q as O,D as Q,k as f};
