import{S as V,C as _,b as l,j as Q,d as g,a as T,D as w,E as k,t as W,x as N,r as L,n as H,l as K,u as G}from"./open-hands-axios-CtirLpss.js";import{f as J}from"./open-hands-Ce72Fmtl.js";import{r as p}from"./chunk-C37GKA54-CBbYr_fP.js";var X=class extends V{constructor(t,e){super(),this.options=e,this.#s=t,this.#r=null,this.#i=_(),this.options.experimental_prefetchInRender||this.#i.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#s;#t=void 0;#p=void 0;#e=void 0;#n;#u;#i;#r;#b;#l;#d;#o;#h;#a;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#t.addObserver(this),B(this.#t,this.options)?this.#c():this.updateResult(),this.#g())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return x(this.#t,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return x(this.#t,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#y(),this.#O(),this.#t.removeObserver(this)}setOptions(t){const e=this.options,s=this.#t;if(this.options=this.#s.defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof l(this.options.enabled,this.#t)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#C(),this.#t.setOptions(this.options),e._defaulted&&!Q(this.options,e)&&this.#s.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#t,observer:this});const i=this.hasListeners();i&&j(this.#t,s,this.options,e)&&this.#c(),this.updateResult(),i&&(this.#t!==s||l(this.options.enabled,this.#t)!==l(e.enabled,this.#t)||g(this.options.staleTime,this.#t)!==g(e.staleTime,this.#t))&&this.#R();const a=this.#v();i&&(this.#t!==s||l(this.options.enabled,this.#t)!==l(e.enabled,this.#t)||a!==this.#a)&&this.#m(a)}getOptimisticResult(t){const e=this.#s.getQueryCache().build(this.#s,t),s=this.createResult(e,t);return Z(this,s)&&(this.#e=s,this.#u=this.options,this.#n=this.#t.state),s}getCurrentResult(){return this.#e}trackResult(t,e){return new Proxy(t,{get:(s,i)=>(this.trackProp(i),e?.(i),Reflect.get(s,i))})}trackProp(t){this.#f.add(t)}getCurrentQuery(){return this.#t}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#s.defaultQueryOptions(t),s=this.#s.getQueryCache().build(this.#s,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#c({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#e))}#c(t){this.#C();let e=this.#t.fetch(this.options,t);return t?.throwOnError||(e=e.catch(T)),e}#R(){this.#y();const t=g(this.options.staleTime,this.#t);if(w||this.#e.isStale||!k(t))return;const s=W(this.#e.dataUpdatedAt,t)+1;this.#o=setTimeout(()=>{this.#e.isStale||this.updateResult()},s)}#v(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#t):this.options.refetchInterval)??!1}#m(t){this.#O(),this.#a=t,!(w||l(this.options.enabled,this.#t)===!1||!k(this.#a)||this.#a===0)&&(this.#h=setInterval(()=>{(this.options.refetchIntervalInBackground||N.isFocused())&&this.#c()},this.#a))}#g(){this.#R(),this.#m(this.#v())}#y(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}#O(){this.#h&&(clearInterval(this.#h),this.#h=void 0)}createResult(t,e){const s=this.#t,i=this.options,a=this.#e,c=this.#n,r=this.#u,o=t!==s?t.state:this.#p,{state:d}=t;let n={...d},m=!1,h;if(e._optimisticResults){const u=this.hasListeners(),y=!u&&B(t,e),v=u&&j(t,s,e,i);(y||v)&&(n={...n,...J(d.data,t.options)}),e._optimisticResults==="isRestoring"&&(n.fetchStatus="idle")}let{error:F,errorUpdatedAt:D,status:b}=n;h=n.data;let P=!1;if(e.placeholderData!==void 0&&h===void 0&&b==="pending"){let u;a?.isPlaceholderData&&e.placeholderData===r?.placeholderData?(u=a.data,P=!0):u=typeof e.placeholderData=="function"?e.placeholderData(this.#d?.state.data,this.#d):e.placeholderData,u!==void 0&&(b="success",h=L(a?.data,u,e),m=!0)}if(e.select&&h!==void 0&&!P)if(a&&h===c?.data&&e.select===this.#b)h=this.#l;else try{this.#b=e.select,h=e.select(h),h=L(a?.data,h,e),this.#l=h,this.#r=null}catch(u){this.#r=u}this.#r&&(F=this.#r,h=this.#l,D=Date.now(),b="error");const C=n.fetchStatus==="fetching",S=b==="pending",E=b==="error",U=S&&C,M=h!==void 0,f={status:b,fetchStatus:n.fetchStatus,isPending:S,isSuccess:b==="success",isError:E,isInitialLoading:U,isLoading:U,data:h,dataUpdatedAt:n.dataUpdatedAt,error:F,errorUpdatedAt:D,failureCount:n.fetchFailureCount,failureReason:n.fetchFailureReason,errorUpdateCount:n.errorUpdateCount,isFetched:n.dataUpdateCount>0||n.errorUpdateCount>0,isFetchedAfterMount:n.dataUpdateCount>o.dataUpdateCount||n.errorUpdateCount>o.errorUpdateCount,isFetching:C,isRefetching:C&&!S,isLoadingError:E&&!M,isPaused:n.fetchStatus==="paused",isPlaceholderData:m,isRefetchError:E&&M,isStale:I(t,e),refetch:this.refetch,promise:this.#i,isEnabled:l(e.enabled,t)!==!1};if(this.options.experimental_prefetchInRender){const u=O=>{f.status==="error"?O.reject(f.error):f.data!==void 0&&O.resolve(f.data)},y=()=>{const O=this.#i=f.promise=_();u(O)},v=this.#i;switch(v.status){case"pending":t.queryHash===s.queryHash&&u(v);break;case"fulfilled":(f.status==="error"||f.data!==v.value)&&y();break;case"rejected":(f.status!=="error"||f.error!==v.reason)&&y();break}}return f}updateResult(){const t=this.#e,e=this.createResult(this.#t,this.options);if(this.#n=this.#t.state,this.#u=this.options,this.#n.data!==void 0&&(this.#d=this.#t),Q(e,t))return;this.#e=e;const s=()=>{if(!t)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!this.#f.size)return!0;const c=new Set(a??this.#f);return this.options.throwOnError&&c.add("error"),Object.keys(this.#e).some(r=>{const R=r;return this.#e[R]!==t[R]&&c.has(R)})};this.#S({listeners:s()})}#C(){const t=this.#s.getQueryCache().build(this.#s,this.options);if(t===this.#t)return;const e=this.#t;this.#t=t,this.#p=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#g()}#S(t){H.batch(()=>{t.listeners&&this.listeners.forEach(e=>{e(this.#e)}),this.#s.getQueryCache().notify({query:this.#t,type:"observerResultsUpdated"})})}};function Y(t,e){return l(e.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&e.retryOnMount===!1)}function B(t,e){return Y(t,e)||t.state.data!==void 0&&x(t,e,e.refetchOnMount)}function x(t,e,s){if(l(e.enabled,t)!==!1&&g(e.staleTime,t)!=="static"){const i=typeof s=="function"?s(t):s;return i==="always"||i!==!1&&I(t,e)}return!1}function j(t,e,s,i){return(t!==e||l(i.enabled,t)===!1)&&(!s.suspense||t.state.status!=="error")&&I(t,s)}function I(t,e){return l(e.enabled,t)!==!1&&t.isStaleByTime(g(e.staleTime,t))}function Z(t,e){return!Q(t.getCurrentResult(),e)}var z=p.createContext(!1),$=()=>p.useContext(z);z.Provider;function q(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var tt=p.createContext(q()),et=()=>p.useContext(tt),st=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},it=t=>{p.useEffect(()=>{t.clearReset()},[t])},rt=({result:t,errorResetBoundary:e,throwOnError:s,query:i,suspense:a})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(a&&t.data===void 0||K(s,[t.error,i])),at=t=>{if(t.suspense){const e=i=>i==="static"?i:Math.max(i??1e3,1e3),s=t.staleTime;t.staleTime=typeof s=="function"?(...i)=>e(s(...i)):e(s),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3))}},nt=(t,e)=>t.isLoading&&t.isFetching&&!e,ot=(t,e)=>t?.suspense&&e.isPending,A=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function ht(t,e,s){const i=$(),a=et(),c=G(s),r=c.defaultQueryOptions(t);c.getDefaultOptions().queries?._experimental_beforeQuery?.(r),r._optimisticResults=i?"isRestoring":"optimistic",at(r),st(r,a),it(a);const R=!c.getQueryCache().get(r.queryHash),[o]=p.useState(()=>new e(c,r)),d=o.getOptimisticResult(r),n=!i&&t.subscribed!==!1;if(p.useSyncExternalStore(p.useCallback(m=>{const h=n?o.subscribe(H.batchCalls(m)):T;return o.updateResult(),h},[o,n]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),p.useEffect(()=>{o.setOptions(r)},[r,o]),ot(r,d))throw A(r,o,a);if(rt({result:d,errorResetBoundary:a,throwOnError:r.throwOnError,query:c.getQueryCache().get(r.queryHash),suspense:r.suspense}))throw d.error;return c.getDefaultOptions().queries?._experimental_afterQuery?.(r,d),r.experimental_prefetchInRender&&!w&&nt(d,i)&&(R?A(r,o,a):c.getQueryCache().get(r.queryHash)?.promise)?.catch(T).finally(()=>{o.updateResult()}),r.notifyOnChangeProps?d:o.trackResult(d)}function ft(t,e){return ht(t,X,e)}export{X as Q,$ as a,et as b,st as c,it as d,at as e,A as f,rt as g,ot as s,ft as u,nt as w};
