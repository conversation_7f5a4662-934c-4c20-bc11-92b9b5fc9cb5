import{u as i}from"./open-hands-axios-CtirLpss.js";import{u}from"./brand-button-3Z8FN4qR.js";import{p as c}from"./module-5laXsVNO.js";import{O as m}from"./open-hands-Ce72Fmtl.js";const g=()=>{const n=i();return u({mutationKey:["create-conversation"],mutationFn:async r=>{const{query:t,repository:e,suggestedTask:a,conversationInstructions:o,createMicroagent:s}=r;return m.createConversation(e?.name,e?.gitProvider,t,a,e?.branch,o,s)},onSuccess:async(r,{query:t,repository:e})=>{c.capture("initial_query_submitted",{entry_point:"task_form",query_character_length:t?.length,has_repository:!!e}),await n.invalidateQueries({queryKey:["user","conversations"]})}})};export{g as u};
