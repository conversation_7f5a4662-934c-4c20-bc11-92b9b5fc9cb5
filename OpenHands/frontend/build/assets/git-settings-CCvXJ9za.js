import{j as t,w as z,R as u}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as J}from"./use-config-jdwF3W4-.js";import{u as W}from"./use-settings-CSlhfPqo.js";import{B as b,u as Y}from"./brand-button-3Z8FN4qR.js";import{u as Z}from"./use-logout-Bpg8Vogf.js";import{I as s}from"./declaration-xyc84-tJ.js";import{S as p}from"./settings-input-i4i_IemM.js";import{u as c}from"./useTranslation-BG59QWH_.js";import{T as x}from"./Trans-CI4prNur.js";import{K as d}from"./key-status-icon-DbpY1ViG.js";import{a as tt,d as et}from"./custom-toast-handlers-CR9P-jKI.js";import{r as st}from"./retrieve-axios-error-message-CYr77e_f.js";import{I as G}from"./input-skeleton-FOAHYnV9.js";import{S as y}from"./subtext-skeleton-DyZMS3uA.js";import{u as nt}from"./open-hands-axios-CtirLpss.js";import{S as ot}from"./secrets-service-CrMMoq9G.js";import{u as at}from"./use-user-providers-CVWOd-tS.js";import"./useQuery-Cu2nkJ8V.js";import"./open-hands-Ce72Fmtl.js";import"./module-5laXsVNO.js";import"./mutation-B9dSlWD-.js";import"./utils-KsbccAr1.js";import"./optional-tag-e1gRgM9y.js";import"./i18nInstance-DBIXdvxg.js";import"./index-cxP66Ws3.js";function it(){const{t:e}=c();return t.jsx("p",{"data-testid":"github-token-help-anchor",className:"text-xs",children:t.jsx(x,{i18nKey:s.GITHUB$TOKEN_HELP_TEXT,components:[t.jsx("a",{"aria-label":e(s.GIT$GITHUB_TOKEN_HELP_LINK),href:"https://github.com/settings/tokens/new?description=openhands-app&scopes=repo,user,workflow",target:"_blank",className:"underline underline-offset-2",rel:"noopener noreferrer"},"github-token-help-anchor-link"),t.jsx("a",{"aria-label":e(s.GIT$GITHUB_TOKEN_SEE_MORE_LINK),href:"https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token",target:"_blank",className:"underline underline-offset-2",rel:"noopener noreferrer"},"github-token-help-anchor-link-2")]})})}function rt({onChange:e,onGitHubHostChange:r,isGitHubTokenSet:o,name:l,githubHostSet:a}){const{t:i}=c();return t.jsxs("div",{className:"flex flex-col gap-6",children:[t.jsx(p,{testId:l,name:l,onChange:e,label:i(s.GITHUB$TOKEN_LABEL),type:"password",className:"w-full max-w-[680px]",placeholder:o?"<hidden>":"",startContent:o&&t.jsx(d,{testId:"gh-set-token-indicator",isSet:o})}),t.jsx(p,{onChange:r||(()=>{}),name:"github-host-input",testId:"github-host-input",label:i(s.GITHUB$HOST_LABEL),type:"text",className:"w-full max-w-[680px]",placeholder:"github.com",defaultValue:a||void 0,startContent:a&&a.trim()!==""&&t.jsx(d,{testId:"gh-set-host-indicator",isSet:!0})}),t.jsx(it,{})]})}function lt(){const{t:e}=c();return t.jsx("p",{"data-testid":"gitlab-token-help-anchor",className:"text-xs",children:t.jsx(x,{i18nKey:s.GITLAB$TOKEN_HELP_TEXT,components:[t.jsx("a",{"aria-label":e(s.GIT$GITLAB_TOKEN_HELP_LINK),href:"https://gitlab.com/-/user_settings/personal_access_tokens?name=openhands-app&scopes=api,read_user,read_repository,write_repository",target:"_blank",className:"underline underline-offset-2",rel:"noopener noreferrer"},"gitlab-token-help-anchor-link"),t.jsx("a",{"aria-label":e(s.GIT$GITLAB_TOKEN_SEE_MORE_LINK),href:"https://docs.gitlab.com/user/profile/personal_access_tokens/",target:"_blank",className:"underline underline-offset-2",rel:"noopener noreferrer"},"gitlab-token-help-anchor-link-2")]})})}function ct({onChange:e,onGitLabHostChange:r,isGitLabTokenSet:o,name:l,gitlabHostSet:a}){const{t:i}=c();return t.jsxs("div",{className:"flex flex-col gap-6",children:[t.jsx(p,{testId:l,name:l,onChange:e,label:i(s.GITLAB$TOKEN_LABEL),type:"password",className:"w-full max-w-[680px]",placeholder:o?"<hidden>":"",startContent:o&&t.jsx(d,{testId:"gl-set-token-indicator",isSet:o})}),t.jsx(p,{onChange:r||(()=>{}),name:"gitlab-host-input",testId:"gitlab-host-input",label:i(s.GITLAB$HOST_LABEL),type:"text",className:"w-full max-w-[680px]",placeholder:"gitlab.com",defaultValue:a||void 0,startContent:a&&a.trim()!==""&&t.jsx(d,{testId:"gl-set-host-indicator",isSet:!0})}),t.jsx(lt,{})]})}function ut(){const{t:e}=c();return t.jsx("p",{"data-testid":"bitbucket-token-help-anchor",className:"text-xs",children:t.jsx(x,{i18nKey:s.BITBUCKET$TOKEN_HELP_TEXT,components:[t.jsx("a",{"aria-label":e(s.GIT$BITBUCKET_TOKEN_HELP_LINK),href:"https://bitbucket.org/account/settings/app-passwords/new?scopes=repository:write,pullrequest:write,issue:write",target:"_blank",className:"underline underline-offset-2",rel:"noopener noreferrer"},"bitbucket-token-help-anchor-link"),t.jsx("a",{"aria-label":e(s.GIT$BITBUCKET_TOKEN_SEE_MORE_LINK),href:"https://support.atlassian.com/bitbucket-cloud/docs/app-passwords/",target:"_blank",className:"underline underline-offset-2",rel:"noopener noreferrer"},"bitbucket-token-help-anchor-link-2")]})})}function pt({onChange:e,onBitbucketHostChange:r,isBitbucketTokenSet:o,name:l,bitbucketHostSet:a}){const{t:i}=c();return t.jsxs("div",{className:"flex flex-col gap-6",children:[t.jsx(p,{testId:l,name:l,onChange:e,label:i(s.BITBUCKET$TOKEN_LABEL),type:"password",className:"w-full max-w-[680px]",placeholder:o?"<hidden>":"username:app_password",startContent:o&&t.jsx(d,{testId:"bb-set-token-indicator",isSet:o})}),t.jsx(p,{onChange:r||(()=>{}),name:"bitbucket-host-input",testId:"bitbucket-host-input",label:i(s.BITBUCKET$HOST_LABEL),type:"text",className:"w-full max-w-[680px]",placeholder:"bitbucket.org",defaultValue:a||void 0,startContent:a&&a.trim()!==""&&t.jsx(d,{testId:"bb-set-host-indicator",isSet:!0})}),t.jsx(ut,{})]})}function dt({slug:e}){const{t:r}=c();return t.jsx("a",{"data-testid":"configure-github-repositories-button",href:`https://github.com/apps/${e}/installations/new`,target:"_blank",rel:"noreferrer noopener",className:"py-9",children:t.jsx(b,{type:"button",variant:"secondary",children:r(s.GITHUB$CONFIGURE_REPOS)})})}function ht(){const{t:e}=c();return t.jsx("a",{"data-testid":"install-slack-app-button",href:"https://slack.com/oauth/v2/authorize?client_id=7477886716822.8729519890534&scope=app_mentions:read,channels:history,chat:write,groups:history,im:history,mpim:history,users:read&user_scope=",target:"_blank",rel:"noreferrer noopener",className:"py-9",children:t.jsx(b,{type:"button",variant:"secondary",children:e(s.SLACK$INSTALL_APP)})})}function bt(){return t.jsxs("div",{className:"px-11 py-9 flex flex-col gap-12",children:[t.jsxs("div",{className:"flex flex-col gap-6",children:[t.jsx(G,{}),t.jsx(y,{})]}),t.jsxs("div",{className:"flex flex-col gap-6",children:[t.jsx(G,{}),t.jsx(y,{})]})]})}const gt=()=>{const e=nt();return Y({mutationFn:({providers:r})=>ot.addGitProvider(r),onSuccess:async()=>{await e.invalidateQueries({queryKey:["settings"]})},meta:{disableToast:!0}})};function mt(){const{t:e}=c(),{mutate:r,isPending:o}=gt(),{mutate:l}=Z(),{data:a,isLoading:i}=W(),{providers:g}=at(),{data:m}=J(),[B,k]=u.useState(!1),[L,T]=u.useState(!1),[C,I]=u.useState(!1),[K,E]=u.useState(!1),[w,_]=u.useState(!1),[A,S]=u.useState(!1),O=a?.PROVIDER_TOKENS_SET.github,v=a?.PROVIDER_TOKENS_SET.gitlab,P=a?.PROVIDER_TOKENS_SET.bitbucket,h=m?.APP_MODE==="saas",N=g.includes("github"),j=g.includes("gitlab"),H=g.includes("bitbucket"),$=async n=>{if(n.get("disconnect-tokens-button")!==null){l();return}const R=n.get("github-token-input")?.toString()||"",U=n.get("gitlab-token-input")?.toString()||"",M=n.get("bitbucket-token-input")?.toString()||"",q=n.get("github-host-input")?.toString()||"",F=n.get("gitlab-host-input")?.toString()||"",X=n.get("bitbucket-host-input")?.toString()||"";r({providers:{github:{token:R,host:q},gitlab:{token:U,host:F},bitbucket:{token:M,host:X}}},{onSuccess:()=>{et(e(s.SETTINGS$SAVED))},onError:D=>{const Q=st(D);tt(Q||e(s.ERROR$GENERIC))},onSettled:()=>{k(!1),T(!1),I(!1),E(!1),_(!1),S(!1)}})},V=!B&&!L&&!C&&!K&&!w&&!A,f=h&&m.APP_SLUG;return t.jsxs("form",{"data-testid":"git-settings-screen",action:$,className:"flex flex-col h-full justify-between",children:[!i&&t.jsxs("div",{className:"p-9 flex flex-col gap-12",children:[f&&!i&&t.jsx(dt,{slug:m.APP_SLUG}),f&&!i&&t.jsx(ht,{}),!h&&t.jsx(rt,{name:"github-token-input",isGitHubTokenSet:N,onChange:n=>{k(!!n)},onGitHubHostChange:n=>{E(!!n)},githubHostSet:O}),!h&&t.jsx(ct,{name:"gitlab-token-input",isGitLabTokenSet:j,onChange:n=>{T(!!n)},onGitLabHostChange:n=>{_(!!n)},gitlabHostSet:v}),!h&&t.jsx(pt,{name:"bitbucket-token-input",isBitbucketTokenSet:H,onChange:n=>{I(!!n)},onBitbucketHostChange:n=>{S(!!n)},bitbucketHostSet:P})]}),i&&t.jsx(bt,{}),t.jsx("div",{className:"flex gap-6 p-6 justify-end border-t border-t-tertiary",children:!f&&t.jsxs(t.Fragment,{children:[t.jsx(b,{testId:"disconnect-tokens-button",name:"disconnect-tokens-button",type:"submit",variant:"secondary",isDisabled:!N&&!j&&!H,children:e(s.GIT$DISCONNECT_TOKENS)}),t.jsxs(b,{testId:"submit-button",type:"submit",variant:"primary",isDisabled:o||V,children:[!o&&e("SETTINGS$SAVE_CHANGES"),o&&e("SETTINGS$SAVING")]})]})})]})}const qt=z(mt);export{qt as default};
