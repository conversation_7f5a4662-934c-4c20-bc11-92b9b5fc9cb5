import{i as M}from"./i18next-CO45VQzB.js";import{I as w}from"./declaration-xyc84-tJ.js";import{r as q}from"./retrieve-axios-error-message-CYr77e_f.js";import{a as E}from"./custom-toast-handlers-CR9P-jKI.js";import{S as k,m as _,n,p as A,q as C,a as o,e as z,v as L,w as U,x as J,y as D,d as F,z as V,k as S,B as K,s as X}from"./open-hands-axios-CtirLpss.js";import{Q as Y}from"./open-hands-Ce72Fmtl.js";import{M as Z}from"./mutation-B9dSlWD-.js";var j=class extends k{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,s){const r=t.queryKey,i=t.queryHash??_(r,t);let a=this.get(i);return a||(a=new Y({client:e,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(r)}),this.add(a)),a}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){n.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){const t={exact:!0,...e};return this.getAll().find(s=>A(t,s))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter(s=>A(e,s)):t}notify(e){n.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){n.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){n.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},B=class extends k{constructor(e={}){super(),this.config=e,this.#e=new Set,this.#t=new Map,this.#s=0}#e;#t;#s;build(e,t,s){const r=new Z({mutationCache:this,mutationId:++this.#s,options:e.defaultMutationOptions(t),state:s});return this.add(r),r}add(e){this.#e.add(e);const t=y(e);if(typeof t=="string"){const s=this.#t.get(t);s?s.push(e):this.#t.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#e.delete(e)){const t=y(e);if(typeof t=="string"){const s=this.#t.get(t);if(s)if(s.length>1){const r=s.indexOf(e);r!==-1&&s.splice(r,1)}else s[0]===e&&this.#t.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=y(e);if(typeof t=="string"){const r=this.#t.get(t)?.find(i=>i.state.status==="pending");return!r||r===e}else return!0}runNext(e){const t=y(e);return typeof t=="string"?this.#t.get(t)?.find(r=>r!==e&&r.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){n.batch(()=>{this.#e.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#e.clear(),this.#t.clear()})}getAll(){return Array.from(this.#e)}find(e){const t={exact:!0,...e};return this.getAll().find(s=>C(t,s))}findAll(e={}){return this.getAll().filter(t=>C(e,t))}notify(e){n.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){const e=this.getAll().filter(t=>t.state.isPaused);return n.batch(()=>Promise.all(e.map(t=>t.continue().catch(o))))}};function y(e){return e.options.scope?.id}function T(e){return{onFetch:(t,s)=>{const r=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],c=t.state.data?.pageParams||[];let l={pages:[],pageParams:[]},d=0;const m=async()=>{let p=!1;const N=u=>{Object.defineProperty(u,"signal",{enumerable:!0,get:()=>(t.signal.aborted?p=!0:t.signal.addEventListener("abort",()=>{p=!0}),t.signal)})},G=z(t.options,t.fetchOptions),v=async(u,h,f)=>{if(p)return Promise.reject();if(h==null&&u.pages.length)return Promise.resolve(u);const W=(()=>{const O={client:t.client,queryKey:t.queryKey,pageParam:h,direction:f?"backward":"forward",meta:t.options.meta};return N(O),O})(),$=await G(W),{maxPages:P}=t.options,b=f?L:U;return{pages:b(u.pages,$,P),pageParams:b(u.pageParams,h,P)}};if(i&&a.length){const u=i==="backward",h=u?x:I,f={pages:a,pageParams:c},Q=h(r,f);l=await v(f,Q,u)}else{const u=e??a.length;do{const h=d===0?c[0]??r.initialPageParam:I(r,l);if(d>0&&h==null)break;l=await v(l,h),d++}while(d<u)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(m,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=m}}}function I(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function x(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}var ee=class{#e;#t;#s;#i;#a;#r;#n;#u;constructor(e={}){this.#e=e.queryCache||new j,this.#t=e.mutationCache||new B,this.#s=e.defaultOptions||{},this.#i=new Map,this.#a=new Map,this.#r=0}mount(){this.#r++,this.#r===1&&(this.#n=J.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#u=D.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#r--,this.#r===0&&(this.#n?.(),this.#n=void 0,this.#u?.(),this.#u=void 0)}isFetching(e){return this.#e.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#t.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#e.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=this.#e.build(this,t),r=s.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(F(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#e.findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),a=this.#e.get(r.queryHash)?.state.data,c=V(t,a);if(c!==void 0)return this.#e.build(this,r).setData(c,{...s,manual:!0})}setQueriesData(e,t,s){return n.batch(()=>this.#e.findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#e.get(t.queryHash)?.state}removeQueries(e){const t=this.#e;n.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=this.#e;return n.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},r=n.batch(()=>this.#e.findAll(e).map(i=>i.cancel(s)));return Promise.all(r).then(o).catch(o)}invalidateQueries(e,t={}){return n.batch(()=>(this.#e.findAll(e).forEach(s=>{s.invalidate()}),e?.refetchType==="none"?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},r=n.batch(()=>this.#e.findAll(e).filter(i=>!i.isDisabled()&&!i.isStatic()).map(i=>{let a=i.fetch(void 0,s);return s.throwOnError||(a=a.catch(o)),i.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(r).then(o)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=this.#e.build(this,t);return s.isStaleByTime(F(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(o).catch(o)}fetchInfiniteQuery(e){return e.behavior=T(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(o).catch(o)}ensureInfiniteQueryData(e){return e.behavior=T(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return D.isOnline()?this.#t.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#t}getDefaultOptions(){return this.#s}setDefaultOptions(e){this.#s=e}setQueryDefaults(e,t){this.#i.set(S(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#i.values()],s={};return t.forEach(r=>{K(e,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(e,t){this.#a.set(S(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#a.values()],s={};return t.forEach(r=>{K(e,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#s.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=_(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===X&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#s.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#e.clear(),this.#t.clear()}};const R=(e,t)=>{(e?.response?.status===401||e?.status===401)&&t.invalidateQueries({queryKey:["user","authenticated"]})},g=new Set,H=new ee({queryCache:new j({onError:(e,t)=>{if(t.queryKey[0]==="user"&&t.queryKey[1]==="authenticated"||R(e,H),!t.meta?.disableToast){const r=q(e);g.has(r||"")||(E(r||M.t(w.ERROR$GENERIC)),g.add(r),setTimeout(()=>{g.delete(r)},3e3))}}}),mutationCache:new B({onError:(e,t,s,r)=>{if(R(e,H),!r?.meta?.disableToast){const i=q(e);E(i||M.t(w.ERROR$GENERIC))}}})});export{H as q};
