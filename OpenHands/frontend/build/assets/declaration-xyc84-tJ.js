var T=(E=>(<PERSON><PERSON>IN<PERSON>$SCHEDULED_MESSAGE="MAINTENANC<PERSON>$SCHEDULED_MESSAGE",E.MICROAGENT$NO_REPOSITORY_FOUND="MICROAGENT$NO_REPOSITORY_FOUND",E<PERSON>MICROAGENT$ADD_TO_MICROAGENT="MICROAGENT$ADD_TO_MICROAGENT",E<PERSON>MICROAGENT$WHAT_TO_ADD="MICROAGENT$WHAT_TO_ADD",E.MICROAGENT$WHERE_TO_PUT="MICROAGENT$WHERE_TO_PUT",E.MICROAGENT$ADD_TRIGGER="MICROAGENT$ADD_TRIGGER",E.MICROAGENT$WHAT_TO_REMEMBER="MICROAGENT$WHAT_TO_REMEMBER",E.MICROAGENT$ADD_TRIGGERS="MICROAGENT$ADD_TRIGGERS",E<PERSON>MICROAGENT$WAIT_FOR_RUNTIME="MICROAGENT$WAIT_FOR_RUNTIME",<PERSON><PERSON><PERSON>CROAGENT$ADDING_CONTEXT="MICROAGENT$ADDING_CONTEXT",E.MICROAGENT$VIEW_CONVERSATION="MICROAGENT$VIEW_CONVERSATION",E.MICROAGENT$SUCCESS_PR_READY="MICROAGENT$SUCCESS_PR_READY",E.MICROAGENT$STATUS_CREATING="MICROAGENT$STATUS_CREATING",E.MICROAGENT$STATUS_OPENING_PR="MICROAGENT$STATUS_OPENING_PR",E.MICROAGENT$STATUS_COMPLETED="MICROAGENT$STATUS_COMPLETED",E.MICROAGENT$STATUS_ERROR="MICROAGENT$STATUS_ERROR",E.MICROAGENT$VIEW_YOUR_PR="MICROAGENT$VIEW_YOUR_PR",E.MICROAGENT$DESCRIBE_WHAT_TO_ADD="MICROAGENT$DESCRIBE_WHAT_TO_ADD",E.MICROAGENT$SELECT_FILE_OR_CUSTOM="MICROAGENT$SELECT_FILE_OR_CUSTOM",E.MICROAGENT$TYPE_TRIGGER_SPACE="MICROAGENT$TYPE_TRIGGER_SPACE",E.MICROAGENT$LOADING_PROMPT="MICROAGENT$LOADING_PROMPT",E.MICROAGENT$CANCEL="MICROAGENT$CANCEL",E.MICROAGENT$LAUNCH="MICROAGENT$LAUNCH",E.STATUS$WEBSOCKET_CLOSED="STATUS$WEBSOCKET_CLOSED",E.HOME$LAUNCH_FROM_SCRATCH="HOME$LAUNCH_FROM_SCRATCH",E.HOME$READ_THIS="HOME$READ_THIS",E.AUTH$LOGGING_BACK_IN="AUTH$LOGGING_BACK_IN",E.SECURITY$LOW_RISK="SECURITY$LOW_RISK",E.SECURITY$MEDIUM_RISK="SECURITY$MEDIUM_RISK",E.SECURITY$HIGH_RISK="SECURITY$HIGH_RISK",E.SECURITY$UNKNOWN_RISK="SECURITY$UNKNOWN_RISK",E.FINISH$TASK_COMPLETED_SUCCESSFULLY="FINISH$TASK_COMPLETED_SUCCESSFULLY",E.FINISH$TASK_NOT_COMPLETED="FINISH$TASK_NOT_COMPLETED",E.FINISH$TASK_COMPLETED_PARTIALLY="FINISH$TASK_COMPLETED_PARTIALLY",E.EVENT$UNKNOWN_EVENT="EVENT$UNKNOWN_EVENT",E.OBSERVATION$COMMAND_NO_OUTPUT="OBSERVATION$COMMAND_NO_OUTPUT",E.OBSERVATION$MCP_NO_OUTPUT="OBSERVATION$MCP_NO_OUTPUT",E.MCP_OBSERVATION$ARGUMENTS="MCP_OBSERVATION$ARGUMENTS",E.MCP_OBSERVATION$OUTPUT="MCP_OBSERVATION$OUTPUT",E.OBSERVATION$ERROR_PREFIX="OBSERVATION$ERROR_PREFIX",E.TASK$ADDRESSING_TASK="TASK$ADDRESSING_TASK",E.SECRETS$SECRET_VALUE_REQUIRED="SECRETS$SECRET_VALUE_REQUIRED",E.SECRETS$ADD_SECRET="SECRETS$ADD_SECRET",E.SECRETS$EDIT_SECRET="SECRETS$EDIT_SECRET",E.SECRETS$NO_SECRETS_FOUND="SECRETS$NO_SECRETS_FOUND",E.SECRETS$ADD_NEW_SECRET="SECRETS$ADD_NEW_SECRET",E.SECRETS$CONFIRM_DELETE_KEY="SECRETS$CONFIRM_DELETE_KEY",E.SETTINGS$MCP_TITLE="SETTINGS$MCP_TITLE",E.SETTINGS$MCP_DESCRIPTION="SETTINGS$MCP_DESCRIPTION",E.SETTINGS$NAV_MCP="SETTINGS$NAV_MCP",E.SETTINGS$MCP_CONFIGURATION="SETTINGS$MCP_CONFIGURATION",E.SETTINGS$MCP_EDIT_CONFIGURATION="SETTINGS$MCP_EDIT_CONFIGURATION",E.SETTINGS$MCP_CONFIRM_CHANGES="SETTINGS$MCP_CONFIRM_CHANGES",E.SETTINGS$MCP_CONFIG_DESCRIPTION="SETTINGS$MCP_CONFIG_DESCRIPTION",E.SETTINGS$MCP_CONFIG_ERROR="SETTINGS$MCP_CONFIG_ERROR",E.SETTINGS$MCP_CONFIG_EXAMPLE="SETTINGS$MCP_CONFIG_EXAMPLE",E.SETTINGS$MCP_NO_SERVERS_CONFIGURED="SETTINGS$MCP_NO_SERVERS_CONFIGURED",E.SETTINGS$MCP_SSE_SERVERS="SETTINGS$MCP_SSE_SERVERS",E.SETTINGS$MCP_STDIO_SERVERS="SETTINGS$MCP_STDIO_SERVERS",E.SETTINGS$MCP_API_KEY="SETTINGS$MCP_API_KEY",E.SETTINGS$MCP_API_KEY_NOT_SET="SETTINGS$MCP_API_KEY_NOT_SET",E.SETTINGS$MCP_COMMAND="SETTINGS$MCP_COMMAND",E.SETTINGS$MCP_ARGS="SETTINGS$MCP_ARGS",E.SETTINGS$MCP_ENV="SETTINGS$MCP_ENV",E.SETTINGS$MCP_NAME="SETTINGS$MCP_NAME",E.SETTINGS$MCP_URL="SETTINGS$MCP_URL",E.SETTINGS$MCP_LEARN_MORE="SETTINGS$MCP_LEARN_MORE",E.SETTINGS$MCP_ERROR_SSE_ARRAY="SETTINGS$MCP_ERROR_SSE_ARRAY",E.SETTINGS$MCP_ERROR_STDIO_ARRAY="SETTINGS$MCP_ERROR_STDIO_ARRAY",E.SETTINGS$MCP_ERROR_SSE_URL="SETTINGS$MCP_ERROR_SSE_URL",E.SETTINGS$MCP_ERROR_STDIO_PROPS="SETTINGS$MCP_ERROR_STDIO_PROPS",E.SETTINGS$MCP_ERROR_INVALID_JSON="SETTINGS$MCP_ERROR_INVALID_JSON",E.SETTINGS$MCP_DEFAULT_CONFIG="SETTINGS$MCP_DEFAULT_CONFIG",E.HOME$CONNECT_PROVIDER_MESSAGE="HOME$CONNECT_PROVIDER_MESSAGE",E.HOME$LETS_START_BUILDING="HOME$LETS_START_BUILDING",E.HOME$OPENHANDS_DESCRIPTION="HOME$OPENHANDS_DESCRIPTION",E.HOME$NOT_SURE_HOW_TO_START="HOME$NOT_SURE_HOW_TO_START",E.HOME$CONNECT_TO_REPOSITORY="HOME$CONNECT_TO_REPOSITORY",E.HOME$LOADING="HOME$LOADING",E.HOME$LOADING_REPOSITORIES="HOME$LOADING_REPOSITORIES",E.HOME$FAILED_TO_LOAD_REPOSITORIES="HOME$FAILED_TO_LOAD_REPOSITORIES",E.HOME$LOADING_BRANCHES="HOME$LOADING_BRANCHES",E.HOME$FAILED_TO_LOAD_BRANCHES="HOME$FAILED_TO_LOAD_BRANCHES",E.HOME$OPEN_ISSUE="HOME$OPEN_ISSUE",E.HOME$FIX_FAILING_CHECKS="HOME$FIX_FAILING_CHECKS",E.HOME$RESOLVE_MERGE_CONFLICTS="HOME$RESOLVE_MERGE_CONFLICTS",E.HOME$RESOLVE_UNRESOLVED_COMMENTS="HOME$RESOLVE_UNRESOLVED_COMMENTS",E.HOME$LAUNCH="HOME$LAUNCH",E.SETTINGS$ADVANCED="SETTINGS$ADVANCED",E.SETTINGS$BASE_URL="SETTINGS$BASE_URL",E.SETTINGS$AGENT="SETTINGS$AGENT",E.SETTINGS$ENABLE_MEMORY_CONDENSATION="SETTINGS$ENABLE_MEMORY_CONDENSATION",E.SETTINGS$LANGUAGE="SETTINGS$LANGUAGE",E.ACTION$PUSH_TO_BRANCH="ACTION$PUSH_TO_BRANCH",E.ACTION$PUSH_CREATE_PR="ACTION$PUSH_CREATE_PR",E.ACTION$PUSH_CHANGES_TO_PR="ACTION$PUSH_CHANGES_TO_PR",E.ANALYTICS$TITLE="ANALYTICS$TITLE",E.ANALYTICS$DESCRIPTION="ANALYTICS$DESCRIPTION",E.ANALYTICS$SEND_ANONYMOUS_DATA="ANALYTICS$SEND_ANONYMOUS_DATA",E.ANALYTICS$CONFIRM_PREFERENCES="ANALYTICS$CONFIRM_PREFERENCES",E.SETTINGS$SAVING="SETTINGS$SAVING",E.SETTINGS$SAVE_CHANGES="SETTINGS$SAVE_CHANGES",E.SETTINGS$NAV_INTEGRATIONS="SETTINGS$NAV_INTEGRATIONS",E.SETTINGS$NAV_APPLICATION="SETTINGS$NAV_APPLICATION",E.SETTINGS$NAV_CREDITS="SETTINGS$NAV_CREDITS",E.SETTINGS$NAV_SECRETS="SETTINGS$NAV_SECRETS",E.SETTINGS$NAV_API_KEYS="SETTINGS$NAV_API_KEYS",E.SETTINGS$NAV_LLM="SETTINGS$NAV_LLM",E.GIT$MERGE_REQUEST="GIT$MERGE_REQUEST",E.GIT$GITLAB_API="GIT$GITLAB_API",E.GIT$PULL_REQUEST="GIT$PULL_REQUEST",E.GIT$GITHUB_API="GIT$GITHUB_API",E.BUTTON$COPY="BUTTON$COPY",E.BUTTON$COPIED="BUTTON$COPIED",E.APP$TITLE="APP$TITLE",E.BROWSER$TITLE="BROWSER$TITLE",E.BROWSER$EMPTY_MESSAGE="BROWSER$EMPTY_MESSAGE",E.SETTINGS$TITLE="SETTINGS$TITLE",E.CONVERSATION$START_NEW="CONVERSATION$START_NEW",E.CONVERSATION$REPOSITORY="CONVERSATION$REPOSITORY",E.CONVERSATION$BRANCH="CONVERSATION$BRANCH",E.CONVERSATION$GIT_PROVIDER="CONVERSATION$GIT_PROVIDER",E.ACCOUNT_SETTINGS$TITLE="ACCOUNT_SETTINGS$TITLE",E.WORKSPACE$TERMINAL_TAB_LABEL="WORKSPACE$TERMINAL_TAB_LABEL",E.WORKSPACE$BROWSER_TAB_LABEL="WORKSPACE$BROWSER_TAB_LABEL",E.WORKSPACE$JUPYTER_TAB_LABEL="WORKSPACE$JUPYTER_TAB_LABEL",E.WORKSPACE$CODE_EDITOR_TAB_LABEL="WORKSPACE$CODE_EDITOR_TAB_LABEL",E.WORKSPACE$TITLE="WORKSPACE$TITLE",E.TERMINAL$WAITING_FOR_CLIENT="TERMINAL$WAITING_FOR_CLIENT",E.CODE_EDITOR$FILE_SAVED_SUCCESSFULLY="CODE_EDITOR$FILE_SAVED_SUCCESSFULLY",E.CODE_EDITOR$SAVING_LABEL="CODE_EDITOR$SAVING_LABEL",E.CODE_EDITOR$SAVE_LABEL="CODE_EDITOR$SAVE_LABEL",E.CODE_EDITOR$OPTIONS="CODE_EDITOR$OPTIONS",E.CODE_EDITOR$FILE_SAVE_ERROR="CODE_EDITOR$FILE_SAVE_ERROR",E.CODE_EDITOR$EMPTY_MESSAGE="CODE_EDITOR$EMPTY_MESSAGE",E.FILE_SERVICE$SELECT_FILE_ERROR="FILE_SERVICE$SELECT_FILE_ERROR",E.FILE_SERVICE$UPLOAD_FILES_ERROR="FILE_SERVICE$UPLOAD_FILES_ERROR",E.FILE_SERVICE$LIST_FILES_ERROR="FILE_SERVICE$LIST_FILES_ERROR",E.FILE_SERVICE$SAVE_FILE_ERROR="FILE_SERVICE$SAVE_FILE_ERROR",E.SUGGESTIONS$INCREASE_TEST_COVERAGE="SUGGESTIONS$INCREASE_TEST_COVERAGE",E.SUGGESTIONS$AUTO_MERGE_PRS="SUGGESTIONS$AUTO_MERGE_PRS",E.SUGGESTIONS$FIX_README="SUGGESTIONS$FIX_README",E.SUGGESTIONS$CLEAN_DEPENDENCIES="SUGGESTIONS$CLEAN_DEPENDENCIES",E.SETTINGS$LLM_SETTINGS="SETTINGS$LLM_SETTINGS",E.SETTINGS$GIT_SETTINGS="SETTINGS$GIT_SETTINGS",E.SETTINGS$SOUND_NOTIFICATIONS="SETTINGS$SOUND_NOTIFICATIONS",E.SETTINGS$MAX_BUDGET_PER_TASK="SETTINGS$MAX_BUDGET_PER_TASK",E.SETTINGS$MAX_BUDGET_PER_CONVERSATION="SETTINGS$MAX_BUDGET_PER_CONVERSATION",E.SETTINGS$PROACTIVE_CONVERSATION_STARTERS="SETTINGS$PROACTIVE_CONVERSATION_STARTERS",E.SETTINGS$SEARCH_API_KEY="SETTINGS$SEARCH_API_KEY",E.SETTINGS$SEARCH_API_KEY_OPTIONAL="SETTINGS$SEARCH_API_KEY_OPTIONAL",E.SETTINGS$SEARCH_API_KEY_INSTRUCTIONS="SETTINGS$SEARCH_API_KEY_INSTRUCTIONS",E.SETTINGS$CUSTOM_MODEL="SETTINGS$CUSTOM_MODEL",E.GITHUB$CODE_NOT_IN_GITHUB="GITHUB$CODE_NOT_IN_GITHUB",E.GITHUB$START_FROM_SCRATCH="GITHUB$START_FROM_SCRATCH",E.AVATAR$ALT_TEXT="AVATAR$ALT_TEXT",E.BRANDING$ALL_HANDS_AI="BRANDING$ALL_HANDS_AI",E.BRANDING$ALL_HANDS_LOGO="BRANDING$ALL_HANDS_LOGO",E.ERROR$GENERIC="ERROR$GENERIC",E.GITHUB$AUTH_SCOPE="GITHUB$AUTH_SCOPE",E.FILE_SERVICE$INVALID_FILE_PATH="FILE_SERVICE$INVALID_FILE_PATH",E.VSCODE$OPEN="VSCODE$OPEN",E.VSCODE$TITLE="VSCODE$TITLE",E.VSCODE$LOADING="VSCODE$LOADING",E.VSCODE$URL_NOT_AVAILABLE="VSCODE$URL_NOT_AVAILABLE",E.VSCODE$FETCH_ERROR="VSCODE$FETCH_ERROR",E.VSCODE$CROSS_ORIGIN_WARNING="VSCODE$CROSS_ORIGIN_WARNING",E.VSCODE$URL_PARSE_ERROR="VSCODE$URL_PARSE_ERROR",E.VSCODE$OPEN_IN_NEW_TAB="VSCODE$OPEN_IN_NEW_TAB",E.INCREASE_TEST_COVERAGE="INCREASE_TEST_COVERAGE",E.AUTO_MERGE_PRS="AUTO_MERGE_PRS",E.FIX_README="FIX_README",E.CLEAN_DEPENDENCIES="CLEAN_DEPENDENCIES",E.CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_LABEL="CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_LABEL",E.LLM$PROVIDER="LLM$PROVIDER",E.LLM$SELECT_PROVIDER_PLACEHOLDER="LLM$SELECT_PROVIDER_PLACEHOLDER",E.API$KEY="API$KEY",E.API$DONT_KNOW_KEY="API$DONT_KNOW_KEY",E.BUTTON$SAVE="BUTTON$SAVE",E.BUTTON$CLOSE="BUTTON$CLOSE",E.MODAL$CONFIRM_RESET_TITLE="MODAL$CONFIRM_RESET_TITLE",E.MODAL$CONFIRM_RESET_MESSAGE="MODAL$CONFIRM_RESET_MESSAGE",E.MODAL$END_SESSION_TITLE="MODAL$END_SESSION_TITLE",E.MODAL$END_SESSION_MESSAGE="MODAL$END_SESSION_MESSAGE",E.BUTTON$END_SESSION="BUTTON$END_SESSION",E.BUTTON$CANCEL="BUTTON$CANCEL",E.EXIT_PROJECT$CONFIRM="EXIT_PROJECT$CONFIRM",E.EXIT_PROJECT$TITLE="EXIT_PROJECT$TITLE",E.LANGUAGE$LABEL="LANGUAGE$LABEL",E.GITHUB$TOKEN_LABEL="GITHUB$TOKEN_LABEL",E.GITHUB$HOST_LABEL="GITHUB$HOST_LABEL",E.GITHUB$TOKEN_OPTIONAL="GITHUB$TOKEN_OPTIONAL",E.GITHUB$GET_TOKEN="GITHUB$GET_TOKEN",E.GITHUB$TOKEN_HELP_TEXT="GITHUB$TOKEN_HELP_TEXT",E.GITHUB$TOKEN_LINK_TEXT="GITHUB$TOKEN_LINK_TEXT",E.GITHUB$INSTRUCTIONS_LINK_TEXT="GITHUB$INSTRUCTIONS_LINK_TEXT",E.COMMON$HERE="COMMON$HERE",E.GITHUB$TOKEN_INVALID="GITHUB$TOKEN_INVALID",E.BUTTON$DISCONNECT="BUTTON$DISCONNECT",E.GITHUB$CONFIGURE_REPOS="GITHUB$CONFIGURE_REPOS",E.SLACK$INSTALL_APP="SLACK$INSTALL_APP",E.COMMON$CLICK_FOR_INSTRUCTIONS="COMMON$CLICK_FOR_INSTRUCTIONS",E.LLM$SELECT_MODEL_PLACEHOLDER="LLM$SELECT_MODEL_PLACEHOLDER",E.LLM$MODEL="LLM$MODEL",E.CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_PLACEHOLDER="CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_PLACEHOLDER",E.CONFIGURATION$MODAL_TITLE="CONFIGURATION$MODAL_TITLE",E.CONFIGURATION$MODEL_SELECT_LABEL="CONFIGURATION$MODEL_SELECT_LABEL",E.CONFIGURATION$MODEL_SELECT_PLACEHOLDER="CONFIGURATION$MODEL_SELECT_PLACEHOLDER",E.CONFIGURATION$AGENT_SELECT_LABEL="CONFIGURATION$AGENT_SELECT_LABEL",E.CONFIGURATION$AGENT_SELECT_PLACEHOLDER="CONFIGURATION$AGENT_SELECT_PLACEHOLDER",E.CONFIGURATION$LANGUAGE_SELECT_LABEL="CONFIGURATION$LANGUAGE_SELECT_LABEL",E.CONFIGURATION$LANGUAGE_SELECT_PLACEHOLDER="CONFIGURATION$LANGUAGE_SELECT_PLACEHOLDER",E.CONFIGURATION$SECURITY_SELECT_LABEL="CONFIGURATION$SECURITY_SELECT_LABEL",E.CONFIGURATION$SECURITY_SELECT_PLACEHOLDER="CONFIGURATION$SECURITY_SELECT_PLACEHOLDER",E.CONFIGURATION$MODAL_CLOSE_BUTTON_LABEL="CONFIGURATION$MODAL_CLOSE_BUTTON_LABEL",E.CONFIGURATION$MODAL_SAVE_BUTTON_LABEL="CONFIGURATION$MODAL_SAVE_BUTTON_LABEL",E.CONFIGURATION$MODAL_RESET_BUTTON_LABEL="CONFIGURATION$MODAL_RESET_BUTTON_LABEL",E.STATUS$CONNECTED_TO_SERVER="STATUS$CONNECTED_TO_SERVER",E.PROJECT$NEW_PROJECT="PROJECT$NEW_PROJECT",E.BROWSER$SCREENSHOT="BROWSER$SCREENSHOT",E.TIME$MINUTES_AGO="TIME$MINUTES_AGO",E.TIME$HOURS_AGO="TIME$HOURS_AGO",E.TIME$DAYS_AGO="TIME$DAYS_AGO",E.SETTINGS_FORM$RUNTIME_SIZE_LABEL="SETTINGS_FORM$RUNTIME_SIZE_LABEL",E.CONFIGURATION$SETTINGS_NEED_UPDATE_MESSAGE="CONFIGURATION$SETTINGS_NEED_UPDATE_MESSAGE",E.CONFIGURATION$AGENT_LOADING="CONFIGURATION$AGENT_LOADING",E.CONFIGURATION$AGENT_RUNNING="CONFIGURATION$AGENT_RUNNING",E.CONFIGURATION$ERROR_FETCH_MODELS="CONFIGURATION$ERROR_FETCH_MODELS",E.CONFIGURATION$SETTINGS_NOT_FOUND="CONFIGURATION$SETTINGS_NOT_FOUND",E.CONNECT_TO_GITHUB_BY_TOKEN_MODAL$TERMS_OF_SERVICE="CONNECT_TO_GITHUB_BY_TOKEN_MODAL$TERMS_OF_SERVICE",E.SESSION$SERVER_CONNECTED_MESSAGE="SESSION$SERVER_CONNECTED_MESSAGE",E.SESSION$SESSION_HANDLING_ERROR_MESSAGE="SESSION$SESSION_HANDLING_ERROR_MESSAGE",E.SESSION$SESSION_CONNECTION_ERROR_MESSAGE="SESSION$SESSION_CONNECTION_ERROR_MESSAGE",E.SESSION$SOCKET_NOT_INITIALIZED_ERROR_MESSAGE="SESSION$SOCKET_NOT_INITIALIZED_ERROR_MESSAGE",E.EXPLORER$UPLOAD_ERROR_MESSAGE="EXPLORER$UPLOAD_ERROR_MESSAGE",E.EXPLORER$LABEL_DROP_FILES="EXPLORER$LABEL_DROP_FILES",E.EXPLORER$UPLOAD_SUCCESS_MESSAGE="EXPLORER$UPLOAD_SUCCESS_MESSAGE",E.EXPLORER$NO_FILES_UPLOADED_MESSAGE="EXPLORER$NO_FILES_UPLOADED_MESSAGE",E.EXPLORER$UPLOAD_PARTIAL_SUCCESS_MESSAGE="EXPLORER$UPLOAD_PARTIAL_SUCCESS_MESSAGE",E.EXPLORER$UPLOAD_UNEXPECTED_RESPONSE_MESSAGE="EXPLORER$UPLOAD_UNEXPECTED_RESPONSE_MESSAGE",E.EXPLORER$VSCODE_SWITCHING_MESSAGE="EXPLORER$VSCODE_SWITCHING_MESSAGE",E.EXPLORER$VSCODE_SWITCHING_ERROR_MESSAGE="EXPLORER$VSCODE_SWITCHING_ERROR_MESSAGE",E.LOAD_SESSION$MODAL_TITLE="LOAD_SESSION$MODAL_TITLE",E.LOAD_SESSION$MODAL_CONTENT="LOAD_SESSION$MODAL_CONTENT",E.LOAD_SESSION$RESUME_SESSION_MODAL_ACTION_LABEL="LOAD_SESSION$RESUME_SESSION_MODAL_ACTION_LABEL",E.LOAD_SESSION$START_NEW_SESSION_MODAL_ACTION_LABEL="LOAD_SESSION$START_NEW_SESSION_MODAL_ACTION_LABEL",E.FEEDBACK$MODAL_TITLE="FEEDBACK$MODAL_TITLE",E.FEEDBACK$MODAL_CONTENT="FEEDBACK$MODAL_CONTENT",E.FEEDBACK$EMAIL_LABEL="FEEDBACK$EMAIL_LABEL",E.FEEDBACK$CONTRIBUTE_LABEL="FEEDBACK$CONTRIBUTE_LABEL",E.FEEDBACK$SHARE_LABEL="FEEDBACK$SHARE_LABEL",E.FEEDBACK$CANCEL_LABEL="FEEDBACK$CANCEL_LABEL",E.FEEDBACK$EMAIL_PLACEHOLDER="FEEDBACK$EMAIL_PLACEHOLDER",E.FEEDBACK$PASSWORD_COPIED_MESSAGE="FEEDBACK$PASSWORD_COPIED_MESSAGE",E.FEEDBACK$GO_TO_FEEDBACK="FEEDBACK$GO_TO_FEEDBACK",E.FEEDBACK$PASSWORD="FEEDBACK$PASSWORD",E.FEEDBACK$INVALID_EMAIL_FORMAT="FEEDBACK$INVALID_EMAIL_FORMAT",E.FEEDBACK$FAILED_TO_SHARE="FEEDBACK$FAILED_TO_SHARE",E.FEEDBACK$COPY_LABEL="FEEDBACK$COPY_LABEL",E.FEEDBACK$SHARING_SETTINGS_LABEL="FEEDBACK$SHARING_SETTINGS_LABEL",E.SECURITY$UNKNOWN_ANALYZER_LABEL="SECURITY$UNKNOWN_ANALYZER_LABEL",E.INVARIANT$UPDATE_POLICY_LABEL="INVARIANT$UPDATE_POLICY_LABEL",E.INVARIANT$UPDATE_SETTINGS_LABEL="INVARIANT$UPDATE_SETTINGS_LABEL",E.INVARIANT$SETTINGS_LABEL="INVARIANT$SETTINGS_LABEL",E.INVARIANT$ASK_CONFIRMATION_RISK_SEVERITY_LABEL="INVARIANT$ASK_CONFIRMATION_RISK_SEVERITY_LABEL",E.INVARIANT$DONT_ASK_FOR_CONFIRMATION_LABEL="INVARIANT$DONT_ASK_FOR_CONFIRMATION_LABEL",E.INVARIANT$INVARIANT_ANALYZER_LABEL="INVARIANT$INVARIANT_ANALYZER_LABEL",E.INVARIANT$INVARIANT_ANALYZER_MESSAGE="INVARIANT$INVARIANT_ANALYZER_MESSAGE",E.INVARIANT$CLICK_TO_LEARN_MORE_LABEL="INVARIANT$CLICK_TO_LEARN_MORE_LABEL",E.INVARIANT$POLICY_LABEL="INVARIANT$POLICY_LABEL",E.INVARIANT$LOG_LABEL="INVARIANT$LOG_LABEL",E.INVARIANT$EXPORT_TRACE_LABEL="INVARIANT$EXPORT_TRACE_LABEL",E.INVARIANT$TRACE_EXPORTED_MESSAGE="INVARIANT$TRACE_EXPORTED_MESSAGE",E.INVARIANT$POLICY_UPDATED_MESSAGE="INVARIANT$POLICY_UPDATED_MESSAGE",E.INVARIANT$SETTINGS_UPDATED_MESSAGE="INVARIANT$SETTINGS_UPDATED_MESSAGE",E.CHAT_INTERFACE$AUGMENTED_PROMPT_FILES_TITLE="CHAT_INTERFACE$AUGMENTED_PROMPT_FILES_TITLE",E.CHAT_INTERFACE$DISCONNECTED="CHAT_INTERFACE$DISCONNECTED",E.CHAT_INTERFACE$CONNECTING="CHAT_INTERFACE$CONNECTING",E.CHAT_INTERFACE$STOPPED="CHAT_INTERFACE$STOPPED",E.CHAT_INTERFACE$INITIALIZING_AGENT_LOADING_MESSAGE="CHAT_INTERFACE$INITIALIZING_AGENT_LOADING_MESSAGE",E.CHAT_INTERFACE$AGENT_INIT_MESSAGE="CHAT_INTERFACE$AGENT_INIT_MESSAGE",E.CHAT_INTERFACE$AGENT_RUNNING_MESSAGE="CHAT_INTERFACE$AGENT_RUNNING_MESSAGE",E.CHAT_INTERFACE$AGENT_AWAITING_USER_INPUT_MESSAGE="CHAT_INTERFACE$AGENT_AWAITING_USER_INPUT_MESSAGE",E.CHAT_INTERFACE$AGENT_RATE_LIMITED_MESSAGE="CHAT_INTERFACE$AGENT_RATE_LIMITED_MESSAGE",E.CHAT_INTERFACE$AGENT_RATE_LIMITED_STOPPED_MESSAGE="CHAT_INTERFACE$AGENT_RATE_LIMITED_STOPPED_MESSAGE",E.CHAT_INTERFACE$AGENT_PAUSED_MESSAGE="CHAT_INTERFACE$AGENT_PAUSED_MESSAGE",E.LANDING$TITLE="LANDING$TITLE",E.LANDING$SUBTITLE="LANDING$SUBTITLE",E.LANDING$START_HELP="LANDING$START_HELP",E.LANDING$START_HELP_LINK="LANDING$START_HELP_LINK",E.SUGGESTIONS$HELLO_WORLD="SUGGESTIONS$HELLO_WORLD",E.SUGGESTIONS$TODO_APP="SUGGESTIONS$TODO_APP",E.SUGGESTIONS$HACKER_NEWS="SUGGESTIONS$HACKER_NEWS",E.LANDING$CHANGE_PROMPT="LANDING$CHANGE_PROMPT",E.GITHUB$CONNECT="GITHUB$CONNECT",E.GITHUB$NO_RESULTS="GITHUB$NO_RESULTS",E.GITHUB$LOADING_REPOSITORIES="GITHUB$LOADING_REPOSITORIES",E.GITHUB$ADD_MORE_REPOS="GITHUB$ADD_MORE_REPOS",E.GITHUB$YOUR_REPOS="GITHUB$YOUR_REPOS",E.GITHUB$PUBLIC_REPOS="GITHUB$PUBLIC_REPOS",E.DOWNLOAD$PREPARING="DOWNLOAD$PREPARING",E.DOWNLOAD$DOWNLOADING="DOWNLOAD$DOWNLOADING",E.DOWNLOAD$FOUND_FILES="DOWNLOAD$FOUND_FILES",E.DOWNLOAD$SCANNING="DOWNLOAD$SCANNING",E.DOWNLOAD$FILES_PROGRESS="DOWNLOAD$FILES_PROGRESS",E.DOWNLOAD$CANCEL="DOWNLOAD$CANCEL",E.ACTION$CONFIRM="ACTION$CONFIRM",E.ACTION$REJECT="ACTION$REJECT",E.BADGE$BETA="BADGE$BETA",E.AGENT$RESUME_TASK="AGENT$RESUME_TASK",E.AGENT$PAUSE_TASK="AGENT$PAUSE_TASK",E.TOS$ACCEPT="TOS$ACCEPT",E.TOS$TERMS="TOS$TERMS",E.USER$ACCOUNT_SETTINGS="USER$ACCOUNT_SETTINGS",E.JUPYTER$OUTPUT_LABEL="JUPYTER$OUTPUT_LABEL",E.BUTTON$STOP="BUTTON$STOP",E.BUTTON$EDIT_TITLE="BUTTON$EDIT_TITLE",E.BUTTON$DOWNLOAD_VIA_VSCODE="BUTTON$DOWNLOAD_VIA_VSCODE",E.BUTTON$DISPLAY_COST="BUTTON$DISPLAY_COST",E.BUTTON$SHOW_AGENT_TOOLS_AND_METADATA="BUTTON$SHOW_AGENT_TOOLS_AND_METADATA",E.LANDING$ATTACH_IMAGES="LANDING$ATTACH_IMAGES",E.LANDING$OPEN_REPO="LANDING$OPEN_REPO",E.LANDING$REPLAY="LANDING$REPLAY",E.LANDING$UPLOAD_TRAJECTORY="LANDING$UPLOAD_TRAJECTORY",E.LANDING$RECENT_CONVERSATION="LANDING$RECENT_CONVERSATION",E.CONVERSATION$CONFIRM_DELETE="CONVERSATION$CONFIRM_DELETE",E.CONVERSATION$CONFIRM_STOP="CONVERSATION$CONFIRM_STOP",E.CONVERSATION$STOP_WARNING="CONVERSATION$STOP_WARNING",E.CONVERSATION$METRICS_INFO="CONVERSATION$METRICS_INFO",E.CONVERSATION$CREATED="CONVERSATION$CREATED",E.CONVERSATION$AGO="CONVERSATION$AGO",E.GITHUB$VSCODE_LINK_DESCRIPTION="GITHUB$VSCODE_LINK_DESCRIPTION",E.CONVERSATION$EXIT_WARNING="CONVERSATION$EXIT_WARNING",E.CONVERSATION$TITLE_UPDATED="CONVERSATION$TITLE_UPDATED",E.LANDING$OR="LANDING$OR",E.SUGGESTIONS$TEST_COVERAGE="SUGGESTIONS$TEST_COVERAGE",E.SUGGESTIONS$AUTO_MERGE="SUGGESTIONS$AUTO_MERGE",E.CHAT_INTERFACE$AGENT_STOPPED_MESSAGE="CHAT_INTERFACE$AGENT_STOPPED_MESSAGE",E.CHAT_INTERFACE$AGENT_FINISHED_MESSAGE="CHAT_INTERFACE$AGENT_FINISHED_MESSAGE",E.CHAT_INTERFACE$AGENT_REJECTED_MESSAGE="CHAT_INTERFACE$AGENT_REJECTED_MESSAGE",E.CHAT_INTERFACE$AGENT_ERROR_MESSAGE="CHAT_INTERFACE$AGENT_ERROR_MESSAGE",E.CHAT_INTERFACE$AGENT_AWAITING_USER_CONFIRMATION_MESSAGE="CHAT_INTERFACE$AGENT_AWAITING_USER_CONFIRMATION_MESSAGE",E.CHAT_INTERFACE$AGENT_ACTION_USER_CONFIRMED_MESSAGE="CHAT_INTERFACE$AGENT_ACTION_USER_CONFIRMED_MESSAGE",E.CHAT_INTERFACE$AGENT_ACTION_USER_REJECTED_MESSAGE="CHAT_INTERFACE$AGENT_ACTION_USER_REJECTED_MESSAGE",E.CHAT_INTERFACE$INPUT_PLACEHOLDER="CHAT_INTERFACE$INPUT_PLACEHOLDER",E.CHAT_INTERFACE$INPUT_CONTINUE_MESSAGE="CHAT_INTERFACE$INPUT_CONTINUE_MESSAGE",E.CHAT_INTERFACE$USER_ASK_CONFIRMATION="CHAT_INTERFACE$USER_ASK_CONFIRMATION",E.CHAT_INTERFACE$USER_CONFIRMED="CHAT_INTERFACE$USER_CONFIRMED",E.CHAT_INTERFACE$USER_REJECTED="CHAT_INTERFACE$USER_REJECTED",E.CHAT_INTERFACE$INPUT_SEND_MESSAGE_BUTTON_CONTENT="CHAT_INTERFACE$INPUT_SEND_MESSAGE_BUTTON_CONTENT",E.CHAT_INTERFACE$CHAT_MESSAGE_COPIED="CHAT_INTERFACE$CHAT_MESSAGE_COPIED",E.CHAT_INTERFACE$CHAT_MESSAGE_COPY_FAILED="CHAT_INTERFACE$CHAT_MESSAGE_COPY_FAILED",E.CHAT_INTERFACE$TOOLTIP_COPY_MESSAGE="CHAT_INTERFACE$TOOLTIP_COPY_MESSAGE",E.CHAT_INTERFACE$TOOLTIP_SEND_MESSAGE="CHAT_INTERFACE$TOOLTIP_SEND_MESSAGE",E.CHAT_INTERFACE$TOOLTIP_UPLOAD_IMAGE="CHAT_INTERFACE$TOOLTIP_UPLOAD_IMAGE",E.CHAT_INTERFACE$INITIAL_MESSAGE="CHAT_INTERFACE$INITIAL_MESSAGE",E.CHAT_INTERFACE$ASSISTANT="CHAT_INTERFACE$ASSISTANT",E.CHAT_INTERFACE$TO_BOTTOM="CHAT_INTERFACE$TO_BOTTOM",E.CHAT_INTERFACE$MESSAGE_ARIA_LABEL="CHAT_INTERFACE$MESSAGE_ARIA_LABEL",E.CHAT_INTERFACE$CHAT_CONVERSATION="CHAT_INTERFACE$CHAT_CONVERSATION",E.CHAT_INTERFACE$UNKNOWN_SENDER="CHAT_INTERFACE$UNKNOWN_SENDER",E.SECURITY_ANALYZER$UNKNOWN_RISK="SECURITY_ANALYZER$UNKNOWN_RISK",E.SECURITY_ANALYZER$LOW_RISK="SECURITY_ANALYZER$LOW_RISK",E.SECURITY_ANALYZER$MEDIUM_RISK="SECURITY_ANALYZER$MEDIUM_RISK",E.SECURITY_ANALYZER$HIGH_RISK="SECURITY_ANALYZER$HIGH_RISK",E.SETTINGS$MODEL_TOOLTIP="SETTINGS$MODEL_TOOLTIP",E.SETTINGS$AGENT_TOOLTIP="SETTINGS$AGENT_TOOLTIP",E.SETTINGS$LANGUAGE_TOOLTIP="SETTINGS$LANGUAGE_TOOLTIP",E.SETTINGS$DISABLED_RUNNING="SETTINGS$DISABLED_RUNNING",E.SETTINGS$API_KEY_PLACEHOLDER="SETTINGS$API_KEY_PLACEHOLDER",E.SETTINGS$LLM_API_KEY="SETTINGS$LLM_API_KEY",E.SETTINGS$LLM_API_KEY_DESCRIPTION="SETTINGS$LLM_API_KEY_DESCRIPTION",E.SETTINGS$REFRESH_LLM_API_KEY="SETTINGS$REFRESH_LLM_API_KEY",E.SETTINGS$CONFIRMATION_MODE="SETTINGS$CONFIRMATION_MODE",E.SETTINGS$CONFIRMATION_MODE_TOOLTIP="SETTINGS$CONFIRMATION_MODE_TOOLTIP",E.SETTINGS$AGENT_SELECT_ENABLED="SETTINGS$AGENT_SELECT_ENABLED",E.SETTINGS$SECURITY_ANALYZER="SETTINGS$SECURITY_ANALYZER",E.SETTINGS$SECURITY_ANALYZER_PLACEHOLDER="SETTINGS$SECURITY_ANALYZER_PLACEHOLDER",E.SETTINGS$DONT_KNOW_API_KEY="SETTINGS$DONT_KNOW_API_KEY",E.SETTINGS$CLICK_FOR_INSTRUCTIONS="SETTINGS$CLICK_FOR_INSTRUCTIONS",E.SETTINGS$SAVED="SETTINGS$SAVED",E.SETTINGS$SAVED_WARNING="SETTINGS$SAVED_WARNING",E.SETTINGS$RESET="SETTINGS$RESET",E.SETTINGS$API_KEYS="SETTINGS$API_KEYS",E.SETTINGS$API_KEYS_DESCRIPTION="SETTINGS$API_KEYS_DESCRIPTION",E.SETTINGS$OPENHANDS_API_KEY_HELP="SETTINGS$OPENHANDS_API_KEY_HELP",E.SETTINGS$OPENHANDS_API_KEY_HELP_TEXT="SETTINGS$OPENHANDS_API_KEY_HELP_TEXT",E.SETTINGS$OPENHANDS_API_KEY_HELP_SUFFIX="SETTINGS$OPENHANDS_API_KEY_HELP_SUFFIX",E.SETTINGS$CREATE_API_KEY="SETTINGS$CREATE_API_KEY",E.SETTINGS$CREATE_API_KEY_DESCRIPTION="SETTINGS$CREATE_API_KEY_DESCRIPTION",E.SETTINGS$DELETE_API_KEY="SETTINGS$DELETE_API_KEY",E.SETTINGS$DELETE_API_KEY_CONFIRMATION="SETTINGS$DELETE_API_KEY_CONFIRMATION",E.SETTINGS$NO_API_KEYS="SETTINGS$NO_API_KEYS",E.SETTINGS$NAME="SETTINGS$NAME",E.SECRETS$DESCRIPTION="SECRETS$DESCRIPTION",E.SETTINGS$KEY_PREFIX="SETTINGS$KEY_PREFIX",E.SETTINGS$CREATED_AT="SETTINGS$CREATED_AT",E.SETTINGS$LAST_USED="SETTINGS$LAST_USED",E.SETTINGS$ACTIONS="SETTINGS$ACTIONS",E.SETTINGS$API_KEY_CREATED="SETTINGS$API_KEY_CREATED",E.SETTINGS$API_KEY_DELETED="SETTINGS$API_KEY_DELETED",E.SETTINGS$API_KEY_WARNING="SETTINGS$API_KEY_WARNING",E.SETTINGS$API_KEY_COPIED="SETTINGS$API_KEY_COPIED",E.SETTINGS$API_KEY_REFRESHED="SETTINGS$API_KEY_REFRESHED",E.SETTINGS$API_KEY_NAME_PLACEHOLDER="SETTINGS$API_KEY_NAME_PLACEHOLDER",E.BUTTON$CREATE="BUTTON$CREATE",E.BUTTON$DELETE="BUTTON$DELETE",E.BUTTON$COPY_TO_CLIPBOARD="BUTTON$COPY_TO_CLIPBOARD",E.BUTTON$REFRESH="BUTTON$REFRESH",E.ERROR$REQUIRED_FIELD="ERROR$REQUIRED_FIELD",E.PLANNER$EMPTY_MESSAGE="PLANNER$EMPTY_MESSAGE",E.FEEDBACK$PUBLIC_LABEL="FEEDBACK$PUBLIC_LABEL",E.FEEDBACK$PRIVATE_LABEL="FEEDBACK$PRIVATE_LABEL",E.SIDEBAR$CONVERSATIONS="SIDEBAR$CONVERSATIONS",E.STATUS$CONNECTING_TO_RUNTIME="STATUS$CONNECTING_TO_RUNTIME",E.STATUS$STARTING_RUNTIME="STATUS$STARTING_RUNTIME",E.STATUS$SETTING_UP_WORKSPACE="STATUS$SETTING_UP_WORKSPACE",E.STATUS$SETTING_UP_GIT_HOOKS="STATUS$SETTING_UP_GIT_HOOKS",E.ACCOUNT_SETTINGS_MODAL$DISCONNECT="ACCOUNT_SETTINGS_MODAL$DISCONNECT",E.ACCOUNT_SETTINGS_MODAL$SAVE="ACCOUNT_SETTINGS_MODAL$SAVE",E.ACCOUNT_SETTINGS_MODAL$CLOSE="ACCOUNT_SETTINGS_MODAL$CLOSE",E.ACCOUNT_SETTINGS_MODAL$GITHUB_TOKEN_INVALID="ACCOUNT_SETTINGS_MODAL$GITHUB_TOKEN_INVALID",E.CONNECT_TO_GITHUB_MODAL$GET_YOUR_TOKEN="CONNECT_TO_GITHUB_MODAL$GET_YOUR_TOKEN",E.CONNECT_TO_GITHUB_MODAL$HERE="CONNECT_TO_GITHUB_MODAL$HERE",E.CONNECT_TO_GITHUB_MODAL$CONNECT="CONNECT_TO_GITHUB_MODAL$CONNECT",E.CONNECT_TO_GITHUB_MODAL$CLOSE="CONNECT_TO_GITHUB_MODAL$CLOSE",E.CONNECT_TO_GITHUB_BY_TOKEN_MODAL$BY_CONNECTING_YOU_AGREE="CONNECT_TO_GITHUB_BY_TOKEN_MODAL$BY_CONNECTING_YOU_AGREE",E.CONNECT_TO_GITHUB_BY_TOKEN_MODAL$CONTINUE="CONNECT_TO_GITHUB_BY_TOKEN_MODAL$CONTINUE",E.LOADING_PROJECT$LOADING="LOADING_PROJECT$LOADING",E.CUSTOM_INPUT$OPTIONAL_LABEL="CUSTOM_INPUT$OPTIONAL_LABEL",E.SETTINGS_FORM$CUSTOM_MODEL_LABEL="SETTINGS_FORM$CUSTOM_MODEL_LABEL",E.SETTINGS_FORM$BASE_URL_LABEL="SETTINGS_FORM$BASE_URL_LABEL",E.SETTINGS_FORM$API_KEY_LABEL="SETTINGS_FORM$API_KEY_LABEL",E.SETTINGS_FORM$DONT_KNOW_API_KEY_LABEL="SETTINGS_FORM$DONT_KNOW_API_KEY_LABEL",E.SETTINGS_FORM$CLICK_HERE_FOR_INSTRUCTIONS_LABEL="SETTINGS_FORM$CLICK_HERE_FOR_INSTRUCTIONS_LABEL",E.SETTINGS_FORM$AGENT_LABEL="SETTINGS_FORM$AGENT_LABEL",E.SETTINGS_FORM$SECURITY_ANALYZER_LABEL="SETTINGS_FORM$SECURITY_ANALYZER_LABEL",E.SETTINGS_FORM$ENABLE_CONFIRMATION_MODE_LABEL="SETTINGS_FORM$ENABLE_CONFIRMATION_MODE_LABEL",E.SETTINGS_FORM$SAVE_LABEL="SETTINGS_FORM$SAVE_LABEL",E.SETTINGS_FORM$CLOSE_LABEL="SETTINGS_FORM$CLOSE_LABEL",E.SETTINGS_FORM$CANCEL_LABEL="SETTINGS_FORM$CANCEL_LABEL",E.SETTINGS_FORM$END_SESSION_LABEL="SETTINGS_FORM$END_SESSION_LABEL",E.SETTINGS_FORM$CHANGING_WORKSPACE_WARNING_MESSAGE="SETTINGS_FORM$CHANGING_WORKSPACE_WARNING_MESSAGE",E.SETTINGS_FORM$ARE_YOU_SURE_LABEL="SETTINGS_FORM$ARE_YOU_SURE_LABEL",E.SETTINGS_FORM$ALL_INFORMATION_WILL_BE_DELETED_MESSAGE="SETTINGS_FORM$ALL_INFORMATION_WILL_BE_DELETED_MESSAGE",E.PROJECT_MENU_DETAILS_PLACEHOLDER$NEW_PROJECT_LABEL="PROJECT_MENU_DETAILS_PLACEHOLDER$NEW_PROJECT_LABEL",E.PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECT_TO_GITHUB="PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECT_TO_GITHUB",E.PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECTED="PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECTED",E.PROJECT_MENU_DETAILS$AGO_LABEL="PROJECT_MENU_DETAILS$AGO_LABEL",E.STATUS$ERROR_LLM_AUTHENTICATION="STATUS$ERROR_LLM_AUTHENTICATION",E.STATUS$ERROR_LLM_SERVICE_UNAVAILABLE="STATUS$ERROR_LLM_SERVICE_UNAVAILABLE",E.STATUS$ERROR_LLM_INTERNAL_SERVER_ERROR="STATUS$ERROR_LLM_INTERNAL_SERVER_ERROR",E.STATUS$ERROR_LLM_OUT_OF_CREDITS="STATUS$ERROR_LLM_OUT_OF_CREDITS",E.STATUS$ERROR_LLM_CONTENT_POLICY_VIOLATION="STATUS$ERROR_LLM_CONTENT_POLICY_VIOLATION",E.STATUS$ERROR_RUNTIME_DISCONNECTED="STATUS$ERROR_RUNTIME_DISCONNECTED",E.STATUS$LLM_RETRY="STATUS$LLM_RETRY",E.AGENT_ERROR$BAD_ACTION="AGENT_ERROR$BAD_ACTION",E.AGENT_ERROR$ACTION_TIMEOUT="AGENT_ERROR$ACTION_TIMEOUT",E.AGENT_ERROR$TOO_MANY_CONVERSATIONS="AGENT_ERROR$TOO_MANY_CONVERSATIONS",E.PROJECT_MENU_CARD_CONTEXT_MENU$CONNECT_TO_GITHUB_LABEL="PROJECT_MENU_CARD_CONTEXT_MENU$CONNECT_TO_GITHUB_LABEL",E.PROJECT_MENU_CARD_CONTEXT_MENU$PUSH_TO_GITHUB_LABEL="PROJECT_MENU_CARD_CONTEXT_MENU$PUSH_TO_GITHUB_LABEL",E.PROJECT_MENU_CARD_CONTEXT_MENU$DOWNLOAD_FILES_LABEL="PROJECT_MENU_CARD_CONTEXT_MENU$DOWNLOAD_FILES_LABEL",E.PROJECT_MENU_CARD$OPEN="PROJECT_MENU_CARD$OPEN",E.ACTION_BUTTON$RESUME="ACTION_BUTTON$RESUME",E.ACTION_BUTTON$PAUSE="ACTION_BUTTON$PAUSE",E.BROWSER$SCREENSHOT_ALT="BROWSER$SCREENSHOT_ALT",E.ERROR_TOAST$CLOSE_BUTTON_LABEL="ERROR_TOAST$CLOSE_BUTTON_LABEL",E.FILE_EXPLORER$UPLOAD="FILE_EXPLORER$UPLOAD",E.ACTION_MESSAGE$RUN="ACTION_MESSAGE$RUN",E.ACTION_MESSAGE$RUN_IPYTHON="ACTION_MESSAGE$RUN_IPYTHON",E.ACTION_MESSAGE$CALL_TOOL_MCP="ACTION_MESSAGE$CALL_TOOL_MCP",E.ACTION_MESSAGE$READ="ACTION_MESSAGE$READ",E.ACTION_MESSAGE$EDIT="ACTION_MESSAGE$EDIT",E.ACTION_MESSAGE$WRITE="ACTION_MESSAGE$WRITE",E.ACTION_MESSAGE$BROWSE="ACTION_MESSAGE$BROWSE",E.ACTION_MESSAGE$BROWSE_INTERACTIVE="ACTION_MESSAGE$BROWSE_INTERACTIVE",E.ACTION_MESSAGE$THINK="ACTION_MESSAGE$THINK",E.ACTION_MESSAGE$SYSTEM="ACTION_MESSAGE$SYSTEM",E.ACTION_MESSAGE$CONDENSATION="ACTION_MESSAGE$CONDENSATION",E.OBSERVATION_MESSAGE$RUN="OBSERVATION_MESSAGE$RUN",E.OBSERVATION_MESSAGE$RUN_IPYTHON="OBSERVATION_MESSAGE$RUN_IPYTHON",E.OBSERVATION_MESSAGE$READ="OBSERVATION_MESSAGE$READ",E.OBSERVATION_MESSAGE$EDIT="OBSERVATION_MESSAGE$EDIT",E.OBSERVATION_MESSAGE$WRITE="OBSERVATION_MESSAGE$WRITE",E.OBSERVATION_MESSAGE$BROWSE="OBSERVATION_MESSAGE$BROWSE",E.OBSERVATION_MESSAGE$MCP="OBSERVATION_MESSAGE$MCP",E.OBSERVATION_MESSAGE$RECALL="OBSERVATION_MESSAGE$RECALL",E.OBSERVATION_MESSAGE$THINK="OBSERVATION_MESSAGE$THINK",E.EXPANDABLE_MESSAGE$SHOW_DETAILS="EXPANDABLE_MESSAGE$SHOW_DETAILS",E.EXPANDABLE_MESSAGE$HIDE_DETAILS="EXPANDABLE_MESSAGE$HIDE_DETAILS",E.AI_SETTINGS$TITLE="AI_SETTINGS$TITLE",E.SETTINGS$DESCRIPTION="SETTINGS$DESCRIPTION",E.SETTINGS$WARNING="SETTINGS$WARNING",E.SIDEBAR$SETTINGS="SIDEBAR$SETTINGS",E.SIDEBAR$DOCS="SIDEBAR$DOCS",E.SUGGESTIONS$ADD_DOCS="SUGGESTIONS$ADD_DOCS",E.SUGGESTIONS$ADD_DOCKERFILE="SUGGESTIONS$ADD_DOCKERFILE",E.STATUS$CONNECTED="STATUS$CONNECTED",E.BROWSER$NO_PAGE_LOADED="BROWSER$NO_PAGE_LOADED",E.USER$AVATAR_PLACEHOLDER="USER$AVATAR_PLACEHOLDER",E.ACCOUNT_SETTINGS$SETTINGS="ACCOUNT_SETTINGS$SETTINGS",E.ACCOUNT_SETTINGS$LOGOUT="ACCOUNT_SETTINGS$LOGOUT",E.SETTINGS_FORM$ADVANCED_OPTIONS_LABEL="SETTINGS_FORM$ADVANCED_OPTIONS_LABEL",E.CONVERSATION$NO_CONVERSATIONS="CONVERSATION$NO_CONVERSATIONS",E.LANDING$SELECT_GIT_REPO="LANDING$SELECT_GIT_REPO",E.BUTTON$SEND="BUTTON$SEND",E.STATUS$BUILDING_RUNTIME="STATUS$BUILDING_RUNTIME",E.SUGGESTIONS$WHAT_TO_BUILD="SUGGESTIONS$WHAT_TO_BUILD",E.SETTINGS_FORM$ENABLE_DEFAULT_CONDENSER_SWITCH_LABEL="SETTINGS_FORM$ENABLE_DEFAULT_CONDENSER_SWITCH_LABEL",E.BUTTON$MARK_HELPFUL="BUTTON$MARK_HELPFUL",E.BUTTON$MARK_NOT_HELPFUL="BUTTON$MARK_NOT_HELPFUL",E.BUTTON$EXPORT_CONVERSATION="BUTTON$EXPORT_CONVERSATION",E.BILLING$CLICK_TO_TOP_UP="BILLING$CLICK_TO_TOP_UP",E.BILLING$YOUVE_GOT_50="BILLING$YOUVE_GOT_50",E.BILLING$ERROR_WHILE_CREATING_SESSION="BILLING$ERROR_WHILE_CREATING_SESSION",E.BILLING$CLAIM_YOUR_50="BILLING$CLAIM_YOUR_50",E.BILLING$POWERED_BY="BILLING$POWERED_BY",E.BILLING$PROCEED_TO_STRIPE="BILLING$PROCEED_TO_STRIPE",E.BILLING$YOURE_IN="BILLING$YOURE_IN",E.PAYMENT$ADD_FUNDS="PAYMENT$ADD_FUNDS",E.PAYMENT$ADD_CREDIT="PAYMENT$ADD_CREDIT",E.PAYMENT$MANAGE_CREDITS="PAYMENT$MANAGE_CREDITS",E.AUTH$SIGN_IN_WITH_GITHUB="AUTH$SIGN_IN_WITH_GITHUB",E.WAITLIST$JOIN="WAITLIST$JOIN",E.WAITLIST$IF_NOT_JOINED="WAITLIST$IF_NOT_JOINED",E.WAITLIST$PATIENCE_MESSAGE="WAITLIST$PATIENCE_MESSAGE",E.WAITLIST$ALMOST_THERE="WAITLIST$ALMOST_THERE",E.PAYMENT$SUCCESS="PAYMENT$SUCCESS",E.PAYMENT$CANCELLED="PAYMENT$CANCELLED",E.SERVED_APP$TITLE="SERVED_APP$TITLE",E.CONVERSATION$UNKNOWN="CONVERSATION$UNKNOWN",E.SETTINGS$RUNTIME_OPTION_1X="SETTINGS$RUNTIME_OPTION_1X",E.SETTINGS$RUNTIME_OPTION_2X="SETTINGS$RUNTIME_OPTION_2X",E.SETTINGS$GET_IN_TOUCH="SETTINGS$GET_IN_TOUCH",E.CONVERSATION$NO_METRICS="CONVERSATION$NO_METRICS",E.CONVERSATION$DOWNLOAD_ERROR="CONVERSATION$DOWNLOAD_ERROR",E.CONVERSATION$UPDATED="CONVERSATION$UPDATED",E.CONVERSATION$TOTAL_COST="CONVERSATION$TOTAL_COST",E.CONVERSATION$BUDGET="CONVERSATION$BUDGET",E.CONVERSATION$BUDGET_USAGE="CONVERSATION$BUDGET_USAGE",E.CONVERSATION$NO_BUDGET_LIMIT="CONVERSATION$NO_BUDGET_LIMIT",E.CONVERSATION$INPUT="CONVERSATION$INPUT",E.CONVERSATION$OUTPUT="CONVERSATION$OUTPUT",E.CONVERSATION$TOTAL="CONVERSATION$TOTAL",E.CONVERSATION$CONTEXT_WINDOW="CONVERSATION$CONTEXT_WINDOW",E.CONVERSATION$USED="CONVERSATION$USED",E.SETTINGS$RUNTIME_SETTINGS="SETTINGS$RUNTIME_SETTINGS",E.SETTINGS$RESET_CONFIRMATION="SETTINGS$RESET_CONFIRMATION",E.ERROR$GENERIC_OOPS="ERROR$GENERIC_OOPS",E.ERROR$UNKNOWN="ERROR$UNKNOWN",E.SETTINGS$FOR_OTHER_OPTIONS="SETTINGS$FOR_OTHER_OPTIONS",E.SETTINGS$SEE_ADVANCED_SETTINGS="SETTINGS$SEE_ADVANCED_SETTINGS",E.SETTINGS_FORM$API_KEY="SETTINGS_FORM$API_KEY",E.SETTINGS_FORM$BASE_URL="SETTINGS_FORM$BASE_URL",E.GITHUB$CONNECT_TO_GITHUB="GITHUB$CONNECT_TO_GITHUB",E.GITLAB$CONNECT_TO_GITLAB="GITLAB$CONNECT_TO_GITLAB",E.BITBUCKET$CONNECT_TO_BITBUCKET="BITBUCKET$CONNECT_TO_BITBUCKET",E.AUTH$SIGN_IN_WITH_IDENTITY_PROVIDER="AUTH$SIGN_IN_WITH_IDENTITY_PROVIDER",E.WAITLIST$JOIN_WAITLIST="WAITLIST$JOIN_WAITLIST",E.ACCOUNT_SETTINGS$ADDITIONAL_SETTINGS="ACCOUNT_SETTINGS$ADDITIONAL_SETTINGS",E.ACCOUNT_SETTINGS$DISCONNECT_FROM_GITHUB="ACCOUNT_SETTINGS$DISCONNECT_FROM_GITHUB",E.CONVERSATION$DELETE_WARNING="CONVERSATION$DELETE_WARNING",E.FEEDBACK$TITLE="FEEDBACK$TITLE",E.FEEDBACK$DESCRIPTION="FEEDBACK$DESCRIPTION",E.EXIT_PROJECT$WARNING="EXIT_PROJECT$WARNING",E.MODEL_SELECTOR$VERIFIED="MODEL_SELECTOR$VERIFIED",E.MODEL_SELECTOR$OTHERS="MODEL_SELECTOR$OTHERS",E.GITLAB$TOKEN_LABEL="GITLAB$TOKEN_LABEL",E.GITLAB$HOST_LABEL="GITLAB$HOST_LABEL",E.GITLAB$GET_TOKEN="GITLAB$GET_TOKEN",E.GITLAB$TOKEN_HELP_TEXT="GITLAB$TOKEN_HELP_TEXT",E.GITLAB$TOKEN_LINK_TEXT="GITLAB$TOKEN_LINK_TEXT",E.GITLAB$INSTRUCTIONS_LINK_TEXT="GITLAB$INSTRUCTIONS_LINK_TEXT",E.BITBUCKET$TOKEN_LABEL="BITBUCKET$TOKEN_LABEL",E.BITBUCKET$HOST_LABEL="BITBUCKET$HOST_LABEL",E.BITBUCKET$GET_TOKEN="BITBUCKET$GET_TOKEN",E.BITBUCKET$TOKEN_HELP_TEXT="BITBUCKET$TOKEN_HELP_TEXT",E.BITBUCKET$TOKEN_LINK_TEXT="BITBUCKET$TOKEN_LINK_TEXT",E.BITBUCKET$INSTRUCTIONS_LINK_TEXT="BITBUCKET$INSTRUCTIONS_LINK_TEXT",E.GITLAB$OR_SEE="GITLAB$OR_SEE",E.AGENT_ERROR$ERROR_ACTION_NOT_EXECUTED="AGENT_ERROR$ERROR_ACTION_NOT_EXECUTED",E.DIFF_VIEWER$LOADING="DIFF_VIEWER$LOADING",E.DIFF_VIEWER$GETTING_LATEST_CHANGES="DIFF_VIEWER$GETTING_LATEST_CHANGES",E.DIFF_VIEWER$NOT_A_GIT_REPO="DIFF_VIEWER$NOT_A_GIT_REPO",E.DIFF_VIEWER$ASK_OH="DIFF_VIEWER$ASK_OH",E.DIFF_VIEWER$NO_CHANGES="DIFF_VIEWER$NO_CHANGES",E.DIFF_VIEWER$WAITING_FOR_RUNTIME="DIFF_VIEWER$WAITING_FOR_RUNTIME",E.SYSTEM_MESSAGE_MODAL$TITLE="SYSTEM_MESSAGE_MODAL$TITLE",E.SYSTEM_MESSAGE_MODAL$AGENT_CLASS="SYSTEM_MESSAGE_MODAL$AGENT_CLASS",E.SYSTEM_MESSAGE_MODAL$OPENHANDS_VERSION="SYSTEM_MESSAGE_MODAL$OPENHANDS_VERSION",E.SYSTEM_MESSAGE_MODAL$SYSTEM_MESSAGE_TAB="SYSTEM_MESSAGE_MODAL$SYSTEM_MESSAGE_TAB",E.SYSTEM_MESSAGE_MODAL$TOOLS_TAB="SYSTEM_MESSAGE_MODAL$TOOLS_TAB",E.SYSTEM_MESSAGE_MODAL$PARAMETERS="SYSTEM_MESSAGE_MODAL$PARAMETERS",E.SYSTEM_MESSAGE_MODAL$NO_TOOLS="SYSTEM_MESSAGE_MODAL$NO_TOOLS",E.TOS$ACCEPT_TERMS_OF_SERVICE="TOS$ACCEPT_TERMS_OF_SERVICE",E.TOS$ACCEPT_TERMS_DESCRIPTION="TOS$ACCEPT_TERMS_DESCRIPTION",E.TOS$CONTINUE="TOS$CONTINUE",E.TOS$ERROR_ACCEPTING="TOS$ERROR_ACCEPTING",E.TIPS$CUSTOMIZE_MICROAGENT="TIPS$CUSTOMIZE_MICROAGENT",E.CONVERSATION$SHOW_MICROAGENTS="CONVERSATION$SHOW_MICROAGENTS",E.CONVERSATION$NO_MICROAGENTS="CONVERSATION$NO_MICROAGENTS",E.CONVERSATION$FAILED_TO_FETCH_MICROAGENTS="CONVERSATION$FAILED_TO_FETCH_MICROAGENTS",E.MICROAGENTS_MODAL$TITLE="MICROAGENTS_MODAL$TITLE",E.MICROAGENTS_MODAL$WARNING="MICROAGENTS_MODAL$WARNING",E.MICROAGENTS_MODAL$TRIGGERS="MICROAGENTS_MODAL$TRIGGERS",E.MICROAGENTS_MODAL$INPUTS="MICROAGENTS_MODAL$INPUTS",E.MICROAGENTS_MODAL$TOOLS="MICROAGENTS_MODAL$TOOLS",E.MICROAGENTS_MODAL$CONTENT="MICROAGENTS_MODAL$CONTENT",E.MICROAGENTS_MODAL$NO_CONTENT="MICROAGENTS_MODAL$NO_CONTENT",E.MICROAGENTS_MODAL$FETCH_ERROR="MICROAGENTS_MODAL$FETCH_ERROR",E.TIPS$SETUP_SCRIPT="TIPS$SETUP_SCRIPT",E.TIPS$VSCODE_INSTANCE="TIPS$VSCODE_INSTANCE",E.TIPS$SAVE_WORK="TIPS$SAVE_WORK",E.TIPS$SPECIFY_FILES="TIPS$SPECIFY_FILES",E.TIPS$HEADLESS_MODE="TIPS$HEADLESS_MODE",E.TIPS$CLI_MODE="TIPS$CLI_MODE",E.TIPS$GITHUB_HOOK="TIPS$GITHUB_HOOK",E.TIPS$BLOG_SIGNUP="TIPS$BLOG_SIGNUP",E.TIPS$API_USAGE="TIPS$API_USAGE",E.TIPS$LEARN_MORE="TIPS$LEARN_MORE",E.TIPS$PROTIP="TIPS$PROTIP",E.FEEDBACK$SUBMITTING_LABEL="FEEDBACK$SUBMITTING_LABEL",E.FEEDBACK$SUBMITTING_MESSAGE="FEEDBACK$SUBMITTING_MESSAGE",E.SETTINGS$NAV_USER="SETTINGS$NAV_USER",E.SETTINGS$USER_TITLE="SETTINGS$USER_TITLE",E.SETTINGS$USER_EMAIL="SETTINGS$USER_EMAIL",E.SETTINGS$USER_EMAIL_LOADING="SETTINGS$USER_EMAIL_LOADING",E.SETTINGS$SAVE="SETTINGS$SAVE",E.SETTINGS$EMAIL_SAVED_SUCCESSFULLY="SETTINGS$EMAIL_SAVED_SUCCESSFULLY",E.SETTINGS$EMAIL_VERIFIED_SUCCESSFULLY="SETTINGS$EMAIL_VERIFIED_SUCCESSFULLY",E.SETTINGS$FAILED_TO_SAVE_EMAIL="SETTINGS$FAILED_TO_SAVE_EMAIL",E.SETTINGS$SENDING="SETTINGS$SENDING",E.SETTINGS$VERIFICATION_EMAIL_SENT="SETTINGS$VERIFICATION_EMAIL_SENT",E.SETTINGS$EMAIL_VERIFICATION_REQUIRED="SETTINGS$EMAIL_VERIFICATION_REQUIRED",E.SETTINGS$INVALID_EMAIL_FORMAT="SETTINGS$INVALID_EMAIL_FORMAT",E.SETTINGS$EMAIL_VERIFICATION_RESTRICTION_MESSAGE="SETTINGS$EMAIL_VERIFICATION_RESTRICTION_MESSAGE",E.SETTINGS$RESEND_VERIFICATION="SETTINGS$RESEND_VERIFICATION",E.SETTINGS$FAILED_TO_RESEND_VERIFICATION="SETTINGS$FAILED_TO_RESEND_VERIFICATION",E.FEEDBACK$RATE_AGENT_PERFORMANCE="FEEDBACK$RATE_AGENT_PERFORMANCE",E.FEEDBACK$SELECT_REASON="FEEDBACK$SELECT_REASON",E.FEEDBACK$SELECT_REASON_COUNTDOWN="FEEDBACK$SELECT_REASON_COUNTDOWN",E.FEEDBACK$REASON_MISUNDERSTOOD_INSTRUCTION="FEEDBACK$REASON_MISUNDERSTOOD_INSTRUCTION",E.FEEDBACK$REASON_FORGOT_CONTEXT="FEEDBACK$REASON_FORGOT_CONTEXT",E.FEEDBACK$REASON_UNNECESSARY_CHANGES="FEEDBACK$REASON_UNNECESSARY_CHANGES",E.FEEDBACK$REASON_SHOULD_ASK_FIRST="FEEDBACK$REASON_SHOULD_ASK_FIRST",E.FEEDBACK$REASON_OTHER="FEEDBACK$REASON_OTHER",E.FEEDBACK$THANK_YOU_FOR_FEEDBACK="FEEDBACK$THANK_YOU_FOR_FEEDBACK",E.FEEDBACK$FAILED_TO_SUBMIT="FEEDBACK$FAILED_TO_SUBMIT",E.HOME$ADD_GITHUB_REPOS="HOME$ADD_GITHUB_REPOS",E.REPOSITORY$SELECT_BRANCH="REPOSITORY$SELECT_BRANCH",E.REPOSITORY$SELECT_REPO="REPOSITORY$SELECT_REPO",E.TASKS$SUGGESTED_TASKS="TASKS$SUGGESTED_TASKS",E.TASKS$NO_TASKS_AVAILABLE="TASKS$NO_TASKS_AVAILABLE",E.TASKS$TASK_SUGGESTIONS_INFO="TASKS$TASK_SUGGESTIONS_INFO",E.TASKS$TASK_SUGGESTIONS_TOOLTIP="TASKS$TASK_SUGGESTIONS_TOOLTIP",E.PAYMENT$SPECIFY_AMOUNT_USD="PAYMENT$SPECIFY_AMOUNT_USD",E.GIT$BITBUCKET_TOKEN_HELP_LINK="GIT$BITBUCKET_TOKEN_HELP_LINK",E.GIT$BITBUCKET_TOKEN_SEE_MORE_LINK="GIT$BITBUCKET_TOKEN_SEE_MORE_LINK",E.GIT$GITHUB_TOKEN_HELP_LINK="GIT$GITHUB_TOKEN_HELP_LINK",E.GIT$GITHUB_TOKEN_SEE_MORE_LINK="GIT$GITHUB_TOKEN_SEE_MORE_LINK",E.GIT$GITLAB_TOKEN_HELP_LINK="GIT$GITLAB_TOKEN_HELP_LINK",E.GIT$GITLAB_TOKEN_SEE_MORE_LINK="GIT$GITLAB_TOKEN_SEE_MORE_LINK",E.SECRETS$SECRET_ALREADY_EXISTS="SECRETS$SECRET_ALREADY_EXISTS",E.SECRETS$API_KEY_EXAMPLE="SECRETS$API_KEY_EXAMPLE",E.MODEL$CUSTOM_MODEL="MODEL$CUSTOM_MODEL",E.SECURITY$SELECT_RISK_SEVERITY="SECURITY$SELECT_RISK_SEVERITY",E.SECURITY$DONT_ASK_CONFIRMATION="SECURITY$DONT_ASK_CONFIRMATION",E.SETTINGS$MAXIMUM_BUDGET_USD="SETTINGS$MAXIMUM_BUDGET_USD",E.GIT$DISCONNECT_TOKENS="GIT$DISCONNECT_TOKENS",E.API$TAVILY_KEY_EXAMPLE="API$TAVILY_KEY_EXAMPLE",E.API$TVLY_KEY_EXAMPLE="API$TVLY_KEY_EXAMPLE",E.SECRETS$CONNECT_GIT_PROVIDER="SECRETS$CONNECT_GIT_PROVIDER",E.SETTINGS$OPENHANDS_API_KEYS="SETTINGS$OPENHANDS_API_KEYS",E.CONVERSATION$BUDGET_USAGE_FORMAT="CONVERSATION$BUDGET_USAGE_FORMAT",E.CONVERSATION$CACHE_HIT="CONVERSATION$CACHE_HIT",E.CONVERSATION$CACHE_WRITE="CONVERSATION$CACHE_WRITE",E.BUTTON$CONFIRM="BUTTON$CONFIRM",E.FORM$VALUE="FORM$VALUE",E.FORM$DESCRIPTION="FORM$DESCRIPTION",E.COMMON$OPTIONAL="COMMON$OPTIONAL",E.BROWSER$SERVER_MESSAGE="BROWSER$SERVER_MESSAGE",E.API$NO_KEY_AVAILABLE="API$NO_KEY_AVAILABLE",E.MICROAGENT_MANAGEMENT$TITLE="MICROAGENT_MANAGEMENT$TITLE",E.MICROAGENT_MANAGEMENT$DESCRIPTION="MICROAGENT_MANAGEMENT$DESCRIPTION",E.MICROAGENT_MANAGEMENT$USE_MICROAGENTS="MICROAGENT_MANAGEMENT$USE_MICROAGENTS",E.AUTH$BY_SIGNING_UP_YOU_AGREE_TO_OUR="AUTH$BY_SIGNING_UP_YOU_AGREE_TO_OUR",E.AUTH$NO_PROVIDERS_CONFIGURED="AUTH$NO_PROVIDERS_CONFIGURED",E.COMMON$TERMS_OF_SERVICE="COMMON$TERMS_OF_SERVICE",E.COMMON$AND="COMMON$AND",E.COMMON$PRIVACY_POLICY="COMMON$PRIVACY_POLICY",E.COMMON$PERSONAL="COMMON$PERSONAL",E.COMMON$REPOSITORIES="COMMON$REPOSITORIES",E.COMMON$ORGANIZATIONS="COMMON$ORGANIZATIONS",E.COMMON$ADD_MICROAGENT="COMMON$ADD_MICROAGENT",E.COMMON$CREATED_ON="COMMON$CREATED_ON",E.MICROAGENT_MANAGEMENT$LEARN_THIS_REPO="MICROAGENT_MANAGEMENT$LEARN_THIS_REPO",E.MICROAGENT_MANAGEMENT$READY_TO_ADD_MICROAGENT="MICROAGENT_MANAGEMENT$READY_TO_ADD_MICROAGENT",E.MICROAGENT_MANAGEMENT$OPENHANDS_CAN_LEARN_ABOUT_REPOSITORIES="MICROAGENT_MANAGEMENT$OPENHANDS_CAN_LEARN_ABOUT_REPOSITORIES",E.MICROAGENT_MANAGEMENT$ADD_A_MICROAGENT_TO="MICROAGENT_MANAGEMENT$ADD_A_MICROAGENT_TO",E.MICROAGENT_MANAGEMENT$ADD_A_MICROAGENT="MICROAGENT_MANAGEMENT$ADD_A_MICROAGENT",E.MICROAGENT_MANAGEMENT$UPDATE_MICROAGENT="MICROAGENT_MANAGEMENT$UPDATE_MICROAGENT",E.MICROAGENT_MANAGEMENT$ADD_MICROAGENT_MODAL_DESCRIPTION="MICROAGENT_MANAGEMENT$ADD_MICROAGENT_MODAL_DESCRIPTION",E.MICROAGENT_MANAGEMENT$WHAT_TO_DO="MICROAGENT_MANAGEMENT$WHAT_TO_DO",E.MICROAGENT_MANAGEMENT$DESCRIBE_WHAT_TO_DO="MICROAGENT_MANAGEMENT$DESCRIBE_WHAT_TO_DO",E.MICROAGENT_MANAGEMENT$ADD_TRIGGERS="MICROAGENT_MANAGEMENT$ADD_TRIGGERS",E.MICROAGENT_MANAGEMENT$HELP_TEXT_DESCRIBING_VALID_TRIGGERS="MICROAGENT_MANAGEMENT$HELP_TEXT_DESCRIBING_VALID_TRIGGERS",E.COMMON$FOR_EXAMPLE="COMMON$FOR_EXAMPLE",E.COMMON$TEST_DB_MIGRATION="COMMON$TEST_DB_MIGRATION",E.COMMON$RUN_TEST="COMMON$RUN_TEST",E.COMMON$RUN_APP="COMMON$RUN_APP",E.COMMON$LEARN_FILE_STRUCTURE="COMMON$LEARN_FILE_STRUCTURE",E.MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_USER_LEVEL_MICROAGENTS="MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_USER_LEVEL_MICROAGENTS",E.MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_MICROAGENTS="MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_MICROAGENTS",E.MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_ORGANIZATION_LEVEL_MICROAGENTS="MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_ORGANIZATION_LEVEL_MICROAGENTS",E.COMMON$SEARCH_REPOSITORIES="COMMON$SEARCH_REPOSITORIES",E.COMMON$READY_FOR_REVIEW="COMMON$READY_FOR_REVIEW",E.COMMON$COMPLETED="COMMON$COMPLETED",E.COMMON$COMPLETED_PARTIALLY="COMMON$COMPLETED_PARTIALLY",E.COMMON$STOPPED="COMMON$STOPPED",E.COMMON$WORKING_ON_IT="COMMON$WORKING_ON_IT",E.MICROAGENT_MANAGEMENT$WE_ARE_WORKING_ON_IT="MICROAGENT_MANAGEMENT$WE_ARE_WORKING_ON_IT",E.MICROAGENT_MANAGEMENT$YOUR_MICROAGENT_IS_READY="MICROAGENT_MANAGEMENT$YOUR_MICROAGENT_IS_READY",E.COMMON$REVIEW_PR_IN="COMMON$REVIEW_PR_IN",E.COMMON$EDIT_IN="COMMON$EDIT_IN",E.COMMON$LEARN="COMMON$LEARN",E.COMMON$LEARN_SOMETHING_NEW="COMMON$LEARN_SOMETHING_NEW",E.COMMON$STARTING="COMMON$STARTING",E.MICROAGENT_MANAGEMENT$ERROR="MICROAGENT_MANAGEMENT$ERROR",E.MICROAGENT_MANAGEMENT$CONVERSATION_STOPPED="MICROAGENT_MANAGEMENT$CONVERSATION_STOPPED",E.MICROAGENT_MANAGEMENT$LEARN_THIS_REPO_MODAL_TITLE="MICROAGENT_MANAGEMENT$LEARN_THIS_REPO_MODAL_TITLE",E.MICROAGENT_MANAGEMENT$LEARN_THIS_REPO_MODAL_DESCRIPTION="MICROAGENT_MANAGEMENT$LEARN_THIS_REPO_MODAL_DESCRIPTION",E.MICROAGENT_MANAGEMENT$WHAT_YOU_WOULD_LIKE_TO_KNOW_ABOUT_THIS_REPO="MICROAGENT_MANAGEMENT$WHAT_YOU_WOULD_LIKE_TO_KNOW_ABOUT_THIS_REPO",E.MICROAGENT_MANAGEMENT$DESCRIBE_WHAT_TO_KNOW_ABOUT_THIS_REPO="MICROAGENT_MANAGEMENT$DESCRIBE_WHAT_TO_KNOW_ABOUT_THIS_REPO",E.MICROAGENT_MANAGEMENT$UPDATE_MICROAGENT_MODAL_DESCRIPTION="MICROAGENT_MANAGEMENT$UPDATE_MICROAGENT_MODAL_DESCRIPTION",E))(T||{});export{T as I};
