import{u as M}from"./useQuery-Cu2nkJ8V.js";import{O as g}from"./open-hands-Ce72Fmtl.js";import{R as p,j as i}from"./chunk-C37GKA54-CBbYr_fP.js";import{I as d}from"./declaration-xyc84-tJ.js";import{m as O}from"./map-provider-g8SgAMsv.js";import{u as N}from"./useTranslation-BG59QWH_.js";import{a as S,l as m}from"./chunk-S6H5EOGR-Bwn62IP6.js";function v(s){return null}v.getCollectionNode=function*(l){let{children:t,title:a,items:n}=l;yield{type:"section",props:l,hasChildNodes:!0,rendered:a,"aria-label":l["aria-label"],*childNodes(){if(typeof t=="function"){if(!n)throw new Error("props.children was a function but props.items is missing");for(let r of n)yield{type:"item",value:r,renderer:t}}else{let r=[];p.Children.forEach(t,c=>{r.push({type:"item",element:c})}),yield*r}}}};let j=v;var C=j,f=C;const $=async()=>({models:await g.getModels(),agents:await g.getAgents(),securityAnalyzers:await g.getSecurityAnalyzers()}),Q=()=>M({queryKey:["ai-config-options"],queryFn:$,staleTime:1e3*60*5,gcTime:1e3*60*15}),w=s=>!Number.isNaN(Number(s)),L=["openhands","anthropic","openai","mistral"],P=["o3-mini-2025-01-31","o3-2025-04-16","o3","o4-mini-2025-04-16","claude-3-5-sonnet-20241022","claude-3-7-sonnet-20250219","claude-sonnet-4-20250514","claude-opus-4-20250514","gemini-2.5-pro","o4-mini","deepseek-chat","devstral-small-2505","devstral-small-2507","devstral-medium-2507","kimi-k2-0711-preview","qwen3-coder-480b"],A=["gpt-4o","gpt-4o-mini","gpt-4.1","gpt-4.1-2025-04-14","o3","o3-2025-04-16","o4-mini","o4-mini-2025-04-16","codex-mini-latest"],V=["claude-3-5-sonnet-20240620","claude-3-5-sonnet-20241022","claude-3-5-haiku-20241022","claude-3-7-sonnet-20250219","claude-sonnet-4-20250514","claude-opus-4-20250514"],T=["devstral-small-2507","devstral-medium-2507","devstral-small-2505"],D=["claude-sonnet-4-20250514","claude-opus-4-20250514","gemini-2.5-pro","o3","o4-mini","devstral-small-2507","devstral-medium-2507","devstral-small-2505","kimi-k2-0711-preview","qwen3-coder-480b"],U="openhands/claude-sonnet-4-20250514",k=s=>s[1]&&s[1][0]&&w(s[1][0]),I=s=>{let l="/",t=s.split(l);if(t.length===1&&(l=".",t=s.split(l),k(t)&&(t=[t.join(l)])),t.length===1)return A.includes(t[0])?{provider:"openai",model:t[0],separator:"/"}:V.includes(t[0])?{provider:"anthropic",model:t[0],separator:"/"}:T.includes(t[0])?{provider:"mistral",model:t[0],separator:"/"}:D.includes(t[0])?{provider:"openhands",model:t[0],separator:"/"}:{provider:"",model:s,separator:""};const[a,...n]=t;return{provider:a,model:n.join(l),separator:l}},G=s=>{const l={};return s.forEach(t=>{const{separator:a,provider:n,model:r}=I(t);if(n==="anthropic"&&a===".")return;const c=n||"other";l[c]||(l[c]={separator:a,models:[]}),l[c].models.push(r)}),l};function J({isDisabled:s,models:l,currentModel:t,onChange:a}){const[,n]=p.useState(null),[r,c]=p.useState(null),[b,E]=p.useState(null),h=()=>r==="openhands"?D:P;p.useEffect(()=>{if(t){const{provider:e,model:u}=I(t);n(t),c(e),E(u)}},[t]);const R=e=>{c(e),E(null);const u=l[e]?.separator||"";n(e+u)},y=e=>{const u=l[r||""]?.separator||"";let x=r+u+e;r==="openai"&&(x=e),n(x),E(e),a?.(x)},_=()=>{c(null),n(null)},{t:o}=N();return i.jsxs("div",{className:"flex flex-col md:flex-row w-[full] max-w-[680px] justify-between gap-4 md:gap-[46px]",children:[i.jsxs("fieldset",{className:"flex flex-col gap-2.5 w-full",children:[i.jsx("label",{className:"text-sm",children:o(d.LLM$PROVIDER)}),i.jsxs(S,{"data-testid":"llm-provider-input",isRequired:!0,isVirtualized:!1,name:"llm-provider-input",isDisabled:s,"aria-label":o(d.LLM$PROVIDER),placeholder:o(d.LLM$SELECT_PROVIDER_PLACEHOLDER),isClearable:!1,onSelectionChange:e=>{e?.toString()&&R(e.toString())},onInputChange:e=>!e&&_(),defaultSelectedKey:r??void 0,selectedKey:r,classNames:{popoverContent:"bg-tertiary rounded-xl border border-[#717888]"},inputProps:{classNames:{inputWrapper:"bg-tertiary border border-[#717888] h-10 w-full rounded-sm p-2 placeholder:italic"}},children:[i.jsx(f,{title:o(d.MODEL_SELECTOR$VERIFIED),children:L.filter(e=>l[e]).map(e=>i.jsx(m,{"data-testid":`provider-item-${e}`,children:O(e)},e))}),Object.keys(l).some(e=>!L.includes(e))?i.jsx(f,{title:o(d.MODEL_SELECTOR$OTHERS),children:Object.keys(l).filter(e=>!L.includes(e)).map(e=>i.jsx(m,{children:O(e)},e))}):null]})]}),i.jsxs("fieldset",{className:"flex flex-col gap-2.5 w-full",children:[i.jsx("label",{className:"text-sm",children:o(d.LLM$MODEL)}),i.jsxs(S,{"data-testid":"llm-model-input",isRequired:!0,isVirtualized:!1,name:"llm-model-input","aria-label":o(d.LLM$MODEL),placeholder:o(d.LLM$SELECT_MODEL_PLACEHOLDER),isClearable:!1,onSelectionChange:e=>{e?.toString()&&y(e.toString())},isDisabled:s||!r,selectedKey:b,defaultSelectedKey:b??void 0,classNames:{popoverContent:"bg-tertiary rounded-xl border border-[#717888]"},inputProps:{classNames:{inputWrapper:"bg-tertiary border border-[#717888] h-10 w-full rounded-sm p-2 placeholder:italic"}},children:[i.jsx(f,{title:o(d.MODEL_SELECTOR$VERIFIED),children:h().filter(e=>l[r||""]?.models?.includes(e)).map(e=>i.jsx(m,{children:e},e))}),l[r||""]?.models?.some(e=>!h().includes(e))?i.jsx(f,{title:o(d.MODEL_SELECTOR$OTHERS),children:l[r||""]?.models.filter(e=>!h().includes(e)).map(e=>i.jsx(m,{"data-testid":`model-item-${e}`,children:e},e))}):null]})]})]})}function X({testId:s,text:l,linkText:t,href:a,suffix:n}){return i.jsxs("p",{"data-testid":s,className:"text-xs",children:[l," ",i.jsx("a",{href:a,rel:"noreferrer noopener",target:"_blank",className:"underline underline-offset-2",children:t}),n&&` ${n}`]})}export{U as D,X as H,J as M,I as e,G as o,Q as u};
