import{w as m,a,j as t,N as l,O as g}from"./chunk-C37GKA54-CBbYr_fP.js";import{S as p}from"./settings-BJuJgO6o.js";import{c as N}from"./utils-KsbccAr1.js";import{u as T}from"./use-config-jdwF3W4-.js";import{I as x}from"./declaration-xyc84-tJ.js";import{O as d}from"./open-hands-Ce72Fmtl.js";import{q as o}from"./query-client-config-CJn-5u6A.js";import{u as f}from"./useTranslation-BG59QWH_.js";import"./useQuery-Cu2nkJ8V.js";import"./open-hands-axios-CtirLpss.js";import"./i18next-CO45VQzB.js";import"./retrieve-axios-error-message-CYr77e_f.js";import"./custom-toast-handlers-CR9P-jKI.js";import"./index-cxP66Ws3.js";import"./mutation-B9dSlWD-.js";import"./i18nInstance-DBIXdvxg.js";const E=["/settings/user","/settings/billing","/settings/credits","/settings/api-keys"],I=[{to:"/settings/user",text:"SETTINGS$NAV_USER"},{to:"/settings/integrations",text:"SETTINGS$NAV_INTEGRATIONS"},{to:"/settings/app",text:"SETTINGS$NAV_APPLICATION"},{to:"/settings/billing",text:"SETTINGS$NAV_CREDITS"},{to:"/settings/secrets",text:"SETTINGS$NAV_SECRETS"},{to:"/settings/api-keys",text:"SETTINGS$NAV_API_KEYS"}],u=[{to:"/settings",text:"SETTINGS$NAV_LLM"},{to:"/settings/mcp",text:"SETTINGS$NAV_MCP"},{to:"/settings/integrations",text:"SETTINGS$NAV_INTEGRATIONS"},{to:"/settings/app",text:"SETTINGS$NAV_APPLICATION"},{to:"/settings/secrets",text:"SETTINGS$NAV_SECRETS"}],D=async({request:r})=>{const i=new URL(r.url),{pathname:n}=i;let s=o.getQueryData(["config"]);s||(s=await d.getConfig(),o.setQueryData(["config"],s));const e=s?.APP_MODE==="saas";return e&&n==="/settings"?a("/settings/user"):!e&&E.includes(n)?a("/settings"):null};function A(){const{t:r}=f(),{data:i}=T(),s=i?.APP_MODE==="saas"?I:u;return t.jsxs("main",{"data-testid":"settings-screen",className:"bg-base-secondary border border-tertiary h-full rounded-xl flex flex-col",children:[t.jsxs("header",{className:"px-3 py-1.5 border-b border-b-tertiary flex items-center gap-2",children:[t.jsx(p,{width:16,height:16}),t.jsx("h1",{className:"text-sm leading-6",children:r(x.SETTINGS$TITLE)})]}),t.jsx("nav",{"data-testid":"settings-navbar",className:"flex items-end gap-6 px-9 border-b border-tertiary",children:s.map(({to:e,text:S})=>t.jsx(l,{end:!0,to:e,className:({isActive:c})=>N("border-b-2 border-transparent py-2.5 px-4 min-w-[40px] flex items-center justify-center",c&&"border-primary"),children:t.jsx("span",{className:"text-[#F9FBFE] text-sm",children:r(S)})},e))}),t.jsx("div",{className:"flex flex-col grow overflow-auto",children:t.jsx(g,{})})]})}const k=m(A);export{D as clientLoader,k as default};
