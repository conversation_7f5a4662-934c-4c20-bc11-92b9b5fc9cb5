import{r as a,j as s}from"./chunk-C37GKA54-CBbYr_fP.js";import{c as r}from"./utils-KsbccAr1.js";const n=e=>a.createElement("svg",{width:66,height:66,viewBox:"0 0 66 66",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},a.createElement("path",{d:"M63 33C63 16.4315 49.5685 3 33 3C16.4315 3 3 16.4315 3 33C3 49.5685 16.4315 63 33 63",stroke:"#007AFF",strokeWidth:6,strokeLinecap:"round"}));function l({size:e}){const t=e==="small"?"w-[25px] h-[25px]":"w-[50px] h-[50px]";return s.jsxs("div",{"data-testid":"loading-spinner",className:r("relative",t),children:[s.jsx("div",{className:r("rounded-full border-4 border-[#525252] absolute",t)}),s.jsx(n,{className:r("absolute animate-spin",t)})]})}export{l as L};
