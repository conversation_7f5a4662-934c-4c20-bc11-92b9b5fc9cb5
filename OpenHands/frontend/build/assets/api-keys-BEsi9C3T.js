import{j as e,r as m,w as G}from"./chunk-C37GKA54-CBbYr_fP.js";import{b as Y,c as F,d as B,a as q}from"./index-DvLMSsrd.js";import{I as a}from"./declaration-xyc84-tJ.js";import{u as j,B as c}from"./brand-button-3Z8FN4qR.js";import{L as N}from"./loading-spinner-D6qcLhqr.js";import{a as y,d as E}from"./custom-toast-handlers-CR9P-jKI.js";import{S as U}from"./settings-input-i4i_IemM.js";import{M as z}from"./modal-backdrop-ve4Sk5I2.js";import{o as p,u as A}from"./open-hands-axios-CtirLpss.js";import{u as g}from"./useQuery-Cu2nkJ8V.js";import{u as P}from"./use-config-jdwF3W4-.js";import{u}from"./useTranslation-BG59QWH_.js";import{T as Q}from"./Trans-CI4prNur.js";import"./iconBase-2PDVWRGH.js";import"./mutation-B9dSlWD-.js";import"./utils-KsbccAr1.js";import"./index-cxP66Ws3.js";import"./optional-tag-e1gRgM9y.js";import"./open-hands-Ce72Fmtl.js";import"./i18nInstance-DBIXdvxg.js";function S({isOpen:n,title:t,width:l="500px",children:s,footer:r}){return n?e.jsx(z,{children:e.jsxs("div",{className:"bg-base-secondary p-6 rounded-xl flex flex-col gap-4 border border-tertiary",style:{width:l},children:[e.jsx("h3",{className:"text-xl font-bold",children:t}),s,e.jsx("div",{className:"w-full flex gap-2 mt-2",children:r})]})}):null}class I{static async getApiKeys(){const{data:t}=await p.get("/api/keys");return Array.isArray(t)?t:[]}static async createApiKey(t){const{data:l}=await p.post("/api/keys",{name:t});return l}static async deleteApiKey(t){await p.delete(`/api/keys/${t}`)}}const K="api-keys";function H(){const{data:n}=P();return g({queryKey:[K],enabled:n?.APP_MODE==="saas",queryFn:async()=>{const t=await I.getApiKeys();return Array.isArray(t)?t:[]},staleTime:1e3*60*5,gcTime:1e3*60*15})}function V(){const n=A();return j({mutationFn:async t=>I.createApiKey(t),onSuccess:()=>{n.invalidateQueries({queryKey:[K]})}})}function W({isOpen:n,onClose:t,onKeyCreated:l}){const{t:s}=u(),[r,i]=m.useState(""),o=V(),h=async()=>{if(!r.trim()){y(s(a.ERROR$REQUIRED_FIELD));return}try{const d=await o.mutateAsync(r);l(d),E(s(a.SETTINGS$API_KEY_CREATED)),i("")}catch{y(s(a.ERROR$GENERIC))}},x=()=>{i(""),t()},f=e.jsxs(e.Fragment,{children:[e.jsx(c,{type:"button",variant:"primary",className:"grow",onClick:h,isDisabled:o.isPending||!r.trim(),children:o.isPending?e.jsx(N,{size:"small"}):s(a.BUTTON$CREATE)}),e.jsx(c,{type:"button",variant:"secondary",className:"grow",onClick:x,isDisabled:o.isPending,children:s(a.BUTTON$CANCEL)})]});return e.jsx(S,{isOpen:n,title:s(a.SETTINGS$CREATE_API_KEY),footer:f,children:e.jsxs("div",{"data-testid":"create-api-key-modal",children:[e.jsx("p",{className:"text-sm text-gray-300",children:s(a.SETTINGS$CREATE_API_KEY_DESCRIPTION)}),e.jsx(U,{testId:"api-key-name-input",label:s(a.SETTINGS$NAME),placeholder:s(a.SETTINGS$API_KEY_NAME_PLACEHOLDER),value:r,onChange:d=>i(d),className:"w-full mt-4",type:"text"})]})})}function J(){const n=A();return j({mutationFn:async t=>{await I.deleteApiKey(t)},onSuccess:()=>{n.invalidateQueries({queryKey:[K]})}})}function X({isOpen:n,keyToDelete:t,onClose:l}){const{t:s}=u(),r=J(),i=async()=>{if(t)try{await r.mutateAsync(t.id),E(s(a.SETTINGS$API_KEY_DELETED)),l()}catch{y(s(a.ERROR$GENERIC))}};if(!t)return null;const o=e.jsxs(e.Fragment,{children:[e.jsx(c,{type:"button",variant:"danger",className:"grow",onClick:i,isDisabled:r.isPending,children:r.isPending?e.jsx(N,{size:"small"}):s(a.BUTTON$DELETE)}),e.jsx(c,{type:"button",variant:"secondary",className:"grow",onClick:l,isDisabled:r.isPending,children:s(a.BUTTON$CANCEL)})]});return e.jsx(S,{isOpen:n&&!!t,title:s(a.SETTINGS$DELETE_API_KEY),footer:o,children:e.jsx("div",{"data-testid":"delete-api-key-modal",children:e.jsx("p",{className:"text-sm break-all",children:s(a.SETTINGS$DELETE_API_KEY_CONFIRMATION,{name:t.name})})})})}function Z({isOpen:n,newlyCreatedKey:t,onClose:l}){const{t:s}=u(),r=()=>{t&&(navigator.clipboard.writeText(t.key),E(s(a.SETTINGS$API_KEY_COPIED)))};if(!t)return null;const i=e.jsxs(e.Fragment,{children:[e.jsx(c,{type:"button",variant:"primary",onClick:r,children:s(a.BUTTON$COPY_TO_CLIPBOARD)}),e.jsx(c,{type:"button",variant:"secondary",onClick:l,children:s(a.BUTTON$CLOSE)})]});return e.jsx(S,{isOpen:n&&!!t,title:s(a.SETTINGS$API_KEY_CREATED),width:"600px",footer:i,children:e.jsxs("div",{"data-testid":"new-api-key-modal",children:[e.jsx("p",{className:"text-sm",children:s(a.SETTINGS$API_KEY_WARNING)}),e.jsx("div",{className:"bg-base-tertiary p-4 rounded-md font-mono text-sm break-all mt-4",children:t.key})]})})}const w="llm-api-key";function ee(){const{data:n}=P();return g({queryKey:[w],enabled:n?.APP_MODE==="saas",queryFn:async()=>{const{data:t}=await p.get("/api/keys/llm/byor");return t},staleTime:1e3*60*5,gcTime:1e3*60*15})}function te(){const n=A();return j({mutationFn:async()=>{const{data:t}=await p.post("/api/keys/llm/byor/refresh");return t},onSuccess:()=>{n.invalidateQueries({queryKey:[w]})}})}function se({llmApiKey:n,isLoadingLlmKey:t,refreshLlmApiKey:l}){const{t:s}=u(),[r,i]=m.useState(!1),o=()=>{l.mutate(void 0,{onSuccess:()=>{E(s(a.SETTINGS$API_KEY_REFRESHED,{defaultValue:"API key refreshed successfully"}))},onError:()=>{y(s(a.ERROR$GENERIC))}})};return t||!n?null:e.jsxs("div",{className:"border-b border-gray-200 pb-6 mb-6 flex flex-col gap-6",children:[e.jsx("h3",{className:"text-xl font-medium text-white",children:s(a.SETTINGS$LLM_API_KEY)}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(c,{type:"button",variant:"primary",onClick:o,isDisabled:l.isPending,children:l.isPending?e.jsx(N,{size:"small"}):s(a.SETTINGS$REFRESH_LLM_API_KEY)})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-300 mb-2",children:s(a.SETTINGS$LLM_API_KEY_DESCRIPTION)}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("div",{className:"flex-1 bg-base-tertiary rounded-md py-2 flex items-center",children:[e.jsx("div",{className:"flex-1",children:n.key?e.jsx("div",{className:"flex items-center",children:r?e.jsx("span",{className:"text-white font-mono",children:n.key}):e.jsx("span",{className:"text-white",children:"•".repeat(20)})}):e.jsx("span",{className:"text-white",children:s(a.API$NO_KEY_AVAILABLE)})}),e.jsxs("div",{className:"flex items-center",children:[n.key&&e.jsx("button",{type:"button",className:"text-white hover:text-gray-300 mr-2","aria-label":r?"Hide API key":"Show API key",title:r?"Hide API key":"Show API key",onClick:()=>i(!r),children:r?e.jsx(Y,{size:20}):e.jsx(F,{size:20})}),e.jsx("button",{type:"button",className:"text-white hover:text-gray-300 mr-2","aria-label":"Copy API key",title:"Copy API key",onClick:()=>{n.key&&(navigator.clipboard.writeText(n.key),E(s(a.SETTINGS$API_KEY_COPIED)))},children:e.jsx(B,{size:20})})]})]})})]})]})}function ae({apiKeys:n,isLoading:t,onDeleteKey:l}){const{t:s}=u(),r=i=>i?new Date(i).toLocaleString():"Never";return t?e.jsx("div",{className:"flex justify-center p-4",children:e.jsx(N,{size:"large"})}):!Array.isArray(n)||n.length===0?null:e.jsx("div",{className:"border border-tertiary rounded-md overflow-hidden",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-base-tertiary",children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-left p-3 text-sm font-medium",children:s(a.SETTINGS$NAME)}),e.jsx("th",{className:"text-left p-3 text-sm font-medium",children:s(a.SETTINGS$CREATED_AT)}),e.jsx("th",{className:"text-left p-3 text-sm font-medium",children:s(a.SETTINGS$LAST_USED)}),e.jsx("th",{className:"text-right p-3 text-sm font-medium",children:s(a.SETTINGS$ACTIONS)})]})}),e.jsx("tbody",{children:n.map(i=>e.jsxs("tr",{className:"border-t border-tertiary",children:[e.jsx("td",{className:"p-3 text-sm truncate max-w-[160px]",title:i.name,children:i.name}),e.jsx("td",{className:"p-3 text-sm",children:r(i.created_at)}),e.jsx("td",{className:"p-3 text-sm",children:r(i.last_used_at)}),e.jsx("td",{className:"p-3 text-right",children:e.jsx("button",{type:"button",onClick:()=>l(i),"aria-label":`Delete ${i.name}`,className:"cursor-pointer",children:e.jsx(q,{size:16})})})]},i.id))})]})})}function ne(){const{t:n}=u(),{data:t=[],isLoading:l,error:s}=H(),{data:r,isLoading:i}=ee(),o=te(),[h,x]=m.useState(!1),[f,d]=m.useState(!1),[R,b]=m.useState(null),[v,C]=m.useState(null),[O,_]=m.useState(!1);s&&y(n(a.ERROR$GENERIC));const $=T=>{C(T),x(!1),_(!0)},D=()=>{x(!1)},L=()=>{d(!1),b(null)},k=()=>{_(!1),C(null)},M=T=>{b(T),d(!0)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx(se,{llmApiKey:r,isLoadingLlmKey:i,refreshLlmApiKey:o}),e.jsx("h3",{className:"text-xl font-medium text-white",children:n(a.SETTINGS$OPENHANDS_API_KEYS)}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(c,{type:"button",variant:"primary",onClick:()=>x(!0),children:n(a.SETTINGS$CREATE_API_KEY)})}),e.jsx("p",{className:"text-sm text-gray-300",children:e.jsx(Q,{i18nKey:a.SETTINGS$API_KEYS_DESCRIPTION,components:{a:e.jsx("a",{href:"https://docs.all-hands.dev/usage/cloud/cloud-api",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:underline",children:"API documentation"})}})}),e.jsx(ae,{apiKeys:t,isLoading:l,onDeleteKey:M})]}),e.jsx(W,{isOpen:h,onClose:D,onKeyCreated:$}),e.jsx(X,{isOpen:f,keyToDelete:R,onClose:L}),e.jsx(Z,{isOpen:O,newlyCreatedKey:v,onClose:k})]})}function re(){return e.jsx("div",{className:"flex flex-col grow overflow-auto p-9",children:e.jsx(ne,{})})}const be=G(re);export{be as default};
