import{r as t,j as s}from"./chunk-C37GKA54-CBbYr_fP.js";import{c as n}from"./utils-KsbccAr1.js";const o=e=>t.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},t.createElement("path",{d:"M9.9509 15.588H10.8349L16.5125 9.91042L15.6285 9.0264L10.3991 14.2681L8.00867 11.8777L7.12465 12.7617L9.9509 15.588Z",fill:"currentColor"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.8521 3.29895C14.8443 3.42346 16.712 4.41954 18.0816 5.78916C19.7002 7.53231 20.5718 9.64898 20.5718 12.1392C20.5718 14.1314 19.8247 15.999 18.5796 17.6177C17.3345 19.1118 15.5914 20.2324 13.5992 20.6059C11.607 20.9794 9.61486 20.7304 7.87171 19.7343C6.12856 18.7382 4.75895 17.2441 4.01189 15.3765C3.26482 13.5088 3.14031 11.3921 3.76286 9.52447C4.38542 7.53231 5.50601 5.91367 7.24916 4.79308C8.86779 3.67248 10.86 3.17444 12.8521 3.29895ZM13.4747 19.3608C15.0933 18.9873 16.5874 18.1157 17.708 16.7461C18.7041 15.3765 19.3267 13.7578 19.2022 12.0147C19.2022 10.0225 18.4551 8.03035 17.0855 6.66073C15.8404 5.41563 14.3463 4.66857 12.6031 4.54405C10.9845 4.41954 9.24132 4.79308 7.87171 5.78916C6.50209 6.78524 5.50601 8.15486 5.00797 9.89801C4.50993 11.5166 4.50993 13.2598 5.25699 14.8784C6.00405 16.4971 7.12465 17.7422 8.61877 18.6137C10.1129 19.4853 11.856 19.7343 13.4747 19.3608Z",fill:"currentColor"}));function a({testId:e,isSet:r}){return s.jsx("span",{"data-testid":e||(r?"set-indicator":"unset-indicator"),children:s.jsx(o,{className:n(r?"text-success":"text-danger")})})}export{a as K};
