const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-D_FgxOY2.js","assets/features-animation-CM7oex5Y.js","assets/chunk-S6H5EOGR-Bwn62IP6.js","assets/chunk-C37GKA54-CBbYr_fP.js","assets/utils-KsbccAr1.js","assets/index-yKbcr7Pf.js","assets/preload-helper-BXl3LOEh.js"])))=>i.map(i=>d[i]);
import{r as y,j as r,R as Ee,w as wn,a as jn}from"./chunk-C37GKA54-CBbYr_fP.js";import{q as Fe}from"./query-client-config-CJn-5u6A.js";import{O as ht}from"./open-hands-Ce72Fmtl.js";import{u as K,a as ge}from"./react-redux-B5osdedR.js";import{I as T}from"./declaration-xyc84-tJ.js";import{D as Be,a as Tt,M as Ts}from"./constants-DCYeMzG2.js";import{u as q}from"./useTranslation-BG59QWH_.js";import{i as Ps,j as ft,k as Es,l as Ms,m as In,n as Dn,o as On,p as As}from"./store-Bya9Reqe.js";import{c as pe,b as _n,g as He,d as Ln,e as $n,f as kn}from"./utils-KsbccAr1.js";import{u as Ns}from"./useQuery-Cu2nkJ8V.js";import{Y as Bn,Z as Vn,_ as at,a0 as Rs,a1 as De,a2 as Ke,a3 as Ss,a4 as Fn,a5 as Kn,a6 as Gn,a7 as Un,a8 as qe,a9 as Pt,aa as Wn,ab as Hn,ac as zn,ad as qn,F as mt,G as ot,ae as Te,af as ws,h as js,f as Re,d as Is,ag as Yn,ah as Xn,u as pt,I as we,U as gt,e as Ds,v as Os,y as me,o as fe,x as w,ai as ze,aj as Et,N as _s,A as je,L as rt,C as lt,T as Mt,E as Qn,ak as Zn,al as Jn,am as ei,an as ti,ao as si,ap as Ls,aq as ni,ar as ii,as as $s,at as ai,au as oi,g as ri,av as li,aw as ci,m as di,s as ks}from"./chunk-S6H5EOGR-Bwn62IP6.js";import{i as Ge,e as ui,f as hi,g as fi}from"./index-DvLMSsrd.js";import{S as mi}from"./plus-VpD79E1k.js";import{T as Bs}from"./tooltip-button-Bd3YrFXr.js";import{s as At,u as pi,a as Vs,B as Ue,b as Fs,c as Ks}from"./branch-error-state-BSd8PBsv.js";import{_ as gi}from"./preload-helper-BXl3LOEh.js";import{i as re,f as X,a as xt,b as xi,m as G,c as Z,e as bi,g as Gs,p as yi,h as Se,s as vi,j as Ci,k as ct,l as F,n as Ti,o as Pi,q as Ei,r as Nt,t as Us,F as Ws,u as ye,v as Rt,w as Hs,x as zs,y as qs,z as Ys,A as Xs,B as Mi,C as St,S as Ai,D as Ni,E as Ye,G as xe,H as Oe,I as _e,J as wt,K as Ri,L as Si,M as wi,N as be,O as ji,d as Ii,P as Di}from"./features-animation-CM7oex5Y.js";import{m as Oi}from"./chunk-BOOVDPB6-Dt9AQnxs.js";import{B as se}from"./brand-button-3Z8FN4qR.js";import{r as _i,a as Li,b as $i,o as ki,u as Bi,c as Vi,S as Qs,B as Fi,d as Ki,C as Gi,E as Ui}from"./event-handler-CNbFJRld.js";import{M as Wi,p as Hi}from"./paragraph-D4ROHliG.js";import{M as Zs}from"./modal-backdrop-ve4Sk5I2.js";import{A as zi}from"./agent-state-CFaY3go2.js";import{a as dt,b as Js,c as qi}from"./ws-client-provider-Dmsj8lkD.js";import"./i18next-CO45VQzB.js";import"./retrieve-axios-error-message-CYr77e_f.js";import"./custom-toast-handlers-CR9P-jKI.js";import"./index-cxP66Ws3.js";import"./open-hands-axios-CtirLpss.js";import"./mutation-B9dSlWD-.js";import"./i18nInstance-DBIXdvxg.js";import"./browser-slice-DabBaamq.js";import"./index-yKbcr7Pf.js";import"./iconBase-2PDVWRGH.js";import"./settings-dropdown-input-Did5iUTK.js";import"./optional-tag-e1gRgM9y.js";import"./use-create-conversation-IQqGjJUl.js";import"./module-5laXsVNO.js";import"./use-user-providers-CVWOd-tS.js";import"./use-settings-CSlhfPqo.js";import"./use-config-jdwF3W4-.js";import"./index-Do49u1Ze.js";import"./use-active-conversation-B8Aw3kE2.js";import"./use-conversation-id-0JHAicdF.js";import"./use-optimistic-user-message-tdysaQ5t.js";const Yi=new Set(["opacity","clipPath","filter","transform"]);function Xi(e){return e==="x"||e==="y"?re[e]?null:(re[e]=!0,()=>{re[e]=!1}):re.x||re.y?null:(re.x=re.y=!0,()=>{re.x=re.y=!1})}function en(e){return Bn(e)&&"ownerSVGElement"in e}function Qi(e){return en(e)&&e.tagName==="svg"}const Zi=y.createContext(null);function Ji(){const e=y.useRef(!1);return Vn(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function ea(){const e=Ji(),[t,s]=y.useState(0),n=y.useCallback(()=>{e.current&&s(t+1)},[t]);return[y.useCallback(()=>X.postRender(n),[n]),t]}const ta=e=>!e.isLayoutDirty&&e.willUpdate(!1);function jt(){const e=new Set,t=new WeakMap,s=()=>e.forEach(ta);return{add:n=>{e.add(n),t.set(n,n.addEventListener("willUpdate",s))},remove:n=>{e.delete(n);const a=t.get(n);a&&(a(),t.delete(n)),s()},dirty:s}}const tn=e=>e===!0,sa=e=>tn(e===!0)||e==="id",sn=({children:e,id:t,inherit:s=!0})=>{const n=y.useContext(at),a=y.useContext(Zi),[c,i]=ea(),o=y.useRef(null),l=n.id||a;o.current===null&&(sa(s)&&l&&(t=t?l+"-"+t:l),o.current={id:t,group:tn(s)&&n.group||jt()});const d=y.useMemo(()=>({...o.current,forceRender:c}),[i]);return r.jsx(at.Provider,{value:d,children:e})};function Me(e,t,s,n){return xt(e,t,xi(s),n)}const nn=1e-4,na=1-nn,ia=1+nn,an=.01,aa=0-an,oa=0+an;function z(e){return e.max-e.min}function ra(e,t,s){return Math.abs(e-t)<=s}function It(e,t,s,n=.5){e.origin=n,e.originPoint=G(t.min,t.max,e.origin),e.scale=z(s)/z(t),e.translate=G(s.min,s.max,e.origin)-e.originPoint,(e.scale>=na&&e.scale<=ia||isNaN(e.scale))&&(e.scale=1),(e.translate>=aa&&e.translate<=oa||isNaN(e.translate))&&(e.translate=0)}function Ae(e,t,s,n){It(e.x,t.x,s.x,n?n.originX:void 0),It(e.y,t.y,s.y,n?n.originY:void 0)}function Dt(e,t,s){e.min=s.min+t.min,e.max=e.min+z(t)}function la(e,t,s){Dt(e.x,t.x,s.x),Dt(e.y,t.y,s.y)}function Ot(e,t,s){e.min=t.min-s.min,e.max=e.min+z(t)}function Ne(e,t,s){Ot(e.x,t.x,s.x),Ot(e.y,t.y,s.y)}function te(e){return[e("x"),e("y")]}const on=({current:e})=>e?e.ownerDocument.defaultView:null,_t=(e,t)=>Math.abs(e-t);function ca(e,t){const s=_t(e.x,t.x),n=_t(e.y,t.y);return Math.sqrt(s**2+n**2)}class rn{constructor(t,s,{transformPagePoint:n,contextWindow:a=window,dragSnapToOrigin:c=!1,distanceThreshold:i=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Qe(this.lastMoveEventInfo,this.history),x=this.startEvent!==null,m=ca(f.offset,{x:0,y:0})>=this.distanceThreshold;if(!x&&!m)return;const{point:p}=f,{timestamp:C}=Z;this.history.push({...p,timestamp:C});const{onStart:g,onMove:b}=this.handlers;x||(g&&g(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,f)},this.handlePointerMove=(f,x)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=Xe(x,this.transformPagePoint),X.update(this.updatePoint,!0)},this.handlePointerUp=(f,x)=>{this.end();const{onEnd:m,onSessionEnd:p,resumeAnimation:C}=this.handlers;if(this.dragSnapToOrigin&&C&&C(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=Qe(f.type==="pointercancel"?this.lastMoveEventInfo:Xe(x,this.transformPagePoint),this.history);this.startEvent&&m&&m(f,g),p&&p(f,g)},!bi(t))return;this.dragSnapToOrigin=c,this.handlers=s,this.transformPagePoint=n,this.distanceThreshold=i,this.contextWindow=a||window;const o=Gs(t),l=Xe(o,this.transformPagePoint),{point:d}=l,{timestamp:u}=Z;this.history=[{...d,timestamp:u}];const{onSessionStart:h}=s;h&&h(t,Qe(l,this.history)),this.removeListeners=yi(Me(this.contextWindow,"pointermove",this.handlePointerMove),Me(this.contextWindow,"pointerup",this.handlePointerUp),Me(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Se(this.updatePoint)}}function Xe(e,t){return t?{point:t(e.point)}:e}function Lt(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Qe({point:e},t){return{point:e,delta:Lt(e,ln(t)),offset:Lt(e,da(t)),velocity:ua(t,.1)}}function da(e){return e[0]}function ln(e){return e[e.length-1]}function ua(e,t){if(e.length<2)return{x:0,y:0};let s=e.length-1,n=null;const a=ln(e);for(;s>=0&&(n=e[s],!(a.timestamp-n.timestamp>vi(t)));)s--;if(!n)return{x:0,y:0};const c=Ci(a.timestamp-n.timestamp);if(c===0)return{x:0,y:0};const i={x:(a.x-n.x)/c,y:(a.y-n.y)/c};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function ha(e,{min:t,max:s},n){return t!==void 0&&e<t?e=n?G(t,e,n.min):Math.max(e,t):s!==void 0&&e>s&&(e=n?G(s,e,n.max):Math.min(e,s)),e}function $t(e,t,s){return{min:t!==void 0?e.min+t:void 0,max:s!==void 0?e.max+s-(e.max-e.min):void 0}}function fa(e,{top:t,left:s,bottom:n,right:a}){return{x:$t(e.x,s,a),y:$t(e.y,t,n)}}function kt(e,t){let s=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([s,n]=[n,s]),{min:s,max:n}}function ma(e,t){return{x:kt(e.x,t.x),y:kt(e.y,t.y)}}function pa(e,t){let s=.5;const n=z(e),a=z(t);return a>n?s=ct(t.min,t.max-n,e.min):n>a&&(s=ct(e.min,e.max-a,t.min)),Rs(0,1,s)}function ga(e,t){const s={};return t.min!==void 0&&(s.min=t.min-e.min),t.max!==void 0&&(s.max=t.max-e.min),s}const ut=.35;function xa(e=ut){return e===!1?e=0:e===!0&&(e=ut),{x:Bt(e,"left","right"),y:Bt(e,"top","bottom")}}function Bt(e,t,s){return{min:Vt(e,t),max:Vt(e,s)}}function Vt(e,t){return typeof e=="number"?e:e[t]||0}const ba=new WeakMap;class ya{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=F(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:s=!1,distanceThreshold:n}={}){const{presenceContext:a}=this.visualElement;if(a&&a.isPresent===!1)return;const c=h=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Gs(h).point)},i=(h,f)=>{const{drag:x,dragPropagation:m,onDragStart:p}=this.getProps();if(x&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Xi(x),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=f,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),te(g=>{let b=this.getAxisMotionValue(g).get()||0;if(Ke.test(b)){const{projection:v}=this.visualElement;if(v&&v.layout){const P=v.layout.layoutBox[g];P&&(b=z(P)*(parseFloat(b)/100))}}this.originPoint[g]=b}),p&&X.postRender(()=>p(h,f)),Nt(this.visualElement,"transform");const{animationState:C}=this.visualElement;C&&C.setActive("whileDrag",!0)},o=(h,f)=>{this.latestPointerEvent=h,this.latestPanInfo=f;const{dragPropagation:x,dragDirectionLock:m,onDirectionLock:p,onDrag:C}=this.getProps();if(!x&&!this.openDragLock)return;const{offset:g}=f;if(m&&this.currentDirection===null){this.currentDirection=va(g),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",f.point,g),this.updateAxis("y",f.point,g),this.visualElement.render(),C&&C(h,f)},l=(h,f)=>{this.latestPointerEvent=h,this.latestPanInfo=f,this.stop(h,f),this.latestPointerEvent=null,this.latestPanInfo=null},d=()=>te(h=>this.getAnimationState(h)==="paused"&&this.getAxisMotionValue(h).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new rn(t,{onSessionStart:c,onStart:i,onMove:o,onSessionEnd:l,resumeAnimation:d},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:n,contextWindow:on(this.visualElement)})}stop(t,s){const n=t||this.latestPointerEvent,a=s||this.latestPanInfo,c=this.isDragging;if(this.cancel(),!c||!a||!n)return;const{velocity:i}=a;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&X.postRender(()=>o(n,a))}cancel(){this.isDragging=!1;const{projection:t,animationState:s}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(t,s,n){const{drag:a}=this.getProps();if(!n||!Le(t,a,this.currentDirection))return;const c=this.getAxisMotionValue(t);let i=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(i=ha(i,this.constraints[t],this.elastic[t])),c.set(i)}resolveConstraints(){const{dragConstraints:t,dragElastic:s}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,a=this.constraints;t&&De(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=fa(n.layoutBox,t):this.constraints=!1,this.elastic=xa(s),a!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&te(c=>{this.constraints!==!1&&this.getAxisMotionValue(c)&&(this.constraints[c]=ga(n.layoutBox[c],this.constraints[c]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:s}=this.getProps();if(!t||!De(t))return!1;const n=t.current,{projection:a}=this.visualElement;if(!a||!a.layout)return!1;const c=Ti(n,a.root,this.visualElement.getTransformPagePoint());let i=ma(a.layout.layoutBox,c);if(s){const o=s(Pi(i));this.hasMutatedConstraints=!!o,o&&(i=Ei(o))}return i}startAnimation(t){const{drag:s,dragMomentum:n,dragElastic:a,dragTransition:c,dragSnapToOrigin:i,onDragTransitionEnd:o}=this.getProps(),l=this.constraints||{},d=te(u=>{if(!Le(u,s,this.currentDirection))return;let h=l&&l[u]||{};i&&(h={min:0,max:0});const f=a?200:1e6,x=a?40:1e7,m={type:"inertia",velocity:n?t[u]:0,bounceStiffness:f,bounceDamping:x,timeConstant:750,restDelta:1,restSpeed:10,...c,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(d).then(o)}startAxisValueAnimation(t,s){const n=this.getAxisMotionValue(t);return Nt(this.visualElement,t),n.start(Us(t,n,0,s,this.visualElement,!1))}stopAnimation(){te(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){te(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const s=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),a=n[s];return a||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){te(s=>{const{drag:n}=this.getProps();if(!Le(s,n,this.currentDirection))return;const{projection:a}=this.visualElement,c=this.getAxisMotionValue(s);if(a&&a.layout){const{min:i,max:o}=a.layout.layoutBox[s];c.set(t[s]-G(i,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:s}=this.getProps(),{projection:n}=this.visualElement;if(!De(s)||!n||!this.constraints)return;this.stopAnimation();const a={x:0,y:0};te(i=>{const o=this.getAxisMotionValue(i);if(o&&this.constraints!==!1){const l=o.get();a[i]=pa({min:l,max:l},this.constraints[i])}});const{transformTemplate:c}=this.visualElement.getProps();this.visualElement.current.style.transform=c?c({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),te(i=>{if(!Le(i,t,null))return;const o=this.getAxisMotionValue(i),{min:l,max:d}=this.constraints[i];o.set(G(l,d,a[i]))})}addListeners(){if(!this.visualElement.current)return;ba.set(this.visualElement,this);const t=this.visualElement.current,s=Me(t,"pointerdown",l=>{const{drag:d,dragListener:u=!0}=this.getProps();d&&u&&this.start(l)}),n=()=>{const{dragConstraints:l}=this.getProps();De(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:a}=this.visualElement,c=a.addEventListener("measure",n);a&&!a.layout&&(a.root&&a.root.updateScroll(),a.updateLayout()),X.read(n);const i=xt(window,"resize",()=>this.scalePositionWithinConstraints()),o=a.addEventListener("didUpdate",({delta:l,hasLayoutChanged:d})=>{this.isDragging&&d&&(te(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{i(),s(),c(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:n=!1,dragPropagation:a=!1,dragConstraints:c=!1,dragElastic:i=ut,dragMomentum:o=!0}=t;return{...t,drag:s,dragDirectionLock:n,dragPropagation:a,dragConstraints:c,dragElastic:i,dragMomentum:o}}}function Le(e,t,s){return(t===!0||t===e)&&(s===null||s===e)}function va(e,t=10){let s=null;return Math.abs(e.y)>t?s="y":Math.abs(e.x)>t&&(s="x"),s}class Ca extends Ws{constructor(t){super(t),this.removeGroupControls=ye,this.removeListeners=ye,this.controls=new ya(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ye}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ft=e=>(t,s)=>{e&&X.postRender(()=>e(t,s))};class Ta extends Ws{constructor(){super(...arguments),this.removePointerDownListener=ye}onPointerDown(t){this.session=new rn(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:on(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:s,onPan:n,onPanEnd:a}=this.node.getProps();return{onSessionStart:Ft(t),onStart:Ft(s),onMove:n,onEnd:(c,i)=>{delete this.session,a&&X.postRender(()=>a(c,i))}}}mount(){this.removePointerDownListener=Me(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ve={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Kt(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Pe={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(Ss.test(e))e=parseFloat(e);else return e;const s=Kt(e,t.target.x),n=Kt(e,t.target.y);return`${s}% ${n}%`}},Pa={correct:(e,{treeScale:t,projectionDelta:s})=>{const n=e,a=Rt.parse(e);if(a.length>5)return n;const c=Rt.createTransformer(e),i=typeof a[0]!="number"?1:0,o=s.x.scale*t.x,l=s.y.scale*t.y;a[0+i]/=o,a[1+i]/=l;const d=G(o,l,.5);return typeof a[2+i]=="number"&&(a[2+i]/=d),typeof a[3+i]=="number"&&(a[3+i]/=d),c(a)}};let Ze=!1;class Ea extends y.Component{componentDidMount(){const{visualElement:t,layoutGroup:s,switchLayoutGroup:n,layoutId:a}=this.props,{projection:c}=t;Gn(Ma),c&&(s.group&&s.group.add(c),n&&n.register&&a&&n.register(c),Ze&&c.root.didUpdate(),c.addEventListener("animationComplete",()=>{this.safeToRemove()}),c.setOptions({...c.options,onExitComplete:()=>this.safeToRemove()})),Ve.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:s,visualElement:n,drag:a,isPresent:c}=this.props,{projection:i}=n;return i&&(i.isPresent=c,Ze=!0,a||t.layoutDependency!==s||s===void 0||t.isPresent!==c?i.willUpdate():this.safeToRemove(),t.isPresent!==c&&(c?i.promote():i.relegate()||X.postRender(()=>{const o=i.getStack();(!o||!o.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Hs.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:s,switchLayoutGroup:n}=this.props,{projection:a}=t;Ze=!0,a&&(a.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(a),n&&n.deregister&&n.deregister(a))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function cn(e){const[t,s]=Fn(),n=y.useContext(at);return r.jsx(Ea,{...e,layoutGroup:n,switchLayoutGroup:y.useContext(Kn),isPresent:t,safeToRemove:s})}const Ma={borderRadius:{...Pe,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Pe,borderTopRightRadius:Pe,borderBottomLeftRadius:Pe,borderBottomRightRadius:Pe,boxShadow:Pa};function Aa(e,t,s){const n=Un(e)?e:zs(e);return n.start(Us("",n,t,s)),n.animation}const Na=(e,t)=>e.depth-t.depth;class Ra{constructor(){this.children=[],this.isDirty=!1}add(t){qs(this.children,t),this.isDirty=!0}remove(t){Ys(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Na),this.isDirty=!1,this.children.forEach(t)}}function Sa(e,t){const s=Xs.now(),n=({timestamp:a})=>{const c=a-s;c>=t&&(Se(n),e(c-t))};return X.setup(n,!0),()=>Se(n)}const dn=["TopLeft","TopRight","BottomLeft","BottomRight"],wa=dn.length,Gt=e=>typeof e=="string"?parseFloat(e):e,Ut=e=>typeof e=="number"||Ss.test(e);function ja(e,t,s,n,a,c){a?(e.opacity=G(0,s.opacity??1,Ia(n)),e.opacityExit=G(t.opacity??1,0,Da(n))):c&&(e.opacity=G(t.opacity??1,s.opacity??1,n));for(let i=0;i<wa;i++){const o=`border${dn[i]}Radius`;let l=Wt(t,o),d=Wt(s,o);if(l===void 0&&d===void 0)continue;l||(l=0),d||(d=0),l===0||d===0||Ut(l)===Ut(d)?(e[o]=Math.max(G(Gt(l),Gt(d),n),0),(Ke.test(d)||Ke.test(l))&&(e[o]+="%")):e[o]=d}(t.rotate||s.rotate)&&(e.rotate=G(t.rotate||0,s.rotate||0,n))}function Wt(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Ia=un(0,.5,Mi),Da=un(.5,.95,ye);function un(e,t,s){return n=>n<e?0:n>t?1:s(ct(e,t,n))}function Ht(e,t){e.min=t.min,e.max=t.max}function ee(e,t){Ht(e.x,t.x),Ht(e.y,t.y)}function zt(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function qt(e,t,s,n,a){return e-=t,e=St(e,1/s,n),a!==void 0&&(e=St(e,1/a,n)),e}function Oa(e,t=0,s=1,n=.5,a,c=e,i=e){if(Ke.test(t)&&(t=parseFloat(t),t=G(i.min,i.max,t/100)-i.min),typeof t!="number")return;let o=G(c.min,c.max,n);e===c&&(o-=t),e.min=qt(e.min,t,s,o,a),e.max=qt(e.max,t,s,o,a)}function Yt(e,t,[s,n,a],c,i){Oa(e,t[s],t[n],t[a],t.scale,c,i)}const _a=["x","scaleX","originX"],La=["y","scaleY","originY"];function Xt(e,t,s,n){Yt(e.x,t,_a,s?s.x:void 0,n?n.x:void 0),Yt(e.y,t,La,s?s.y:void 0,n?n.y:void 0)}function Qt(e){return e.translate===0&&e.scale===1}function hn(e){return Qt(e.x)&&Qt(e.y)}function Zt(e,t){return e.min===t.min&&e.max===t.max}function $a(e,t){return Zt(e.x,t.x)&&Zt(e.y,t.y)}function Jt(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function fn(e,t){return Jt(e.x,t.x)&&Jt(e.y,t.y)}function es(e){return z(e.x)/z(e.y)}function ts(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class ka{constructor(){this.members=[]}add(t){qs(this.members,t),t.scheduleRender()}remove(t){if(Ys(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(t){const s=this.members.findIndex(a=>t===a);if(s===0)return!1;let n;for(let a=s;a>=0;a--){const c=this.members[a];if(c.isPresent!==!1){n=c;break}}return n?(this.promote(n),!0):!1}promote(t,s){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,s&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:a}=t.options;a===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:s,resumingFrom:n}=t;s.onExitComplete&&s.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Ba(e,t,s){let n="";const a=e.x.translate/t.x,c=e.y.translate/t.y,i=s?.z||0;if((a||c||i)&&(n=`translate3d(${a}px, ${c}px, ${i}px) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),s){const{transformPerspective:d,rotate:u,rotateX:h,rotateY:f,skewX:x,skewY:m}=s;d&&(n=`perspective(${d}px) ${n}`),u&&(n+=`rotate(${u}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),x&&(n+=`skewX(${x}deg) `),m&&(n+=`skewY(${m}deg) `)}const o=e.x.scale*t.x,l=e.y.scale*t.y;return(o!==1||l!==1)&&(n+=`scale(${o}, ${l})`),n||"none"}const Je=["","X","Y","Z"],Va=1e3;let Fa=0;function et(e,t,s,n){const{latestValues:a}=t;a[e]&&(s[e]=a[e],t.setStaticValue(e,0),n&&(n[e]=0))}function mn(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const s=ji(t);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:a,layoutId:c}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",X,!(a||c))}const{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&mn(n)}function pn({attachResizeListener:e,defaultParent:t,measureScroll:s,checkIsScrollRoot:n,resetTransform:a}){return class{constructor(i={},o=t?.()){this.id=Fa++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Ua),this.nodes.forEach(qa),this.nodes.forEach(Ya),this.nodes.forEach(Wa)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=o?o.root||o:this,this.path=o?[...o.path,o]:[],this.parent=o,this.depth=o?o.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ra)}addEventListener(i,o){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Ai),this.eventHandlers.get(i).add(o)}notifyListeners(i,...o){const l=this.eventHandlers.get(i);l&&l.notify(...o)}hasListeners(i){return this.eventHandlers.has(i)}mount(i){if(this.instance)return;this.isSVG=en(i)&&!Qi(i),this.instance=i;const{layoutId:o,layout:l,visualElement:d}=this.options;if(d&&!d.current&&d.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||o)&&(this.isLayoutDirty=!0),e){let u,h=0;const f=()=>this.root.updateBlockedByResize=!1;X.read(()=>{h=window.innerWidth}),e(i,()=>{const x=window.innerWidth;x!==h&&(h=x,this.root.updateBlockedByResize=!0,u&&u(),u=Sa(f,250),Ve.hasAnimatedSinceResize&&(Ve.hasAnimatedSinceResize=!1,this.nodes.forEach(is)))})}o&&this.root.registerSharedNode(o,this),this.options.animate!==!1&&d&&(o||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||d.getDefaultTransition()||eo,{onLayoutAnimationStart:p,onLayoutAnimationComplete:C}=d.getProps(),g=!this.targetLayout||!fn(this.targetLayout,x),b=!h&&f;if(this.options.layoutRoot||this.resumeFrom||b||h&&(g||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...Ni(m,"layout"),onPlay:p,onComplete:C};(d.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(u,b)}else h||is(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Se(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Xa),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&mn(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:o,layout:l}=this.options;if(o===void 0&&!l)return;const d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ss);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(ns);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(za),this.nodes.forEach(Ka),this.nodes.forEach(Ga)):this.nodes.forEach(ns),this.clearAllSnapshots();const o=Xs.now();Z.delta=Rs(0,1e3/60,o-Z.timestamp),Z.timestamp=o,Z.isProcessing=!0,Ye.update.process(Z),Ye.preRender.process(Z),Ye.render.process(Z),Z.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Hs.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Ha),this.sharedNodes.forEach(Qa)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,X.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){X.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!z(this.snapshot.measuredBox.x)&&!z(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=F(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:o}=this.options;o&&o.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let o=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(o=!1),o&&this.instance){const l=n(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:l,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!a)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,o=this.projectionDelta&&!hn(this.projectionDelta),l=this.getTransformTemplate(),d=l?l(this.latestValues,""):void 0,u=d!==this.prevTransformTemplateValue;i&&this.instance&&(o||xe(this.latestValues)||u)&&(a(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const o=this.measurePageBox();let l=this.removeElementScroll(o);return i&&(l=this.removeTransform(l)),to(l),{animationId:this.root.animationId,measuredBox:o,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:i}=this.options;if(!i)return F();const o=i.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(so))){const{scroll:d}=this.root;d&&(Oe(o.x,d.offset.x),Oe(o.y,d.offset.y))}return o}removeElementScroll(i){const o=F();if(ee(o,i),this.scroll?.wasRoot)return o;for(let l=0;l<this.path.length;l++){const d=this.path[l],{scroll:u,options:h}=d;d!==this.root&&u&&h.layoutScroll&&(u.wasRoot&&ee(o,i),Oe(o.x,u.offset.x),Oe(o.y,u.offset.y))}return o}applyTransform(i,o=!1){const l=F();ee(l,i);for(let d=0;d<this.path.length;d++){const u=this.path[d];!o&&u.options.layoutScroll&&u.scroll&&u!==u.root&&_e(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),xe(u.latestValues)&&_e(l,u.latestValues)}return xe(this.latestValues)&&_e(l,this.latestValues),l}removeTransform(i){const o=F();ee(o,i);for(let l=0;l<this.path.length;l++){const d=this.path[l];if(!d.instance||!xe(d.latestValues))continue;wt(d.latestValues)&&d.updateSnapshot();const u=F(),h=d.measurePageBox();ee(u,h),Xt(o,d.latestValues,d.snapshot?d.snapshot.layoutBox:void 0,u)}return xe(this.latestValues)&&Xt(o,this.latestValues),o}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Z.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){const o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==o;if(!(i||l&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=Z.timestamp,!this.targetDelta&&!this.relativeTarget){const f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=F(),this.relativeTargetOrigin=F(),Ne(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),ee(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=F(),this.targetWithTransforms=F()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),la(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ee(this.target,this.layout.layoutBox),Ri(this.target,this.targetDelta)):ee(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=F(),this.relativeTargetOrigin=F(),Ne(this.relativeTargetOrigin,this.target,f.target),ee(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||wt(this.parent.latestValues)||Si(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const i=this.getLead(),o=!!this.resumingFrom||this!==i;let l=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(l=!1),o&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===Z.timestamp&&(l=!1),l)return;const{layout:d,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||u))return;ee(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;wi(this.layoutCorrected,this.treeScale,this.path,o),i.layout&&!i.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(i.target=i.layout.layoutBox,i.targetWithTransforms=F());const{target:x}=i;if(!x){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(zt(this.prevProjectionDelta.x,this.projectionDelta.x),zt(this.prevProjectionDelta.y,this.projectionDelta.y)),Ae(this.projectionDelta,this.layoutCorrected,x,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ts(this.projectionDelta.x,this.prevProjectionDelta.x)||!ts(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){if(this.options.visualElement?.scheduleRender(),i){const o=this.getStack();o&&o.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=be(),this.projectionDelta=be(),this.projectionDeltaWithTransform=be()}setAnimationOrigin(i,o=!1){const l=this.snapshot,d=l?l.latestValues:{},u={...this.latestValues},h=be();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!o;const f=F(),x=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=x!==m,C=this.getStack(),g=!C||C.members.length<=1,b=!!(p&&!g&&this.options.crossfade===!0&&!this.path.some(Ja));this.animationProgress=0;let v;this.mixTargetDelta=P=>{const E=P/1e3;as(h.x,i.x,E),as(h.y,i.y,E),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ne(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Za(this.relativeTarget,this.relativeTargetOrigin,f,E),v&&$a(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=F()),ee(v,this.relativeTarget)),p&&(this.animationValues=u,ja(u,d,this.latestValues,E,b,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=E},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Se(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=X.update(()=>{Ve.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=zs(0)),this.currentAnimation=Aa(this.motionValue,[0,1e3],{...i,velocity:0,isSync:!0,onUpdate:o=>{this.mixTargetDelta(o),i.onUpdate&&i.onUpdate(o)},onStop:()=>{},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Va),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:o,target:l,layout:d,latestValues:u}=i;if(!(!o||!l||!d)){if(this!==i&&this.layout&&d&&gn(this.options.animationType,this.layout.layoutBox,d.layoutBox)){l=this.target||F();const h=z(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+h;const f=z(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+f}ee(o,l),_e(o,u),Ae(this.projectionDeltaWithTransform,this.layoutCorrected,o,u)}}registerSharedNode(i,o){this.sharedNodes.has(i)||this.sharedNodes.set(i,new ka),this.sharedNodes.get(i).add(o);const d=o.options.initialPromotionConfig;o.promote({transition:d?d.transition:void 0,preserveFollowOpacity:d&&d.shouldPreserveFollowOpacity?d.shouldPreserveFollowOpacity(o):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){const{layoutId:i}=this.options;return i?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:i}=this.options;return i?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:o,preserveFollowOpacity:l}={}){const d=this.getStack();d&&d.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),o&&this.setOptions({transition:o})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let o=!1;const{latestValues:l}=i;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(o=!0),!o)return;const d={};l.z&&et("z",i,d,this.animationValues);for(let u=0;u<Je.length;u++)et(`rotate${Je[u]}`,i,d,this.animationValues),et(`skew${Je[u]}`,i,d,this.animationValues);i.render();for(const u in d)i.setStaticValue(u,d[u]),this.animationValues&&(this.animationValues[u]=d[u]);i.scheduleRender()}applyProjectionStyles(i,o){if(!this.instance||this.isSVG)return;if(!this.isVisible){i.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,i.visibility="",i.opacity="",i.pointerEvents=qe(o?.pointerEvents)||"",i.transform=l?l(this.latestValues,""):"none";return}const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(i.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,i.pointerEvents=qe(o?.pointerEvents)||""),this.hasProjected&&!xe(this.latestValues)&&(i.transform=l?l({},""):"none",this.hasProjected=!1);return}i.visibility="";const u=d.animationValues||d.latestValues;this.applyTransformsToTarget();let h=Ba(this.projectionDeltaWithTransform,this.treeScale,u);l&&(h=l(u,h)),i.transform=h;const{x:f,y:x}=this.projectionDelta;i.transformOrigin=`${f.origin*100}% ${x.origin*100}% 0`,d.animationValues?i.opacity=d===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:i.opacity=d===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const m in Pt){if(u[m]===void 0)continue;const{correct:p,applyTo:C,isCSSVariable:g}=Pt[m],b=h==="none"?u[m]:p(u[m],d);if(C){const v=C.length;for(let P=0;P<v;P++)i[C[P]]=b}else g?this.options.visualElement.renderState.vars[m]=b:i[m]=b}this.options.layoutId&&(i.pointerEvents=d===this?qe(o?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>i.currentAnimation?.stop()),this.root.nodes.forEach(ss),this.root.sharedNodes.clear()}}}function Ka(e){e.updateLayout()}function Ga(e){const t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:n}=e.layout,{animationType:a}=e.options,c=t.source!==e.layout.source;a==="size"?te(u=>{const h=c?t.measuredBox[u]:t.layoutBox[u],f=z(h);h.min=s[u].min,h.max=h.min+f}):gn(a,t.layoutBox,s)&&te(u=>{const h=c?t.measuredBox[u]:t.layoutBox[u],f=z(s[u]);h.max=h.min+f,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[u].max=e.relativeTarget[u].min+f)});const i=be();Ae(i,s,t.layoutBox);const o=be();c?Ae(o,e.applyTransform(n,!0),t.measuredBox):Ae(o,s,t.layoutBox);const l=!hn(i);let d=!1;if(!e.resumeFrom){const u=e.getClosestProjectingParent();if(u&&!u.resumeFrom){const{snapshot:h,layout:f}=u;if(h&&f){const x=F();Ne(x,t.layoutBox,h.layoutBox);const m=F();Ne(m,s,f.layoutBox),fn(x,m)||(d=!0),u.options.layoutRoot&&(e.relativeTarget=m,e.relativeTargetOrigin=x,e.relativeParent=u)}}}e.notifyListeners("didUpdate",{layout:s,snapshot:t,delta:o,layoutDelta:i,hasLayoutChanged:l,hasRelativeLayoutChanged:d})}else if(e.isLead()){const{onExitComplete:s}=e.options;s&&s()}e.options.transition=void 0}function Ua(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Wa(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Ha(e){e.clearSnapshot()}function ss(e){e.clearMeasurements()}function ns(e){e.isLayoutDirty=!1}function za(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function is(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function qa(e){e.resolveTargetDelta()}function Ya(e){e.calcProjection()}function Xa(e){e.resetSkewAndRotation()}function Qa(e){e.removeLeadSnapshot()}function as(e,t,s){e.translate=G(t.translate,0,s),e.scale=G(t.scale,1,s),e.origin=t.origin,e.originPoint=t.originPoint}function os(e,t,s,n){e.min=G(t.min,s.min,n),e.max=G(t.max,s.max,n)}function Za(e,t,s,n){os(e.x,t.x,s.x,n),os(e.y,t.y,s.y,n)}function Ja(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const eo={duration:.45,ease:[.4,0,.1,1]},rs=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ls=rs("applewebkit/")&&!rs("chrome/")?Math.round:ye;function cs(e){e.min=ls(e.min),e.max=ls(e.max)}function to(e){cs(e.x),cs(e.y)}function gn(e,t,s){return e==="position"||e==="preserve-aspect"&&!ra(es(t),es(s),.2)}function so(e){return e!==e.root&&e.scroll?.wasRoot}const no=pn({attachResizeListener:(e,t)=>xt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tt={current:void 0},xn=pn({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!tt.current){const e=new no({});e.mount(window),e.setOptions({layoutScroll:!0}),tt.current=e}return tt.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),io={pan:{Feature:Ta},drag:{Feature:Ca,ProjectionNode:xn,MeasureLayout:cn}},ao={layout:{ProjectionNode:xn,MeasureLayout:cn}},oo={...Ii,...io,...ao};class ro extends Di{constructor(){super(...arguments),this.isEnabled=!1}add(t){(Wn.has(t)||Yi.has(t))&&(this.isEnabled=!0,this.update())}update(){this.set(this.isEnabled?"transform":"auto")}}function lo(){return Hn(()=>new ro("auto"))}function co(e,t){let s=t?.isDisabled,[n,a]=y.useState(!1);return zn(()=>{if(e?.current&&!s){let c=()=>{if(e.current){let o=qn(e.current,{tabbable:!0});a(!!o.nextNode())}};c();let i=new MutationObserver(c);return i.observe(e.current,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["tabIndex","disabled"]}),()=>{i.disconnect()}}}),s?!1:n}var ds=mt({slots:{base:"inline-flex",tabList:["flex","p-1","h-fit","gap-2","items-center","flex-nowrap","overflow-x-scroll","scrollbar-hide","bg-default-100"],tab:["z-0","w-full","px-3","py-1","flex","group","relative","justify-center","items-center","outline-solid outline-transparent","cursor-pointer","transition-opacity","tap-highlight-transparent","data-[disabled=true]:cursor-not-allowed","data-[disabled=true]:opacity-30","data-[hover-unselected=true]:opacity-disabled",...ot],tabContent:["relative","z-10","text-inherit","whitespace-nowrap","transition-colors","text-default-500","group-data-[selected=true]:text-foreground"],cursor:["absolute","z-0","bg-white"],panel:["py-3","px-1","outline-solid outline-transparent","data-[inert=true]:hidden",...ot],tabWrapper:[]},variants:{variant:{solid:{cursor:"inset-0"},light:{tabList:"bg-transparent dark:bg-transparent",cursor:"inset-0"},underlined:{tabList:"bg-transparent dark:bg-transparent",cursor:"h-[2px] w-[80%] bottom-0 shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]"},bordered:{tabList:"bg-transparent dark:bg-transparent border-medium border-default-200 shadow-xs",cursor:"inset-0"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{tabList:"rounded-medium",tab:"h-7 text-tiny rounded-small",cursor:"rounded-small"},md:{tabList:"rounded-medium",tab:"h-8 text-small rounded-small",cursor:"rounded-small"},lg:{tabList:"rounded-large",tab:"h-9 text-medium rounded-medium",cursor:"rounded-medium"}},radius:{none:{tabList:"rounded-none",tab:"rounded-none",cursor:"rounded-none"},sm:{tabList:"rounded-medium",tab:"rounded-small",cursor:"rounded-small"},md:{tabList:"rounded-medium",tab:"rounded-small",cursor:"rounded-small"},lg:{tabList:"rounded-large",tab:"rounded-medium",cursor:"rounded-medium"},full:{tabList:"rounded-full",tab:"rounded-full",cursor:"rounded-full"}},fullWidth:{true:{base:"w-full",tabList:"w-full"}},isDisabled:{true:{tabList:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{tab:"transition-none",tabContent:"transition-none"}},placement:{top:{},start:{tabList:"flex-col",panel:"py-0 px-3",tabWrapper:"flex"},end:{tabList:"flex-col",panel:"py-0 px-3",tabWrapper:"flex flex-row-reverse"},bottom:{tabWrapper:"flex flex-col-reverse"}}},defaultVariants:{color:"default",variant:"solid",size:"md",fullWidth:!1,isDisabled:!1},compoundVariants:[{variant:["solid","bordered","light"],color:"default",class:{cursor:["bg-background","dark:bg-default","shadow-small"],tabContent:"group-data-[selected=true]:text-default-foreground"}},{variant:["solid","bordered","light"],color:"primary",class:{cursor:Te.solid.primary,tabContent:"group-data-[selected=true]:text-primary-foreground"}},{variant:["solid","bordered","light"],color:"secondary",class:{cursor:Te.solid.secondary,tabContent:"group-data-[selected=true]:text-secondary-foreground"}},{variant:["solid","bordered","light"],color:"success",class:{cursor:Te.solid.success,tabContent:"group-data-[selected=true]:text-success-foreground"}},{variant:["solid","bordered","light"],color:"warning",class:{cursor:Te.solid.warning,tabContent:"group-data-[selected=true]:text-warning-foreground"}},{variant:["solid","bordered","light"],color:"danger",class:{cursor:Te.solid.danger,tabContent:"group-data-[selected=true]:text-danger-foreground"}},{variant:"underlined",color:"default",class:{cursor:"bg-foreground",tabContent:"group-data-[selected=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{cursor:"bg-primary",tabContent:"group-data-[selected=true]:text-primary"}},{variant:"underlined",color:"secondary",class:{cursor:"bg-secondary",tabContent:"group-data-[selected=true]:text-secondary"}},{variant:"underlined",color:"success",class:{cursor:"bg-success",tabContent:"group-data-[selected=true]:text-success"}},{variant:"underlined",color:"warning",class:{cursor:"bg-warning",tabContent:"group-data-[selected=true]:text-warning"}},{variant:"underlined",color:"danger",class:{cursor:"bg-danger",tabContent:"group-data-[selected=true]:text-danger"}},{disableAnimation:!0,variant:"underlined",class:{tab:["after:content-['']","after:absolute","after:bottom-0","after:h-[2px]","after:w-[80%]","after:opacity-0","after:shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","data-[selected=true]:after:opacity-100"]}},{disableAnimation:!0,color:"default",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-default data-[selected=true]:text-default-foreground"}},{disableAnimation:!0,color:"primary",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-primary data-[selected=true]:text-primary-foreground"}},{disableAnimation:!0,color:"secondary",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-secondary data-[selected=true]:text-secondary-foreground"}},{disableAnimation:!0,color:"success",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-success data-[selected=true]:text-success-foreground"}},{disableAnimation:!0,color:"warning",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-warning data-[selected=true]:text-warning-foreground"}},{disableAnimation:!0,color:"danger",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-danger data-[selected=true]:text-danger-foreground"}},{disableAnimation:!0,color:"default",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-foreground"}},{disableAnimation:!0,color:"primary",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-primary"}},{disableAnimation:!0,color:"secondary",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-secondary"}},{disableAnimation:!0,color:"success",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-success"}},{disableAnimation:!0,color:"warning",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-warning"}},{disableAnimation:!0,color:"danger",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-danger"}}],compoundSlots:[{variant:"underlined",slots:["tab","tabList","cursor"],class:["rounded-none"]}]}),uo=mt({base:"px-2",variants:{variant:{light:"",shadow:"px-4 shadow-medium rounded-medium bg-content1",bordered:"px-4 border-medium border-divider rounded-medium",splitted:"flex flex-col gap-2"},fullWidth:{true:"w-full"}},defaultVariants:{variant:"light",fullWidth:!0}}),ho=mt({slots:{base:"",heading:"",trigger:["flex py-4 w-full h-full gap-3 outline-solid outline-transparent items-center tap-highlight-transparent",...ot],startContent:"shrink-0",indicator:"text-default-400",titleWrapper:"flex-1 flex flex-col text-start",title:"text-foreground text-medium",subtitle:"text-small text-foreground-500 font-normal",content:"py-2"},variants:{variant:{splitted:{base:"px-4 bg-content1 shadow-medium rounded-medium"}},isCompact:{true:{trigger:"py-2",title:"text-medium",subtitle:"text-small",indicator:"text-medium",content:"py-1"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},hideIndicator:{true:{indicator:"hidden"}},disableAnimation:{true:{content:"hidden data-[open=true]:block"},false:{indicator:"transition-transform",trigger:"transition-opacity"}},disableIndicatorAnimation:{true:{indicator:"transition-none"},false:{indicator:"rotate-0 data-[open=true]:-rotate-90 rtl:-rotate-180 rtl:data-[open=true]:-rotate-90"}}},defaultVariants:{size:"md",radius:"lg",isDisabled:!1,hideIndicator:!1,disableIndicatorAnimation:!1}});function fo(e,t){let{elementType:s="button",isDisabled:n,onPress:a,onPressStart:c,onPressEnd:i,onPressUp:o,onPressChange:l,preventFocusOnPress:d,allowFocusWhenDisabled:u,onClick:h,href:f,target:x,rel:m,type:p="button"}=e,C;s==="button"?C={type:p,disabled:n,form:e.form,formAction:e.formAction,formEncType:e.formEncType,formMethod:e.formMethod,formNoValidate:e.formNoValidate,formTarget:e.formTarget,name:e.name,value:e.value}:C={role:"button",href:s==="a"&&!n?f:void 0,target:s==="a"?x:void 0,type:s==="input"?p:void 0,disabled:s==="input"?n:void 0,"aria-disabled":!n||s==="input"?void 0:n,rel:s==="a"?m:void 0};let{pressProps:g,isPressed:b}=ws({onPressStart:c,onPressEnd:i,onPressChange:l,onPress:a,onPressUp:o,onClick:h,isDisabled:n,preventFocusOnPress:d,ref:t}),{focusableProps:v}=js(e,t);u&&(v.tabIndex=n?-1:v.tabIndex);let P=Re(v,g,Is(e,{labelable:!0}));return{isPressed:b,buttonProps:Re(C,P,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}function mo(e,t,s){let{item:n,isDisabled:a}=e,c=n.key,i=t.selectionManager,o=y.useId(),l=y.useId(),d=t.disabledKeys.has(n.key)||a;y.useEffect(()=>{c===t.focusedKey&&document.activeElement!==s.current&&s.current&&Yn(s.current)},[s,c,t.focusedKey]);let u=y.useCallback(p=>{i.canSelectItem(c)&&(i.select(c,p),t.toggleKey(c))},[c,i]);const h=y.useCallback(p=>{i.selectionBehavior==="replace"&&i.extendSelection(p),i.setFocusedKey(p)},[i]),f=y.useCallback(p=>{const g={ArrowDown:()=>{const b=t.collection.getKeyAfter(c);if(b&&t.disabledKeys.has(b)){const v=t.collection.getKeyAfter(b);v&&h(v)}else b&&h(b)},ArrowUp:()=>{const b=t.collection.getKeyBefore(c);if(b&&t.disabledKeys.has(b)){const v=t.collection.getKeyBefore(b);v&&h(v)}else b&&h(b)},Home:()=>{const b=t.collection.getFirstKey();b&&h(b)},End:()=>{const b=t.collection.getLastKey();b&&h(b)}}[p.key];g&&(p.preventDefault(),i.canSelectItem(c)&&g(p))},[c,i]);let{buttonProps:x}=fo({id:o,elementType:"button",isDisabled:d,onKeyDown:f,onPress:u},s),m=t.selectionManager.isSelected(n.key);return{buttonProps:{...x,"aria-expanded":m,"aria-controls":m?l:void 0},regionProps:{id:l,role:"region","aria-labelledby":o}}}function po(e,t,s){let{listProps:n}=Xn({...e,...t,allowsTabNavigation:!0,disallowSelectAll:!0,ref:s});return delete n.onKeyDownCapture,{accordionProps:{...n,tabIndex:void 0}}}function go(e){var t,s;const n=pt(),{ref:a,as:c,item:i,onFocusChange:o}=e,{state:l,className:d,indicator:u,children:h,title:f,subtitle:x,startContent:m,motionProps:p,focusedKey:C,variant:g,isCompact:b=!1,classNames:v={},isDisabled:P=!1,hideIndicator:E=!1,disableAnimation:R=(t=n?.disableAnimation)!=null?t:!1,keepContentMounted:L=!1,disableIndicatorAnimation:S=!1,HeadingComponent:V=c||"h2",onPress:M,onPressStart:O,onPressEnd:I,onPressChange:D,onPressUp:_,onClick:$,...U}=e,N=c||"div",ne=typeof N=="string",J=we(a),j=l.disabledKeys.has(i.key)||P,k=l.selectionManager.isSelected(i.key),{buttonProps:le,regionProps:ie}=mo({item:i,isDisabled:j},{...l,focusedKey:C},J),{onFocus:H,onBlur:ce,...de}=le,{isFocused:ue,isFocusVisible:he,focusProps:B}=gt({autoFocus:(s=i.props)==null?void 0:s.autoFocus}),{isHovered:W,hoverProps:ae}=Ds({isDisabled:j}),{pressProps:oe,isPressed:ve}=ws({ref:J,isDisabled:j,onPress:M,onPressStart:O,onPressEnd:I,onPressChange:D,onPressUp:_}),Ce=y.useCallback(()=>{o?.(!0,i.key)},[]),Ie=y.useCallback(()=>{o?.(!1,i.key)},[]),A=y.useMemo(()=>({...v}),[Os(v)]),Y=y.useMemo(()=>ho({isCompact:b,isDisabled:j,hideIndicator:E,disableAnimation:R,disableIndicatorAnimation:S,variant:g}),[b,j,E,R,S,g]),yt=me(A?.base,d),Pn=y.useCallback((Q={})=>({"data-open":w(k),"data-disabled":w(j),"data-slot":"base",className:Y.base({class:yt}),...fe(ze(U,{enabled:ne}),Q)}),[yt,ne,U,Y,i.props,k,j]),En=(Q={})=>{var vt,Ct;return{ref:J,"data-open":w(k),"data-focus":w(ue),"data-focus-visible":w(he),"data-disabled":w(j),"data-hover":w(W),"data-pressed":w(ve),"data-slot":"trigger",className:Y.trigger({class:A?.trigger}),onFocus:Et(Ce,H,B.onFocus,U.onFocus,(vt=i.props)==null?void 0:vt.onFocus),onBlur:Et(Ie,ce,B.onBlur,U.onBlur,(Ct=i.props)==null?void 0:Ct.onBlur),...fe(de,ae,oe,Q,{onClick:_s(oe.onClick,$)})}},Mn=y.useCallback((Q={})=>({"data-open":w(k),"data-disabled":w(j),"data-slot":"content",className:Y.content({class:A?.content}),...fe(ie,Q)}),[Y,A,ie,k,j,A?.content]),An=y.useCallback((Q={})=>({"aria-hidden":w(!0),"data-open":w(k),"data-disabled":w(j),"data-slot":"indicator",className:Y.indicator({class:A?.indicator}),...Q}),[Y,A?.indicator,k,j,A?.indicator]),Nn=y.useCallback((Q={})=>({"data-open":w(k),"data-disabled":w(j),"data-slot":"heading",className:Y.heading({class:A?.heading}),...Q}),[Y,A?.heading,k,j,A?.heading]),Rn=y.useCallback((Q={})=>({"data-open":w(k),"data-disabled":w(j),"data-slot":"title",className:Y.title({class:A?.title}),...Q}),[Y,A?.title,k,j,A?.title]),Sn=y.useCallback((Q={})=>({"data-open":w(k),"data-disabled":w(j),"data-slot":"subtitle",className:Y.subtitle({class:A?.subtitle}),...Q}),[Y,A,k,j,A?.subtitle]);return{Component:N,HeadingComponent:V,item:i,slots:Y,classNames:A,domRef:J,indicator:u,children:h,title:f,subtitle:x,startContent:m,isOpen:k,isDisabled:j,hideIndicator:E,keepContentMounted:L,disableAnimation:R,motionProps:p,getBaseProps:Pn,getHeadingProps:Nn,getButtonProps:En,getContentProps:Mn,getIndicatorProps:An,getTitleProps:Rn,getSubtitleProps:Sn}}var us=e=>r.jsx("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:r.jsx("path",{d:"M15.5 19l-7-7 7-7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5"})}),hs=()=>gi(()=>import("./index-D_FgxOY2.js"),__vite__mapDeps([0,1,2,3,4,5,6])).then(e=>e.default),bn=je((e,t)=>{const{Component:s,HeadingComponent:n,classNames:a,slots:c,indicator:i,children:o,title:l,subtitle:d,startContent:u,isOpen:h,isDisabled:f,hideIndicator:x,keepContentMounted:m,disableAnimation:p,motionProps:C,getBaseProps:g,getHeadingProps:b,getButtonProps:v,getTitleProps:P,getSubtitleProps:E,getContentProps:R,getIndicatorProps:L}=go({...e,ref:t}),S=lo(),M=y.useMemo(()=>typeof i=="function"?i({indicator:r.jsx(us,{}),isOpen:h,isDisabled:f}):i||null,[i,h,f])||r.jsx(us,{}),O=y.useMemo(()=>{if(p)return m?r.jsx("div",{...R(),children:o}):h&&r.jsx("div",{...R(),children:o});const I={exit:{...Mt.collapse.exit,overflowY:"hidden"},enter:{...Mt.collapse.enter,overflowY:"unset"}};return m?r.jsx(rt,{features:hs,children:r.jsx(lt.section,{animate:h?"enter":"exit",exit:"exit",initial:"exit",style:{willChange:S},variants:I,onKeyDown:D=>{D.stopPropagation()},...C,children:r.jsx("div",{...R(),children:o})},"accordion-content")}):r.jsx(Qn,{initial:!1,children:h&&r.jsx(rt,{features:hs,children:r.jsx(lt.section,{animate:"enter",exit:"exit",initial:"exit",style:{willChange:S},variants:I,onKeyDown:D=>{D.stopPropagation()},...C,children:r.jsx("div",{...R(),children:o})},"accordion-content")})})},[h,p,m,o,C]);return r.jsxs(s,{...g(),children:[r.jsx(n,{...b(),children:r.jsxs("button",{...v(),children:[u&&r.jsx("div",{className:c.startContent({class:a?.startContent}),children:u}),r.jsxs("div",{className:c.titleWrapper({class:a?.titleWrapper}),children:[l&&r.jsx("span",{...P(),children:l}),d&&r.jsx("span",{...E(),children:d})]}),!x&&M&&r.jsx("span",{...L(),children:M})]})}),O]})});bn.displayName="HeroUI.AccordionItem";var xo=bn;class bo{*[Symbol.iterator](){yield*this.iterable}get size(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(t){let s=this.keyMap.get(t);var n;return s&&(n=s.prevKey)!==null&&n!==void 0?n:null}getKeyAfter(t){let s=this.keyMap.get(t);var n;return s&&(n=s.nextKey)!==null&&n!==void 0?n:null}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(t){var s;return(s=this.keyMap.get(t))!==null&&s!==void 0?s:null}at(t){const s=[...this.getKeys()];return this.getItem(s[t])}constructor(t,{expandedKeys:s}={}){this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.iterable=t,s=s||new Set;let n=o=>{if(this.keyMap.set(o.key,o),o.childNodes&&(o.type==="section"||s.has(o.key)))for(let l of o.childNodes)n(l)};for(let o of t)n(o);let a=null,c=0;for(let[o,l]of this.keyMap)a?(a.nextKey=o,l.prevKey=a.key):(this.firstKey=o,l.prevKey=void 0),l.type==="item"&&(l.index=c++),a=l,a.nextKey=void 0;var i;this.lastKey=(i=a?.key)!==null&&i!==void 0?i:null}}function yo(e){let{onExpandedChange:t}=e,[s,n]=Zn(e.expandedKeys?new Set(e.expandedKeys):void 0,e.defaultExpandedKeys?new Set(e.defaultExpandedKeys):new Set,t),a=Jn(e),c=y.useMemo(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),i=ei(e,y.useCallback(l=>new bo(l,{expandedKeys:s}),[s]),null);return y.useEffect(()=>{a.focusedKey!=null&&!i.getItem(a.focusedKey)&&a.setFocusedKey(null)},[i,a.focusedKey]),{collection:i,expandedKeys:s,disabledKeys:c,toggleKey:l=>{n(vo(s,l))},setExpandedKeys:n,selectionManager:new ti(i,a)}}function vo(e,t){let s=new Set(e);return s.has(t)?s.delete(t):s.add(t),s}function Co(e){var t;const s=pt(),{ref:n,as:a,className:c,items:i,variant:o,motionProps:l,expandedKeys:d,disabledKeys:u,selectedKeys:h,children:f,defaultExpandedKeys:x,selectionMode:m="single",selectionBehavior:p="toggle",keepContentMounted:C=!1,disallowEmptySelection:g,defaultSelectedKeys:b,onExpandedChange:v,onSelectionChange:P,dividerProps:E={},isCompact:R=!1,isDisabled:L=!1,showDivider:S=!0,hideIndicator:V=!1,disableAnimation:M=(t=s?.disableAnimation)!=null?t:!1,disableIndicatorAnimation:O=!1,itemClasses:I,...D}=e,[_,$]=y.useState(null),U=a||"div",N=typeof U=="string",ne=we(n),J=y.useMemo(()=>uo({variant:o,className:c}),[o,c]),k={children:y.useMemo(()=>{let B=[];return Ee.Children.map(f,W=>{var ae;if(Ee.isValidElement(W)&&typeof((ae=W.props)==null?void 0:ae.children)!="string"){const oe=Ee.cloneElement(W,{hasChildItems:!1});B.push(oe)}else B.push(W)}),B},[f]),items:i},le={expandedKeys:d,defaultExpandedKeys:x,onExpandedChange:v},ie={disabledKeys:u,selectedKeys:h,selectionMode:m,selectionBehavior:p,disallowEmptySelection:g,defaultSelectedKeys:b??x,onSelectionChange:P,...k,...le},H=yo(ie);H.selectionManager.setFocusedKey=B=>{$(B)};const{accordionProps:ce}=po({...k,...le},H,ne),de=y.useMemo(()=>({state:H,focusedKey:_,motionProps:l,isCompact:R,isDisabled:L,hideIndicator:V,disableAnimation:M,keepContentMounted:C,disableIndicatorAnimation:O}),[_,R,L,V,h,M,C,H?.expandedKeys.values,O,H.expandedKeys.size,H.disabledKeys.size,l]),ue=y.useCallback((B={})=>({ref:ne,className:J,"data-orientation":"vertical",...fe(ce,ze(D,{enabled:N}),B)}),[]),he=y.useCallback((B,W)=>{B&&$(W)},[]);return{Component:U,values:de,state:H,focusedKey:_,getBaseProps:ue,isSplitted:o==="splitted",classNames:J,showDivider:S,dividerProps:E,disableAnimation:M,handleFocusChanged:he,itemClasses:I}}var yn=je((e,t)=>{const{Component:s,values:n,state:a,isSplitted:c,showDivider:i,getBaseProps:o,disableAnimation:l,handleFocusChanged:d,itemClasses:u,dividerProps:h}=Co({...e,ref:t}),f=y.useCallback((m,p)=>d(m,p),[d]),x=y.useMemo(()=>[...a.collection].map((m,p)=>{const C={...u,...m.props.classNames||{}};return r.jsxs(y.Fragment,{children:[r.jsx(xo,{item:m,variant:e.variant,onFocusChange:f,...n,...m.props,classNames:C}),!m.props.hidden&&!c&&i&&p<a.collection.size-1&&r.jsx(si,{...h})]},m.key)}),[n,u,f,c,i,a.collection]);return r.jsx(s,{...o(),children:l?x:r.jsx(sn,{children:x})})});yn.displayName="HeroUI.Accordion";var To=yn,Po=Ls,Eo=Po;const fs=e=>typeof e=="object"&&e!=null&&e.nodeType===1,ms=(e,t)=>(!t||e!=="hidden")&&e!=="visible"&&e!=="clip",$e=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const s=getComputedStyle(e,null);return ms(s.overflowY,t)||ms(s.overflowX,t)||(n=>{const a=(c=>{if(!c.ownerDocument||!c.ownerDocument.defaultView)return null;try{return c.ownerDocument.defaultView.frameElement}catch{return null}})(n);return!!a&&(a.clientHeight<n.scrollHeight||a.clientWidth<n.scrollWidth)})(e)}return!1},ke=(e,t,s,n,a,c,i,o)=>c<e&&i>t||c>e&&i<t?0:c<=e&&o<=s||i>=t&&o>=s?c-e-n:i>t&&o<s||c<e&&o>s?i-t+a:0,Mo=e=>{const t=e.parentElement;return t??(e.getRootNode().host||null)},ps=(e,t)=>{var s,n,a,c;if(typeof document>"u")return[];const{scrollMode:i,block:o,inline:l,boundary:d,skipOverflowHiddenElements:u}=t,h=typeof d=="function"?d:U=>U!==d;if(!fs(e))throw new TypeError("Invalid target");const f=document.scrollingElement||document.documentElement,x=[];let m=e;for(;fs(m)&&h(m);){if(m=Mo(m),m===f){x.push(m);break}m!=null&&m===document.body&&$e(m)&&!$e(document.documentElement)||m!=null&&$e(m,u)&&x.push(m)}const p=(n=(s=window.visualViewport)==null?void 0:s.width)!=null?n:innerWidth,C=(c=(a=window.visualViewport)==null?void 0:a.height)!=null?c:innerHeight,{scrollX:g,scrollY:b}=window,{height:v,width:P,top:E,right:R,bottom:L,left:S}=e.getBoundingClientRect(),{top:V,right:M,bottom:O,left:I}=(U=>{const N=window.getComputedStyle(U);return{top:parseFloat(N.scrollMarginTop)||0,right:parseFloat(N.scrollMarginRight)||0,bottom:parseFloat(N.scrollMarginBottom)||0,left:parseFloat(N.scrollMarginLeft)||0}})(e);let D=o==="start"||o==="nearest"?E-V:o==="end"?L+O:E+v/2-V+O,_=l==="center"?S+P/2-I+M:l==="end"?R+M:S-I;const $=[];for(let U=0;U<x.length;U++){const N=x[U],{height:ne,width:J,top:j,right:k,bottom:le,left:ie}=N.getBoundingClientRect();if(i==="if-needed"&&E>=0&&S>=0&&L<=C&&R<=p&&(N===f&&!$e(N)||E>=j&&L<=le&&S>=ie&&R<=k))return $;const H=getComputedStyle(N),ce=parseInt(H.borderLeftWidth,10),de=parseInt(H.borderTopWidth,10),ue=parseInt(H.borderRightWidth,10),he=parseInt(H.borderBottomWidth,10);let B=0,W=0;const ae="offsetWidth"in N?N.offsetWidth-N.clientWidth-ce-ue:0,oe="offsetHeight"in N?N.offsetHeight-N.clientHeight-de-he:0,ve="offsetWidth"in N?N.offsetWidth===0?0:J/N.offsetWidth:0,Ce="offsetHeight"in N?N.offsetHeight===0?0:ne/N.offsetHeight:0;if(f===N)B=o==="start"?D:o==="end"?D-C:o==="nearest"?ke(b,b+C,C,de,he,b+D,b+D+v,v):D-C/2,W=l==="start"?_:l==="center"?_-p/2:l==="end"?_-p:ke(g,g+p,p,ce,ue,g+_,g+_+P,P),B=Math.max(0,B+b),W=Math.max(0,W+g);else{B=o==="start"?D-j-de:o==="end"?D-le+he+oe:o==="nearest"?ke(j,le,ne,de,he+oe,D,D+v,v):D-(j+ne/2)+oe/2,W=l==="start"?_-ie-ce:l==="center"?_-(ie+J/2)+ae/2:l==="end"?_-k+ue+ae:ke(ie,k,J,ce,ue+ae,_,_+P,P);const{scrollLeft:Ie,scrollTop:A}=N;B=Ce===0?0:Math.max(0,Math.min(A+B/Ce,N.scrollHeight-ne/Ce+oe)),W=ve===0?0:Math.max(0,Math.min(Ie+W/ve,N.scrollWidth-J/ve+ae)),D+=A-B,_+=Ie-W}$.push({el:N,top:B,left:W})}return $},Ao=e=>e===!1?{block:"end",inline:"nearest"}:(t=>t===Object(t)&&Object.keys(t).length!==0)(e)?e:{block:"start",inline:"nearest"};function No(e,t){if(!e.isConnected||!(n=>{let a=n;for(;a&&a.parentNode;){if(a.parentNode===document)return!0;a=a.parentNode instanceof ShadowRoot?a.parentNode.host:a.parentNode}return!1})(e))return;if((n=>typeof n=="object"&&typeof n.behavior=="function")(t))return t.behavior(ps(e,t));const s=typeof t=="boolean"||t==null?void 0:t.behavior;for(const{el:n,top:a,left:c}of ps(e,Ao(t)))n.scroll({top:a,left:c,behavior:s})}function Ro(e={}){const{rerender:t=!1,delay:s=0}=e,n=y.useRef(!1),[a,c]=y.useState(!1);return y.useEffect(()=>{n.current=!0;let i=null;return t&&(s>0?i=setTimeout(()=>{c(!0)},s):c(!0)),()=>{n.current=!1,t&&c(!1),i&&clearTimeout(i)}},[t]),[y.useCallback(()=>n.current,[]),a]}const vn=new WeakMap;function We(e,t,s){return e?(typeof t=="string"&&(t=t.replace(/\s+/g,"")),`${vn.get(e)}-${s}-${t}`):""}function So(e,t,s){let{key:n,isDisabled:a,shouldSelectOnPressUp:c}=e,{selectionManager:i,selectedKey:o}=t,l=n===o,d=a||t.isDisabled||t.selectionManager.isDisabled(n),{itemProps:u,isPressed:h}=ni({selectionManager:i,key:n,ref:s,isDisabled:d,shouldSelectOnPressUp:c,linkBehavior:"selection"}),f=We(t,n,"tab"),x=We(t,n,"tabpanel"),{tabIndex:m}=u,p=t.collection.getItem(n),C=Is(p?.props,{labelable:!0});delete C.id;let g=ii(p?.props),{focusableProps:b}=js({isDisabled:d},s);return{tabProps:Re(C,b,g,u,{id:f,"aria-selected":l,"aria-disabled":d||void 0,"aria-controls":l?x:void 0,tabIndex:d?void 0:m,role:"tab"}),isSelected:l,isDisabled:d,isPressed:h}}function wo(e,t,s){let n=co(s)?void 0:0;var a;const c=We(t,(a=e.id)!==null&&a!==void 0?a:t?.selectedKey,"tabpanel"),i=$s({...e,id:c,"aria-labelledby":We(t,t?.selectedKey,"tab")});return{tabPanelProps:Re(i,{tabIndex:n,role:"tabpanel","aria-describedby":e["aria-describedby"],"aria-details":e["aria-details"]})}}class jo{getKeyLeftOf(t){return this.flipDirection?this.getNextKey(t):this.getPreviousKey(t)}getKeyRightOf(t){return this.flipDirection?this.getPreviousKey(t):this.getNextKey(t)}isDisabled(t){var s,n;return this.disabledKeys.has(t)||!!(!((n=this.collection.getItem(t))===null||n===void 0||(s=n.props)===null||s===void 0)&&s.isDisabled)}getFirstKey(){let t=this.collection.getFirstKey();return t!=null&&this.isDisabled(t)&&(t=this.getNextKey(t)),t}getLastKey(){let t=this.collection.getLastKey();return t!=null&&this.isDisabled(t)&&(t=this.getPreviousKey(t)),t}getKeyAbove(t){return this.tabDirection?null:this.getPreviousKey(t)}getKeyBelow(t){return this.tabDirection?null:this.getNextKey(t)}getNextKey(t){let s=t;do s=this.collection.getKeyAfter(s),s==null&&(s=this.collection.getFirstKey());while(s!=null&&this.isDisabled(s));return s}getPreviousKey(t){let s=t;do s=this.collection.getKeyBefore(s),s==null&&(s=this.collection.getLastKey());while(s!=null&&this.isDisabled(s));return s}constructor(t,s,n,a=new Set){this.collection=t,this.flipDirection=s==="rtl"&&n==="horizontal",this.disabledKeys=a,this.tabDirection=n==="horizontal"}}function Io(e,t,s){let{orientation:n="horizontal",keyboardActivation:a="automatic"}=e,{collection:c,selectionManager:i,disabledKeys:o}=t,{direction:l}=ai(),d=y.useMemo(()=>new jo(c,l,n,o),[c,o,n,l]),{collectionProps:u}=oi({ref:s,selectionManager:i,keyboardDelegate:d,selectOnFocus:a==="automatic",disallowEmptySelection:!0,scrollRef:s,linkBehavior:"selection"}),h=ri();vn.set(t,h);let f=$s({...e,id:h});return{tabListProps:{...Re(u,f),role:"tablist","aria-orientation":n,tabIndex:void 0}}}var Cn=je((e,t)=>{var s,n;const{as:a,tabKey:c,destroyInactiveTabPanel:i,state:o,className:l,slots:d,classNames:u,...h}=e,f=a||"div",x=we(t),{tabPanelProps:m}=wo({...e,id:String(c)},o,x),{focusProps:p,isFocused:C,isFocusVisible:g}=gt(),b=o.selectedItem,v=o.collection.getItem(c).props.children,P=me(u?.panel,l,(s=b?.props)==null?void 0:s.className),E=c===b?.key;return!v||!E&&i?null:r.jsx(f,{ref:x,"data-focus":C,"data-focus-visible":g,"data-inert":E?void 0:"true",inert:li(!E),...E&&fe(m,p,h),className:(n=d.panel)==null?void 0:n.call(d,{class:P}),"data-slot":"panel",children:v})});Cn.displayName="HeroUI.TabPanel";var Do=Cn,Tn=je((e,t)=>{var s;const{className:n,as:a,item:c,state:i,classNames:o,isDisabled:l,listRef:d,slots:u,motionProps:h,disableAnimation:f,disableCursorAnimation:x,shouldSelectOnPressUp:m,tabRef:p,...C}=e,{key:g}=c,b=we(t),v=a||(e.href?"a":"button"),P=typeof v=="string",{tabProps:E,isSelected:R,isDisabled:L,isPressed:S}=So({key:g,isDisabled:l,shouldSelectOnPressUp:m},i,b);e.children==null&&delete E["aria-controls"];const V=l||L,{focusProps:M,isFocused:O,isFocusVisible:I}=gt(),{hoverProps:D,isHovered:_}=Ds({isDisabled:V}),$=me(o?.tab,n),[,U]=Ro({rerender:!0}),N=()=>{!b?.current||!d?.current||No(b.current,{scrollMode:"if-needed",behavior:"smooth",block:"end",inline:"end",boundary:d?.current})};return r.jsxs(v,{ref:Oi(b,p),"data-disabled":w(L),"data-focus":w(O),"data-focus-visible":w(I),"data-hover":w(_),"data-hover-unselected":w((_||S)&&!R),"data-pressed":w(S),"data-selected":w(R),"data-slot":"tab",...fe(E,V?{}:{...M,...D},ze(C,{enabled:P,omitPropNames:new Set(["title"]),omitEventNames:new Set(["onClick"])}),{onClick:_s(N,E.onClick)}),className:(s=u.tab)==null?void 0:s.call(u,{class:$}),title:C?.titleValue,type:v==="button"?"button":void 0,children:[R&&!f&&!x&&U?r.jsx(rt,{features:oo,children:r.jsx(lt.span,{className:u.cursor({class:o?.cursor}),"data-slot":"cursor",layoutDependency:!1,layoutId:"cursor",transition:{type:"spring",bounce:.15,duration:.5},...h})}):null,r.jsx("div",{className:u.tabContent({class:o?.tabContent}),"data-slot":"tabContent",children:c.rendered})]})});Tn.displayName="HeroUI.Tab";var Oo=Tn;function _o(e){var t,s;let n=ci({...e,onSelectionChange:e.onSelectionChange?l=>{var d;l!=null&&((d=e.onSelectionChange)===null||d===void 0||d.call(e,l))}:void 0,suppressTextValueWarning:!0,defaultSelectedKey:(s=(t=e.defaultSelectedKey)!==null&&t!==void 0?t:gs(e.collection,e.disabledKeys?new Set(e.disabledKeys):new Set))!==null&&s!==void 0?s:void 0}),{selectionManager:a,collection:c,selectedKey:i}=n,o=y.useRef(i);return y.useEffect(()=>{let l=i;e.selectedKey==null&&(a.isEmpty||l==null||!c.getItem(l))&&(l=gs(c,n.disabledKeys),l!=null&&a.setSelectedKeys([l])),(l!=null&&a.focusedKey==null||!a.isFocused&&l!==o.current)&&a.setFocusedKey(l),o.current=l}),{...n,isDisabled:e.isDisabled||!1}}function gs(e,t){let s=null;if(e){var n,a,c,i;for(s=e.getFirstKey();s!=null&&(t.has(s)||!((a=e.getItem(s))===null||a===void 0||(n=a.props)===null||n===void 0)&&n.isDisabled)&&s!==e.getLastKey();)s=e.getKeyAfter(s);s!=null&&(t.has(s)||!((i=e.getItem(s))===null||i===void 0||(c=i.props)===null||c===void 0)&&c.isDisabled)&&s===e.getLastKey()&&(s=e.getFirstKey())}return s}function Lo(e){var t,s,n;const a=pt(),[c,i]=di(e,ds.variantKeys),{ref:o,as:l,className:d,classNames:u,children:h,disableCursorAnimation:f,motionProps:x,isVertical:m=!1,shouldSelectOnPressUp:p=!0,destroyInactiveTabPanel:C=!0,...g}=c,b=l||"div",v=typeof b=="string",P=we(o),E=(s=(t=e?.disableAnimation)!=null?t:a?.disableAnimation)!=null?s:!1,R=_o({children:h,...g}),{tabListProps:L}=Io(g,R,P),S=y.useMemo(()=>ds({...i,disableAnimation:E,...m?{placement:"start"}:{}}),[Os(i),E,m]),V=me(u?.base,d),M=y.useMemo(()=>({state:R,slots:S,classNames:u,motionProps:x,disableAnimation:E,listRef:P,shouldSelectOnPressUp:p,disableCursorAnimation:f,isDisabled:e?.isDisabled}),[R,S,P,x,E,f,p,e?.isDisabled,u]),O=y.useCallback($=>({"data-slot":"base",className:S.base({class:me(V,$?.className)}),...fe(ze(g,{enabled:v}),$)}),[V,g,S]),I=(n=i.placement)!=null?n:m?"start":"top",D=y.useCallback($=>({"data-slot":"tabWrapper",className:S.tabWrapper({class:me(u?.tabWrapper,$?.className)}),"data-placement":I,"data-vertical":m||I==="start"||I==="end"?"vertical":"horizontal"}),[u,S,I,m]),_=y.useCallback($=>({ref:P,"data-slot":"tabList",className:S.tabList({class:me(u?.tabList,$?.className)}),...fe(L,$)}),[P,L,u,S]);return{Component:b,domRef:P,state:R,values:M,destroyInactiveTabPanel:C,getBaseProps:O,getTabListProps:_,getWrapperProps:D}}var $o=je(function(t,s){const{Component:n,values:a,state:c,destroyInactiveTabPanel:i,getBaseProps:o,getTabListProps:l,getWrapperProps:d}=Lo({...t,ref:s}),u=y.useId(),h=!t.disableAnimation&&!t.disableCursorAnimation,f={state:c,listRef:a.listRef,slots:a.slots,classNames:a.classNames,isDisabled:a.isDisabled,motionProps:a.motionProps,disableAnimation:a.disableAnimation,shouldSelectOnPressUp:a.shouldSelectOnPressUp,disableCursorAnimation:a.disableCursorAnimation},x=[...c.collection].map(p=>r.jsx(Oo,{item:p,...f,...p.props},p.key)),m=r.jsxs(r.Fragment,{children:[r.jsx("div",{...o(),children:r.jsx(n,{...l(),children:h?r.jsx(sn,{id:u,children:x}):x})}),[...c.collection].map(p=>r.jsx(Do,{classNames:a.classNames,destroyInactiveTabPanel:i,slots:a.slots,state:a.state,tabKey:p.key},p.key))]});return"placement"in t||"isVertical"in t?r.jsx("div",{...d(),children:m}):m}),ko=$o,Bo=Ls,st=Bo;const Vo=e=>y.createElement("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},y.createElement("path",{d:"M8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8C15 11.866 11.866 15 8 15ZM8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16Z",fill:"white"}),y.createElement("path",{d:"M5.25511 5.78615C5.24752 5.92237 5.3599 6.03271 5.49634 6.03271H6.32082C6.45889 6.03271 6.56868 5.92013 6.58723 5.78331C6.67618 5.12718 7.1265 4.64893 7.92922 4.64893C8.61477 4.64893 9.24318 4.9917 9.24318 5.81689C9.24318 6.45166 8.86867 6.74365 8.27834 7.18799C7.60549 7.67676 7.07229 8.24805 7.11037 9.1748L7.11334 9.39161C7.11521 9.52833 7.22658 9.63818 7.36332 9.63818H8.17434C8.31241 9.63818 8.42434 9.52625 8.42434 9.38818V9.28271C8.42434 8.56543 8.69729 8.35596 9.43361 7.79736C10.043 7.33398 10.6778 6.81982 10.6778 5.74072C10.6778 4.22998 9.40188 3.5 8.00539 3.5C6.73831 3.5 5.34964 4.09061 5.25511 5.78615ZM6.81203 11.5488C6.81203 12.082 7.23732 12.4756 7.82131 12.4756C8.43068 12.4756 8.84963 12.082 8.84963 11.5488C8.84963 10.9966 8.43068 10.6094 7.82131 10.6094C7.23732 10.6094 6.81203 10.9966 6.81203 11.5488Z",fill:"white"}));function Fo(){const{t:e}=q();return r.jsxs("div",{children:[r.jsx("h1",{className:"text-white text-[28px] font-bold",children:e(T.MICROAGENT_MANAGEMENT$DESCRIPTION)}),r.jsxs("p",{className:"text-white text-sm font-normal leading-[20px] pt-2",children:[e(T.MICROAGENT_MANAGEMENT$USE_MICROAGENTS),r.jsx("a",{href:Be.MICROAGENTS.MICROAGENTS_OVERVIEW,target:"_blank",rel:"noopener noreferrer",children:r.jsx(Vo,{className:"inline-block ml-1"})})]})]})}function xs({microagent:e,conversation:t,repository:s}){const{t:n}=q(),{selectedMicroagentItem:a}=K(C=>C.microagentManagement),c=ge(),{status:i,runtime_status:o,pr_number:l}=t??{},d=e?`.openhands/microagents/${e.name}`:"",u=y.useMemo(()=>e?Tt(new Date(e.created_at)):t?Tt(new Date(t.created_at)):"",[e,t]),h=!!(l&&l.length>0),f=y.useMemo(()=>h?n(T.COMMON$READY_FOR_REVIEW):i==="STARTING"||o==="STATUS$STARTING_RUNTIME"?n(T.COMMON$STARTING):i==="STOPPED"||o==="STATUS$STOPPED"?n(T.COMMON$STOPPED):o==="STATUS$ERROR"?n(T.MICROAGENT$STATUS_ERROR):i==="RUNNING"&&o==="STATUS$READY"?n(T.MICROAGENT$STATUS_OPENING_PR):"",[i,o,n,h]),x=e?.name??t?.title,m=y.useMemo(()=>e&&a?.microagent?a.microagent.name===e.name:t&&a?.conversation?a.conversation.conversation_id===t.conversation_id:!1,[e,t,a]),p=()=>{c(Ps(e?{microagent:e,conversation:null}:{microagent:null,conversation:t})),c(ft(s))};return r.jsx("div",{className:pe("rounded-lg bg-[#ffffff0d] border border-[#ffffff33] p-4 cursor-pointer hover:bg-[#ffffff33] hover:border-[#C9B974] transition-all duration-300",m&&"bg-[#ffffff33] border-[#C9B974]"),onClick:p,children:r.jsxs("div",{className:"flex flex-col items-start gap-2",children:[f&&r.jsx("div",{className:"px-[6px] py-[2px] text-[11px] font-medium bg-[#C9B97433] text-white rounded-2xl",children:f}),r.jsx("div",{className:"text-white text-[16px] font-semibold",children:x}),!!e&&r.jsx("div",{className:"text-white text-sm font-normal",children:d}),r.jsxs("div",{className:"text-white text-sm font-normal",children:[n(T.COMMON$CREATED_ON)," ",u]})]})})}function bs({repository:e}){const t=ge(),{t:s}=q(),n=()=>{t(Es(!0)),t(ft(e))};return r.jsx("div",{className:"flex items-center justify-center rounded-lg bg-[#ffffff0d] border border-dashed border-[#ffffff4d] p-4 hover:bg-[#ffffff33] hover:border-[#C9B974] transition-all duration-300 cursor-pointer",onClick:n,"data-testid":"learn-this-repo-trigger",children:r.jsx("span",{className:"text-[16px] font-normal text-[#8480FF]",children:s(T.MICROAGENT_MANAGEMENT$LEARN_THIS_REPO)})})}const Ko=(e,t,s=!1)=>Ns({queryKey:["repository","microagents",e,t],queryFn:()=>ht.getRepositoryMicroagents(e,t),enabled:!!e&&!!t,staleTime:s?0:1e3*60*5,gcTime:s?0:1e3*60*15}),Go=(e,t,s=20,n=!1)=>Ns({queryKey:["conversations","search",e,t,s],queryFn:()=>ht.searchConversations(e,t,s),enabled:!0,staleTime:n?0:1e3*60*5,gcTime:n?0:1e3*60*15});function Uo({repository:e}){const{selectedMicroagentItem:t}=K(g=>g.microagentManagement),s=ge(),{full_name:n}=e,[a,c]=n.split("/"),{data:i,isLoading:o,isError:l}=Ko(a,c,!0),{data:d,isLoading:u,isError:h}=Go(n,"microagent_management",1e3,!0);y.useEffect(()=>{const g=d&&d.length>0,b=t?.conversation;if(g&&b){const v=d.find(P=>P.conversation_id===b.conversation_id);v&&s(Ps({microagent:null,conversation:v}))}},[d]);const f=o||u,x=l||h;if(f)return r.jsx("div",{className:"pb-4 flex justify-center",children:r.jsx(ks,{size:"sm","data-testid":"loading-spinner"})});if(x)return r.jsx("div",{className:"pb-4",children:r.jsx(bs,{repository:e})});const m=i?.length||0,p=d?.length||0,C=m+p;return r.jsxs("div",{className:"pb-4",children:[C===0&&r.jsx(bs,{repository:e}),m>0&&i?.map(g=>r.jsx("div",{className:"pb-4 last:pb-0",children:r.jsx(xs,{microagent:g,repository:e})},g.name)),p>0&&d?.map(g=>r.jsx("div",{className:"pb-4 last:pb-0",children:r.jsx(xs,{conversation:g,repository:e})},g.conversation_id))]})}function nt({title:e,documentationUrl:t}){return r.jsx("div",{className:"flex items-center justify-center pt-10",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("h2",{className:"text-white text-sm font-medium",children:e}),r.jsx("a",{href:t,target:"_blank",rel:"noopener noreferrer",children:r.jsx(Ge,{className:"text-primary"})})]})})}function Wo({gitProvider:e}){return r.jsxs(r.Fragment,{children:[e==="github"&&r.jsx(ui,{size:14}),e==="gitlab"&&r.jsx(hi,{}),e==="bitbucket"&&r.jsx(fi,{})]})}function Ho({repository:e}){const{t}=q(),{addMicroagentModalVisible:s}=K(c=>c.microagentManagement),n=ge(),a=c=>{c.stopPropagation(),n(Ms(!s)),n(ft(e))};return r.jsx("div",{onClick:a,children:r.jsx(Bs,{tooltip:t(T.COMMON$ADD_MICROAGENT),ariaLabel:t(T.COMMON$ADD_MICROAGENT),className:"p-0 min-w-0 h-6 w-6 flex items-center justify-center bg-transparent cursor-pointer",testId:"add-microagent-button",placement:"bottom",children:r.jsx(mi,{width:22,height:22})})})}function zo({repository:e}){return r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(Wo,{gitProvider:e.git_provider}),r.jsx(Bs,{tooltip:e.full_name,ariaLabel:e.full_name,className:"text-white text-base font-normal bg-transparent p-0 min-w-0 h-auto cursor-pointer truncate max-w-[232px]",testId:"repository-name-tooltip",placement:"bottom",children:r.jsx("span",{children:e.full_name})})]}),r.jsx(Ho,{repository:e})]})}function it({repositories:e,tabType:t}){const{t:s}=q(),[n,a]=y.useState(""),c=e.length,i=y.useMemo(()=>{if(!n.trim())return e;const o=At(n);return e.filter(l=>At(l.full_name).includes(o))},[e,n]);if(c===0){if(t==="personal")return r.jsx(nt,{title:s(T.MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_USER_LEVEL_MICROAGENTS),documentationUrl:Be.MICROAGENTS.MICROAGENTS_OVERVIEW});if(t==="repositories")return r.jsx(nt,{title:s(T.MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_MICROAGENTS),documentationUrl:Be.MICROAGENTS.MICROAGENTS_OVERVIEW});if(t==="organizations")return r.jsx(nt,{title:s(T.MICROAGENT_MANAGEMENT$YOU_DO_NOT_HAVE_ORGANIZATION_LEVEL_MICROAGENTS),documentationUrl:Be.MICROAGENTS.ORGANIZATION_AND_USER_MICROAGENTS})}return r.jsxs("div",{className:"flex flex-col gap-4 w-full",children:[r.jsxs("div",{className:"flex flex-col gap-2 w-full",children:[r.jsx("label",{htmlFor:"repository-search",className:"sr-only",children:s(T.COMMON$SEARCH_REPOSITORIES)}),r.jsx("input",{id:"repository-search",name:"repository-search",type:"text",placeholder:`${s(T.COMMON$SEARCH_REPOSITORIES)}...`,value:n,onChange:o=>a(o.target.value),className:pe("bg-tertiary border border-[#717888] bg-[#454545] w-full rounded-sm p-2 placeholder:italic placeholder:text-tertiary-alt","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})]}),r.jsx(To,{variant:"splitted",className:"w-full px-0 gap-3",itemClasses:{base:"shadow-none bg-transparent border border-[#ffffff40] rounded-[6px] cursor-pointer",trigger:"cursor-pointer gap-1"},selectionMode:"multiple",children:i.map(o=>r.jsx(Eo,{"aria-label":o.full_name,title:r.jsx(zo,{repository:o}),children:r.jsx(Uo,{repository:o})},o.id))})]})}function qo(){const{t:e}=q(),{repositories:t,personalRepositories:s,organizationRepositories:n}=K(a=>a.microagentManagement);return r.jsx("div",{className:"flex w-full flex-col",children:r.jsxs(ko,{"aria-label":"Options",classNames:{base:"py-6",tabList:"w-full bg-transparent border border-[#ffffff40] rounded-[6px]",tab:"px-2 h-[22px]",tabContent:"text-white text-[12px] font-normal",panel:"p-0",cursor:"bg-[#C9B97480] rounded-sm"},children:[r.jsx(st,{title:e(T.COMMON$PERSONAL),children:r.jsx(it,{repositories:s,tabType:"personal"})},"personal"),r.jsx(st,{title:e(T.COMMON$REPOSITORIES),children:r.jsx(it,{repositories:t,tabType:"repositories"})},"repositories"),r.jsx(st,{title:e(T.COMMON$ORGANIZATIONS),children:r.jsx(it,{repositories:n,tabType:"organizations"})},"organizations")]})})}function ys({isSmallerScreen:e=!1}){const t=ge(),{t:s}=q(),{data:n,isLoading:a}=pi();return y.useEffect(()=>{if(n){const c=[],i=[],o=[];n.forEach(l=>{const d=l.full_name.endsWith("/.openhands");l.owner_type==="user"&&d?c.push(l):l.owner_type==="organization"&&d?i.push(l):o.push(l)}),t(In(c)),t(Dn(i)),t(On(o))}},[n,t]),r.jsxs("div",{className:pe("w-[418px] h-full max-h-full overflow-y-auto overflow-x-hidden border-r border-[#525252] bg-[#24272E] rounded-tl-lg rounded-bl-lg py-10 px-6 flex flex-col",e&&"w-full border-none"),children:[r.jsx(Fo,{}),a?r.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 flex-1",children:[r.jsx(ks,{size:"sm"}),r.jsx("span",{className:"text-sm text-white",children:s("HOME$LOADING_REPOSITORIES")})]}):r.jsx(qo,{})]})}function vs(){const{t:e}=q();return r.jsxs("div",{className:"flex-1 flex flex-col h-full items-center justify-center",children:[r.jsx("div",{className:"text-[#F9FBFE] text-xl font-bold pb-4",children:e(T.MICROAGENT_MANAGEMENT$READY_TO_ADD_MICROAGENT)}),r.jsx("div",{className:"text-white text-sm font-normal text-center max-w-[455px]",children:e(T.MICROAGENT_MANAGEMENT$OPENHANDS_CAN_LEARN_ABOUT_REPOSITORIES)})]})}function bt({size:e="medium",className:t}){const n={small:"w-3 h-3",medium:"w-4 h-4",large:"w-5 h-5"}[e];return r.jsx("div",{"data-testid":"loader",className:pe("flex items-center justify-center",t),children:r.jsx("div",{className:pe("loader rounded-full",n)})})}function Yo(){const{t:e}=q(),{selectedMicroagentItem:t}=K(a=>a.microagentManagement),{conversation:s}=t??{},{conversation_id:n}=s??{};return n?r.jsxs("div",{className:"flex-1 flex flex-col h-full items-center justify-center",children:[r.jsxs("div",{className:"text-[#ffffff99] text-[22px] font-semibold pb-2",children:[e(T.COMMON$WORKING_ON_IT),"!"]}),r.jsx("div",{className:"text-[#ffffff99] text-[18px] font-normal text-center max-w-[518px] pb-[22px]",children:e(T.MICROAGENT_MANAGEMENT$WE_ARE_WORKING_ON_IT)}),r.jsx(bt,{size:"small",className:"pb-[22px]"}),r.jsx("a",{href:`/conversations/${n}`,target:"_blank",rel:"noopener noreferrer",children:r.jsx(se,{type:"button",variant:"secondary",testId:"view-conversation-button",children:e(T.MICROAGENT$VIEW_CONVERSATION)})})]}):null}function Xo(){const{t:e}=q(),{selectedMicroagentItem:t}=K(o=>o.microagentManagement),{conversation:s}=t??{},{conversation_id:n,selected_repository:a,git_provider:c,pr_number:i}=s??{};return n?r.jsxs("div",{className:"flex-1 flex flex-col h-full items-center justify-center",children:[r.jsx("div",{className:"text-[#ffffff99] text-[22px] font-bold pb-[22px] text-center max-w-[455px]",children:e(T.MICROAGENT_MANAGEMENT$YOUR_MICROAGENT_IS_READY)}),r.jsxs("div",{className:"flex gap-[22px]",children:[r.jsx("a",{href:`/conversations/${n}`,target:"_blank",rel:"noopener noreferrer",children:r.jsx(se,{type:"button",variant:"secondary",testId:"view-conversation-button",children:e(T.MICROAGENT$VIEW_CONVERSATION)})}),r.jsx("a",{href:a&&c&&i&&i.length>0?_n(i[0],c,a):"/#",target:"_blank",rel:"noopener noreferrer",children:r.jsx(se,{type:"button",variant:"primary",testId:"view-conversation-button",children:`${e(T.COMMON$REVIEW_PR_IN)} ${He(c)}`})})]})]}):null}function Qo(){const{t:e}=q(),t=ge(),{selectedMicroagentItem:s}=K(o=>o.microagentManagement),{selectedRepository:n}=K(o=>o.microagentManagement),{microagent:a}=s??{};if(!a||!n)return null;const c=Ln(n.git_provider,n.full_name,a.path),i=()=>{t(As(!0))};return r.jsxs("div",{className:"flex items-center justify-between pb-2",children:[r.jsx("span",{className:"text-sm text-[#ffffff99]",children:n.full_name}),r.jsxs("div",{className:"flex items-center justify-end gap-2",children:[r.jsx("a",{href:c,target:"_blank",rel:"noopener noreferrer",children:r.jsx(se,{type:"button",variant:"secondary",testId:"edit-in-git-button",className:"py-1 px-2",children:`${e(T.COMMON$EDIT_IN)} ${He(n.git_provider)}`})}),r.jsx(se,{type:"button",variant:"primary",onClick:i,testId:"learn-button",className:"py-1 px-2",children:e(T.COMMON$LEARN_SOMETHING_NEW)})]})]})}function Zo(){const{selectedMicroagentItem:e}=K(c=>c.microagentManagement),{selectedRepository:t}=K(c=>c.microagentManagement),{microagent:s}=e??{},n=()=>s?!s.triggers||s.triggers.length===0?s.content:`
  ${`
  ---

  triggers:
  ${s.triggers.map(i=>` - ${i}`).join(`
`)}

  ---
  `}

  ${s.content}
  `:"";if(!s||!t)return null;const a=n();return r.jsx("div",{className:"w-full h-full p-6 bg-[#ffffff1a] rounded-2xl text-white text-sm",children:r.jsx(Wi,{components:{code:Vi,ul:Bi,ol:ki,a:$i,p:Hi},remarkPlugins:[_i,Li],children:a})})}function Jo(){const{selectedMicroagentItem:e}=K(n=>n.microagentManagement),{selectedRepository:t}=K(n=>n.microagentManagement),{microagent:s}=e??{};return!s||!t?null:r.jsxs("div",{className:"flex flex-col w-full h-full p-6 overflow-auto",children:[r.jsx(Qo,{}),r.jsx("span",{className:"text-white text-2xl font-medium pb-2",children:s.name}),r.jsx("span",{className:"text-white text-lg font-medium pb-6",children:s.path}),r.jsx("div",{className:"flex-1",children:r.jsx(Zo,{})})]})}function er(){const{t:e}=q(),{selectedMicroagentItem:t}=K(a=>a.microagentManagement),{conversation:s}=t??{},{conversation_id:n}=s??{};return n?r.jsxs("div",{className:"flex-1 flex flex-col h-full items-center justify-center",children:[r.jsx("div",{className:"text-[#ffffff99] text-[22px] font-bold pb-[22px] text-center max-w-[455px]",children:e(T.MICROAGENT_MANAGEMENT$ERROR)}),r.jsx(bt,{size:"small",className:"pb-[22px]"}),r.jsx("a",{href:`/conversations/${n}`,target:"_blank",rel:"noopener noreferrer",children:r.jsx(se,{type:"button",variant:"secondary",testId:"view-conversation-button",children:e(T.MICROAGENT$VIEW_CONVERSATION)})})]}):null}function tr(){const{t:e}=q(),{selectedMicroagentItem:t}=K(a=>a.microagentManagement),{conversation:s}=t??{},{conversation_id:n}=s??{};return n?r.jsxs("div",{className:"flex-1 flex flex-col h-full items-center justify-center",children:[r.jsx("div",{className:"text-[#ffffff99] text-[22px] font-bold pb-[22px] text-center max-w-[455px]",children:e(T.MICROAGENT_MANAGEMENT$CONVERSATION_STOPPED)}),r.jsx(bt,{size:"small",className:"pb-[22px]"}),r.jsx("a",{href:`/conversations/${n}`,target:"_blank",rel:"noopener noreferrer",children:r.jsx(se,{type:"button",variant:"secondary",testId:"view-conversation-button",children:e(T.MICROAGENT$VIEW_CONVERSATION)})})]}):null}function Cs(){const{selectedMicroagentItem:e}=K(n=>n.microagentManagement),{microagent:t,conversation:s}=e??{};if(t)return r.jsx(Jo,{});if(s){if(s.pr_number&&s.pr_number.length>0)return r.jsx(Xo,{});const n=s.status==="STARTING"||s.runtime_status==="STATUS$STARTING_RUNTIME",a=s.status==="RUNNING"&&s.runtime_status==="STATUS$READY";return n||a?r.jsx(Yo,{}):s.runtime_status==="STATUS$ERROR"?r.jsx(er,{}):s.status==="STOPPED"||s.runtime_status==="STATUS$STOPPED"?r.jsx(tr,{}):r.jsx(vs,{})}return r.jsx(vs,{})}function sr({onConfirm:e,onCancel:t,isLoading:s=!1,isUpdate:n=!1}){const{t:a}=q(),[c,i]=y.useState([]),[o,l]=y.useState(""),[d,u]=y.useState(null),{selectedRepository:h}=K(M=>M.microagentManagement),{selectedMicroagentItem:f}=K(M=>M.microagentManagement),{microagent:x}=f??{},m=y.useRef(!1);y.useEffect(()=>{n&&x&&(l(x.content),i(x.triggers||[]))},[n,x]);const{data:p,isLoading:C,isError:g}=Vs(h?.full_name||null),b=p?.map(M=>({key:M.name,label:M.name}));y.useEffect(()=>{if(p&&p.length>0&&!d&&!C){const M=p.find(I=>I.name==="main"),O=p.find(I=>I.name==="master");M?u(M):O&&u(O)}},[p,C,d]);const v=y.useMemo(()=>n?a(T.MICROAGENT_MANAGEMENT$UPDATE_MICROAGENT):h?`${a(T.MICROAGENT_MANAGEMENT$ADD_A_MICROAGENT_TO)} ${h.full_name}`:a(T.MICROAGENT_MANAGEMENT$ADD_A_MICROAGENT),[n,h,a]),P=y.useMemo(()=>a(n?T.MICROAGENT_MANAGEMENT$UPDATE_MICROAGENT_MODAL_DESCRIPTION:T.MICROAGENT_MANAGEMENT$ADD_MICROAGENT_MODAL_DESCRIPTION),[n,a]),E=M=>{M.preventDefault(),o.trim()&&e({query:o.trim(),triggers:c,selectedBranch:d?.name||"",microagentPath:x?.path||""})},R=()=>{o.trim()&&e({query:o.trim(),triggers:c,selectedBranch:d?.name||"",microagentPath:x?.path||""})},L=M=>{const O=p?.find(I=>I.name===M);u(O||null),m.current=!1},S=M=>{M===""||M.trim()===""?(u(null),m.current=!0):m.current=!1},V=()=>h?C?r.jsx(Fs,{wrapperClassName:"max-w-full w-full"}):g?r.jsx(Ks,{wrapperClassName:"max-w-full w-full"}):r.jsx(Ue,{items:b||[],onSelectionChange:L,onInputChange:S,isDisabled:!1,selectedKey:d?.name,wrapperClassName:"max-w-full w-full",label:a(T.REPOSITORY$SELECT_BRANCH)}):r.jsx(Ue,{items:[],onSelectionChange:()=>{},onInputChange:()=>{},isDisabled:!0,wrapperClassName:"max-w-full w-full",label:a(T.REPOSITORY$SELECT_BRANCH)});return r.jsx(Zs,{onClose:t,children:r.jsxs(Ts,{className:"items-start rounded-[12px] p-6 min-w-[611px]",children:[r.jsxs("div",{className:"flex flex-col gap-2 w-full",children:[r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("h2",{className:"text-white text-xl font-medium",children:v}),r.jsx("a",{href:"https://docs.all-hands.dev/usage/prompting/microagents-overview#microagents-overview",target:"_blank",rel:"noopener noreferrer",children:r.jsx(Ge,{className:"text-primary"})})]}),r.jsx("button",{type:"button",onClick:t,className:"cursor-pointer",children:r.jsx(Qs,{width:24,height:24,color:"#F9FBFE"})})]}),r.jsx("span",{className:"text-white text-sm font-normal",children:P})]}),r.jsxs("form",{"data-testid":"add-microagent-modal",onSubmit:E,className:"flex flex-col gap-6 w-full",children:[V(),r.jsxs("label",{htmlFor:"query-input",className:"flex flex-col gap-2 w-full text-sm font-normal",children:[a(T.MICROAGENT_MANAGEMENT$WHAT_TO_DO),r.jsx("textarea",{required:!0,"data-testid":"query-input",name:"query-input",value:o,onChange:M=>l(M.target.value),placeholder:a(T.MICROAGENT_MANAGEMENT$DESCRIBE_WHAT_TO_DO),rows:6,className:pe("bg-tertiary border border-[#717888] bg-[#454545] w-full rounded-sm p-2 placeholder:italic placeholder:text-tertiary-alt resize-none","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})]}),r.jsxs("label",{htmlFor:"trigger-input",className:"flex flex-col gap-2.5 w-full text-sm",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[a(T.MICROAGENT_MANAGEMENT$ADD_TRIGGERS),r.jsx("a",{href:"https://docs.all-hands.dev/usage/prompting/microagents-keyword",target:"_blank",rel:"noopener noreferrer",children:r.jsx(Ge,{className:"text-primary"})})]}),r.jsx(Fi,{name:"trigger-input",value:c,placeholder:a("MICROAGENT$TYPE_TRIGGER_SPACE"),onChange:i}),r.jsx("span",{className:"text-xs text-[#ffffff80] font-normal",children:a(T.MICROAGENT_MANAGEMENT$HELP_TEXT_DESCRIBING_VALID_TRIGGERS)})]})]}),r.jsxs("div",{className:"flex items-center justify-end gap-2 w-full",onClick:M=>M.stopPropagation(),children:[r.jsx(se,{type:"button",variant:"secondary",onClick:t,testId:"cancel-button",children:a(T.BUTTON$CANCEL)}),r.jsx(se,{type:"button",variant:"primary",onClick:R,testId:"confirm-button",isDisabled:!o.trim()||s||C||!d||g,children:a(s||C?T.HOME$LOADING:T.MICROAGENT$LAUNCH)})]})]})})}function nr({onConfirm:e,onCancel:t,isLoading:s=!1}){const{t:n}=q(),[a,c]=y.useState(""),[i,o]=y.useState(null),{selectedRepository:l}=K(v=>v.microagentManagement),d=y.useRef(!1),{data:u,isLoading:h,isError:f}=Vs(l?.full_name||null),x=u?.map(v=>({key:v.name,label:v.name}));y.useEffect(()=>{if(u&&u.length>0&&!i&&!h){const v=u.find(E=>E.name==="main"),P=u.find(E=>E.name==="master");v?o(v):P&&o(P)}},[u,h,i]);const m=v=>{v.preventDefault(),a.trim()&&e({query:a.trim(),selectedBranch:i?.name||""})},p=()=>{a.trim()&&e({query:a.trim(),selectedBranch:i?.name||""})},C=v=>{const P=u?.find(E=>E.name===v);o(P||null),d.current=!1},g=v=>{v===""||v.trim()===""?(o(null),d.current=!0):d.current=!1},b=()=>l?h?r.jsx(Fs,{wrapperClassName:"max-w-full w-full"}):f?r.jsx(Ks,{wrapperClassName:"max-w-full w-full"}):r.jsx(Ue,{items:x||[],onSelectionChange:C,onInputChange:g,isDisabled:!1,selectedKey:i?.name,wrapperClassName:"max-w-full w-full",label:n(T.REPOSITORY$SELECT_BRANCH)}):r.jsx(Ue,{items:[],onSelectionChange:()=>{},onInputChange:()=>{},isDisabled:!0,wrapperClassName:"max-w-full w-full",label:n(T.REPOSITORY$SELECT_BRANCH)});return r.jsx(Zs,{onClose:t,children:r.jsxs(Ts,{className:"items-start rounded-[12px] p-6 min-w-[611px]","data-testid":"learn-this-repo-modal",children:[r.jsxs("div",{className:"flex flex-col gap-2 w-full",children:[r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("h2",{className:"text-white text-xl font-medium","data-testid":"modal-title",children:n(T.MICROAGENT_MANAGEMENT$LEARN_THIS_REPO_MODAL_TITLE)}),r.jsx("a",{href:"https://docs.all-hands.dev/usage/prompting/microagents-overview#microagents-overview",target:"_blank",rel:"noopener noreferrer","data-testid":"modal-info-link",children:r.jsx(Ge,{className:"text-primary"})})]}),r.jsx("button",{type:"button",onClick:t,className:"cursor-pointer","data-testid":"modal-close-button",children:r.jsx(Qs,{width:24,height:24,color:"#F9FBFE"})})]}),r.jsx("span",{className:"text-white text-sm font-normal","data-testid":"modal-description",children:n(T.MICROAGENT_MANAGEMENT$LEARN_THIS_REPO_MODAL_DESCRIPTION)})]}),r.jsxs("form",{"data-testid":"learn-this-repo-form",onSubmit:m,className:"flex flex-col gap-6 w-full",children:[r.jsx("div",{"data-testid":"branch-selector-container",children:b()}),r.jsxs("label",{htmlFor:"query-input",className:"flex flex-col gap-2 w-full text-sm font-normal",children:[n(T.MICROAGENT_MANAGEMENT$WHAT_YOU_WOULD_LIKE_TO_KNOW_ABOUT_THIS_REPO),r.jsx("textarea",{required:!0,"data-testid":"query-input",name:"query-input",value:a,onChange:v=>c(v.target.value),placeholder:n(T.MICROAGENT_MANAGEMENT$DESCRIBE_WHAT_TO_KNOW_ABOUT_THIS_REPO),rows:6,className:pe("bg-tertiary border border-[#717888] bg-[#454545] w-full rounded-sm p-2 placeholder:italic placeholder:text-tertiary-alt resize-none","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})]})]}),r.jsxs("div",{className:"flex items-center justify-end gap-2 w-full",onClick:v=>v.stopPropagation(),"data-testid":"modal-actions",children:[r.jsx(se,{type:"button",variant:"secondary",onClick:t,testId:"cancel-button",children:n(T.BUTTON$CANCEL)}),r.jsx(se,{type:"button",variant:"primary",onClick:p,testId:"confirm-button",isDisabled:!a.trim()||s||h||!i||f,children:n(s||h?T.HOME$LOADING:T.MICROAGENT$LAUNCH)})]})]})})}const ir=e=>typeof e=="object"&&e!==null&&"error"in e&&e.error===!0,ar=e=>dt(e)&&Js(e)&&e.extras.agent_state===zi.ERROR,or=e=>{const t=ir(e)||ar(e),s=dt(e)&&Js(e),n=dt(e)&&qi(e);return t||s||n},rr=(e,t,s,n,a)=>`Create a microagent for the repository ${e} by following the steps below:

- Step 1: Create a markdown file inside the .openhands/microagents folder with the name of the microagent (The microagent must be created in the .openhands/microagents folder and should be able to perform the described task when triggered).

- This is the instructions about what the microagent should do: ${t.query}

${t.triggers&&t.triggers.length>0?`
- This is the triggers of the microagent: ${t.triggers.join(", ")}
`:"- Please be noted that the microagent doesn't have any triggers."}

- Step 2: Create a new branch for the repository ${e}, must avoid duplicated branches.

- Step 3: Please push the changes to your branch on ${He(a)} and create a ${s}. Please create a meaningful branch name that describes the changes. If a ${s} template exists in the repository, please follow it when creating the ${n} description.
`,lr=(e,t,s,n,a)=>`Update the microagent for the repository ${e} by following the steps below:


- Step 1: Update the microagent. This is the path of the microagent: ${t.microagentPath} (The updated microagent must be in the .openhands/microagents folder and should be able to perform the described task when triggered).

- This is the updated instructions about what the microagent should do: ${t.query}

${t.triggers&&t.triggers.length>0?`
- This is the triggers of the microagent: ${t.triggers.join(", ")}
`:"- Please be noted that the microagent doesn't have any triggers."}

- Step 2: Create a new branch for the repository ${e}, must avoid duplicated branches.

- Step 3: Please push the changes to your branch on ${He(a)} and create a ${s}. Please create a meaningful branch name that describes the changes. If a ${s} template exists in the repository, please follow it when creating the ${n} description.
`;function cr(){const[e,t]=y.useState(window.innerWidth),{addMicroagentModalVisible:s,updateMicroagentModalVisible:n,selectedRepository:a,learnThisRepoModalVisible:c}=K(g=>g.microagentManagement),i=ge(),{createConversationAndSubscribe:o,isPending:l}=Ki();function d(){t(window.innerWidth)}y.useEffect(()=>(window.addEventListener("resize",d),()=>{window.removeEventListener("resize",d)}),[]);const u=(g=!1)=>{i(g?As(!1):Ms(!1))},h=Ee.useCallback(g=>{Fe.invalidateQueries({queryKey:["conversations","search",g,"microagent_management"]})},[]),f=Ee.useCallback(g=>{const b=a&&typeof a=="object"?a.full_name:"";or(g)&&h(b)},[h,a]),x=(g,b=!1)=>{if(!a||typeof a!="object")return;const v=a,P=v.full_name,E=v.git_provider,R=E==="gitlab",L=kn(R),S=$n(R),V=b?lr(P,g,L,S,E):rr(P,g,L,S,E),M={repo:P,git_provider:E,title:g.query};o({query:V,conversationInstructions:V,repository:{name:P,branch:g.selectedBranch,gitProvider:E},createMicroagent:M,onSuccessCallback:()=>{h(P);const[O,I]=P.split("/");Fe.invalidateQueries({queryKey:["repository-microagents",O,I]}),u(b)},onEventCallback:O=>{f(O)}})},m=()=>{i(Es(!1))},p=g=>{if(!a||typeof a!="object")return;const b=a,v=b.full_name,P=b.git_provider;o({query:g.query,conversationInstructions:g.query,repository:{name:v,branch:g.selectedBranch,gitProvider:P},onSuccessCallback:()=>{m()}})},C=()=>r.jsxs(r.Fragment,{children:[(s||n)&&r.jsx(sr,{onConfirm:g=>x(g,n),onCancel:()=>u(n),isLoading:l,isUpdate:n}),c&&r.jsx(nr,{onCancel:m,onConfirm:p,isLoading:l})]});return e<1024?r.jsxs("div",{className:"w-full h-full flex flex-col gap-6",children:[r.jsx("div",{className:"w-full rounded-lg border border-[#525252] bg-[#24272E] max-h-[494px] min-h-[494px]",children:r.jsx(ys,{isSmallerScreen:!0})}),r.jsx("div",{className:"w-full rounded-lg border border-[#525252] bg-[#24272E] flex-1 min-h-[494px]",children:r.jsx(Cs,{})}),C()]}):r.jsxs("div",{className:"w-full h-full flex rounded-lg border border-[#525252] bg-[#24272E] overflow-hidden",children:[r.jsx(ys,{}),r.jsx("div",{className:"flex-1",children:r.jsx(Cs,{})}),C()]})}const nl=async({request:e})=>{const t=new URL(e.url),{pathname:s}=t;let n=Fe.getQueryData(["config"]);return n||(n=await ht.getConfig(),Fe.setQueryData(["config"],n)),n?.FEATURE_FLAGS.HIDE_MICROAGENT_MANAGEMENT&&s==="/microagent-management"?jn("/"):null};function dr(){return r.jsx(Gi,{children:r.jsx(Ui,{children:r.jsx(cr,{})})})}const il=wn(dr);export{nl as clientLoader,il as default};
