import{r as e}from"./chunk-C37GKA54-CBbYr_fP.js";const l=t=>e.createElement("svg",{width:27,height:26,viewBox:"0 0 27 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t},e.createElement("g",{clipPath:"url(#clip0_40000158_408)"},e.createElement("path",{d:"M13.5381 1.83496C7.32618 1.83496 2.29074 6.8704 2.29074 13.0823C2.29074 19.2942 7.32618 24.3297 13.5381 24.3297C19.75 24.3297 24.7855 19.2942 24.7855 13.0823C24.7855 6.8704 19.75 1.83496 13.5381 1.83496Z",stroke:"currentColor",strokeWidth:1.8,strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M20.5007 12H14.5007V6C14.5007 5.73478 14.3953 5.48043 14.2078 5.29289C14.0203 5.10536 13.7659 5 13.5007 5C13.2355 5 12.9811 5.10536 12.7936 5.29289C12.6061 5.48043 12.5007 5.73478 12.5007 6V12H6.5007C6.23549 12 5.98113 12.1054 5.7936 12.2929C5.60606 12.4804 5.5007 12.7348 5.5007 13C5.5007 13.2652 5.60606 13.5196 5.7936 13.7071C5.98113 13.8946 6.23549 14 6.5007 14H12.5007V20C12.5007 20.2652 12.6061 20.5196 12.7936 20.7071C12.9811 20.8946 13.2355 21 13.5007 21C13.7659 21 14.0203 20.8946 14.2078 20.7071C14.3953 20.5196 14.5007 20.2652 14.5007 20V14H20.5007C20.7659 14 21.0203 13.8946 21.2078 13.7071C21.3953 13.5196 21.5007 13.2652 21.5007 13C21.5007 12.7348 21.3953 12.4804 21.2078 12.2929C21.0203 12.1054 20.7659 12 20.5007 12Z",fill:"currentColor"})),e.createElement("defs",null,e.createElement("clipPath",{id:"clip0_40000158_408"},e.createElement("rect",{width:26,height:26,fill:"white",transform:"translate(0.5)"}))));export{l as S};
