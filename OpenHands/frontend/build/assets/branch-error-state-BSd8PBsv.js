import{u as n}from"./useQuery-Cu2nkJ8V.js";import{O as o}from"./open-hands-Ce72Fmtl.js";import{j as r}from"./chunk-C37GKA54-CBbYr_fP.js";import{S as l}from"./settings-dropdown-input-Did5iUTK.js";import{I as x}from"./declaration-xyc84-tJ.js";import{c as t}from"./utils-KsbccAr1.js";import{u as a}from"./useTranslation-BG59QWH_.js";import{s as h}from"./chunk-S6H5EOGR-Bwn62IP6.js";const N=()=>n({queryKey:["repositories"],queryFn:o.retrieveUserGitRepositories,staleTime:1e3*60*5,gcTime:1e3*60*15}),O=e=>n({queryKey:["repository",e,"branches"],queryFn:async()=>e?o.getRepositoryBranches(e):[],enabled:!!e,staleTime:1e3*60*5}),j=e=>e.trim().replace(/https?:\/\//,"").replace(/github.com\//,"").replace(/\.git$/,"").toLowerCase();function S({items:e,onSelectionChange:s,onInputChange:i,isDisabled:c,selectedKey:m,wrapperClassName:p,label:d}){const{t:u}=a();return r.jsx(l,{testId:"branch-dropdown",name:"branch-dropdown",placeholder:u(x.REPOSITORY$SELECT_BRANCH),items:e,wrapperClassName:t("max-w-[500px]",p),onSelectionChange:s,onInputChange:i,isDisabled:c,selectedKey:m,label:d})}function I({wrapperClassName:e}){const{t:s}=a();return r.jsxs("div",{"data-testid":"branch-dropdown-loading",className:t("flex items-center gap-2 max-w-[500px] h-10 px-3 bg-tertiary border border-[#717888] rounded-sm",e),children:[r.jsx(h,{size:"sm"}),r.jsx("span",{className:"text-sm",children:s("HOME$LOADING_BRANCHES")})]})}function T({wrapperClassName:e}){const{t:s}=a();return r.jsx("div",{"data-testid":"branch-dropdown-error",className:t("flex items-center gap-2 max-w-[500px] h-10 px-3 bg-tertiary border border-[#717888] rounded-sm text-red-500",e),children:r.jsx("span",{className:"text-sm",children:s("HOME$FAILED_TO_LOAD_BRANCHES")})})}export{S as B,O as a,I as b,T as c,j as s,N as u};
