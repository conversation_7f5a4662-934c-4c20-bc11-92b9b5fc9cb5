import{g as U,r as x}from"./chunk-C37GKA54-CBbYr_fP.js";import{w as R,i as L,a as F,b as I,I as Y}from"./useTranslation-BG59QWH_.js";import{g as q,a as Z}from"./i18nInstance-DBIXdvxg.js";var P,H;function G(){return H||(H=1,P={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),P}var Q=G();const K=U(Q);var ee=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function W(e){var t={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},n=e.match(/<\/?([^\s]+?)[/\s>]/);if(n&&(t.name=n[1],(K[n[1]]||e.charAt(e.length-2)==="/")&&(t.voidElement=!0),t.name.startsWith("!--"))){var r=e.indexOf("-->");return{type:"comment",comment:r!==-1?e.slice(4,r):""}}for(var i=new RegExp(ee),a=null;(a=i.exec(e))!==null;)if(a[0].trim())if(a[1]){var u=a[1].trim(),p=[u,""];u.indexOf("=")>-1&&(p=u.split("=")),t.attrs[p[0]]=p[1],i.lastIndex--}else a[2]&&(t.attrs[a[2]]=a[3].trim().substring(1,a[3].length-1));return t}var te=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,ne=/^\s*$/,re=Object.create(null);function z(e,t){switch(t.type){case"text":return e+t.content;case"tag":return e+="<"+t.name+(t.attrs?function(n){var r=[];for(var i in n)r.push(i+'="'+n[i]+'"');return r.length?" "+r.join(" "):""}(t.attrs):"")+(t.voidElement?"/>":">"),t.voidElement?e:e+t.children.reduce(z,"")+"</"+t.name+">";case"comment":return e+"<!--"+t.comment+"-->"}}var se={parse:function(e,t){t||(t={}),t.components||(t.components=re);var n,r=[],i=[],a=-1,u=!1;if(e.indexOf("<")!==0){var p=e.indexOf("<");r.push({type:"text",content:p===-1?e:e.substring(0,p)})}return e.replace(te,function(h,b){if(u){if(h!=="</"+n.name+">")return;u=!1}var f,g=h.charAt(1)!=="/",E=h.startsWith("<!--"),d=b+h.length,o=e.charAt(d);if(E){var O=W(h);return a<0?(r.push(O),r):((f=i[a]).children.push(O),r)}if(g&&(a++,(n=W(h)).type==="tag"&&t.components[n.name]&&(n.type="component",u=!0),n.voidElement||u||!o||o==="<"||n.children.push({type:"text",content:e.slice(d,e.indexOf("<",d))}),a===0&&r.push(n),(f=i[a-1])&&f.children.push(n),i[a]=n),(!g||n.voidElement)&&(a>-1&&(n.voidElement||n.name===h.slice(2,-1))&&(a--,n=a===-1?r:i[a]),!u&&o!=="<"&&o)){f=a===-1?r:i[a].children;var v=e.indexOf("<",d),l=e.slice(d,v===-1?void 0:v);ne.test(l)&&(l=" "),(v>-1&&a+f.length>=0||l!==" ")&&f.push({type:"text",content:l})}}),r},stringify:function(e){return e.reduce(function(t,n){return t+z("",n)},"")}};const w=(e,t)=>{if(!e)return!1;const n=e.props?.children??e.children;return t?n.length>0:!!n},B=e=>{if(!e)return[];const t=e.props?.children??e.children;return e.props?.i18nIsDynamicList?S(t):t},ae=e=>Array.isArray(e)&&e.every(x.isValidElement),S=e=>Array.isArray(e)?e:[e],ie=(e,t)=>{const n={...t};return n.props=Object.assign(e.props,t.props),n},J=(e,t,n,r)=>{if(!e)return"";let i="";const a=S(e),u=t?.transSupportBasicHtmlNodes?t.transKeepBasicHtmlNodesFor??[]:[];return a.forEach((p,h)=>{if(L(p)){i+=`${p}`;return}if(x.isValidElement(p)){const{props:b,type:f}=p,g=Object.keys(b).length,E=u.indexOf(f)>-1,d=b.children;if(!d&&E&&!g){i+=`<${f}/>`;return}if(!d&&(!E||g)||b.i18nIsDynamicList){i+=`<${h}></${h}>`;return}if(E&&g===1&&L(d)){i+=`<${f}>${d}</${f}>`;return}const o=J(d,t,n,r);i+=`<${h}>${o}</${h}>`;return}if(p===null){F(n,"TRANS_NULL_VALUE","Passed in a null value as child",{i18nKey:r});return}if(I(p)){const{format:b,...f}=p,g=Object.keys(f);if(g.length===1){const E=b?`${g[0]}, ${b}`:g[0];i+=`{{${E}}}`;return}F(n,"TRANS_INVALID_OBJ","Invalid child - Object should only have keys {{ value, format }} (format is optional).",{i18nKey:r,child:p});return}F(n,"TRANS_INVALID_VAR","Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.",{i18nKey:r,child:p})}),i},oe=(e,t,n,r,i,a,u)=>{if(n==="")return[];const p=i.transKeepBasicHtmlNodesFor||[],h=n&&new RegExp(p.map(l=>`<${l}`).join("|")).test(n);if(!e&&!t&&!h&&!u)return[n];const b=t??{},f=l=>{S(l).forEach(c=>{L(c)||(w(c)?f(B(c)):I(c)&&!x.isValidElement(c)&&Object.assign(b,c))})};f(e);const g=se.parse(`<0>${n}</0>`),E={...b,...a},d=(l,T,c)=>{const A=B(l),k=O(A,T.children,c);return ae(A)&&k.length===0||l.props?.i18nIsDynamicList?A:k},o=(l,T,c,A,k)=>{l.dummy?(l.children=T,c.push(x.cloneElement(l,{key:A},k?void 0:T))):c.push(...x.Children.map([l],m=>{const s={...m.props};return delete s.i18nIsDynamicList,x.createElement(m.type,{...s,key:A,ref:m.props.ref??m.ref},k?null:T)}))},O=(l,T,c)=>{const A=S(l);return S(T).reduce((m,s,C)=>{const j=s.children?.[0]?.content&&r.services.interpolator.interpolate(s.children[0].content,E,r.language);if(s.type==="tag"){let N=A[parseInt(s.name,10)];!N&&t&&(N=t[s.name]),c.length===1&&!N&&(N=c[0][s.name]),N||(N={});const y=Object.keys(s.attrs).length!==0?ie({props:s.attrs},N):N,V=x.isValidElement(y),D=V&&w(s,!0)&&!s.voidElement,_=h&&I(y)&&y.dummy&&!V,M=I(t)&&Object.hasOwnProperty.call(t,s.name);if(L(y)){const $=r.services.interpolator.interpolate(y,E,r.language);m.push($)}else if(w(y)||D){const $=d(y,s,c);o(y,$,m,C)}else if(_){const $=O(A,s.children,c);o(y,$,m,C)}else if(Number.isNaN(parseFloat(s.name)))if(M){const $=d(y,s,c);o(y,$,m,C,s.voidElement)}else if(i.transSupportBasicHtmlNodes&&p.indexOf(s.name)>-1)if(s.voidElement)m.push(x.createElement(s.name,{key:`${s.name}-${C}`}));else{const $=O(A,s.children,c);m.push(x.createElement(s.name,{key:`${s.name}-${C}`},$))}else if(s.voidElement)m.push(`<${s.name} />`);else{const $=O(A,s.children,c);m.push(`<${s.name}>${$}</${s.name}>`)}else if(I(y)&&!V){const $=s.children[0]?j:null;$&&m.push($)}else o(y,j,m,C,s.children.length!==1||!j)}else if(s.type==="text"){const N=i.transWrapTextNodes,y=u?i.unescape(r.services.interpolator.interpolate(s.content,E,r.language)):r.services.interpolator.interpolate(s.content,E,r.language);N?m.push(x.createElement(N,{key:`${s.name}-${C}`},y)):m.push(y)}return m},[])},v=O([{dummy:!0,children:e||[]}],g,S(e||[]));return B(v[0])},X=(e,t,n)=>{const r=e.key||t,i=x.cloneElement(e,{key:r});if(!i.props||!i.props.children||n.indexOf(`${t}/>`)<0&&n.indexOf(`${t} />`)<0)return i;function a(){return x.createElement(x.Fragment,null,i)}return x.createElement(a,{key:r})},le=(e,t)=>e.map((n,r)=>X(n,r,t)),ce=(e,t)=>{const n={};return Object.keys(e).forEach(r=>{Object.assign(n,{[r]:X(e[r],r,t)})}),n},ue=(e,t,n,r)=>e?Array.isArray(e)?le(e,t):I(e)?ce(e,t):(R(n,"TRANS_INVALID_COMPONENTS",'<Trans /> "components" prop expects an object or array',{i18nKey:r}),null):null,pe=e=>!I(e)||Array.isArray(e)?!1:Object.keys(e).reduce((t,n)=>t&&Number.isNaN(Number.parseFloat(n)),!0);function fe({children:e,count:t,parent:n,i18nKey:r,context:i,tOptions:a={},values:u,defaults:p,components:h,ns:b,i18n:f,t:g,shouldUnescape:E,...d}){const o=f||q();if(!o)return R(o,"NO_I18NEXT_INSTANCE","Trans: You need to pass in an i18next instance using i18nextReactModule",{i18nKey:r}),e;const O=g||o.t.bind(o)||(_=>_),v={...Z(),...o.options?.react};let l=b||O.ns||o.options?.defaultNS;l=L(l)?[l]:l||["translation"];const T=J(e,v,o,r),c=p||T||v.transEmptyNodeValue||r,{hashTransKey:A}=v,k=r||(A?A(T||c):T||c);o.options?.interpolation?.defaultVariables&&(u=u&&Object.keys(u).length>0?{...u,...o.options.interpolation.defaultVariables}:{...o.options.interpolation.defaultVariables});const m=u||t!==void 0&&!o.options?.interpolation?.alwaysFormat||!e?a.interpolation:{interpolation:{...a.interpolation,prefix:"#$?",suffix:"?$#"}},s={...a,context:i||a.context,count:t,...u,...m,defaultValue:c,ns:l},C=k?O(k,s):c,j=ue(h,C,o,r);let N=j||e,y=null;pe(j)&&(y=j,N=e);const V=oe(N,y,C,o,v,s,E),D=n??v.defaultTransParent;return D?x.createElement(D,d,V):V}function de({children:e,count:t,parent:n,i18nKey:r,context:i,tOptions:a={},values:u,defaults:p,components:h,ns:b,i18n:f,t:g,shouldUnescape:E,...d}){const{i18n:o,defaultNS:O}=x.useContext(Y)||{},v=f||o||q(),l=g||v?.t.bind(v);return fe({children:e,count:t,parent:n,i18nKey:r,context:i,tOptions:a,values:u,defaults:p,components:h,ns:b||l?.ns||O||v?.options?.defaultNS,i18n:v,t:g,shouldUnescape:E,...d})}export{de as T};
