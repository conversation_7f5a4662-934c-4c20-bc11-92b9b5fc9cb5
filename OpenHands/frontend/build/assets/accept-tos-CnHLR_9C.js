import{j as e,w as h,b as x,c as T,R as S}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as g,B as b}from"./brand-button-3Z8FN4qR.js";import{I as s}from"./declaration-xyc84-tJ.js";import{S as j}from"./all-hands-logo-D_ipIZjH.js";import{u as l}from"./useTranslation-BG59QWH_.js";import{h as C}from"./handle-capture-consent-L0_Qc6gK.js";import{o as w}from"./open-hands-axios-CtirLpss.js";import"./mutation-B9dSlWD-.js";import"./utils-KsbccAr1.js";import"./i18nInstance-DBIXdvxg.js";import"./module-5laXsVNO.js";function O({onChange:o}){const{t}=l();return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",onChange:o}),e.jsxs("span",{children:[t(s.TOS$ACCEPT)," ",e.jsx("a",{href:"https://www.all-hands.dev/tos",target:"_blank",rel:"noopener noreferrer",className:"underline underline-offset-2 text-blue-500 hover:text-blue-700",children:t(s.TOS$TERMS)})]})]})}const k=h(function(){const{t}=l(),d=x(),[m]=T(),[c,p]=S.useState(!1),i=m.get("redirect_url")||"/",{mutate:u,isPending:a}=g({mutationFn:async()=>(C(!0),w.post("/api/accept_tos",{redirect_url:i})),onSuccess:n=>{const r=n.data.redirect_url||i;r.startsWith("http://")||r.startsWith("https://")?window.location.href=r:d(r)},onError:()=>{window.location.href="/"}}),f=()=>{c&&!a&&u()};return e.jsx("div",{className:"flex flex-col items-center justify-center h-full",children:e.jsxs("div",{className:"border border-tertiary p-8 rounded-lg max-w-md w-full flex flex-col gap-6 items-center bg-base-secondary",children:[e.jsx(j,{width:68,height:46}),e.jsxs("div",{className:"flex flex-col gap-2 w-full items-center text-center",children:[e.jsx("h1",{className:"text-2xl font-bold",children:t(s.TOS$ACCEPT_TERMS_OF_SERVICE)}),e.jsx("p",{className:"text-sm text-gray-500",children:t(s.TOS$ACCEPT_TERMS_DESCRIPTION)})]}),e.jsx(O,{onChange:()=>p(n=>!n)}),e.jsx(b,{isDisabled:!c||a,type:"button",variant:"primary",onClick:f,className:"w-full",children:t(a?s.HOME$LOADING:s.TOS$CONTINUE)})]})})});export{k as default};
