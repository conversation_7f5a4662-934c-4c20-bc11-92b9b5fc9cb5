import{r as p,R}from"./chunk-C37GKA54-CBbYr_fP.js";import{Q,a as S,b as C,e as q,c as x,d as H,s as O,f as g,w as M,g as k,u as A}from"./useQuery-Cu2nkJ8V.js";import{S as I,n as f,g as P,u as T,a as B,h as L}from"./open-hands-axios-CtirLpss.js";import{O as D}from"./open-hands-Ce72Fmtl.js";import{u as F}from"./use-conversation-id-0JHAicdF.js";import{u as z}from"./use-runtime-is-ready-CsTtc0dU.js";function v(e,s){const t=new Set(s);return e.filter(i=>!t.has(i))}function K(e,s,t){const i=e.slice(0);return i[s]=t,i}var U=class extends I{#r;#s;#i;#n;#e;#t;#o;#u;#a=[];constructor(e,s,t){super(),this.#r=e,this.#n=t,this.#i=[],this.#e=[],this.#s=[],this.setQueries(s)}onSubscribe(){this.listeners.size===1&&this.#e.forEach(e=>{e.subscribe(s=>{this.#p(e,s)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#e.forEach(e=>{e.destroy()})}setQueries(e,s){this.#i=e,this.#n=s,f.batch(()=>{const t=this.#e,i=this.#l(this.#i);this.#a=i,i.forEach(r=>r.observer.setOptions(r.defaultedQueryOptions));const n=i.map(r=>r.observer),u=n.map(r=>r.getCurrentResult()),o=n.some((r,c)=>r!==t[c]);t.length===n.length&&!o||(this.#e=n,this.#s=u,this.hasListeners()&&(v(t,n).forEach(r=>{r.destroy()}),v(n,t).forEach(r=>{r.subscribe(c=>{this.#p(r,c)})}),this.#f()))})}getCurrentResult(){return this.#s}getQueries(){return this.#e.map(e=>e.getCurrentQuery())}getObservers(){return this.#e}getOptimisticResult(e,s){const t=this.#l(e),i=t.map(n=>n.observer.getOptimisticResult(n.defaultedQueryOptions));return[i,n=>this.#h(n??i,s),()=>this.#c(i,t)]}#c(e,s){return s.map((t,i)=>{const n=e[i];return t.defaultedQueryOptions.notifyOnChangeProps?n:t.observer.trackResult(n,u=>{s.forEach(o=>{o.observer.trackProp(u)})})})}#h(e,s){return s?((!this.#t||this.#s!==this.#u||s!==this.#o)&&(this.#o=s,this.#u=this.#s,this.#t=P(this.#t,s(e))),this.#t):e}#l(e){const s=new Map(this.#e.map(i=>[i.options.queryHash,i])),t=[];return e.forEach(i=>{const n=this.#r.defaultQueryOptions(i),u=s.get(n.queryHash);u?t.push({defaultedQueryOptions:n,observer:u}):t.push({defaultedQueryOptions:n,observer:new Q(this.#r,n)})}),t}#p(e,s){const t=this.#e.indexOf(e);t!==-1&&(this.#s=K(this.#s,t,s),this.#f())}#f(){if(this.hasListeners()){const e=this.#t,s=this.#c(this.#s,this.#a),t=this.#h(s,this.#n?.combine);e!==t&&f.batch(()=>{this.listeners.forEach(i=>{i(this.#s)})})}}};function W({queries:e,...s},t){const i=T(t),n=S(),u=C(),o=p.useMemo(()=>e.map(a=>{const l=i.defaultQueryOptions(a);return l._optimisticResults=n?"isRestoring":"optimistic",l}),[e,i,n]);o.forEach(a=>{q(a),x(a,u)}),H(u);const[r]=p.useState(()=>new U(i,o,s)),[c,w,E]=r.getOptimisticResult(o,s.combine),d=!n&&s.subscribed!==!1;p.useSyncExternalStore(p.useCallback(a=>d?r.subscribe(f.batchCalls(a)):B,[r,d]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),p.useEffect(()=>{r.setQueries(o,s)},[o,s,r]);const b=c.some((a,l)=>O(o[l],a))?c.flatMap((a,l)=>{const h=o[l];if(h){const m=new Q(i,h);if(O(h,a))return g(h,m,u);M(a,n)&&g(h,m,u)}return[]}):[];if(b.length>0)throw Promise.all(b);const y=c.find((a,l)=>{const h=o[l];return h&&k({result:a,errorResetBoundary:u,throwOnError:h.throwOnError,query:i.getQueryCache().get(h.queryHash),suspense:h.suspense})});if(y?.error)throw y.error;return w(E())}const Y=()=>{const[e,s]=R.useState(null),{conversationId:t}=F(),i=z(),{data:n}=A({queryKey:[t,"hosts"],queryFn:async()=>({hosts:await D.getWebHosts(t)}),enabled:i&&!!t,initialData:{hosts:[]},meta:{disableToast:!0}}),o=W({queries:n.hosts.map(r=>({queryKey:[t,"hosts",r],queryFn:async()=>{try{return await L.get(r),r}catch{return""}},meta:{disableToast:!0}}))}).map(r=>r.data);return R.useEffect(()=>{const r=o.find(c=>c);s(r||"")},[o]),{activeHost:e}};export{Y as u};
