import{V as s}from"./index-cxP66Ws3.js";const o=(t,r=5e3,a=1e4)=>{const c=t.length/16.666666666666668*1e3*1.5;return Math.min(Math.max(c,r),a)},n={background:"#454545",border:"1px solid #717888",color:"#fff",borderRadius:"4px"},e={position:"top-right",style:n},l=t=>{const r=o(t,4e3);s.error(t,{...e,duration:r})},p=t=>{const r=o(t,5e3);s.success(t,{...e,duration:r})};export{e as T,l as a,o as c,p as d};
