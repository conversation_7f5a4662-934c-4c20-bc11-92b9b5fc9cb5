import{j as e}from"./chunk-C37GKA54-CBbYr_fP.js";import{c as s}from"./utils-KsbccAr1.js";import{O as N}from"./optional-tag-e1gRgM9y.js";function D({testId:t,name:r,label:l,type:i,defaultValue:d,value:o,placeholder:c,showOptionalTag:n,isDisabled:m,startContent:p,className:x,onChange:a,required:b,min:f,max:u,step:g,pattern:j}){return e.jsxs("label",{className:s("flex flex-col gap-2.5 w-fit",x),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[p,e.jsx("span",{className:"text-sm",children:l}),n&&e.jsx(N,{})]}),e.jsx("input",{"data-testid":t,onChange:h=>a&&a(h.target.value),name:r,disabled:m,type:i,defaultValue:d,value:o,placeholder:c,min:f,max:u,step:g,required:b,pattern:j,className:s("bg-tertiary border border-[#717888] h-10 w-full rounded-sm p-2 placeholder:italic placeholder:text-tertiary-alt","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})]})}export{D as S};
