import{j as s,r as x,w as p}from"./chunk-C37GKA54-CBbYr_fP.js";import{p as _}from"./module-5laXsVNO.js";import{u}from"./use-settings-CSlhfPqo.js";import{u as T}from"./use-save-settings-Bb4p0cGE.js";import{I as e}from"./declaration-xyc84-tJ.js";import{u as S}from"./useTranslation-BG59QWH_.js";import{B as h}from"./brand-button-3Z8FN4qR.js";import{c as g}from"./utils-KsbccAr1.js";import{T as C}from"./Trans-CI4prNur.js";import{a as j,d as I}from"./custom-toast-handlers-CR9P-jKI.js";import{r as v}from"./retrieve-axios-error-message-CYr77e_f.js";import"./useQuery-Cu2nkJ8V.js";import"./open-hands-axios-CtirLpss.js";import"./open-hands-Ce72Fmtl.js";import"./use-config-jdwF3W4-.js";import"./i18nInstance-DBIXdvxg.js";import"./mutation-B9dSlWD-.js";import"./index-cxP66Ws3.js";function y({servers:n}){const{t:a}=S();return s.jsxs("div",{children:[s.jsxs("h4",{className:"text-sm font-medium mb-2",children:[a(e.SETTINGS$MCP_SSE_SERVERS)," ",s.jsxs("span",{className:"text-gray-500",children:["(",n.length,")"]})]}),n.map((t,r)=>s.jsxs("div",{className:"mb-2 p-2 bg-base-tertiary rounded-md",children:[s.jsxs("div",{className:"text-sm",children:[s.jsxs("span",{className:"font-medium",children:[a(e.SETTINGS$MCP_URL),":"]})," ",typeof t=="string"?t:t.url]}),typeof t!="string"&&t.api_key&&s.jsxs("div",{className:"mt-1 text-sm text-gray-500",children:[s.jsxs("span",{className:"font-medium",children:[a(e.SETTINGS$MCP_API_KEY),":"]})," ",t.api_key?"Configured":a(e.SETTINGS$MCP_API_KEY_NOT_SET)]})]},`sse-${r}`))]})}function R({servers:n}){const{t:a}=S();return s.jsxs("div",{children:[s.jsxs("h4",{className:"text-sm font-medium mb-2",children:[a(e.SETTINGS$MCP_STDIO_SERVERS)," ",s.jsxs("span",{className:"text-gray-500",children:["(",n.length,")"]})]}),n.map((t,r)=>s.jsxs("div",{className:"mb-2 p-2 bg-base-tertiary rounded-md",children:[s.jsxs("div",{className:"text-sm",children:[s.jsxs("span",{className:"font-medium",children:[a(e.SETTINGS$MCP_NAME),":"]})," ",t.name]}),s.jsxs("div",{className:"mt-1 text-sm text-gray-500",children:[s.jsxs("span",{className:"font-medium",children:[a(e.SETTINGS$MCP_COMMAND),":"]})," ",s.jsx("code",{className:"font-mono",children:t.command})]}),t.args&&t.args.length>0&&s.jsxs("div",{className:"mt-1 text-sm text-gray-500",children:[s.jsxs("span",{className:"font-medium",children:[a(e.SETTINGS$MCP_ARGS),":"]})," ",s.jsx("code",{className:"font-mono",children:t.args.join(" ")})]}),t.env&&Object.keys(t.env).length>0&&s.jsxs("div",{className:"mt-1 text-sm text-gray-500",children:[s.jsxs("span",{className:"font-medium",children:[a(e.SETTINGS$MCP_ENV),":"]})," ",s.jsx("code",{className:"font-mono",children:Object.entries(t.env).map(([o,l])=>`${o}=${l}`).join(", ")})]})]},`stdio-${r}`))]})}function M({mcpConfig:n,onChange:a,onCancel:t}){const{t:r}=S(),[o,l]=x.useState(()=>n?JSON.stringify(n,null,2):r(e.SETTINGS$MCP_DEFAULT_CONFIG)),[d,m]=x.useState(null),N=i=>{l(i.target.value)},f=()=>{try{const i=JSON.parse(o);if(!i.sse_servers||!Array.isArray(i.sse_servers))throw new Error(r(e.SETTINGS$MCP_ERROR_SSE_ARRAY));if(!i.stdio_servers||!Array.isArray(i.stdio_servers))throw new Error(r(e.SETTINGS$MCP_ERROR_STDIO_ARRAY));for(const c of i.sse_servers)if(typeof c!="string"&&(!c.url||typeof c.url!="string"))throw new Error(r(e.SETTINGS$MCP_ERROR_SSE_URL));for(const c of i.stdio_servers)if(!c.name||!c.command)throw new Error(r(e.SETTINGS$MCP_ERROR_STDIO_PROPS));a(i),m(null)}catch(i){m(i instanceof Error?i.message:r(e.SETTINGS$MCP_ERROR_INVALID_JSON))}};return s.jsxs("div",{children:[s.jsx("p",{className:"mb-2 text-sm text-gray-400",children:s.jsx(C,{i18nKey:e.SETTINGS$MCP_CONFIG_DESCRIPTION,components:{a:s.jsx("a",{href:"https://docs.all-hands.dev/usage/mcp",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:underline",children:"documentation"})}})}),s.jsx("textarea",{className:g("w-full h-64 resize-y p-2 rounded-sm text-sm font-mono","bg-tertiary border border-[#717888]","placeholder:italic placeholder:text-tertiary-alt","focus:outline-none focus:ring-1 focus:ring-primary","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed"),value:o,onChange:N,spellCheck:"false"}),d&&s.jsxs("div",{className:"mt-2 p-2 bg-red-100 border border-red-300 rounded-md text-sm text-red-700",children:[s.jsx("strong",{children:r(e.SETTINGS$MCP_CONFIG_ERROR)})," ",d]}),s.jsxs("div",{className:"mt-2 text-sm text-gray-400",children:[s.jsx("strong",{children:r(e.SETTINGS$MCP_CONFIG_EXAMPLE)})," ",s.jsx("code",{children:'{ "sse_servers": ["https://example-mcp-server.com/sse"], "stdio_servers": [{ "name": "fetch", "command": "uvx", "args": ["mcp-server-fetch"] }] }'})]}),s.jsxs("div",{className:"mt-4 flex justify-end gap-3",children:[s.jsx(h,{type:"button",variant:"secondary",onClick:t,children:r(e.BUTTON$CANCEL)}),s.jsx(h,{type:"button",variant:"primary",onClick:f,children:r(e.SETTINGS$MCP_CONFIRM_CHANGES)})]})]})}function P({mcpConfig:n,onChange:a}){const{t}=S(),[r,o]=x.useState(!1),l=m=>{a(m),o(!1)},d=n||{sse_servers:[],stdio_servers:[]};return s.jsxs("div",{children:[s.jsxs("div",{className:"flex flex-col gap-2 mb-6",children:[s.jsx("div",{className:"text-sm font-medium",children:t(e.SETTINGS$MCP_TITLE)}),s.jsx("p",{className:"text-xs text-[#A3A3A3]",children:t(e.SETTINGS$MCP_DESCRIPTION)})]}),!r&&s.jsx("div",{className:"flex justify-between items-center mb-4",children:s.jsx("div",{className:"flex items-center",children:s.jsx(h,{type:"button",variant:"primary",onClick:()=>o(!0),children:t(e.SETTINGS$MCP_EDIT_CONFIGURATION)})})}),s.jsx("div",{children:r?s.jsx(M,{mcpConfig:n,onChange:l,onCancel:()=>o(!1)}):s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"flex flex-col gap-6",children:[s.jsx("div",{children:s.jsx(y,{servers:d.sse_servers})}),s.jsx("div",{children:s.jsx(R,{servers:d.stdio_servers})})]}),d.sse_servers.length===0&&d.stdio_servers.length===0&&s.jsx("div",{className:"mt-4 p-2 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-700",children:t(e.SETTINGS$MCP_NO_SERVERS_CONFIGURED)})]})})]})}function G(){const{t:n}=S(),{data:a,isLoading:t}=u(),{mutate:r,isPending:o}=T(),[l,d]=x.useState(a?.MCP_CONFIG),[m,N]=x.useState(!1),f=c=>{d(c),N(!0)},i=()=>{a&&r({MCP_CONFIG:l},{onSuccess:()=>{I(n(e.SETTINGS$SAVED)),_.capture("settings_saved",{HAS_MCP_CONFIG:l?"YES":"NO",MCP_SSE_SERVERS_COUNT:l?.sse_servers?.length||0,MCP_STDIO_SERVERS_COUNT:l?.stdio_servers?.length||0}),N(!1)},onError:c=>{const E=v(c);j(E||n(e.ERROR$GENERIC))}})};return t?s.jsx("div",{className:"p-9",children:n(e.HOME$LOADING)}):s.jsxs("form",{"data-testid":"mcp-settings-screen",action:i,className:"flex flex-col h-full justify-between",children:[s.jsx("div",{className:"p-9 flex flex-col gap-12",children:s.jsx(P,{mcpConfig:l,onChange:f})}),s.jsx("div",{className:"flex gap-6 p-6 justify-end border-t border-t-tertiary",children:s.jsxs(h,{testId:"submit-button",type:"submit",variant:"primary",isDisabled:!m||o,children:[!o&&n(e.SETTINGS$SAVE_CHANGES),o&&n(e.SETTINGS$SAVING)]})})]})}const q=p(G);export{q as default};
