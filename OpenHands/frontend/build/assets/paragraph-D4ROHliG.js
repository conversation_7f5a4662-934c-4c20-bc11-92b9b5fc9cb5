import{g as Mt,j as Ge,R as be}from"./chunk-C37GKA54-CBbYr_fP.js";function bn(){return bn=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},bn.apply(null,arguments)}function Rr(e,n){if(e==null)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)!==-1)continue;t[r]=e[r]}return t}function Eu(){}function Fr(e,n){const t={};return(e[e.length-1]===""?[...e,""]:e).join((t.padRight?" ":"")+","+(t.padLeft===!1?"":" ")).trim()}const _r=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Mr=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,jr={};function Kn(e,n){return(jr.jsx?Mr:_r).test(e)}const Br=/[ \t\n\f\r]/g;function Hr(e){return typeof e=="object"?e.type==="text"?Jn(e.value):!1:Jn(e)}function Jn(e){return e.replace(Br,"")===""}class qe{constructor(n,t,r){this.normal=t,this.property=n,r&&(this.space=r)}}qe.prototype.normal={};qe.prototype.property={};qe.prototype.space=void 0;function jt(e,n){const t={},r={};for(const i of e)Object.assign(t,i.property),Object.assign(r,i.normal);return new qe(t,r,n)}function kn(e){return e.toLowerCase()}class te{constructor(n,t){this.attribute=t,this.property=n}}te.prototype.attribute="";te.prototype.booleanish=!1;te.prototype.boolean=!1;te.prototype.commaOrSpaceSeparated=!1;te.prototype.commaSeparated=!1;te.prototype.defined=!1;te.prototype.mustUseProperty=!1;te.prototype.number=!1;te.prototype.overloadedBoolean=!1;te.prototype.property="";te.prototype.spaceSeparated=!1;te.prototype.space=void 0;let Ur=0;const D=Ce(),X=Ce(),wn=Ce(),w=Ce(),V=Ce(),Le=Ce(),le=Ce();function Ce(){return 2**++Ur}const Sn=Object.freeze(Object.defineProperty({__proto__:null,boolean:D,booleanish:X,commaOrSpaceSeparated:le,commaSeparated:Le,number:w,overloadedBoolean:wn,spaceSeparated:V},Symbol.toStringTag,{value:"Module"})),ln=Object.keys(Sn);class On extends te{constructor(n,t,r,i){let l=-1;if(super(n,t),Zn(this,"space",i),typeof r=="number")for(;++l<ln.length;){const o=ln[l];Zn(this,ln[l],(r&Sn[o])===Sn[o])}}}On.prototype.defined=!0;function Zn(e,n,t){t&&(e[n]=t)}function Ne(e){const n={},t={};for(const[r,i]of Object.entries(e.properties)){const l=new On(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),n[r]=l,t[kn(r)]=r,t[kn(l.attribute)]=r}return new qe(n,t,e.space)}const Bt=Ne({properties:{ariaActiveDescendant:null,ariaAtomic:X,ariaAutoComplete:null,ariaBusy:X,ariaChecked:X,ariaColCount:w,ariaColIndex:w,ariaColSpan:w,ariaControls:V,ariaCurrent:null,ariaDescribedBy:V,ariaDetails:null,ariaDisabled:X,ariaDropEffect:V,ariaErrorMessage:null,ariaExpanded:X,ariaFlowTo:V,ariaGrabbed:X,ariaHasPopup:null,ariaHidden:X,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:V,ariaLevel:w,ariaLive:null,ariaModal:X,ariaMultiLine:X,ariaMultiSelectable:X,ariaOrientation:null,ariaOwns:V,ariaPlaceholder:null,ariaPosInSet:w,ariaPressed:X,ariaReadOnly:X,ariaRelevant:null,ariaRequired:X,ariaRoleDescription:V,ariaRowCount:w,ariaRowIndex:w,ariaRowSpan:w,ariaSelected:X,ariaSetSize:w,ariaSort:null,ariaValueMax:w,ariaValueMin:w,ariaValueNow:w,ariaValueText:null,role:null},transform(e,n){return n==="role"?n:"aria-"+n.slice(4).toLowerCase()}});function Ht(e,n){return n in e?e[n]:n}function Ut(e,n){return Ht(e,n.toLowerCase())}const Vr=Ne({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Le,acceptCharset:V,accessKey:V,action:null,allow:null,allowFullScreen:D,allowPaymentRequest:D,allowUserMedia:D,alt:null,as:null,async:D,autoCapitalize:null,autoComplete:V,autoFocus:D,autoPlay:D,blocking:V,capture:null,charSet:null,checked:D,cite:null,className:V,cols:w,colSpan:null,content:null,contentEditable:X,controls:D,controlsList:V,coords:w|Le,crossOrigin:null,data:null,dateTime:null,decoding:null,default:D,defer:D,dir:null,dirName:null,disabled:D,download:wn,draggable:X,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:D,formTarget:null,headers:V,height:w,hidden:wn,high:w,href:null,hrefLang:null,htmlFor:V,httpEquiv:V,id:null,imageSizes:null,imageSrcSet:null,inert:D,inputMode:null,integrity:null,is:null,isMap:D,itemId:null,itemProp:V,itemRef:V,itemScope:D,itemType:V,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:D,low:w,manifest:null,max:null,maxLength:w,media:null,method:null,min:null,minLength:w,multiple:D,muted:D,name:null,nonce:null,noModule:D,noValidate:D,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:D,optimum:w,pattern:null,ping:V,placeholder:null,playsInline:D,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:D,referrerPolicy:null,rel:V,required:D,reversed:D,rows:w,rowSpan:w,sandbox:V,scope:null,scoped:D,seamless:D,selected:D,shadowRootClonable:D,shadowRootDelegatesFocus:D,shadowRootMode:null,shape:null,size:w,sizes:null,slot:null,span:w,spellCheck:X,src:null,srcDoc:null,srcLang:null,srcSet:null,start:w,step:null,style:null,tabIndex:w,target:null,title:null,translate:null,type:null,typeMustMatch:D,useMap:null,value:X,width:w,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:V,axis:null,background:null,bgColor:null,border:w,borderColor:null,bottomMargin:w,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:D,declare:D,event:null,face:null,frame:null,frameBorder:null,hSpace:w,leftMargin:w,link:null,longDesc:null,lowSrc:null,marginHeight:w,marginWidth:w,noResize:D,noHref:D,noShade:D,noWrap:D,object:null,profile:null,prompt:null,rev:null,rightMargin:w,rules:null,scheme:null,scrolling:X,standby:null,summary:null,text:null,topMargin:w,valueType:null,version:null,vAlign:null,vLink:null,vSpace:w,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:D,disableRemotePlayback:D,prefix:null,property:null,results:w,security:null,unselectable:null},space:"html",transform:Ut}),qr=Ne({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:le,accentHeight:w,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:w,amplitude:w,arabicForm:null,ascent:w,attributeName:null,attributeType:null,azimuth:w,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:w,by:null,calcMode:null,capHeight:w,className:V,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:w,diffuseConstant:w,direction:null,display:null,dur:null,divisor:w,dominantBaseline:null,download:D,dx:null,dy:null,edgeMode:null,editable:null,elevation:w,enableBackground:null,end:null,event:null,exponent:w,externalResourcesRequired:null,fill:null,fillOpacity:w,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Le,g2:Le,glyphName:Le,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:w,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:w,horizOriginX:w,horizOriginY:w,id:null,ideographic:w,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:w,k:w,k1:w,k2:w,k3:w,k4:w,kernelMatrix:le,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:w,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:w,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:w,overlineThickness:w,paintOrder:null,panose1:null,path:null,pathLength:w,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:V,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:w,pointsAtY:w,pointsAtZ:w,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:le,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:le,rev:le,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:le,requiredFeatures:le,requiredFonts:le,requiredFormats:le,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:w,specularExponent:w,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:w,strikethroughThickness:w,string:null,stroke:null,strokeDashArray:le,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:w,strokeOpacity:w,strokeWidth:null,style:null,surfaceScale:w,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:le,tabIndex:w,tableValues:null,target:null,targetX:w,targetY:w,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:le,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:w,underlineThickness:w,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:w,values:null,vAlphabetic:w,vMathematical:w,vectorEffect:null,vHanging:w,vIdeographic:w,version:null,vertAdvY:w,vertOriginX:w,vertOriginY:w,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:w,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Ht}),Vt=Ne({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,n){return"xlink:"+n.slice(5).toLowerCase()}}),qt=Ne({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Ut}),$t=Ne({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,n){return"xml:"+n.slice(3).toLowerCase()}}),$r={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},Wr=/[A-Z]/g,et=/-[a-z]/g,Yr=/^data[-\w.:]+$/i;function Xr(e,n){const t=kn(n);let r=n,i=te;if(t in e.normal)return e.property[e.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&Yr.test(n)){if(n.charAt(4)==="-"){const l=n.slice(5).replace(et,Gr);r="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=n.slice(4);if(!et.test(l)){let o=l.replace(Wr,Qr);o.charAt(0)!=="-"&&(o="-"+o),n="data"+o}}i=On}return new i(r,n)}function Qr(e){return"-"+e.toLowerCase()}function Gr(e){return e.charAt(1).toUpperCase()}const Kr=jt([Bt,Vr,Vt,qt,$t],"html"),zn=jt([Bt,qr,Vt,qt,$t],"svg");function Jr(e){return e.join(" ").trim()}var Ie={},on,nt;function Zr(){if(nt)return on;nt=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,t=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,l=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,o=/^[;\s]*/,a=/^\s+|\s+$/g,c=`
`,u="/",s="*",p="",m="comment",f="declaration";on=function(b,P){if(typeof b!="string")throw new TypeError("First argument must be a string");if(!b)return[];P=P||{};var x=1,T=1;function C(I){var v=I.match(n);v&&(x+=v.length);var A=I.lastIndexOf(c);T=~A?I.length-A:T+I.length}function j(){var I={line:x,column:T};return function(v){return v.position=new R(I),H(),v}}function R(I){this.start=I,this.end={line:x,column:T},this.source=P.source}R.prototype.content=b;function y(I){var v=new Error(P.source+":"+x+":"+T+": "+I);if(v.reason=I,v.filename=P.source,v.line=x,v.column=T,v.source=b,!P.silent)throw v}function N(I){var v=I.exec(b);if(v){var A=v[0];return C(A),b=b.slice(A.length),v}}function H(){N(t)}function F(I){var v;for(I=I||[];v=M();)v!==!1&&I.push(v);return I}function M(){var I=j();if(!(u!=b.charAt(0)||s!=b.charAt(1))){for(var v=2;p!=b.charAt(v)&&(s!=b.charAt(v)||u!=b.charAt(v+1));)++v;if(v+=2,p===b.charAt(v-1))return y("End of comment missing");var A=b.slice(2,v-2);return T+=2,C(A),b=b.slice(v),T+=2,I({type:m,comment:A})}}function L(){var I=j(),v=N(r);if(v){if(M(),!N(i))return y("property missing ':'");var A=N(l),W=I({type:f,property:k(v[0].replace(e,p)),value:A?k(A[0].replace(e,p)):p});return N(o),W}}function E(){var I=[];F(I);for(var v;v=L();)v!==!1&&(I.push(v),F(I));return I}return H(),E()};function k(b){return b?b.replace(a,p):p}return on}var tt;function ei(){if(tt)return Ie;tt=1;var e=Ie&&Ie.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ie,"__esModule",{value:!0}),Ie.default=t;var n=e(Zr());function t(r,i){var l=null;if(!r||typeof r!="string")return l;var o=(0,n.default)(r),a=typeof i=="function";return o.forEach(function(c){if(c.type==="declaration"){var u=c.property,s=c.value;a?i(u,s,c):s&&(l=l||{},l[u]=s)}}),l}return Ie}var Re={},rt;function ni(){if(rt)return Re;rt=1,Object.defineProperty(Re,"__esModule",{value:!0}),Re.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,n=/-([a-z])/g,t=/^[^-]+$/,r=/^-(webkit|moz|ms|o|khtml)-/,i=/^-(ms)-/,l=function(u){return!u||t.test(u)||e.test(u)},o=function(u,s){return s.toUpperCase()},a=function(u,s){return"".concat(s,"-")},c=function(u,s){return s===void 0&&(s={}),l(u)?u:(u=u.toLowerCase(),s.reactCompat?u=u.replace(i,a):u=u.replace(r,a),u.replace(n,o))};return Re.camelCase=c,Re}var Fe,it;function ti(){if(it)return Fe;it=1;var e=Fe&&Fe.__importDefault||function(i){return i&&i.__esModule?i:{default:i}},n=e(ei()),t=ni();function r(i,l){var o={};return!i||typeof i!="string"||(0,n.default)(i,function(a,c){a&&c&&(o[(0,t.camelCase)(a,l)]=c)}),o}return r.default=r,Fe=r,Fe}var ri=ti();const ii=Mt(ri),Wt=Yt("end"),Dn=Yt("start");function Yt(e){return n;function n(t){const r=t&&t.position&&t.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function li(e){const n=Dn(e),t=Wt(e);if(n&&t)return{start:n,end:t}}function je(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?lt(e.position):"start"in e||"end"in e?lt(e):"line"in e||"column"in e?Cn(e):""}function Cn(e){return ot(e&&e.line)+":"+ot(e&&e.column)}function lt(e){return Cn(e&&e.start)+"-"+Cn(e&&e.end)}function ot(e){return e&&typeof e=="number"?e:1}class J extends Error{constructor(n,t,r){super(),typeof t=="string"&&(r=t,t=void 0);let i="",l={},o=!1;if(t&&("line"in t&&"column"in t?l={place:t}:"start"in t&&"end"in t?l={place:t}:"type"in t?l={ancestors:[t],place:t.position}:l={...t}),typeof n=="string"?i=n:!l.cause&&n&&(o=!0,i=n.message,l.cause=n),!l.ruleId&&!l.source&&typeof r=="string"){const c=r.indexOf(":");c===-1?l.ruleId=r:(l.source=r.slice(0,c),l.ruleId=r.slice(c+1))}if(!l.place&&l.ancestors&&l.ancestors){const c=l.ancestors[l.ancestors.length-1];c&&(l.place=c.position)}const a=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=a?a.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=a?a.line:void 0,this.name=je(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=o&&l.cause&&typeof l.cause.stack=="string"?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}J.prototype.file="";J.prototype.name="";J.prototype.reason="";J.prototype.message="";J.prototype.stack="";J.prototype.column=void 0;J.prototype.line=void 0;J.prototype.ancestors=void 0;J.prototype.cause=void 0;J.prototype.fatal=void 0;J.prototype.place=void 0;J.prototype.ruleId=void 0;J.prototype.source=void 0;const Rn={}.hasOwnProperty,oi=new Map,ai=/[A-Z]/g,ui=new Set(["table","tbody","thead","tfoot","tr"]),si=new Set(["td","th"]),Xt="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ci(e,n){if(!n||n.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const t=n.filePath||void 0;let r;if(n.development){if(typeof n.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=xi(t,n.jsxDEV)}else{if(typeof n.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof n.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=yi(t,n.jsx,n.jsxs)}const i={Fragment:n.Fragment,ancestors:[],components:n.components||{},create:r,elementAttributeNameCase:n.elementAttributeNameCase||"react",evaluater:n.createEvaluater?n.createEvaluater():void 0,filePath:t,ignoreInvalidStyle:n.ignoreInvalidStyle||!1,passKeys:n.passKeys!==!1,passNode:n.passNode||!1,schema:n.space==="svg"?zn:Kr,stylePropertyNameCase:n.stylePropertyNameCase||"dom",tableCellAlignToStyle:n.tableCellAlignToStyle!==!1},l=Qt(i,e,void 0);return l&&typeof l!="string"?l:i.create(e,i.Fragment,{children:l||void 0},void 0)}function Qt(e,n,t){if(n.type==="element")return pi(e,n,t);if(n.type==="mdxFlowExpression"||n.type==="mdxTextExpression")return fi(e,n);if(n.type==="mdxJsxFlowElement"||n.type==="mdxJsxTextElement")return mi(e,n,t);if(n.type==="mdxjsEsm")return hi(e,n);if(n.type==="root")return di(e,n,t);if(n.type==="text")return gi(e,n)}function pi(e,n,t){const r=e.schema;let i=r;n.tagName.toLowerCase()==="svg"&&r.space==="html"&&(i=zn,e.schema=i),e.ancestors.push(n);const l=Kt(e,n.tagName,!1),o=bi(e,n);let a=_n(e,n);return ui.has(n.tagName)&&(a=a.filter(function(c){return typeof c=="string"?!Hr(c):!0})),Gt(e,o,l,n),Fn(o,a),e.ancestors.pop(),e.schema=r,e.create(n,l,o,t)}function fi(e,n){if(n.data&&n.data.estree&&e.evaluater){const r=n.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}Ue(e,n.position)}function hi(e,n){if(n.data&&n.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(n.data.estree);Ue(e,n.position)}function mi(e,n,t){const r=e.schema;let i=r;n.name==="svg"&&r.space==="html"&&(i=zn,e.schema=i),e.ancestors.push(n);const l=n.name===null?e.Fragment:Kt(e,n.name,!0),o=ki(e,n),a=_n(e,n);return Gt(e,o,l,n),Fn(o,a),e.ancestors.pop(),e.schema=r,e.create(n,l,o,t)}function di(e,n,t){const r={};return Fn(r,_n(e,n)),e.create(n,e.Fragment,r,t)}function gi(e,n){return n.value}function Gt(e,n,t,r){typeof t!="string"&&t!==e.Fragment&&e.passNode&&(n.node=r)}function Fn(e,n){if(n.length>0){const t=n.length>1?n:n[0];t&&(e.children=t)}}function yi(e,n,t){return r;function r(i,l,o,a){const u=Array.isArray(o.children)?t:n;return a?u(l,o,a):u(l,o)}}function xi(e,n){return t;function t(r,i,l,o){const a=Array.isArray(l.children),c=Dn(r);return n(i,l,o,a,{columnNumber:c?c.column-1:void 0,fileName:e,lineNumber:c?c.line:void 0},void 0)}}function bi(e,n){const t={};let r,i;for(i in n.properties)if(i!=="children"&&Rn.call(n.properties,i)){const l=wi(e,i,n.properties[i]);if(l){const[o,a]=l;e.tableCellAlignToStyle&&o==="align"&&typeof a=="string"&&si.has(n.tagName)?r=a:t[o]=a}}if(r){const l=t.style||(t.style={});l[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return t}function ki(e,n){const t={};for(const r of n.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const l=r.data.estree.body[0];l.type;const o=l.expression;o.type;const a=o.properties[0];a.type,Object.assign(t,e.evaluater.evaluateExpression(a.argument))}else Ue(e,n.position);else{const i=r.name;let l;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const a=r.value.data.estree.body[0];a.type,l=e.evaluater.evaluateExpression(a.expression)}else Ue(e,n.position);else l=r.value===null?!0:r.value;t[i]=l}return t}function _n(e,n){const t=[];let r=-1;const i=e.passKeys?new Map:oi;for(;++r<n.children.length;){const l=n.children[r];let o;if(e.passKeys){const c=l.type==="element"?l.tagName:l.type==="mdxJsxFlowElement"||l.type==="mdxJsxTextElement"?l.name:void 0;if(c){const u=i.get(c)||0;o=c+"-"+u,i.set(c,u+1)}}const a=Qt(e,l,o);a!==void 0&&t.push(a)}return t}function wi(e,n,t){const r=Xr(e.schema,n);if(!(t==null||typeof t=="number"&&Number.isNaN(t))){if(Array.isArray(t)&&(t=r.commaSeparated?Fr(t):Jr(t)),r.property==="style"){let i=typeof t=="object"?t:Si(e,String(t));return e.stylePropertyNameCase==="css"&&(i=Ci(i)),["style",i]}return[e.elementAttributeNameCase==="react"&&r.space?$r[r.property]||r.property:r.attribute,t]}}function Si(e,n){try{return ii(n,{reactCompat:!0})}catch(t){if(e.ignoreInvalidStyle)return{};const r=t,i=new J("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=e.filePath||void 0,i.url=Xt+"#cannot-parse-style-attribute",i}}function Kt(e,n,t){let r;if(!t)r={type:"Literal",value:n};else if(n.includes(".")){const i=n.split(".");let l=-1,o;for(;++l<i.length;){const a=Kn(i[l])?{type:"Identifier",name:i[l]}:{type:"Literal",value:i[l]};o=o?{type:"MemberExpression",object:o,property:a,computed:!!(l&&a.type==="Literal"),optional:!1}:a}r=o}else r=Kn(n)&&!/^[a-z]/.test(n)?{type:"Identifier",name:n}:{type:"Literal",value:n};if(r.type==="Literal"){const i=r.value;return Rn.call(e.components,i)?e.components[i]:i}if(e.evaluater)return e.evaluater.evaluateExpression(r);Ue(e)}function Ue(e,n){const t=new J("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:n,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=Xt+"#cannot-handle-mdx-estrees-without-createevaluater",t}function Ci(e){const n={};let t;for(t in e)Rn.call(e,t)&&(n[Ei(t)]=e[t]);return n}function Ei(e){let n=e.replace(ai,vi);return n.slice(0,3)==="ms-"&&(n="-"+n),n}function vi(e){return"-"+e.toLowerCase()}const an={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},Ii={};function Pi(e,n){const t=Ii,r=typeof t.includeImageAlt=="boolean"?t.includeImageAlt:!0,i=typeof t.includeHtml=="boolean"?t.includeHtml:!0;return Jt(e,r,i)}function Jt(e,n,t){if(Ti(e)){if("value"in e)return e.type==="html"&&!t?"":e.value;if(n&&"alt"in e&&e.alt)return e.alt;if("children"in e)return at(e.children,n,t)}return Array.isArray(e)?at(e,n,t):""}function at(e,n,t){const r=[];let i=-1;for(;++i<e.length;)r[i]=Jt(e[i],n,t);return r.join("")}function Ti(e){return!!(e&&typeof e=="object")}const ut=document.createElement("i");function Mn(e){const n="&"+e+";";ut.innerHTML=n;const t=ut.textContent;return t.charCodeAt(t.length-1)===59&&e!=="semi"||t===n?!1:t}function he(e,n,t,r){const i=e.length;let l=0,o;if(n<0?n=-n>i?0:i+n:n=n>i?i:n,t=t>0?t:0,r.length<1e4)o=Array.from(r),o.unshift(n,t),e.splice(...o);else for(t&&e.splice(n,t);l<r.length;)o=r.slice(l,l+1e4),o.unshift(n,0),e.splice(...o),l+=1e4,n+=1e4}function ae(e,n){return e.length>0?(he(e,e.length,0,n),e):n}const st={}.hasOwnProperty;function Li(e){const n={};let t=-1;for(;++t<e.length;)Ai(n,e[t]);return n}function Ai(e,n){let t;for(t in n){const i=(st.call(e,t)?e[t]:void 0)||(e[t]={}),l=n[t];let o;if(l)for(o in l){st.call(i,o)||(i[o]=[]);const a=l[o];Ni(i[o],Array.isArray(a)?a:a?[a]:[])}}}function Ni(e,n){let t=-1;const r=[];for(;++t<n.length;)(n[t].add==="after"?e:r).push(n[t]);he(e,0,0,r)}function Zt(e,n){const t=Number.parseInt(e,n);return t<9||t===11||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||(t&65535)===65535||(t&65535)===65534||t>1114111?"�":String.fromCodePoint(t)}function Ae(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const fe=we(/[A-Za-z]/),oe=we(/[\dA-Za-z]/),Oi=we(/[#-'*+\--9=?A-Z^-~]/);function En(e){return e!==null&&(e<32||e===127)}const vn=we(/\d/),zi=we(/[\dA-Fa-f]/),Di=we(/[!-/:-@[-`{-~]/);function O(e){return e!==null&&e<-2}function ne(e){return e!==null&&(e<0||e===32)}function B(e){return e===-2||e===-1||e===32}const Ri=we(new RegExp("\\p{P}|\\p{S}","u")),Fi=we(/\s/);function we(e){return n;function n(t){return t!==null&&t>-1&&e.test(String.fromCharCode(t))}}function Oe(e){const n=[];let t=-1,r=0,i=0;for(;++t<e.length;){const l=e.charCodeAt(t);let o="";if(l===37&&oe(e.charCodeAt(t+1))&&oe(e.charCodeAt(t+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){const a=e.charCodeAt(t+1);l<56320&&a>56319&&a<57344?(o=String.fromCharCode(l,a),i=1):o="�"}else o=String.fromCharCode(l);o&&(n.push(e.slice(r,t),encodeURIComponent(o)),r=t+i+1,o=""),i&&(t+=i,i=0)}return n.join("")+e.slice(r)}function q(e,n,t,r){const i=r?r-1:Number.POSITIVE_INFINITY;let l=0;return o;function o(c){return B(c)?(e.enter(t),a(c)):n(c)}function a(c){return B(c)&&l++<i?(e.consume(c),a):(e.exit(t),n(c))}}const _i={tokenize:Mi};function Mi(e){const n=e.attempt(this.parser.constructs.contentInitial,r,i);let t;return n;function r(a){if(a===null){e.consume(a);return}return e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),q(e,n,"linePrefix")}function i(a){return e.enter("paragraph"),l(a)}function l(a){const c=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=c),t=c,o(a)}function o(a){if(a===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(a);return}return O(a)?(e.consume(a),e.exit("chunkText"),l):(e.consume(a),o)}}const ji={tokenize:Bi},ct={tokenize:Hi};function Bi(e){const n=this,t=[];let r=0,i,l,o;return a;function a(C){if(r<t.length){const j=t[r];return n.containerState=j[1],e.attempt(j[0].continuation,c,u)(C)}return u(C)}function c(C){if(r++,n.containerState._closeFlow){n.containerState._closeFlow=void 0,i&&T();const j=n.events.length;let R=j,y;for(;R--;)if(n.events[R][0]==="exit"&&n.events[R][1].type==="chunkFlow"){y=n.events[R][1].end;break}x(r);let N=j;for(;N<n.events.length;)n.events[N][1].end={...y},N++;return he(n.events,R+1,0,n.events.slice(j)),n.events.length=N,u(C)}return a(C)}function u(C){if(r===t.length){if(!i)return m(C);if(i.currentConstruct&&i.currentConstruct.concrete)return k(C);n.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return n.containerState={},e.check(ct,s,p)(C)}function s(C){return i&&T(),x(r),m(C)}function p(C){return n.parser.lazy[n.now().line]=r!==t.length,o=n.now().offset,k(C)}function m(C){return n.containerState={},e.attempt(ct,f,k)(C)}function f(C){return r++,t.push([n.currentConstruct,n.containerState]),m(C)}function k(C){if(C===null){i&&T(),x(0),e.consume(C);return}return i=i||n.parser.flow(n.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:l}),b(C)}function b(C){if(C===null){P(e.exit("chunkFlow"),!0),x(0),e.consume(C);return}return O(C)?(e.consume(C),P(e.exit("chunkFlow")),r=0,n.interrupt=void 0,a):(e.consume(C),b)}function P(C,j){const R=n.sliceStream(C);if(j&&R.push(null),C.previous=l,l&&(l.next=C),l=C,i.defineSkip(C.start),i.write(R),n.parser.lazy[C.start.line]){let y=i.events.length;for(;y--;)if(i.events[y][1].start.offset<o&&(!i.events[y][1].end||i.events[y][1].end.offset>o))return;const N=n.events.length;let H=N,F,M;for(;H--;)if(n.events[H][0]==="exit"&&n.events[H][1].type==="chunkFlow"){if(F){M=n.events[H][1].end;break}F=!0}for(x(r),y=N;y<n.events.length;)n.events[y][1].end={...M},y++;he(n.events,H+1,0,n.events.slice(N)),n.events.length=y}}function x(C){let j=t.length;for(;j-- >C;){const R=t[j];n.containerState=R[1],R[0].exit.call(n,e)}t.length=C}function T(){i.write([null]),l=void 0,i=void 0,n.containerState._closeFlow=void 0}}function Hi(e,n,t){return q(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function pt(e){if(e===null||ne(e)||Fi(e))return 1;if(Ri(e))return 2}function jn(e,n,t){const r=[];let i=-1;for(;++i<e.length;){const l=e[i].resolveAll;l&&!r.includes(l)&&(n=l(n,t),r.push(l))}return n}const In={name:"attention",resolveAll:Ui,tokenize:Vi};function Ui(e,n){let t=-1,r,i,l,o,a,c,u,s;for(;++t<e.length;)if(e[t][0]==="enter"&&e[t][1].type==="attentionSequence"&&e[t][1]._close){for(r=t;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&n.sliceSerialize(e[r][1]).charCodeAt(0)===n.sliceSerialize(e[t][1]).charCodeAt(0)){if((e[r][1]._close||e[t][1]._open)&&(e[t][1].end.offset-e[t][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[t][1].end.offset-e[t][1].start.offset)%3))continue;c=e[r][1].end.offset-e[r][1].start.offset>1&&e[t][1].end.offset-e[t][1].start.offset>1?2:1;const p={...e[r][1].end},m={...e[t][1].start};ft(p,-c),ft(m,c),o={type:c>1?"strongSequence":"emphasisSequence",start:p,end:{...e[r][1].end}},a={type:c>1?"strongSequence":"emphasisSequence",start:{...e[t][1].start},end:m},l={type:c>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[t][1].start}},i={type:c>1?"strong":"emphasis",start:{...o.start},end:{...a.end}},e[r][1].end={...o.start},e[t][1].start={...a.end},u=[],e[r][1].end.offset-e[r][1].start.offset&&(u=ae(u,[["enter",e[r][1],n],["exit",e[r][1],n]])),u=ae(u,[["enter",i,n],["enter",o,n],["exit",o,n],["enter",l,n]]),u=ae(u,jn(n.parser.constructs.insideSpan.null,e.slice(r+1,t),n)),u=ae(u,[["exit",l,n],["enter",a,n],["exit",a,n],["exit",i,n]]),e[t][1].end.offset-e[t][1].start.offset?(s=2,u=ae(u,[["enter",e[t][1],n],["exit",e[t][1],n]])):s=0,he(e,r-1,t-r+3,u),t=r+u.length-s-2;break}}for(t=-1;++t<e.length;)e[t][1].type==="attentionSequence"&&(e[t][1].type="data");return e}function Vi(e,n){const t=this.parser.constructs.attentionMarkers.null,r=this.previous,i=pt(r);let l;return o;function o(c){return l=c,e.enter("attentionSequence"),a(c)}function a(c){if(c===l)return e.consume(c),a;const u=e.exit("attentionSequence"),s=pt(c),p=!s||s===2&&i||t.includes(c),m=!i||i===2&&s||t.includes(r);return u._open=!!(l===42?p:p&&(i||!m)),u._close=!!(l===42?m:m&&(s||!p)),n(c)}}function ft(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}const qi={name:"autolink",tokenize:$i};function $i(e,n,t){let r=0;return i;function i(f){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(f),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l}function l(f){return fe(f)?(e.consume(f),o):f===64?t(f):u(f)}function o(f){return f===43||f===45||f===46||oe(f)?(r=1,a(f)):u(f)}function a(f){return f===58?(e.consume(f),r=0,c):(f===43||f===45||f===46||oe(f))&&r++<32?(e.consume(f),a):(r=0,u(f))}function c(f){return f===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(f),e.exit("autolinkMarker"),e.exit("autolink"),n):f===null||f===32||f===60||En(f)?t(f):(e.consume(f),c)}function u(f){return f===64?(e.consume(f),s):Oi(f)?(e.consume(f),u):t(f)}function s(f){return oe(f)?p(f):t(f)}function p(f){return f===46?(e.consume(f),r=0,s):f===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(f),e.exit("autolinkMarker"),e.exit("autolink"),n):m(f)}function m(f){if((f===45||oe(f))&&r++<63){const k=f===45?m:p;return e.consume(f),k}return t(f)}}const nn={partial:!0,tokenize:Wi};function Wi(e,n,t){return r;function r(l){return B(l)?q(e,i,"linePrefix")(l):i(l)}function i(l){return l===null||O(l)?n(l):t(l)}}const er={continuation:{tokenize:Xi},exit:Qi,name:"blockQuote",tokenize:Yi};function Yi(e,n,t){const r=this;return i;function i(o){if(o===62){const a=r.containerState;return a.open||(e.enter("blockQuote",{_container:!0}),a.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(o),e.exit("blockQuoteMarker"),l}return t(o)}function l(o){return B(o)?(e.enter("blockQuotePrefixWhitespace"),e.consume(o),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(o))}}function Xi(e,n,t){const r=this;return i;function i(o){return B(o)?q(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o):l(o)}function l(o){return e.attempt(er,n,t)(o)}}function Qi(e){e.exit("blockQuote")}const nr={name:"characterEscape",tokenize:Gi};function Gi(e,n,t){return r;function r(l){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(l),e.exit("escapeMarker"),i}function i(l){return Di(l)?(e.enter("characterEscapeValue"),e.consume(l),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(l)}}const tr={name:"characterReference",tokenize:Ki};function Ki(e,n,t){const r=this;let i=0,l,o;return a;function a(p){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(p),e.exit("characterReferenceMarker"),c}function c(p){return p===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(p),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),l=31,o=oe,s(p))}function u(p){return p===88||p===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(p),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),l=6,o=zi,s):(e.enter("characterReferenceValue"),l=7,o=vn,s(p))}function s(p){if(p===59&&i){const m=e.exit("characterReferenceValue");return o===oe&&!Mn(r.sliceSerialize(m))?t(p):(e.enter("characterReferenceMarker"),e.consume(p),e.exit("characterReferenceMarker"),e.exit("characterReference"),n)}return o(p)&&i++<l?(e.consume(p),s):t(p)}}const ht={partial:!0,tokenize:Zi},mt={concrete:!0,name:"codeFenced",tokenize:Ji};function Ji(e,n,t){const r=this,i={partial:!0,tokenize:R};let l=0,o=0,a;return c;function c(y){return u(y)}function u(y){const N=r.events[r.events.length-1];return l=N&&N[1].type==="linePrefix"?N[2].sliceSerialize(N[1],!0).length:0,a=y,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),s(y)}function s(y){return y===a?(o++,e.consume(y),s):o<3?t(y):(e.exit("codeFencedFenceSequence"),B(y)?q(e,p,"whitespace")(y):p(y))}function p(y){return y===null||O(y)?(e.exit("codeFencedFence"),r.interrupt?n(y):e.check(ht,b,j)(y)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),m(y))}function m(y){return y===null||O(y)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),p(y)):B(y)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),q(e,f,"whitespace")(y)):y===96&&y===a?t(y):(e.consume(y),m)}function f(y){return y===null||O(y)?p(y):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),k(y))}function k(y){return y===null||O(y)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),p(y)):y===96&&y===a?t(y):(e.consume(y),k)}function b(y){return e.attempt(i,j,P)(y)}function P(y){return e.enter("lineEnding"),e.consume(y),e.exit("lineEnding"),x}function x(y){return l>0&&B(y)?q(e,T,"linePrefix",l+1)(y):T(y)}function T(y){return y===null||O(y)?e.check(ht,b,j)(y):(e.enter("codeFlowValue"),C(y))}function C(y){return y===null||O(y)?(e.exit("codeFlowValue"),T(y)):(e.consume(y),C)}function j(y){return e.exit("codeFenced"),n(y)}function R(y,N,H){let F=0;return M;function M(A){return y.enter("lineEnding"),y.consume(A),y.exit("lineEnding"),L}function L(A){return y.enter("codeFencedFence"),B(A)?q(y,E,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(A):E(A)}function E(A){return A===a?(y.enter("codeFencedFenceSequence"),I(A)):H(A)}function I(A){return A===a?(F++,y.consume(A),I):F>=o?(y.exit("codeFencedFenceSequence"),B(A)?q(y,v,"whitespace")(A):v(A)):H(A)}function v(A){return A===null||O(A)?(y.exit("codeFencedFence"),N(A)):H(A)}}}function Zi(e,n,t){const r=this;return i;function i(o){return o===null?t(o):(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),l)}function l(o){return r.parser.lazy[r.now().line]?t(o):n(o)}}const un={name:"codeIndented",tokenize:nl},el={partial:!0,tokenize:tl};function nl(e,n,t){const r=this;return i;function i(u){return e.enter("codeIndented"),q(e,l,"linePrefix",5)(u)}function l(u){const s=r.events[r.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?o(u):t(u)}function o(u){return u===null?c(u):O(u)?e.attempt(el,o,c)(u):(e.enter("codeFlowValue"),a(u))}function a(u){return u===null||O(u)?(e.exit("codeFlowValue"),o(u)):(e.consume(u),a)}function c(u){return e.exit("codeIndented"),n(u)}}function tl(e,n,t){const r=this;return i;function i(o){return r.parser.lazy[r.now().line]?t(o):O(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),i):q(e,l,"linePrefix",5)(o)}function l(o){const a=r.events[r.events.length-1];return a&&a[1].type==="linePrefix"&&a[2].sliceSerialize(a[1],!0).length>=4?n(o):O(o)?i(o):t(o)}}const rl={name:"codeText",previous:ll,resolve:il,tokenize:ol};function il(e){let n=e.length-4,t=3,r,i;if((e[t][1].type==="lineEnding"||e[t][1].type==="space")&&(e[n][1].type==="lineEnding"||e[n][1].type==="space")){for(r=t;++r<n;)if(e[r][1].type==="codeTextData"){e[t][1].type="codeTextPadding",e[n][1].type="codeTextPadding",t+=2,n-=2;break}}for(r=t-1,n++;++r<=n;)i===void 0?r!==n&&e[r][1].type!=="lineEnding"&&(i=r):(r===n||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),n-=r-i-2,r=i+2),i=void 0);return e}function ll(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function ol(e,n,t){let r=0,i,l;return o;function o(p){return e.enter("codeText"),e.enter("codeTextSequence"),a(p)}function a(p){return p===96?(e.consume(p),r++,a):(e.exit("codeTextSequence"),c(p))}function c(p){return p===null?t(p):p===32?(e.enter("space"),e.consume(p),e.exit("space"),c):p===96?(l=e.enter("codeTextSequence"),i=0,s(p)):O(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),c):(e.enter("codeTextData"),u(p))}function u(p){return p===null||p===32||p===96||O(p)?(e.exit("codeTextData"),c(p)):(e.consume(p),u)}function s(p){return p===96?(e.consume(p),i++,s):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),n(p)):(l.type="codeTextData",u(p))}}class al{constructor(n){this.left=n?[...n]:[],this.right=[]}get(n){if(n<0||n>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+n+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return n<this.left.length?this.left[n]:this.right[this.right.length-n+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(n,t){const r=t??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(n,r):n>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-n+this.left.length).reverse():this.left.slice(n).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(n,t,r){const i=t||0;this.setCursor(Math.trunc(n));const l=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&_e(this.left,r),l.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(n){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(n)}pushMany(n){this.setCursor(Number.POSITIVE_INFINITY),_e(this.left,n)}unshift(n){this.setCursor(0),this.right.push(n)}unshiftMany(n){this.setCursor(0),_e(this.right,n.reverse())}setCursor(n){if(!(n===this.left.length||n>this.left.length&&this.right.length===0||n<0&&this.left.length===0))if(n<this.left.length){const t=this.left.splice(n,Number.POSITIVE_INFINITY);_e(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-n,Number.POSITIVE_INFINITY);_e(this.left,t.reverse())}}}function _e(e,n){let t=0;if(n.length<1e4)e.push(...n);else for(;t<n.length;)e.push(...n.slice(t,t+1e4)),t+=1e4}function rr(e){const n={};let t=-1,r,i,l,o,a,c,u;const s=new al(e);for(;++t<s.length;){for(;t in n;)t=n[t];if(r=s.get(t),t&&r[1].type==="chunkFlow"&&s.get(t-1)[1].type==="listItemPrefix"&&(c=r[1]._tokenizer.events,l=0,l<c.length&&c[l][1].type==="lineEndingBlank"&&(l+=2),l<c.length&&c[l][1].type==="content"))for(;++l<c.length&&c[l][1].type!=="content";)c[l][1].type==="chunkText"&&(c[l][1]._isInFirstContentOfListItem=!0,l++);if(r[0]==="enter")r[1].contentType&&(Object.assign(n,ul(s,t)),t=n[t],u=!0);else if(r[1]._container){for(l=t,i=void 0;l--;)if(o=s.get(l),o[1].type==="lineEnding"||o[1].type==="lineEndingBlank")o[0]==="enter"&&(i&&(s.get(i)[1].type="lineEndingBlank"),o[1].type="lineEnding",i=l);else if(!(o[1].type==="linePrefix"||o[1].type==="listItemIndent"))break;i&&(r[1].end={...s.get(i)[1].start},a=s.slice(i,t),a.unshift(r),s.splice(i,t-i+1,a))}}return he(e,0,Number.POSITIVE_INFINITY,s.slice(0)),!u}function ul(e,n){const t=e.get(n)[1],r=e.get(n)[2];let i=n-1;const l=[];let o=t._tokenizer;o||(o=r.parser[t.contentType](t.start),t._contentTypeTextTrailing&&(o._contentTypeTextTrailing=!0));const a=o.events,c=[],u={};let s,p,m=-1,f=t,k=0,b=0;const P=[b];for(;f;){for(;e.get(++i)[1]!==f;);l.push(i),f._tokenizer||(s=r.sliceStream(f),f.next||s.push(null),p&&o.defineSkip(f.start),f._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=!0),o.write(s),f._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=void 0)),p=f,f=f.next}for(f=t;++m<a.length;)a[m][0]==="exit"&&a[m-1][0]==="enter"&&a[m][1].type===a[m-1][1].type&&a[m][1].start.line!==a[m][1].end.line&&(b=m+1,P.push(b),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(o.events=[],f?(f._tokenizer=void 0,f.previous=void 0):P.pop(),m=P.length;m--;){const x=a.slice(P[m],P[m+1]),T=l.pop();c.push([T,T+x.length-1]),e.splice(T,2,x)}for(c.reverse(),m=-1;++m<c.length;)u[k+c[m][0]]=k+c[m][1],k+=c[m][1]-c[m][0]-1;return u}const sl={resolve:pl,tokenize:fl},cl={partial:!0,tokenize:hl};function pl(e){return rr(e),e}function fl(e,n){let t;return r;function r(a){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),i(a)}function i(a){return a===null?l(a):O(a)?e.check(cl,o,l)(a):(e.consume(a),i)}function l(a){return e.exit("chunkContent"),e.exit("content"),n(a)}function o(a){return e.consume(a),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,i}}function hl(e,n,t){const r=this;return i;function i(o){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),q(e,l,"linePrefix")}function l(o){if(o===null||O(o))return t(o);const a=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&a&&a[1].type==="linePrefix"&&a[2].sliceSerialize(a[1],!0).length>=4?n(o):e.interrupt(r.parser.constructs.flow,t,n)(o)}}function ir(e,n,t,r,i,l,o,a,c){const u=c||Number.POSITIVE_INFINITY;let s=0;return p;function p(x){return x===60?(e.enter(r),e.enter(i),e.enter(l),e.consume(x),e.exit(l),m):x===null||x===32||x===41||En(x)?t(x):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),b(x))}function m(x){return x===62?(e.enter(l),e.consume(x),e.exit(l),e.exit(i),e.exit(r),n):(e.enter(a),e.enter("chunkString",{contentType:"string"}),f(x))}function f(x){return x===62?(e.exit("chunkString"),e.exit(a),m(x)):x===null||x===60||O(x)?t(x):(e.consume(x),x===92?k:f)}function k(x){return x===60||x===62||x===92?(e.consume(x),f):f(x)}function b(x){return!s&&(x===null||x===41||ne(x))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),n(x)):s<u&&x===40?(e.consume(x),s++,b):x===41?(e.consume(x),s--,b):x===null||x===32||x===40||En(x)?t(x):(e.consume(x),x===92?P:b)}function P(x){return x===40||x===41||x===92?(e.consume(x),b):b(x)}}function lr(e,n,t,r,i,l){const o=this;let a=0,c;return u;function u(f){return e.enter(r),e.enter(i),e.consume(f),e.exit(i),e.enter(l),s}function s(f){return a>999||f===null||f===91||f===93&&!c||f===94&&!a&&"_hiddenFootnoteSupport"in o.parser.constructs?t(f):f===93?(e.exit(l),e.enter(i),e.consume(f),e.exit(i),e.exit(r),n):O(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),p(f))}function p(f){return f===null||f===91||f===93||O(f)||a++>999?(e.exit("chunkString"),s(f)):(e.consume(f),c||(c=!B(f)),f===92?m:p)}function m(f){return f===91||f===92||f===93?(e.consume(f),a++,p):p(f)}}function or(e,n,t,r,i,l){let o;return a;function a(m){return m===34||m===39||m===40?(e.enter(r),e.enter(i),e.consume(m),e.exit(i),o=m===40?41:m,c):t(m)}function c(m){return m===o?(e.enter(i),e.consume(m),e.exit(i),e.exit(r),n):(e.enter(l),u(m))}function u(m){return m===o?(e.exit(l),c(o)):m===null?t(m):O(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),q(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(m))}function s(m){return m===o||m===null||O(m)?(e.exit("chunkString"),u(m)):(e.consume(m),m===92?p:s)}function p(m){return m===o||m===92?(e.consume(m),s):s(m)}}function Be(e,n){let t;return r;function r(i){return O(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,r):B(i)?q(e,r,t?"linePrefix":"lineSuffix")(i):n(i)}}const ml={name:"definition",tokenize:gl},dl={partial:!0,tokenize:yl};function gl(e,n,t){const r=this;let i;return l;function l(f){return e.enter("definition"),o(f)}function o(f){return lr.call(r,e,a,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(f)}function a(f){return i=Ae(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),f===58?(e.enter("definitionMarker"),e.consume(f),e.exit("definitionMarker"),c):t(f)}function c(f){return ne(f)?Be(e,u)(f):u(f)}function u(f){return ir(e,s,t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(f)}function s(f){return e.attempt(dl,p,p)(f)}function p(f){return B(f)?q(e,m,"whitespace")(f):m(f)}function m(f){return f===null||O(f)?(e.exit("definition"),r.parser.defined.push(i),n(f)):t(f)}}function yl(e,n,t){return r;function r(a){return ne(a)?Be(e,i)(a):t(a)}function i(a){return or(e,l,t,"definitionTitle","definitionTitleMarker","definitionTitleString")(a)}function l(a){return B(a)?q(e,o,"whitespace")(a):o(a)}function o(a){return a===null||O(a)?n(a):t(a)}}const xl={name:"hardBreakEscape",tokenize:bl};function bl(e,n,t){return r;function r(l){return e.enter("hardBreakEscape"),e.consume(l),i}function i(l){return O(l)?(e.exit("hardBreakEscape"),n(l)):t(l)}}const kl={name:"headingAtx",resolve:wl,tokenize:Sl};function wl(e,n){let t=e.length-2,r=3,i,l;return e[r][1].type==="whitespace"&&(r+=2),t-2>r&&e[t][1].type==="whitespace"&&(t-=2),e[t][1].type==="atxHeadingSequence"&&(r===t-1||t-4>r&&e[t-2][1].type==="whitespace")&&(t-=r+1===t?2:4),t>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[t][1].end},l={type:"chunkText",start:e[r][1].start,end:e[t][1].end,contentType:"text"},he(e,r,t-r+1,[["enter",i,n],["enter",l,n],["exit",l,n],["exit",i,n]])),e}function Sl(e,n,t){let r=0;return i;function i(s){return e.enter("atxHeading"),l(s)}function l(s){return e.enter("atxHeadingSequence"),o(s)}function o(s){return s===35&&r++<6?(e.consume(s),o):s===null||ne(s)?(e.exit("atxHeadingSequence"),a(s)):t(s)}function a(s){return s===35?(e.enter("atxHeadingSequence"),c(s)):s===null||O(s)?(e.exit("atxHeading"),n(s)):B(s)?q(e,a,"whitespace")(s):(e.enter("atxHeadingText"),u(s))}function c(s){return s===35?(e.consume(s),c):(e.exit("atxHeadingSequence"),a(s))}function u(s){return s===null||s===35||ne(s)?(e.exit("atxHeadingText"),a(s)):(e.consume(s),u)}}const Cl=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],dt=["pre","script","style","textarea"],El={concrete:!0,name:"htmlFlow",resolveTo:Pl,tokenize:Tl},vl={partial:!0,tokenize:Al},Il={partial:!0,tokenize:Ll};function Pl(e){let n=e.length;for(;n--&&!(e[n][0]==="enter"&&e[n][1].type==="htmlFlow"););return n>1&&e[n-2][1].type==="linePrefix"&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2)),e}function Tl(e,n,t){const r=this;let i,l,o,a,c;return u;function u(d){return s(d)}function s(d){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(d),p}function p(d){return d===33?(e.consume(d),m):d===47?(e.consume(d),l=!0,b):d===63?(e.consume(d),i=3,r.interrupt?n:h):fe(d)?(e.consume(d),o=String.fromCharCode(d),P):t(d)}function m(d){return d===45?(e.consume(d),i=2,f):d===91?(e.consume(d),i=5,a=0,k):fe(d)?(e.consume(d),i=4,r.interrupt?n:h):t(d)}function f(d){return d===45?(e.consume(d),r.interrupt?n:h):t(d)}function k(d){const Z="CDATA[";return d===Z.charCodeAt(a++)?(e.consume(d),a===Z.length?r.interrupt?n:E:k):t(d)}function b(d){return fe(d)?(e.consume(d),o=String.fromCharCode(d),P):t(d)}function P(d){if(d===null||d===47||d===62||ne(d)){const Z=d===47,se=o.toLowerCase();return!Z&&!l&&dt.includes(se)?(i=1,r.interrupt?n(d):E(d)):Cl.includes(o.toLowerCase())?(i=6,Z?(e.consume(d),x):r.interrupt?n(d):E(d)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?t(d):l?T(d):C(d))}return d===45||oe(d)?(e.consume(d),o+=String.fromCharCode(d),P):t(d)}function x(d){return d===62?(e.consume(d),r.interrupt?n:E):t(d)}function T(d){return B(d)?(e.consume(d),T):M(d)}function C(d){return d===47?(e.consume(d),M):d===58||d===95||fe(d)?(e.consume(d),j):B(d)?(e.consume(d),C):M(d)}function j(d){return d===45||d===46||d===58||d===95||oe(d)?(e.consume(d),j):R(d)}function R(d){return d===61?(e.consume(d),y):B(d)?(e.consume(d),R):C(d)}function y(d){return d===null||d===60||d===61||d===62||d===96?t(d):d===34||d===39?(e.consume(d),c=d,N):B(d)?(e.consume(d),y):H(d)}function N(d){return d===c?(e.consume(d),c=null,F):d===null||O(d)?t(d):(e.consume(d),N)}function H(d){return d===null||d===34||d===39||d===47||d===60||d===61||d===62||d===96||ne(d)?R(d):(e.consume(d),H)}function F(d){return d===47||d===62||B(d)?C(d):t(d)}function M(d){return d===62?(e.consume(d),L):t(d)}function L(d){return d===null||O(d)?E(d):B(d)?(e.consume(d),L):t(d)}function E(d){return d===45&&i===2?(e.consume(d),W):d===60&&i===1?(e.consume(d),Y):d===62&&i===4?(e.consume(d),K):d===63&&i===3?(e.consume(d),h):d===93&&i===5?(e.consume(d),G):O(d)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(vl,re,I)(d)):d===null||O(d)?(e.exit("htmlFlowData"),I(d)):(e.consume(d),E)}function I(d){return e.check(Il,v,re)(d)}function v(d){return e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),A}function A(d){return d===null||O(d)?I(d):(e.enter("htmlFlowData"),E(d))}function W(d){return d===45?(e.consume(d),h):E(d)}function Y(d){return d===47?(e.consume(d),o="",Q):E(d)}function Q(d){if(d===62){const Z=o.toLowerCase();return dt.includes(Z)?(e.consume(d),K):E(d)}return fe(d)&&o.length<8?(e.consume(d),o+=String.fromCharCode(d),Q):E(d)}function G(d){return d===93?(e.consume(d),h):E(d)}function h(d){return d===62?(e.consume(d),K):d===45&&i===2?(e.consume(d),h):E(d)}function K(d){return d===null||O(d)?(e.exit("htmlFlowData"),re(d)):(e.consume(d),K)}function re(d){return e.exit("htmlFlow"),n(d)}}function Ll(e,n,t){const r=this;return i;function i(o){return O(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),l):t(o)}function l(o){return r.parser.lazy[r.now().line]?t(o):n(o)}}function Al(e,n,t){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(nn,n,t)}}const Nl={name:"htmlText",tokenize:Ol};function Ol(e,n,t){const r=this;let i,l,o;return a;function a(h){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(h),c}function c(h){return h===33?(e.consume(h),u):h===47?(e.consume(h),R):h===63?(e.consume(h),C):fe(h)?(e.consume(h),H):t(h)}function u(h){return h===45?(e.consume(h),s):h===91?(e.consume(h),l=0,k):fe(h)?(e.consume(h),T):t(h)}function s(h){return h===45?(e.consume(h),f):t(h)}function p(h){return h===null?t(h):h===45?(e.consume(h),m):O(h)?(o=p,Y(h)):(e.consume(h),p)}function m(h){return h===45?(e.consume(h),f):p(h)}function f(h){return h===62?W(h):h===45?m(h):p(h)}function k(h){const K="CDATA[";return h===K.charCodeAt(l++)?(e.consume(h),l===K.length?b:k):t(h)}function b(h){return h===null?t(h):h===93?(e.consume(h),P):O(h)?(o=b,Y(h)):(e.consume(h),b)}function P(h){return h===93?(e.consume(h),x):b(h)}function x(h){return h===62?W(h):h===93?(e.consume(h),x):b(h)}function T(h){return h===null||h===62?W(h):O(h)?(o=T,Y(h)):(e.consume(h),T)}function C(h){return h===null?t(h):h===63?(e.consume(h),j):O(h)?(o=C,Y(h)):(e.consume(h),C)}function j(h){return h===62?W(h):C(h)}function R(h){return fe(h)?(e.consume(h),y):t(h)}function y(h){return h===45||oe(h)?(e.consume(h),y):N(h)}function N(h){return O(h)?(o=N,Y(h)):B(h)?(e.consume(h),N):W(h)}function H(h){return h===45||oe(h)?(e.consume(h),H):h===47||h===62||ne(h)?F(h):t(h)}function F(h){return h===47?(e.consume(h),W):h===58||h===95||fe(h)?(e.consume(h),M):O(h)?(o=F,Y(h)):B(h)?(e.consume(h),F):W(h)}function M(h){return h===45||h===46||h===58||h===95||oe(h)?(e.consume(h),M):L(h)}function L(h){return h===61?(e.consume(h),E):O(h)?(o=L,Y(h)):B(h)?(e.consume(h),L):F(h)}function E(h){return h===null||h===60||h===61||h===62||h===96?t(h):h===34||h===39?(e.consume(h),i=h,I):O(h)?(o=E,Y(h)):B(h)?(e.consume(h),E):(e.consume(h),v)}function I(h){return h===i?(e.consume(h),i=void 0,A):h===null?t(h):O(h)?(o=I,Y(h)):(e.consume(h),I)}function v(h){return h===null||h===34||h===39||h===60||h===61||h===96?t(h):h===47||h===62||ne(h)?F(h):(e.consume(h),v)}function A(h){return h===47||h===62||ne(h)?F(h):t(h)}function W(h){return h===62?(e.consume(h),e.exit("htmlTextData"),e.exit("htmlText"),n):t(h)}function Y(h){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),Q}function Q(h){return B(h)?q(e,G,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(h):G(h)}function G(h){return e.enter("htmlTextData"),o(h)}}const Bn={name:"labelEnd",resolveAll:Fl,resolveTo:_l,tokenize:Ml},zl={tokenize:jl},Dl={tokenize:Bl},Rl={tokenize:Hl};function Fl(e){let n=-1;const t=[];for(;++n<e.length;){const r=e[n][1];if(t.push(e[n]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const i=r.type==="labelImage"?4:2;r.type="data",n+=i}}return e.length!==t.length&&he(e,0,e.length,t),e}function _l(e,n){let t=e.length,r=0,i,l,o,a;for(;t--;)if(i=e[t][1],l){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[t][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(o){if(e[t][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(l=t,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(o=t);const c={type:e[l][1].type==="labelLink"?"link":"image",start:{...e[l][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[l][1].start},end:{...e[o][1].end}},s={type:"labelText",start:{...e[l+r+2][1].end},end:{...e[o-2][1].start}};return a=[["enter",c,n],["enter",u,n]],a=ae(a,e.slice(l+1,l+r+3)),a=ae(a,[["enter",s,n]]),a=ae(a,jn(n.parser.constructs.insideSpan.null,e.slice(l+r+4,o-3),n)),a=ae(a,[["exit",s,n],e[o-2],e[o-1],["exit",u,n]]),a=ae(a,e.slice(o+1)),a=ae(a,[["exit",c,n]]),he(e,l,e.length,a),e}function Ml(e,n,t){const r=this;let i=r.events.length,l,o;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){l=r.events[i][1];break}return a;function a(m){return l?l._inactive?p(m):(o=r.parser.defined.includes(Ae(r.sliceSerialize({start:l.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(m),e.exit("labelMarker"),e.exit("labelEnd"),c):t(m)}function c(m){return m===40?e.attempt(zl,s,o?s:p)(m):m===91?e.attempt(Dl,s,o?u:p)(m):o?s(m):p(m)}function u(m){return e.attempt(Rl,s,p)(m)}function s(m){return n(m)}function p(m){return l._balanced=!0,t(m)}}function jl(e,n,t){return r;function r(p){return e.enter("resource"),e.enter("resourceMarker"),e.consume(p),e.exit("resourceMarker"),i}function i(p){return ne(p)?Be(e,l)(p):l(p)}function l(p){return p===41?s(p):ir(e,o,a,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(p)}function o(p){return ne(p)?Be(e,c)(p):s(p)}function a(p){return t(p)}function c(p){return p===34||p===39||p===40?or(e,u,t,"resourceTitle","resourceTitleMarker","resourceTitleString")(p):s(p)}function u(p){return ne(p)?Be(e,s)(p):s(p)}function s(p){return p===41?(e.enter("resourceMarker"),e.consume(p),e.exit("resourceMarker"),e.exit("resource"),n):t(p)}}function Bl(e,n,t){const r=this;return i;function i(a){return lr.call(r,e,l,o,"reference","referenceMarker","referenceString")(a)}function l(a){return r.parser.defined.includes(Ae(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(a):t(a)}function o(a){return t(a)}}function Hl(e,n,t){return r;function r(l){return e.enter("reference"),e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),i}function i(l){return l===93?(e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),e.exit("reference"),n):t(l)}}const Ul={name:"labelStartImage",resolveAll:Bn.resolveAll,tokenize:Vl};function Vl(e,n,t){const r=this;return i;function i(a){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(a),e.exit("labelImageMarker"),l}function l(a){return a===91?(e.enter("labelMarker"),e.consume(a),e.exit("labelMarker"),e.exit("labelImage"),o):t(a)}function o(a){return a===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(a):n(a)}}const ql={name:"labelStartLink",resolveAll:Bn.resolveAll,tokenize:$l};function $l(e,n,t){const r=this;return i;function i(o){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelLink"),l}function l(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(o):n(o)}}const sn={name:"lineEnding",tokenize:Wl};function Wl(e,n){return t;function t(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),q(e,n,"linePrefix")}}const Ke={name:"thematicBreak",tokenize:Yl};function Yl(e,n,t){let r=0,i;return l;function l(u){return e.enter("thematicBreak"),o(u)}function o(u){return i=u,a(u)}function a(u){return u===i?(e.enter("thematicBreakSequence"),c(u)):r>=3&&(u===null||O(u))?(e.exit("thematicBreak"),n(u)):t(u)}function c(u){return u===i?(e.consume(u),r++,c):(e.exit("thematicBreakSequence"),B(u)?q(e,a,"whitespace")(u):a(u))}}const ee={continuation:{tokenize:Kl},exit:Zl,name:"list",tokenize:Gl},Xl={partial:!0,tokenize:eo},Ql={partial:!0,tokenize:Jl};function Gl(e,n,t){const r=this,i=r.events[r.events.length-1];let l=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,o=0;return a;function a(f){const k=r.containerState.type||(f===42||f===43||f===45?"listUnordered":"listOrdered");if(k==="listUnordered"?!r.containerState.marker||f===r.containerState.marker:vn(f)){if(r.containerState.type||(r.containerState.type=k,e.enter(k,{_container:!0})),k==="listUnordered")return e.enter("listItemPrefix"),f===42||f===45?e.check(Ke,t,u)(f):u(f);if(!r.interrupt||f===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),c(f)}return t(f)}function c(f){return vn(f)&&++o<10?(e.consume(f),c):(!r.interrupt||o<2)&&(r.containerState.marker?f===r.containerState.marker:f===41||f===46)?(e.exit("listItemValue"),u(f)):t(f)}function u(f){return e.enter("listItemMarker"),e.consume(f),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||f,e.check(nn,r.interrupt?t:s,e.attempt(Xl,m,p))}function s(f){return r.containerState.initialBlankLine=!0,l++,m(f)}function p(f){return B(f)?(e.enter("listItemPrefixWhitespace"),e.consume(f),e.exit("listItemPrefixWhitespace"),m):t(f)}function m(f){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(f)}}function Kl(e,n,t){const r=this;return r.containerState._closeFlow=void 0,e.check(nn,i,l);function i(a){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,q(e,n,"listItemIndent",r.containerState.size+1)(a)}function l(a){return r.containerState.furtherBlankLines||!B(a)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(a)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(Ql,n,o)(a))}function o(a){return r.containerState._closeFlow=!0,r.interrupt=void 0,q(e,e.attempt(ee,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a)}}function Jl(e,n,t){const r=this;return q(e,i,"listItemIndent",r.containerState.size+1);function i(l){const o=r.events[r.events.length-1];return o&&o[1].type==="listItemIndent"&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?n(l):t(l)}}function Zl(e){e.exit(this.containerState.type)}function eo(e,n,t){const r=this;return q(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(l){const o=r.events[r.events.length-1];return!B(l)&&o&&o[1].type==="listItemPrefixWhitespace"?n(l):t(l)}}const gt={name:"setextUnderline",resolveTo:no,tokenize:to};function no(e,n){let t=e.length,r,i,l;for(;t--;)if(e[t][0]==="enter"){if(e[t][1].type==="content"){r=t;break}e[t][1].type==="paragraph"&&(i=t)}else e[t][1].type==="content"&&e.splice(t,1),!l&&e[t][1].type==="definition"&&(l=t);const o={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",l?(e.splice(i,0,["enter",o,n]),e.splice(l+1,0,["exit",e[r][1],n]),e[r][1].end={...e[l][1].end}):e[r][1]=o,e.push(["exit",o,n]),e}function to(e,n,t){const r=this;let i;return l;function l(u){let s=r.events.length,p;for(;s--;)if(r.events[s][1].type!=="lineEnding"&&r.events[s][1].type!=="linePrefix"&&r.events[s][1].type!=="content"){p=r.events[s][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||p)?(e.enter("setextHeadingLine"),i=u,o(u)):t(u)}function o(u){return e.enter("setextHeadingLineSequence"),a(u)}function a(u){return u===i?(e.consume(u),a):(e.exit("setextHeadingLineSequence"),B(u)?q(e,c,"lineSuffix")(u):c(u))}function c(u){return u===null||O(u)?(e.exit("setextHeadingLine"),n(u)):t(u)}}const ro={tokenize:io};function io(e){const n=this,t=e.attempt(nn,r,e.attempt(this.parser.constructs.flowInitial,i,q(e,e.attempt(this.parser.constructs.flow,i,e.attempt(sl,i)),"linePrefix")));return t;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEndingBlank"),e.consume(l),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t}function i(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),n.currentConstruct=void 0,t}}const lo={resolveAll:ur()},oo=ar("string"),ao=ar("text");function ar(e){return{resolveAll:ur(e==="text"?uo:void 0),tokenize:n};function n(t){const r=this,i=this.parser.constructs[e],l=t.attempt(i,o,a);return o;function o(s){return u(s)?l(s):a(s)}function a(s){if(s===null){t.consume(s);return}return t.enter("data"),t.consume(s),c}function c(s){return u(s)?(t.exit("data"),l(s)):(t.consume(s),c)}function u(s){if(s===null)return!0;const p=i[s];let m=-1;if(p)for(;++m<p.length;){const f=p[m];if(!f.previous||f.previous.call(r,r.previous))return!0}return!1}}}function ur(e){return n;function n(t,r){let i=-1,l;for(;++i<=t.length;)l===void 0?t[i]&&t[i][1].type==="data"&&(l=i,i++):(!t[i]||t[i][1].type!=="data")&&(i!==l+2&&(t[l][1].end=t[i-1][1].end,t.splice(l+2,i-l-2),i=l+2),l=void 0);return e?e(t,r):t}}function uo(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||e[t][1].type==="lineEnding")&&e[t-1][1].type==="data"){const r=e[t-1][1],i=n.sliceStream(r);let l=i.length,o=-1,a=0,c;for(;l--;){const u=i[l];if(typeof u=="string"){for(o=u.length;u.charCodeAt(o-1)===32;)a++,o--;if(o)break;o=-1}else if(u===-2)c=!0,a++;else if(u!==-1){l++;break}}if(n._contentTypeTextTrailing&&t===e.length&&(a=0),a){const u={type:t===e.length||c||a<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?o:r.start._bufferIndex+o,_index:r.start._index+l,line:r.end.line,column:r.end.column-a,offset:r.end.offset-a},end:{...r.end}};r.end={...u.start},r.start.offset===r.end.offset?Object.assign(r,u):(e.splice(t,0,["enter",u,n],["exit",u,n]),t+=2)}t++}return e}const so={42:ee,43:ee,45:ee,48:ee,49:ee,50:ee,51:ee,52:ee,53:ee,54:ee,55:ee,56:ee,57:ee,62:er},co={91:ml},po={[-2]:un,[-1]:un,32:un},fo={35:kl,42:Ke,45:[gt,Ke],60:El,61:gt,95:Ke,96:mt,126:mt},ho={38:tr,92:nr},mo={[-5]:sn,[-4]:sn,[-3]:sn,33:Ul,38:tr,42:In,60:[qi,Nl],91:ql,92:[xl,nr],93:Bn,95:In,96:rl},go={null:[In,lo]},yo={null:[42,95]},xo={null:[]},bo=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:yo,contentInitial:co,disable:xo,document:so,flow:fo,flowInitial:po,insideSpan:go,string:ho,text:mo},Symbol.toStringTag,{value:"Module"}));function ko(e,n,t){let r={_bufferIndex:-1,_index:0,line:t&&t.line||1,column:t&&t.column||1,offset:t&&t.offset||0};const i={},l=[];let o=[],a=[];const c={attempt:N(R),check:N(y),consume:T,enter:C,exit:j,interrupt:N(y,{interrupt:!0})},u={code:null,containerState:{},defineSkip:b,events:[],now:k,parser:e,previous:null,sliceSerialize:m,sliceStream:f,write:p};let s=n.tokenize.call(u,c);return n.resolveAll&&l.push(n),u;function p(L){return o=ae(o,L),P(),o[o.length-1]!==null?[]:(H(n,0),u.events=jn(l,u.events,u),u.events)}function m(L,E){return So(f(L),E)}function f(L){return wo(o,L)}function k(){const{_bufferIndex:L,_index:E,line:I,column:v,offset:A}=r;return{_bufferIndex:L,_index:E,line:I,column:v,offset:A}}function b(L){i[L.line]=L.column,M()}function P(){let L;for(;r._index<o.length;){const E=o[r._index];if(typeof E=="string")for(L=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===L&&r._bufferIndex<E.length;)x(E.charCodeAt(r._bufferIndex));else x(E)}}function x(L){s=s(L)}function T(L){O(L)?(r.line++,r.column=1,r.offset+=L===-3?2:1,M()):L!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=L}function C(L,E){const I=E||{};return I.type=L,I.start=k(),u.events.push(["enter",I,u]),a.push(I),I}function j(L){const E=a.pop();return E.end=k(),u.events.push(["exit",E,u]),E}function R(L,E){H(L,E.from)}function y(L,E){E.restore()}function N(L,E){return I;function I(v,A,W){let Y,Q,G,h;return Array.isArray(v)?re(v):"tokenize"in v?re([v]):K(v);function K($){return me;function me(ce){const ye=ce!==null&&$[ce],Ee=ce!==null&&$.null,We=[...Array.isArray(ye)?ye:ye?[ye]:[],...Array.isArray(Ee)?Ee:Ee?[Ee]:[]];return re(We)(ce)}}function re($){return Y=$,Q=0,$.length===0?W:d($[Q])}function d($){return me;function me(ce){return h=F(),G=$,$.partial||(u.currentConstruct=$),$.name&&u.parser.constructs.disable.null.includes($.name)?se():$.tokenize.call(E?Object.assign(Object.create(u),E):u,c,Z,se)(ce)}}function Z($){return L(G,h),A}function se($){return h.restore(),++Q<Y.length?d(Y[Q]):W}}}function H(L,E){L.resolveAll&&!l.includes(L)&&l.push(L),L.resolve&&he(u.events,E,u.events.length-E,L.resolve(u.events.slice(E),u)),L.resolveTo&&(u.events=L.resolveTo(u.events,u))}function F(){const L=k(),E=u.previous,I=u.currentConstruct,v=u.events.length,A=Array.from(a);return{from:v,restore:W};function W(){r=L,u.previous=E,u.currentConstruct=I,u.events.length=v,a=A,M()}}function M(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function wo(e,n){const t=n.start._index,r=n.start._bufferIndex,i=n.end._index,l=n.end._bufferIndex;let o;if(t===i)o=[e[t].slice(r,l)];else{if(o=e.slice(t,i),r>-1){const a=o[0];typeof a=="string"?o[0]=a.slice(r):o.shift()}l>0&&o.push(e[i].slice(0,l))}return o}function So(e,n){let t=-1;const r=[];let i;for(;++t<e.length;){const l=e[t];let o;if(typeof l=="string")o=l;else switch(l){case-5:{o="\r";break}case-4:{o=`
`;break}case-3:{o=`\r
`;break}case-2:{o=n?" ":"	";break}case-1:{if(!n&&i)continue;o=" ";break}default:o=String.fromCharCode(l)}i=l===-2,r.push(o)}return r.join("")}function Co(e){const r={constructs:Li([bo,...(e||{}).extensions||[]]),content:i(_i),defined:[],document:i(ji),flow:i(ro),lazy:{},string:i(oo),text:i(ao)};return r;function i(l){return o;function o(a){return ko(r,l,a)}}}function Eo(e){for(;!rr(e););return e}const yt=/[\0\t\n\r]/g;function vo(){let e=1,n="",t=!0,r;return i;function i(l,o,a){const c=[];let u,s,p,m,f;for(l=n+(typeof l=="string"?l.toString():new TextDecoder(o||void 0).decode(l)),p=0,n="",t&&(l.charCodeAt(0)===65279&&p++,t=void 0);p<l.length;){if(yt.lastIndex=p,u=yt.exec(l),m=u&&u.index!==void 0?u.index:l.length,f=l.charCodeAt(m),!u){n=l.slice(p);break}if(f===10&&p===m&&r)c.push(-3),r=void 0;else switch(r&&(c.push(-5),r=void 0),p<m&&(c.push(l.slice(p,m)),e+=m-p),f){case 0:{c.push(65533),e++;break}case 9:{for(s=Math.ceil(e/4)*4,c.push(-2);e++<s;)c.push(-1);break}case 10:{c.push(-4),e=1;break}default:r=!0,e=1}p=m+1}return a&&(r&&c.push(-5),n&&c.push(n),c.push(null)),c}}const Io=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Po(e){return e.replace(Io,To)}function To(e,n,t){if(n)return n;if(t.charCodeAt(0)===35){const i=t.charCodeAt(1),l=i===120||i===88;return Zt(t.slice(l?2:1),l?16:10)}return Mn(t)||e}const sr={}.hasOwnProperty;function Lo(e,n,t){return typeof n!="string"&&(t=n,n=void 0),Ao(t)(Eo(Co(t).document().write(vo()(e,n,!0))))}function Ao(e){const n={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:l(Qn),autolinkProtocol:F,autolinkEmail:F,atxHeading:l(Wn),blockQuote:l(Ee),characterEscape:F,characterReference:F,codeFenced:l(We),codeFencedFenceInfo:o,codeFencedFenceMeta:o,codeIndented:l(We,o),codeText:l(Ir,o),codeTextData:F,data:F,codeFlowValue:F,definition:l(Pr),definitionDestinationString:o,definitionLabelString:o,definitionTitleString:o,emphasis:l(Tr),hardBreakEscape:l(Yn),hardBreakTrailing:l(Yn),htmlFlow:l(Xn,o),htmlFlowData:F,htmlText:l(Xn,o),htmlTextData:F,image:l(Lr),label:o,link:l(Qn),listItem:l(Ar),listItemValue:m,listOrdered:l(Gn,p),listUnordered:l(Gn),paragraph:l(Nr),reference:d,referenceString:o,resourceDestinationString:o,resourceTitleString:o,setextHeading:l(Wn),strong:l(Or),thematicBreak:l(Dr)},exit:{atxHeading:c(),atxHeadingSequence:R,autolink:c(),autolinkEmail:ye,autolinkProtocol:ce,blockQuote:c(),characterEscapeValue:M,characterReferenceMarkerHexadecimal:se,characterReferenceMarkerNumeric:se,characterReferenceValue:$,characterReference:me,codeFenced:c(P),codeFencedFence:b,codeFencedFenceInfo:f,codeFencedFenceMeta:k,codeFlowValue:M,codeIndented:c(x),codeText:c(A),codeTextData:M,data:M,definition:c(),definitionDestinationString:j,definitionLabelString:T,definitionTitleString:C,emphasis:c(),hardBreakEscape:c(E),hardBreakTrailing:c(E),htmlFlow:c(I),htmlFlowData:M,htmlText:c(v),htmlTextData:M,image:c(Y),label:G,labelText:Q,lineEnding:L,link:c(W),listItem:c(),listOrdered:c(),listUnordered:c(),paragraph:c(),referenceString:Z,resourceDestinationString:h,resourceTitleString:K,resource:re,setextHeading:c(H),setextHeadingLineSequence:N,setextHeadingText:y,strong:c(),thematicBreak:c()}};cr(n,(e||{}).mdastExtensions||[]);const t={};return r;function r(g){let S={type:"root",children:[]};const z={stack:[S],tokenStack:[],config:n,enter:a,exit:u,buffer:o,resume:s,data:t},_=[];let U=-1;for(;++U<g.length;)if(g[U][1].type==="listOrdered"||g[U][1].type==="listUnordered")if(g[U][0]==="enter")_.push(U);else{const ue=_.pop();U=i(g,ue,U)}for(U=-1;++U<g.length;){const ue=n[g[U][0]];sr.call(ue,g[U][1].type)&&ue[g[U][1].type].call(Object.assign({sliceSerialize:g[U][2].sliceSerialize},z),g[U][1])}if(z.tokenStack.length>0){const ue=z.tokenStack[z.tokenStack.length-1];(ue[1]||xt).call(z,void 0,ue[0])}for(S.position={start:xe(g.length>0?g[0][1].start:{line:1,column:1,offset:0}),end:xe(g.length>0?g[g.length-2][1].end:{line:1,column:1,offset:0})},U=-1;++U<n.transforms.length;)S=n.transforms[U](S)||S;return S}function i(g,S,z){let _=S-1,U=-1,ue=!1,Se,de,ze,De;for(;++_<=z;){const ie=g[_];switch(ie[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{ie[0]==="enter"?U++:U--,De=void 0;break}case"lineEndingBlank":{ie[0]==="enter"&&(Se&&!De&&!U&&!ze&&(ze=_),De=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:De=void 0}if(!U&&ie[0]==="enter"&&ie[1].type==="listItemPrefix"||U===-1&&ie[0]==="exit"&&(ie[1].type==="listUnordered"||ie[1].type==="listOrdered")){if(Se){let ve=_;for(de=void 0;ve--;){const ge=g[ve];if(ge[1].type==="lineEnding"||ge[1].type==="lineEndingBlank"){if(ge[0]==="exit")continue;de&&(g[de][1].type="lineEndingBlank",ue=!0),ge[1].type="lineEnding",de=ve}else if(!(ge[1].type==="linePrefix"||ge[1].type==="blockQuotePrefix"||ge[1].type==="blockQuotePrefixWhitespace"||ge[1].type==="blockQuoteMarker"||ge[1].type==="listItemIndent"))break}ze&&(!de||ze<de)&&(Se._spread=!0),Se.end=Object.assign({},de?g[de][1].start:ie[1].end),g.splice(de||_,0,["exit",Se,ie[2]]),_++,z++}if(ie[1].type==="listItemPrefix"){const ve={type:"listItem",_spread:!1,start:Object.assign({},ie[1].start),end:void 0};Se=ve,g.splice(_,0,["enter",ve,ie[2]]),_++,z++,ze=void 0,De=!0}}}return g[S][1]._spread=ue,z}function l(g,S){return z;function z(_){a.call(this,g(_),_),S&&S.call(this,_)}}function o(){this.stack.push({type:"fragment",children:[]})}function a(g,S,z){this.stack[this.stack.length-1].children.push(g),this.stack.push(g),this.tokenStack.push([S,z||void 0]),g.position={start:xe(S.start),end:void 0}}function c(g){return S;function S(z){g&&g.call(this,z),u.call(this,z)}}function u(g,S){const z=this.stack.pop(),_=this.tokenStack.pop();if(_)_[0].type!==g.type&&(S?S.call(this,g,_[0]):(_[1]||xt).call(this,g,_[0]));else throw new Error("Cannot close `"+g.type+"` ("+je({start:g.start,end:g.end})+"): it’s not open");z.position.end=xe(g.end)}function s(){return Pi(this.stack.pop())}function p(){this.data.expectingFirstListItemValue=!0}function m(g){if(this.data.expectingFirstListItemValue){const S=this.stack[this.stack.length-2];S.start=Number.parseInt(this.sliceSerialize(g),10),this.data.expectingFirstListItemValue=void 0}}function f(){const g=this.resume(),S=this.stack[this.stack.length-1];S.lang=g}function k(){const g=this.resume(),S=this.stack[this.stack.length-1];S.meta=g}function b(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function P(){const g=this.resume(),S=this.stack[this.stack.length-1];S.value=g.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function x(){const g=this.resume(),S=this.stack[this.stack.length-1];S.value=g.replace(/(\r?\n|\r)$/g,"")}function T(g){const S=this.resume(),z=this.stack[this.stack.length-1];z.label=S,z.identifier=Ae(this.sliceSerialize(g)).toLowerCase()}function C(){const g=this.resume(),S=this.stack[this.stack.length-1];S.title=g}function j(){const g=this.resume(),S=this.stack[this.stack.length-1];S.url=g}function R(g){const S=this.stack[this.stack.length-1];if(!S.depth){const z=this.sliceSerialize(g).length;S.depth=z}}function y(){this.data.setextHeadingSlurpLineEnding=!0}function N(g){const S=this.stack[this.stack.length-1];S.depth=this.sliceSerialize(g).codePointAt(0)===61?1:2}function H(){this.data.setextHeadingSlurpLineEnding=void 0}function F(g){const z=this.stack[this.stack.length-1].children;let _=z[z.length-1];(!_||_.type!=="text")&&(_=zr(),_.position={start:xe(g.start),end:void 0},z.push(_)),this.stack.push(_)}function M(g){const S=this.stack.pop();S.value+=this.sliceSerialize(g),S.position.end=xe(g.end)}function L(g){const S=this.stack[this.stack.length-1];if(this.data.atHardBreak){const z=S.children[S.children.length-1];z.position.end=xe(g.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&n.canContainEols.includes(S.type)&&(F.call(this,g),M.call(this,g))}function E(){this.data.atHardBreak=!0}function I(){const g=this.resume(),S=this.stack[this.stack.length-1];S.value=g}function v(){const g=this.resume(),S=this.stack[this.stack.length-1];S.value=g}function A(){const g=this.resume(),S=this.stack[this.stack.length-1];S.value=g}function W(){const g=this.stack[this.stack.length-1];if(this.data.inReference){const S=this.data.referenceType||"shortcut";g.type+="Reference",g.referenceType=S,delete g.url,delete g.title}else delete g.identifier,delete g.label;this.data.referenceType=void 0}function Y(){const g=this.stack[this.stack.length-1];if(this.data.inReference){const S=this.data.referenceType||"shortcut";g.type+="Reference",g.referenceType=S,delete g.url,delete g.title}else delete g.identifier,delete g.label;this.data.referenceType=void 0}function Q(g){const S=this.sliceSerialize(g),z=this.stack[this.stack.length-2];z.label=Po(S),z.identifier=Ae(S).toLowerCase()}function G(){const g=this.stack[this.stack.length-1],S=this.resume(),z=this.stack[this.stack.length-1];if(this.data.inReference=!0,z.type==="link"){const _=g.children;z.children=_}else z.alt=S}function h(){const g=this.resume(),S=this.stack[this.stack.length-1];S.url=g}function K(){const g=this.resume(),S=this.stack[this.stack.length-1];S.title=g}function re(){this.data.inReference=void 0}function d(){this.data.referenceType="collapsed"}function Z(g){const S=this.resume(),z=this.stack[this.stack.length-1];z.label=S,z.identifier=Ae(this.sliceSerialize(g)).toLowerCase(),this.data.referenceType="full"}function se(g){this.data.characterReferenceType=g.type}function $(g){const S=this.sliceSerialize(g),z=this.data.characterReferenceType;let _;z?(_=Zt(S,z==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):_=Mn(S);const U=this.stack[this.stack.length-1];U.value+=_}function me(g){const S=this.stack.pop();S.position.end=xe(g.end)}function ce(g){M.call(this,g);const S=this.stack[this.stack.length-1];S.url=this.sliceSerialize(g)}function ye(g){M.call(this,g);const S=this.stack[this.stack.length-1];S.url="mailto:"+this.sliceSerialize(g)}function Ee(){return{type:"blockquote",children:[]}}function We(){return{type:"code",lang:null,meta:null,value:""}}function Ir(){return{type:"inlineCode",value:""}}function Pr(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Tr(){return{type:"emphasis",children:[]}}function Wn(){return{type:"heading",depth:0,children:[]}}function Yn(){return{type:"break"}}function Xn(){return{type:"html",value:""}}function Lr(){return{type:"image",title:null,url:"",alt:null}}function Qn(){return{type:"link",title:null,url:"",children:[]}}function Gn(g){return{type:"list",ordered:g.type==="listOrdered",start:null,spread:g._spread,children:[]}}function Ar(g){return{type:"listItem",spread:g._spread,checked:null,children:[]}}function Nr(){return{type:"paragraph",children:[]}}function Or(){return{type:"strong",children:[]}}function zr(){return{type:"text",value:""}}function Dr(){return{type:"thematicBreak"}}}function xe(e){return{line:e.line,column:e.column,offset:e.offset}}function cr(e,n){let t=-1;for(;++t<n.length;){const r=n[t];Array.isArray(r)?cr(e,r):No(e,r)}}function No(e,n){let t;for(t in n)if(sr.call(n,t))switch(t){case"canContainEols":{const r=n[t];r&&e[t].push(...r);break}case"transforms":{const r=n[t];r&&e[t].push(...r);break}case"enter":case"exit":{const r=n[t];r&&Object.assign(e[t],r);break}}}function xt(e,n){throw e?new Error("Cannot close `"+e.type+"` ("+je({start:e.start,end:e.end})+"): a different token (`"+n.type+"`, "+je({start:n.start,end:n.end})+") is open"):new Error("Cannot close document, a token (`"+n.type+"`, "+je({start:n.start,end:n.end})+") is still open")}function Oo(e){const n=this;n.parser=t;function t(r){return Lo(r,{...n.data("settings"),...e,extensions:n.data("micromarkExtensions")||[],mdastExtensions:n.data("fromMarkdownExtensions")||[]})}}function zo(e,n){const t={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(n),!0)};return e.patch(n,t),e.applyData(n,t)}function Do(e,n){const t={type:"element",tagName:"br",properties:{},children:[]};return e.patch(n,t),[e.applyData(n,t),{type:"text",value:`
`}]}function Ro(e,n){const t=n.value?n.value+`
`:"",r={};n.lang&&(r.className=["language-"+n.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:t}]};return n.meta&&(i.data={meta:n.meta}),e.patch(n,i),i=e.applyData(n,i),i={type:"element",tagName:"pre",properties:{},children:[i]},e.patch(n,i),i}function Fo(e,n){const t={type:"element",tagName:"del",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function _o(e,n){const t={type:"element",tagName:"em",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Mo(e,n){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(n.identifier).toUpperCase(),i=Oe(r.toLowerCase()),l=e.footnoteOrder.indexOf(r);let o,a=e.footnoteCounts.get(r);a===void 0?(a=0,e.footnoteOrder.push(r),o=e.footnoteOrder.length):o=l+1,a+=1,e.footnoteCounts.set(r,a);const c={type:"element",tagName:"a",properties:{href:"#"+t+"fn-"+i,id:t+"fnref-"+i+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(o)}]};e.patch(n,c);const u={type:"element",tagName:"sup",properties:{},children:[c]};return e.patch(n,u),e.applyData(n,u)}function jo(e,n){const t={type:"element",tagName:"h"+n.depth,properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Bo(e,n){if(e.options.allowDangerousHtml){const t={type:"raw",value:n.value};return e.patch(n,t),e.applyData(n,t)}}function pr(e,n){const t=n.referenceType;let r="]";if(t==="collapsed"?r+="[]":t==="full"&&(r+="["+(n.label||n.identifier)+"]"),n.type==="imageReference")return[{type:"text",value:"!["+n.alt+r}];const i=e.all(n),l=i[0];l&&l.type==="text"?l.value="["+l.value:i.unshift({type:"text",value:"["});const o=i[i.length-1];return o&&o.type==="text"?o.value+=r:i.push({type:"text",value:r}),i}function Ho(e,n){const t=String(n.identifier).toUpperCase(),r=e.definitionById.get(t);if(!r)return pr(e,n);const i={src:Oe(r.url||""),alt:n.alt};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(n,l),e.applyData(n,l)}function Uo(e,n){const t={src:Oe(n.url)};n.alt!==null&&n.alt!==void 0&&(t.alt=n.alt),n.title!==null&&n.title!==void 0&&(t.title=n.title);const r={type:"element",tagName:"img",properties:t,children:[]};return e.patch(n,r),e.applyData(n,r)}function Vo(e,n){const t={type:"text",value:n.value.replace(/\r?\n|\r/g," ")};e.patch(n,t);const r={type:"element",tagName:"code",properties:{},children:[t]};return e.patch(n,r),e.applyData(n,r)}function qo(e,n){const t=String(n.identifier).toUpperCase(),r=e.definitionById.get(t);if(!r)return pr(e,n);const i={href:Oe(r.url||"")};r.title!==null&&r.title!==void 0&&(i.title=r.title);const l={type:"element",tagName:"a",properties:i,children:e.all(n)};return e.patch(n,l),e.applyData(n,l)}function $o(e,n){const t={href:Oe(n.url)};n.title!==null&&n.title!==void 0&&(t.title=n.title);const r={type:"element",tagName:"a",properties:t,children:e.all(n)};return e.patch(n,r),e.applyData(n,r)}function Wo(e,n,t){const r=e.all(n),i=t?Yo(t):fr(n),l={},o=[];if(typeof n.checked=="boolean"){const s=r[0];let p;s&&s.type==="element"&&s.tagName==="p"?p=s:(p={type:"element",tagName:"p",properties:{},children:[]},r.unshift(p)),p.children.length>0&&p.children.unshift({type:"text",value:" "}),p.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:n.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){const s=r[a];(i||a!==0||s.type!=="element"||s.tagName!=="p")&&o.push({type:"text",value:`
`}),s.type==="element"&&s.tagName==="p"&&!i?o.push(...s.children):o.push(s)}const c=r[r.length-1];c&&(i||c.type!=="element"||c.tagName!=="p")&&o.push({type:"text",value:`
`});const u={type:"element",tagName:"li",properties:l,children:o};return e.patch(n,u),e.applyData(n,u)}function Yo(e){let n=!1;if(e.type==="list"){n=e.spread||!1;const t=e.children;let r=-1;for(;!n&&++r<t.length;)n=fr(t[r])}return n}function fr(e){const n=e.spread;return n??e.children.length>1}function Xo(e,n){const t={},r=e.all(n);let i=-1;for(typeof n.start=="number"&&n.start!==1&&(t.start=n.start);++i<r.length;){const o=r[i];if(o.type==="element"&&o.tagName==="li"&&o.properties&&Array.isArray(o.properties.className)&&o.properties.className.includes("task-list-item")){t.className=["contains-task-list"];break}}const l={type:"element",tagName:n.ordered?"ol":"ul",properties:t,children:e.wrap(r,!0)};return e.patch(n,l),e.applyData(n,l)}function Qo(e,n){const t={type:"element",tagName:"p",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Go(e,n){const t={type:"root",children:e.wrap(e.all(n))};return e.patch(n,t),e.applyData(n,t)}function Ko(e,n){const t={type:"element",tagName:"strong",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Jo(e,n){const t=e.all(n),r=t.shift(),i=[];if(r){const o={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(n.children[0],o),i.push(o)}if(t.length>0){const o={type:"element",tagName:"tbody",properties:{},children:e.wrap(t,!0)},a=Dn(n.children[1]),c=Wt(n.children[n.children.length-1]);a&&c&&(o.position={start:a,end:c}),i.push(o)}const l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(n,l),e.applyData(n,l)}function Zo(e,n,t){const r=t?t.children:void 0,l=(r?r.indexOf(n):1)===0?"th":"td",o=t&&t.type==="table"?t.align:void 0,a=o?o.length:n.children.length;let c=-1;const u=[];for(;++c<a;){const p=n.children[c],m={},f=o?o[c]:void 0;f&&(m.align=f);let k={type:"element",tagName:l,properties:m,children:[]};p&&(k.children=e.all(p),e.patch(p,k),k=e.applyData(p,k)),u.push(k)}const s={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(n,s),e.applyData(n,s)}function ea(e,n){const t={type:"element",tagName:"td",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}const bt=9,kt=32;function na(e){const n=String(e),t=/\r?\n|\r/g;let r=t.exec(n),i=0;const l=[];for(;r;)l.push(wt(n.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=t.exec(n);return l.push(wt(n.slice(i),i>0,!1)),l.join("")}function wt(e,n,t){let r=0,i=e.length;if(n){let l=e.codePointAt(r);for(;l===bt||l===kt;)r++,l=e.codePointAt(r)}if(t){let l=e.codePointAt(i-1);for(;l===bt||l===kt;)i--,l=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function ta(e,n){const t={type:"text",value:na(String(n.value))};return e.patch(n,t),e.applyData(n,t)}function ra(e,n){const t={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(n,t),e.applyData(n,t)}const ia={blockquote:zo,break:Do,code:Ro,delete:Fo,emphasis:_o,footnoteReference:Mo,heading:jo,html:Bo,imageReference:Ho,image:Uo,inlineCode:Vo,linkReference:qo,link:$o,listItem:Wo,list:Xo,paragraph:Qo,root:Go,strong:Ko,table:Jo,tableCell:ea,tableRow:Zo,text:ta,thematicBreak:ra,toml:Ye,yaml:Ye,definition:Ye,footnoteDefinition:Ye};function Ye(){}const hr=-1,tn=0,He=1,Ze=2,Hn=3,Un=4,Vn=5,qn=6,mr=7,dr=8,St=typeof self=="object"?self:globalThis,la=(e,n)=>{const t=(i,l)=>(e.set(l,i),i),r=i=>{if(e.has(i))return e.get(i);const[l,o]=n[i];switch(l){case tn:case hr:return t(o,i);case He:{const a=t([],i);for(const c of o)a.push(r(c));return a}case Ze:{const a=t({},i);for(const[c,u]of o)a[r(c)]=r(u);return a}case Hn:return t(new Date(o),i);case Un:{const{source:a,flags:c}=o;return t(new RegExp(a,c),i)}case Vn:{const a=t(new Map,i);for(const[c,u]of o)a.set(r(c),r(u));return a}case qn:{const a=t(new Set,i);for(const c of o)a.add(r(c));return a}case mr:{const{name:a,message:c}=o;return t(new St[a](c),i)}case dr:return t(BigInt(o),i);case"BigInt":return t(Object(BigInt(o)),i);case"ArrayBuffer":return t(new Uint8Array(o).buffer,o);case"DataView":{const{buffer:a}=new Uint8Array(o);return t(new DataView(a),o)}}return t(new St[l](o),i)};return r},Ct=e=>la(new Map,e)(0),Pe="",{toString:oa}={},{keys:aa}=Object,Me=e=>{const n=typeof e;if(n!=="object"||!e)return[tn,n];const t=oa.call(e).slice(8,-1);switch(t){case"Array":return[He,Pe];case"Object":return[Ze,Pe];case"Date":return[Hn,Pe];case"RegExp":return[Un,Pe];case"Map":return[Vn,Pe];case"Set":return[qn,Pe];case"DataView":return[He,t]}return t.includes("Array")?[He,t]:t.includes("Error")?[mr,t]:[Ze,t]},Xe=([e,n])=>e===tn&&(n==="function"||n==="symbol"),ua=(e,n,t,r)=>{const i=(o,a)=>{const c=r.push(o)-1;return t.set(a,c),c},l=o=>{if(t.has(o))return t.get(o);let[a,c]=Me(o);switch(a){case tn:{let s=o;switch(c){case"bigint":a=dr,s=o.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+c);s=null;break;case"undefined":return i([hr],o)}return i([a,s],o)}case He:{if(c){let m=o;return c==="DataView"?m=new Uint8Array(o.buffer):c==="ArrayBuffer"&&(m=new Uint8Array(o)),i([c,[...m]],o)}const s=[],p=i([a,s],o);for(const m of o)s.push(l(m));return p}case Ze:{if(c)switch(c){case"BigInt":return i([c,o.toString()],o);case"Boolean":case"Number":case"String":return i([c,o.valueOf()],o)}if(n&&"toJSON"in o)return l(o.toJSON());const s=[],p=i([a,s],o);for(const m of aa(o))(e||!Xe(Me(o[m])))&&s.push([l(m),l(o[m])]);return p}case Hn:return i([a,o.toISOString()],o);case Un:{const{source:s,flags:p}=o;return i([a,{source:s,flags:p}],o)}case Vn:{const s=[],p=i([a,s],o);for(const[m,f]of o)(e||!(Xe(Me(m))||Xe(Me(f))))&&s.push([l(m),l(f)]);return p}case qn:{const s=[],p=i([a,s],o);for(const m of o)(e||!Xe(Me(m)))&&s.push(l(m));return p}}const{message:u}=o;return i([a,{name:c,message:u}],o)};return l},Et=(e,{json:n,lossy:t}={})=>{const r=[];return ua(!(n||t),!!n,new Map,r)(e),r},en=typeof structuredClone=="function"?(e,n)=>n&&("json"in n||"lossy"in n)?Ct(Et(e,n)):structuredClone(e):(e,n)=>Ct(Et(e,n));function sa(e,n){const t=[{type:"text",value:"↩"}];return n>1&&t.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(n)}]}),t}function ca(e,n){return"Back to reference "+(e+1)+(n>1?"-"+n:"")}function pa(e){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",t=e.options.footnoteBackContent||sa,r=e.options.footnoteBackLabel||ca,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[];let c=-1;for(;++c<e.footnoteOrder.length;){const u=e.footnoteById.get(e.footnoteOrder[c]);if(!u)continue;const s=e.all(u),p=String(u.identifier).toUpperCase(),m=Oe(p.toLowerCase());let f=0;const k=[],b=e.footnoteCounts.get(p);for(;b!==void 0&&++f<=b;){k.length>0&&k.push({type:"text",value:" "});let T=typeof t=="string"?t:t(c,f);typeof T=="string"&&(T={type:"text",value:T}),k.push({type:"element",tagName:"a",properties:{href:"#"+n+"fnref-"+m+(f>1?"-"+f:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(c,f),className:["data-footnote-backref"]},children:Array.isArray(T)?T:[T]})}const P=s[s.length-1];if(P&&P.type==="element"&&P.tagName==="p"){const T=P.children[P.children.length-1];T&&T.type==="text"?T.value+=" ":P.children.push({type:"text",value:" "}),P.children.push(...k)}else s.push(...k);const x={type:"element",tagName:"li",properties:{id:n+"fn-"+m},children:e.wrap(s,!0)};e.patch(u,x),a.push(x)}if(a.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...en(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:`
`}]}}const gr=function(e){if(e==null)return da;if(typeof e=="function")return rn(e);if(typeof e=="object")return Array.isArray(e)?fa(e):ha(e);if(typeof e=="string")return ma(e);throw new Error("Expected function, string, or object as test")};function fa(e){const n=[];let t=-1;for(;++t<e.length;)n[t]=gr(e[t]);return rn(r);function r(...i){let l=-1;for(;++l<n.length;)if(n[l].apply(this,i))return!0;return!1}}function ha(e){const n=e;return rn(t);function t(r){const i=r;let l;for(l in e)if(i[l]!==n[l])return!1;return!0}}function ma(e){return rn(n);function n(t){return t&&t.type===e}}function rn(e){return n;function n(t,r,i){return!!(ga(t)&&e.call(this,t,typeof r=="number"?r:void 0,i||void 0))}}function da(){return!0}function ga(e){return e!==null&&typeof e=="object"&&"type"in e}const yr=[],ya=!0,vt=!1,xa="skip";function ba(e,n,t,r){let i;typeof n=="function"&&typeof t!="function"?(r=t,t=n):i=n;const l=gr(i),o=r?-1:1;a(e,void 0,[])();function a(c,u,s){const p=c&&typeof c=="object"?c:{};if(typeof p.type=="string"){const f=typeof p.tagName=="string"?p.tagName:typeof p.name=="string"?p.name:void 0;Object.defineProperty(m,"name",{value:"node ("+(c.type+(f?"<"+f+">":""))+")"})}return m;function m(){let f=yr,k,b,P;if((!n||l(c,u,s[s.length-1]||void 0))&&(f=ka(t(c,s)),f[0]===vt))return f;if("children"in c&&c.children){const x=c;if(x.children&&f[0]!==xa)for(b=(r?x.children.length:-1)+o,P=s.concat(x);b>-1&&b<x.children.length;){const T=x.children[b];if(k=a(T,b,P)(),k[0]===vt)return k;b=typeof k[1]=="number"?k[1]:b+o}}return f}}}function ka(e){return Array.isArray(e)?e:typeof e=="number"?[ya,e]:e==null?yr:[e]}function xr(e,n,t,r){let i,l,o;typeof n=="function"&&typeof t!="function"?(l=void 0,o=n,i=t):(l=n,o=t,i=r),ba(e,l,a,i);function a(c,u){const s=u[u.length-1],p=s?s.children.indexOf(c):void 0;return o(c,p,s)}}const Pn={}.hasOwnProperty,wa={};function Sa(e,n){const t=n||wa,r=new Map,i=new Map,l=new Map,o={...ia,...t.handlers},a={all:u,applyData:Ea,definitionById:r,footnoteById:i,footnoteCounts:l,footnoteOrder:[],handlers:o,one:c,options:t,patch:Ca,wrap:Ia};return xr(e,function(s){if(s.type==="definition"||s.type==="footnoteDefinition"){const p=s.type==="definition"?r:i,m=String(s.identifier).toUpperCase();p.has(m)||p.set(m,s)}}),a;function c(s,p){const m=s.type,f=a.handlers[m];if(Pn.call(a.handlers,m)&&f)return f(a,s,p);if(a.options.passThrough&&a.options.passThrough.includes(m)){if("children"in s){const{children:b,...P}=s,x=en(P);return x.children=a.all(s),x}return en(s)}return(a.options.unknownHandler||va)(a,s,p)}function u(s){const p=[];if("children"in s){const m=s.children;let f=-1;for(;++f<m.length;){const k=a.one(m[f],s);if(k){if(f&&m[f-1].type==="break"&&(!Array.isArray(k)&&k.type==="text"&&(k.value=It(k.value)),!Array.isArray(k)&&k.type==="element")){const b=k.children[0];b&&b.type==="text"&&(b.value=It(b.value))}Array.isArray(k)?p.push(...k):p.push(k)}}}return p}}function Ca(e,n){e.position&&(n.position=li(e))}function Ea(e,n){let t=n;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,l=e.data.hProperties;if(typeof r=="string")if(t.type==="element")t.tagName=r;else{const o="children"in t?t.children:[t];t={type:"element",tagName:r,properties:{},children:o}}t.type==="element"&&l&&Object.assign(t.properties,en(l)),"children"in t&&t.children&&i!==null&&i!==void 0&&(t.children=i)}return t}function va(e,n){const t=n.data||{},r="value"in n&&!(Pn.call(t,"hProperties")||Pn.call(t,"hChildren"))?{type:"text",value:n.value}:{type:"element",tagName:"div",properties:{},children:e.all(n)};return e.patch(n,r),e.applyData(n,r)}function Ia(e,n){const t=[];let r=-1;for(n&&t.push({type:"text",value:`
`});++r<e.length;)r&&t.push({type:"text",value:`
`}),t.push(e[r]);return n&&e.length>0&&t.push({type:"text",value:`
`}),t}function It(e){let n=0,t=e.charCodeAt(n);for(;t===9||t===32;)n++,t=e.charCodeAt(n);return e.slice(n)}function Pt(e,n){const t=Sa(e,n),r=t.one(e,void 0),i=pa(t),l=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&l.children.push({type:"text",value:`
`},i),l}function Pa(e,n){return e&&"run"in e?async function(t,r){const i=Pt(t,{file:r,...n});await e.run(i,r)}:function(t,r){return Pt(t,{file:r,...e||n})}}function Tt(e){if(e)throw e}var cn,Lt;function Ta(){if(Lt)return cn;Lt=1;var e=Object.prototype.hasOwnProperty,n=Object.prototype.toString,t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=function(u){return typeof Array.isArray=="function"?Array.isArray(u):n.call(u)==="[object Array]"},l=function(u){if(!u||n.call(u)!=="[object Object]")return!1;var s=e.call(u,"constructor"),p=u.constructor&&u.constructor.prototype&&e.call(u.constructor.prototype,"isPrototypeOf");if(u.constructor&&!s&&!p)return!1;var m;for(m in u);return typeof m>"u"||e.call(u,m)},o=function(u,s){t&&s.name==="__proto__"?t(u,s.name,{enumerable:!0,configurable:!0,value:s.newValue,writable:!0}):u[s.name]=s.newValue},a=function(u,s){if(s==="__proto__")if(e.call(u,s)){if(r)return r(u,s).value}else return;return u[s]};return cn=function c(){var u,s,p,m,f,k,b=arguments[0],P=1,x=arguments.length,T=!1;for(typeof b=="boolean"&&(T=b,b=arguments[1]||{},P=2),(b==null||typeof b!="object"&&typeof b!="function")&&(b={});P<x;++P)if(u=arguments[P],u!=null)for(s in u)p=a(b,s),m=a(u,s),b!==m&&(T&&m&&(l(m)||(f=i(m)))?(f?(f=!1,k=p&&i(p)?p:[]):k=p&&l(p)?p:{},o(b,{name:s,newValue:c(T,k,m)})):typeof m<"u"&&o(b,{name:s,newValue:m}));return b},cn}var La=Ta();const pn=Mt(La);function Tn(e){if(typeof e!="object"||e===null)return!1;const n=Object.getPrototypeOf(e);return(n===null||n===Object.prototype||Object.getPrototypeOf(n)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Aa(){const e=[],n={run:t,use:r};return n;function t(...i){let l=-1;const o=i.pop();if(typeof o!="function")throw new TypeError("Expected function as last argument, not "+o);a(null,...i);function a(c,...u){const s=e[++l];let p=-1;if(c){o(c);return}for(;++p<i.length;)(u[p]===null||u[p]===void 0)&&(u[p]=i[p]);i=u,s?Na(s,a)(...u):o(null,...u)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),n}}function Na(e,n){let t;return r;function r(...o){const a=e.length>o.length;let c;a&&o.push(i);try{c=e.apply(this,o)}catch(u){const s=u;if(a&&t)throw s;return i(s)}a||(c&&c.then&&typeof c.then=="function"?c.then(l,i):c instanceof Error?i(c):l(c))}function i(o,...a){t||(t=!0,n(o,...a))}function l(o){i(null,o)}}const pe={basename:Oa,dirname:za,extname:Da,join:Ra,sep:"/"};function Oa(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');$e(e);let t=0,r=-1,i=e.length,l;if(n===void 0||n.length===0||n.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(l){t=i+1;break}}else r<0&&(l=!0,r=i+1);return r<0?"":e.slice(t,r)}if(n===e)return"";let o=-1,a=n.length-1;for(;i--;)if(e.codePointAt(i)===47){if(l){t=i+1;break}}else o<0&&(l=!0,o=i+1),a>-1&&(e.codePointAt(i)===n.codePointAt(a--)?a<0&&(r=i):(a=-1,r=o));return t===r?r=o:r<0&&(r=e.length),e.slice(t,r)}function za(e){if($e(e),e.length===0)return".";let n=-1,t=e.length,r;for(;--t;)if(e.codePointAt(t)===47){if(r){n=t;break}}else r||(r=!0);return n<0?e.codePointAt(0)===47?"/":".":n===1&&e.codePointAt(0)===47?"//":e.slice(0,n)}function Da(e){$e(e);let n=e.length,t=-1,r=0,i=-1,l=0,o;for(;n--;){const a=e.codePointAt(n);if(a===47){if(o){r=n+1;break}continue}t<0&&(o=!0,t=n+1),a===46?i<0?i=n:l!==1&&(l=1):i>-1&&(l=-1)}return i<0||t<0||l===0||l===1&&i===t-1&&i===r+1?"":e.slice(i,t)}function Ra(...e){let n=-1,t;for(;++n<e.length;)$e(e[n]),e[n]&&(t=t===void 0?e[n]:t+"/"+e[n]);return t===void 0?".":Fa(t)}function Fa(e){$e(e);const n=e.codePointAt(0)===47;let t=_a(e,!n);return t.length===0&&!n&&(t="."),t.length>0&&e.codePointAt(e.length-1)===47&&(t+="/"),n?"/"+t:t}function _a(e,n){let t="",r=0,i=-1,l=0,o=-1,a,c;for(;++o<=e.length;){if(o<e.length)a=e.codePointAt(o);else{if(a===47)break;a=47}if(a===47){if(!(i===o-1||l===1))if(i!==o-1&&l===2){if(t.length<2||r!==2||t.codePointAt(t.length-1)!==46||t.codePointAt(t.length-2)!==46){if(t.length>2){if(c=t.lastIndexOf("/"),c!==t.length-1){c<0?(t="",r=0):(t=t.slice(0,c),r=t.length-1-t.lastIndexOf("/")),i=o,l=0;continue}}else if(t.length>0){t="",r=0,i=o,l=0;continue}}n&&(t=t.length>0?t+"/..":"..",r=2)}else t.length>0?t+="/"+e.slice(i+1,o):t=e.slice(i+1,o),r=o-i-1;i=o,l=0}else a===46&&l>-1?l++:l=-1}return t}function $e(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Ma={cwd:ja};function ja(){return"/"}function Ln(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function Ba(e){if(typeof e=="string")e=new URL(e);else if(!Ln(e)){const n=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw n.code="ERR_INVALID_ARG_TYPE",n}if(e.protocol!=="file:"){const n=new TypeError("The URL must be of scheme file");throw n.code="ERR_INVALID_URL_SCHEME",n}return Ha(e)}function Ha(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const n=e.pathname;let t=-1;for(;++t<n.length;)if(n.codePointAt(t)===37&&n.codePointAt(t+1)===50){const r=n.codePointAt(t+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(n)}const fn=["history","path","basename","stem","extname","dirname"];class br{constructor(n){let t;n?Ln(n)?t={path:n}:typeof n=="string"||Ua(n)?t={value:n}:t=n:t={},this.cwd="cwd"in t?"":Ma.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<fn.length;){const l=fn[r];l in t&&t[l]!==void 0&&t[l]!==null&&(this[l]=l==="history"?[...t[l]]:t[l])}let i;for(i in t)fn.includes(i)||(this[i]=t[i])}get basename(){return typeof this.path=="string"?pe.basename(this.path):void 0}set basename(n){mn(n,"basename"),hn(n,"basename"),this.path=pe.join(this.dirname||"",n)}get dirname(){return typeof this.path=="string"?pe.dirname(this.path):void 0}set dirname(n){At(this.basename,"dirname"),this.path=pe.join(n||"",this.basename)}get extname(){return typeof this.path=="string"?pe.extname(this.path):void 0}set extname(n){if(hn(n,"extname"),At(this.dirname,"extname"),n){if(n.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(n.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=pe.join(this.dirname,this.stem+(n||""))}get path(){return this.history[this.history.length-1]}set path(n){Ln(n)&&(n=Ba(n)),mn(n,"path"),this.path!==n&&this.history.push(n)}get stem(){return typeof this.path=="string"?pe.basename(this.path,this.extname):void 0}set stem(n){mn(n,"stem"),hn(n,"stem"),this.path=pe.join(this.dirname||"",n+(this.extname||""))}fail(n,t,r){const i=this.message(n,t,r);throw i.fatal=!0,i}info(n,t,r){const i=this.message(n,t,r);return i.fatal=void 0,i}message(n,t,r){const i=new J(n,t,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(n){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(n||void 0).decode(this.value)}}function hn(e,n){if(e&&e.includes(pe.sep))throw new Error("`"+n+"` cannot be a path: did not expect `"+pe.sep+"`")}function mn(e,n){if(!e)throw new Error("`"+n+"` cannot be empty")}function At(e,n){if(!e)throw new Error("Setting `"+n+"` requires `path` to be set too")}function Ua(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Va=function(e){const r=this.constructor.prototype,i=r[e],l=function(){return i.apply(l,arguments)};return Object.setPrototypeOf(l,r),l},qa={}.hasOwnProperty;class $n extends Va{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Aa()}copy(){const n=new $n;let t=-1;for(;++t<this.attachers.length;){const r=this.attachers[t];n.use(...r)}return n.data(pn(!0,{},this.namespace)),n}data(n,t){return typeof n=="string"?arguments.length===2?(yn("data",this.frozen),this.namespace[n]=t,this):qa.call(this.namespace,n)&&this.namespace[n]||void 0:n?(yn("data",this.frozen),this.namespace=n,this):this.namespace}freeze(){if(this.frozen)return this;const n=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=t.call(n,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(n){this.freeze();const t=Qe(n),r=this.parser||this.Parser;return dn("parse",r),r(String(t),t)}process(n,t){const r=this;return this.freeze(),dn("process",this.parser||this.Parser),gn("process",this.compiler||this.Compiler),t?i(void 0,t):new Promise(i);function i(l,o){const a=Qe(n),c=r.parse(a);r.run(c,a,function(s,p,m){if(s||!p||!m)return u(s);const f=p,k=r.stringify(f,m);Ya(k)?m.value=k:m.result=k,u(s,m)});function u(s,p){s||!p?o(s):l?l(p):t(void 0,p)}}}processSync(n){let t=!1,r;return this.freeze(),dn("processSync",this.parser||this.Parser),gn("processSync",this.compiler||this.Compiler),this.process(n,i),Ot("processSync","process",t),r;function i(l,o){t=!0,Tt(l),r=o}}run(n,t,r){Nt(n),this.freeze();const i=this.transformers;return!r&&typeof t=="function"&&(r=t,t=void 0),r?l(void 0,r):new Promise(l);function l(o,a){const c=Qe(t);i.run(n,c,u);function u(s,p,m){const f=p||n;s?a(s):o?o(f):r(void 0,f,m)}}}runSync(n,t){let r=!1,i;return this.run(n,t,l),Ot("runSync","run",r),i;function l(o,a){Tt(o),i=a,r=!0}}stringify(n,t){this.freeze();const r=Qe(t),i=this.compiler||this.Compiler;return gn("stringify",i),Nt(n),i(n,r)}use(n,...t){const r=this.attachers,i=this.namespace;if(yn("use",this.frozen),n!=null)if(typeof n=="function")c(n,t);else if(typeof n=="object")Array.isArray(n)?a(n):o(n);else throw new TypeError("Expected usable value, not `"+n+"`");return this;function l(u){if(typeof u=="function")c(u,[]);else if(typeof u=="object")if(Array.isArray(u)){const[s,...p]=u;c(s,p)}else o(u);else throw new TypeError("Expected usable value, not `"+u+"`")}function o(u){if(!("plugins"in u)&&!("settings"in u))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");a(u.plugins),u.settings&&(i.settings=pn(!0,i.settings,u.settings))}function a(u){let s=-1;if(u!=null)if(Array.isArray(u))for(;++s<u.length;){const p=u[s];l(p)}else throw new TypeError("Expected a list of plugins, not `"+u+"`")}function c(u,s){let p=-1,m=-1;for(;++p<r.length;)if(r[p][0]===u){m=p;break}if(m===-1)r.push([u,...s]);else if(s.length>0){let[f,...k]=s;const b=r[m][1];Tn(b)&&Tn(f)&&(f=pn(!0,b,f)),r[m]=[u,f,...k]}}}}const $a=new $n().freeze();function dn(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function gn(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function yn(e,n){if(n)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Nt(e){if(!Tn(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Ot(e,n,t){if(!t)throw new Error("`"+e+"` finished async. Use `"+n+"` instead")}function Qe(e){return Wa(e)?e:new br(e)}function Wa(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Ya(e){return typeof e=="string"||Xa(e)}function Xa(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Qa="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",zt=[],Dt={allowDangerousHtml:!0},Ga=/^(https?|ircs?|mailto|xmpp)$/i,Ka=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function vu(e){const n=Ja(e),t=Za(e);return eu(n.runSync(n.parse(t),t),e)}function Ja(e){const n=e.rehypePlugins||zt,t=e.remarkPlugins||zt,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Dt}:Dt;return $a().use(Oo).use(t).use(Pa,r).use(n)}function Za(e){const n=e.children||"",t=new br;return typeof n=="string"&&(t.value=n),t}function eu(e,n){const t=n.allowedElements,r=n.allowElement,i=n.components,l=n.disallowedElements,o=n.skipHtml,a=n.unwrapDisallowed,c=n.urlTransform||nu;for(const s of Ka)Object.hasOwn(n,s.from)&&(""+s.from+(s.to?"use `"+s.to+"` instead":"remove it")+Qa+s.id,void 0);return xr(e,u),ci(e,{Fragment:Ge.Fragment,components:i,ignoreInvalidStyle:!0,jsx:Ge.jsx,jsxs:Ge.jsxs,passKeys:!0,passNode:!0});function u(s,p,m){if(s.type==="raw"&&m&&typeof p=="number")return o?m.children.splice(p,1):m.children[p]={type:"text",value:s.value},p;if(s.type==="element"){let f;for(f in an)if(Object.hasOwn(an,f)&&Object.hasOwn(s.properties,f)){const k=s.properties[f],b=an[f];(b===null||b.includes(s.tagName))&&(s.properties[f]=c(String(k||""),f,s))}}if(s.type==="element"){let f=t?!t.includes(s.tagName):l?l.includes(s.tagName):!1;if(!f&&r&&typeof p=="number"&&(f=!r(s,p,m)),f&&m&&typeof p=="number")return a&&s.children?m.children.splice(p,1,...s.children):m.children.splice(p,1),p}}}function nu(e){const n=e.indexOf(":"),t=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return n===-1||i!==-1&&n>i||t!==-1&&n>t||r!==-1&&n>r||Ga.test(e.slice(0,n))?e:""}function tu(e,n){if(e==null)return{};var t,r,i=Rr(e,n);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)t=l[r],n.indexOf(t)===-1&&{}.propertyIsEnumerable.call(e,t)&&(i[t]=e[t])}return i}function An(e,n){(n==null||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}function ru(e){if(Array.isArray(e))return An(e)}function iu(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function lu(e,n){if(e){if(typeof e=="string")return An(e,n);var t={}.toString.call(e).slice(8,-1);return t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set"?Array.from(e):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?An(e,n):void 0}}function ou(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Nn(e){return ru(e)||iu(e)||lu(e)||ou()}function Ve(e){"@babel/helpers - typeof";return Ve=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ve(e)}function au(e,n){if(Ve(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var r=t.call(e,n);if(Ve(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function uu(e){var n=au(e,"string");return Ve(n)=="symbol"?n:n+""}function kr(e,n,t){return(n=uu(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Rt(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function Te(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Rt(Object(t),!0).forEach(function(r){kr(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Rt(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function su(e){var n=e.length;if(n===0||n===1)return e;if(n===2)return[e[0],e[1],"".concat(e[0],".").concat(e[1]),"".concat(e[1],".").concat(e[0])];if(n===3)return[e[0],e[1],e[2],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0])];if(n>=4)return[e[0],e[1],e[2],e[3],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[2],".").concat(e[3]),"".concat(e[3],".").concat(e[0]),"".concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[0]),"".concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[3],".").concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[2],".").concat(e[1],".").concat(e[0])]}var xn={};function cu(e){if(e.length===0||e.length===1)return e;var n=e.join(".");return xn[n]||(xn[n]=su(e)),xn[n]}function pu(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2?arguments[2]:void 0,r=e.filter(function(l){return l!=="token"}),i=cu(r);return i.reduce(function(l,o){return Te(Te({},l),t[o])},n)}function Ft(e){return e.join(" ")}function fu(e,n){var t=0;return function(r){return t+=1,r.map(function(i,l){return wr({node:i,stylesheet:e,useInlineStyles:n,key:"code-segment-".concat(t,"-").concat(l)})})}}function wr(e){var n=e.node,t=e.stylesheet,r=e.style,i=r===void 0?{}:r,l=e.useInlineStyles,o=e.key,a=n.properties,c=n.type,u=n.tagName,s=n.value;if(c==="text")return s;if(u){var p=fu(t,l),m;if(!l)m=Te(Te({},a),{},{className:Ft(a.className)});else{var f=Object.keys(t).reduce(function(x,T){return T.split(".").forEach(function(C){x.includes(C)||x.push(C)}),x},[]),k=a.className&&a.className.includes("token")?["token"]:[],b=a.className&&k.concat(a.className.filter(function(x){return!f.includes(x)}));m=Te(Te({},a),{},{className:Ft(b)||void 0,style:pu(a.className,Object.assign({},a.style,i),t)})}var P=p(n.children);return be.createElement(u,bn({key:o},m),P)}}const hu=function(e,n){var t=e.listLanguages();return t.indexOf(n)!==-1};var mu=["language","children","style","customStyle","codeTagProps","useInlineStyles","showLineNumbers","showInlineLineNumbers","startingLineNumber","lineNumberContainerStyle","lineNumberStyle","wrapLines","wrapLongLines","lineProps","renderer","PreTag","CodeTag","code","astGenerator"];function _t(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function ke(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?_t(Object(t),!0).forEach(function(r){kr(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):_t(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var du=/\n/g;function gu(e){return e.match(du)}function yu(e){var n=e.lines,t=e.startingLineNumber,r=e.style;return n.map(function(i,l){var o=l+t;return be.createElement("span",{key:"line-".concat(l),className:"react-syntax-highlighter-line-number",style:typeof r=="function"?r(o):r},"".concat(o,`
`))})}function xu(e){var n=e.codeString,t=e.codeStyle,r=e.containerStyle,i=r===void 0?{float:"left",paddingRight:"10px"}:r,l=e.numberStyle,o=l===void 0?{}:l,a=e.startingLineNumber;return be.createElement("code",{style:Object.assign({},t,i)},yu({lines:n.replace(/\n$/,"").split(`
`),style:o,startingLineNumber:a}))}function bu(e){return"".concat(e.toString().length,".25em")}function Sr(e,n){return{type:"element",tagName:"span",properties:{key:"line-number--".concat(e),className:["comment","linenumber","react-syntax-highlighter-line-number"],style:n},children:[{type:"text",value:e}]}}function Cr(e,n,t){var r={display:"inline-block",minWidth:bu(t),paddingRight:"1em",textAlign:"right",userSelect:"none"},i=typeof e=="function"?e(n):e,l=ke(ke({},r),i);return l}function Je(e){var n=e.children,t=e.lineNumber,r=e.lineNumberStyle,i=e.largestLineNumber,l=e.showInlineLineNumbers,o=e.lineProps,a=o===void 0?{}:o,c=e.className,u=c===void 0?[]:c,s=e.showLineNumbers,p=e.wrapLongLines,m=e.wrapLines,f=m===void 0?!1:m,k=f?ke({},typeof a=="function"?a(t):a):{};if(k.className=k.className?[].concat(Nn(k.className.trim().split(/\s+/)),Nn(u)):u,t&&l){var b=Cr(r,t,i);n.unshift(Sr(t,b))}return p&s&&(k.style=ke({display:"flex"},k.style)),{type:"element",tagName:"span",properties:k,children:n}}function Er(e){for(var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[],r=0;r<e.length;r++){var i=e[r];if(i.type==="text")t.push(Je({children:[i],className:Nn(new Set(n))}));else if(i.children){var l=n.concat(i.properties.className);Er(i.children,l).forEach(function(o){return t.push(o)})}}return t}function ku(e,n,t,r,i,l,o,a,c){var u,s=Er(e.value),p=[],m=-1,f=0;function k(R,y){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];return Je({children:R,lineNumber:y,lineNumberStyle:a,largestLineNumber:o,showInlineLineNumbers:i,lineProps:t,className:N,showLineNumbers:r,wrapLongLines:c,wrapLines:n})}function b(R,y){if(r&&y&&i){var N=Cr(a,y,o);R.unshift(Sr(y,N))}return R}function P(R,y){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];return n||N.length>0?k(R,y,N):b(R,y)}for(var x=function(){var y=s[f],N=y.children[0].value,H=gu(N);if(H){var F=N.split(`
`);F.forEach(function(M,L){var E=r&&p.length+l,I={type:"text",value:"".concat(M,`
`)};if(L===0){var v=s.slice(m+1,f).concat(Je({children:[I],className:y.properties.className})),A=P(v,E);p.push(A)}else if(L===F.length-1){var W=s[f+1]&&s[f+1].children&&s[f+1].children[0],Y={type:"text",value:"".concat(M)};if(W){var Q=Je({children:[Y],className:y.properties.className});s.splice(f+1,0,Q)}else{var G=[Y],h=P(G,E,y.properties.className);p.push(h)}}else{var K=[I],re=P(K,E,y.properties.className);p.push(re)}}),m=f}f++};f<s.length;)x();if(m!==s.length-1){var T=s.slice(m+1,s.length);if(T&&T.length){var C=r&&p.length+l,j=P(T,C);p.push(j)}}return n?p:(u=[]).concat.apply(u,p)}function wu(e){var n=e.rows,t=e.stylesheet,r=e.useInlineStyles;return n.map(function(i,l){return wr({node:i,stylesheet:t,useInlineStyles:r,key:"code-segement".concat(l)})})}function vr(e){return e&&typeof e.highlightAuto<"u"}function Su(e){var n=e.astGenerator,t=e.language,r=e.code,i=e.defaultCodeValue;if(vr(n)){var l=hu(n,t);return t==="text"?{value:i,language:"text"}:l?n.highlight(t,r):n.highlightAuto(r)}try{return t&&t!=="text"?{value:n.highlight(r,t)}:{value:i}}catch{return{value:i}}}function Iu(e,n){return function(r){var i=r.language,l=r.children,o=r.style,a=o===void 0?n:o,c=r.customStyle,u=c===void 0?{}:c,s=r.codeTagProps,p=s===void 0?{className:i?"language-".concat(i):void 0,style:ke(ke({},a['code[class*="language-"]']),a['code[class*="language-'.concat(i,'"]')])}:s,m=r.useInlineStyles,f=m===void 0?!0:m,k=r.showLineNumbers,b=k===void 0?!1:k,P=r.showInlineLineNumbers,x=P===void 0?!0:P,T=r.startingLineNumber,C=T===void 0?1:T,j=r.lineNumberContainerStyle,R=r.lineNumberStyle,y=R===void 0?{}:R,N=r.wrapLines,H=r.wrapLongLines,F=H===void 0?!1:H,M=r.lineProps,L=M===void 0?{}:M,E=r.renderer,I=r.PreTag,v=I===void 0?"pre":I,A=r.CodeTag,W=A===void 0?"code":A,Y=r.code,Q=Y===void 0?(Array.isArray(l)?l[0]:l)||"":Y,G=r.astGenerator,h=tu(r,mu);G=G||e;var K=b?be.createElement(xu,{containerStyle:j,codeStyle:p.style||{},numberStyle:y,startingLineNumber:C,codeString:Q}):null,re=a.hljs||a['pre[class*="language-"]']||{backgroundColor:"#fff"},d=vr(G)?"hljs":"prismjs",Z=f?Object.assign({},h,{style:Object.assign({},re,u)}):Object.assign({},h,{className:h.className?"".concat(d," ").concat(h.className):d,style:Object.assign({},u)});if(F?p.style=ke({whiteSpace:"pre-wrap"},p.style):p.style=ke({whiteSpace:"pre"},p.style),!G)return be.createElement(v,Z,K,be.createElement(W,p,Q));(N===void 0&&E||F)&&(N=!0),E=E||wu;var se=[{type:"text",value:Q}],$=Su({astGenerator:G,language:i,code:Q,defaultCodeValue:se});$.language===null&&($.value=se);var me=$.value.length;me===1&&$.value[0].type==="text"&&(me=$.value[0].value.split(`
`).length);var ce=me+C,ye=ku($,N,L,b,x,C,ce,y,F);return be.createElement(v,Z,be.createElement(W,p,!x&&K,E({rows:ye,stylesheet:a,useInlineStyles:f})))}}function Pu({children:e}){return Ge.jsx("p",{className:"pb-[10px] last:pb-0",children:e})}export{vt as E,vu as M,Rr as _,bn as a,Ri as b,gr as c,pt as d,xr as e,oe as f,fe as g,En as h,nn as i,q as j,O as k,B as l,ne as m,Ae as n,Eu as o,Pu as p,Li as q,jn as r,he as s,Pi as t,Fi as u,ba as v,Iu as w};
