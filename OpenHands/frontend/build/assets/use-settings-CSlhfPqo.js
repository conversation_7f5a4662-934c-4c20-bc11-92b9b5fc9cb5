import{u as r}from"./useQuery-Cu2nkJ8V.js";import{R as o}from"./chunk-C37GKA54-CBbYr_fP.js";import{p as n}from"./module-5laXsVNO.js";import{O as E}from"./open-hands-Ce72Fmtl.js";import{u as A,a as i}from"./use-config-jdwF3W4-.js";import{h as c}from"./open-hands-axios-CtirLpss.js";const T=()=>{const{data:e}=A(),_=i(),s=e?.APP_MODE;return r({queryKey:["user","authenticated",s],queryFn:async()=>{try{return await E.authenticate(s),!0}catch(t){if(c.isAxiosError(t)&&t.response?.status===401)return!1;throw t}},enabled:!!s&&!_,staleTime:1e3*60*5,gcTime:1e3*60*15,retry:!1,meta:{disableToast:!0}})},u={LLM_MODEL:"anthropic/claude-sonnet-4-20250514",LLM_BASE_URL:"",AGENT:"CodeActAgent",LANGUAGE:"en",LLM_API_KEY_SET:!1,SEARCH_API_KEY_SET:!1,CONFIRMATION_MODE:!1,SECURITY_ANALYZER:"",REMOTE_RUNTIME_RESOURCE_FACTOR:1,PROVIDER_TOKENS_SET:{},ENABLE_DEFAULT_CONDENSER:!0,ENABLE_SOUND_NOTIFICATIONS:!1,USER_CONSENTS_TO_ANALYTICS:!1,ENABLE_PROACTIVE_CONVERSATION_STARTERS:!1,SEARCH_API_KEY:"",IS_NEW_USER:!0,MAX_BUDGET_PER_TASK:null,EMAIL:"",EMAIL_VERIFIED:!0,MCP_CONFIG:{sse_servers:[],stdio_servers:[]}},S=async()=>{const e=await E.getSettings();return{LLM_MODEL:e.llm_model,LLM_BASE_URL:e.llm_base_url,AGENT:e.agent,LANGUAGE:e.language,CONFIRMATION_MODE:e.confirmation_mode,SECURITY_ANALYZER:e.security_analyzer,LLM_API_KEY_SET:e.llm_api_key_set,SEARCH_API_KEY_SET:e.search_api_key_set,REMOTE_RUNTIME_RESOURCE_FACTOR:e.remote_runtime_resource_factor,PROVIDER_TOKENS_SET:e.provider_tokens_set,ENABLE_DEFAULT_CONDENSER:e.enable_default_condenser,ENABLE_SOUND_NOTIFICATIONS:e.enable_sound_notifications,ENABLE_PROACTIVE_CONVERSATION_STARTERS:e.enable_proactive_conversation_starters,USER_CONSENTS_TO_ANALYTICS:e.user_consents_to_analytics,SEARCH_API_KEY:e.search_api_key||"",MAX_BUDGET_PER_TASK:e.max_budget_per_task,EMAIL:e.email||"",EMAIL_VERIFIED:e.email_verified,MCP_CONFIG:e.mcp_config,IS_NEW_USER:!1}},f=()=>{const e=i(),{data:_}=T(),s=r({queryKey:["settings"],queryFn:S,retry:(t,a)=>a.status!==404,refetchOnWindowFocus:!1,staleTime:1e3*60*5,gcTime:1e3*60*15,enabled:!e&&!!_,meta:{disableToast:!0}});return o.useEffect(()=>{s.isFetched&&s.data?.LLM_API_KEY_SET&&n.capture("user_activated")},[s.data?.LLM_API_KEY_SET,s.isFetched]),s.error?.status===404?{data:u,error:s.error,isError:s.isError,isLoading:s.isLoading,isFetching:s.isFetching,isFetched:s.isFetched,isSuccess:s.isSuccess,status:s.status,fetchStatus:s.fetchStatus,refetch:s.refetch}:s};export{u as D,T as a,f as u};
