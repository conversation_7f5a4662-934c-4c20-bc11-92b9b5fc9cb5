import{r as he,j as lt}from"./chunk-C37GKA54-CBbYr_fP.js";var Ue=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Q=typeof window>"u"||"Deno"in globalThis;function rr(){}function sr(e,t){return typeof e=="function"?e(t):e}function ft(e){return typeof e=="number"&&e>=0&&e!==1/0}function or(e,t){return Math.max(e+(t||0)-Date.now(),0)}function ir(e,t){return typeof e=="function"?e(t):e}function ar(e,t){return typeof e=="function"?e(t):e}function cr(e,t){const{type:n="all",exact:r,fetchStatus:s,predicate:o,queryKey:i,stale:c}=e;if(i){if(r){if(t.queryHash!==dt(i,t.options))return!1}else if(!pe(t.queryKey,i))return!1}if(n!=="all"){const f=t.isActive();if(n==="active"&&!f||n==="inactive"&&f)return!1}return!(typeof c=="boolean"&&t.isStale()!==c||s&&s!==t.state.fetchStatus||o&&!o(t))}function ur(e,t){const{exact:n,status:r,predicate:s,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(n){if(oe(t.options.mutationKey)!==oe(o))return!1}else if(!pe(t.options.mutationKey,o))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function dt(e,t){return(t?.queryKeyHashFn||oe)(e)}function oe(e){return JSON.stringify(e,(t,n)=>ie(n)?Object.keys(n).sort().reduce((r,s)=>(r[s]=n[s],r),{}):n)}function pe(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>pe(e[n],t[n])):!1}function ke(e,t){if(e===t)return e;const n=Ee(e)&&Ee(t);if(n||ie(e)&&ie(t)){const r=n?e:Object.keys(e),s=r.length,o=n?t:Object.keys(t),i=o.length,c=n?[]:{},f=new Set(r);let l=0;for(let u=0;u<i;u++){const d=n?u:o[u];(!n&&f.has(d)||n)&&e[d]===void 0&&t[d]===void 0?(c[d]=void 0,l++):(c[d]=ke(e[d],t[d]),c[d]===e[d]&&e[d]!==void 0&&l++)}return s===i&&l===s?e:c}return t}function lr(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function Ee(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function ie(e){if(!ge(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!ge(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function ge(e){return Object.prototype.toString.call(e)==="[object Object]"}function ht(e){return new Promise(t=>{setTimeout(t,e)})}function fr(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?ke(e,t):t}function dr(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function hr(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var pt=Symbol();function pr(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:!e.queryFn||e.queryFn===pt?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function mr(e,t){return typeof e=="function"?e(...t):!!e}var mt=class extends Ue{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!Q&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(t=>{typeof t=="boolean"?this.setFocused(t):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return typeof this.#e=="boolean"?this.#e:globalThis.document?.visibilityState!=="hidden"}},yt=new mt,bt=class extends Ue{#e=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!Q&&window.addEventListener){const t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#e!==e&&(this.#e=e,this.listeners.forEach(n=>{n(e)}))}isOnline(){return this.#e}},De=new bt;function wt(){let e,t;const n=new Promise((s,o)=>{e=s,t=o});n.status="pending",n.catch(()=>{});function r(s){Object.assign(n,s),delete n.resolve,delete n.reject}return n.resolve=s=>{r({status:"fulfilled",value:s}),e(s)},n.reject=s=>{r({status:"rejected",reason:s}),t(s)},n}function Et(e){return Math.min(1e3*2**e,3e4)}function gt(e){return(e??"online")==="online"?De.isOnline():!0}var Be=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function yr(e){return e instanceof Be}function br(e){let t=!1,n=0,r=!1,s;const o=wt(),i=m=>{r||(b(new Be(m)),e.abort?.())},c=()=>{t=!0},f=()=>{t=!1},l=()=>yt.isFocused()&&(e.networkMode==="always"||De.isOnline())&&e.canRun(),u=()=>gt(e.networkMode)&&e.canRun(),d=m=>{r||(r=!0,e.onSuccess?.(m),s?.(),o.resolve(m))},b=m=>{r||(r=!0,e.onError?.(m),s?.(),o.reject(m))},g=()=>new Promise(m=>{s=p=>{(r||l())&&m(p)},e.onPause?.()}).then(()=>{s=void 0,r||e.onContinue?.()}),h=()=>{if(r)return;let m;const p=n===0?e.initialPromise:void 0;try{m=p??e.fn()}catch(w){m=Promise.reject(w)}Promise.resolve(m).then(d).catch(w=>{if(r)return;const E=e.retry??(Q?0:3),S=e.retryDelay??Et,C=typeof S=="function"?S(n,w):S,O=E===!0||typeof E=="number"&&n<E||typeof E=="function"&&E(n,w);if(t||!O){b(w);return}n++,e.onFail?.(n,w),ht(C).then(()=>l()?void 0:g()).then(()=>{t?b(w):h()})})};return{promise:o,cancel:i,continue:()=>(s?.(),o),cancelRetry:c,continueRetry:f,canStart:u,start:()=>(u()?h():g().then(h),o)}}var St=e=>setTimeout(e,0);function Rt(){let e=[],t=0,n=c=>{c()},r=c=>{c()},s=St;const o=c=>{t?e.push(c):s(()=>{n(c)})},i=()=>{const c=e;e=[],c.length&&s(()=>{r(()=>{c.forEach(f=>{n(f)})})})};return{batch:c=>{let f;t++;try{f=c()}finally{t--,t||i()}return f},batchCalls:c=>(...f)=>{o(()=>{c(...f)})},schedule:o,setNotifyFunction:c=>{n=c},setBatchNotifyFunction:c=>{r=c},setScheduler:c=>{s=c}}}var wr=Rt(),Er=class{#e;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),ft(this.gcTime)&&(this.#e=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Q?1/0:5*60*1e3))}clearGcTimeout(){this.#e&&(clearTimeout(this.#e),this.#e=void 0)}},qe=he.createContext(void 0),gr=e=>{const t=he.useContext(qe);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Sr=({client:e,children:t})=>(he.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),lt.jsx(qe.Provider,{value:e,children:t}));function ve(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ot}=Object.prototype,{getPrototypeOf:me}=Object,{iterator:G,toStringTag:Me}=Symbol,X=(e=>t=>{const n=Ot.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),F=e=>(e=e.toLowerCase(),t=>X(t)===e),Z=e=>t=>typeof t===e,{isArray:k}=Array,q=Z("undefined");function v(e){return e!==null&&!q(e)&&e.constructor!==null&&!q(e.constructor)&&A(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ie=F("ArrayBuffer");function Tt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ie(e.buffer),t}const At=Z("string"),A=Z("function"),He=Z("number"),M=e=>e!==null&&typeof e=="object",xt=e=>e===!0||e===!1,$=e=>{if(X(e)!=="object")return!1;const t=me(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Me in e)&&!(G in e)},Ct=e=>{if(!M(e)||v(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Ft=F("Date"),Pt=F("File"),Nt=F("Blob"),Lt=F("FileList"),jt=e=>M(e)&&A(e.pipe),_t=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||A(e.append)&&((t=X(e))==="formdata"||t==="object"&&A(e.toString)&&e.toString()==="[object FormData]"))},Ut=F("URLSearchParams"),[kt,Dt,Bt,qt]=["ReadableStream","Request","Response","Headers"].map(F),vt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function I(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),k(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{if(v(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function ze(e,t){if(v(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const j=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,$e=e=>!q(e)&&e!==j;function ae(){const{caseless:e}=$e(this)&&this||{},t={},n=(r,s)=>{const o=e&&ze(t,s)||s;$(t[o])&&$(r)?t[o]=ae(t[o],r):$(r)?t[o]=ae({},r):k(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&I(arguments[r],n);return t}const Mt=(e,t,n,{allOwnKeys:r}={})=>(I(t,(s,o)=>{n&&A(s)?e[o]=ve(s,n):e[o]=s},{allOwnKeys:r}),e),It=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ht=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},zt=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&me(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},$t=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Jt=e=>{if(!e)return null;if(k(e))return e;let t=e.length;if(!He(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Kt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&me(Uint8Array)),Vt=(e,t)=>{const r=(e&&e[G]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Wt=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Qt=F("HTMLFormElement"),Gt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Se=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Xt=F("RegExp"),Je=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};I(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Zt=e=>{Je(e,(t,n)=>{if(A(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(A(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Yt=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return k(e)?r(e):r(String(e).split(t)),n},en=()=>{},tn=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function nn(e){return!!(e&&A(e.append)&&e[Me]==="FormData"&&e[G])}const rn=e=>{const t=new Array(10),n=(r,s)=>{if(M(r)){if(t.indexOf(r)>=0)return;if(v(r))return r;if(!("toJSON"in r)){t[s]=r;const o=k(r)?[]:{};return I(r,(i,c)=>{const f=n(i,s+1);!q(f)&&(o[c]=f)}),t[s]=void 0,o}}return r};return n(e,0)},sn=F("AsyncFunction"),on=e=>e&&(M(e)||A(e))&&A(e.then)&&A(e.catch),Ke=((e,t)=>e?setImmediate:t?((n,r)=>(j.addEventListener("message",({source:s,data:o})=>{s===j&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),j.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",A(j.postMessage)),an=typeof queueMicrotask<"u"?queueMicrotask.bind(j):typeof process<"u"&&process.nextTick||Ke,cn=e=>e!=null&&A(e[G]),a={isArray:k,isArrayBuffer:Ie,isBuffer:v,isFormData:_t,isArrayBufferView:Tt,isString:At,isNumber:He,isBoolean:xt,isObject:M,isPlainObject:$,isEmptyObject:Ct,isReadableStream:kt,isRequest:Dt,isResponse:Bt,isHeaders:qt,isUndefined:q,isDate:Ft,isFile:Pt,isBlob:Nt,isRegExp:Xt,isFunction:A,isStream:jt,isURLSearchParams:Ut,isTypedArray:Kt,isFileList:Lt,forEach:I,merge:ae,extend:Mt,trim:vt,stripBOM:It,inherits:Ht,toFlatObject:zt,kindOf:X,kindOfTest:F,endsWith:$t,toArray:Jt,forEachEntry:Vt,matchAll:Wt,isHTMLForm:Qt,hasOwnProperty:Se,hasOwnProp:Se,reduceDescriptors:Je,freezeMethods:Zt,toObjectSet:Yt,toCamelCase:Gt,noop:en,toFiniteNumber:tn,findKey:ze,global:j,isContextDefined:$e,isSpecCompliantForm:nn,toJSONObject:rn,isAsyncFn:sn,isThenable:on,setImmediate:Ke,asap:an,isIterable:cn};function y(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ve=y.prototype,We={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{We[e]={value:e}});Object.defineProperties(y,We);Object.defineProperty(Ve,"isAxiosError",{value:!0});y.from=(e,t,n,r,s,o)=>{const i=Object.create(Ve);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),y.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const un=null;function ce(e){return a.isPlainObject(e)||a.isArray(e)}function Qe(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Re(e,t,n){return e?e.concat(t).map(function(s,o){return s=Qe(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function ln(e){return a.isArray(e)&&!e.some(ce)}const fn=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function Y(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,p){return!a.isUndefined(p[m])});const r=n.metaTokens,s=n.visitor||u,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function l(h){if(h===null)return"";if(a.isDate(h))return h.toISOString();if(a.isBoolean(h))return h.toString();if(!f&&a.isBlob(h))throw new y("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(h)||a.isTypedArray(h)?f&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function u(h,m,p){let w=h;if(h&&!p&&typeof h=="object"){if(a.endsWith(m,"{}"))m=r?m:m.slice(0,-2),h=JSON.stringify(h);else if(a.isArray(h)&&ln(h)||(a.isFileList(h)||a.endsWith(m,"[]"))&&(w=a.toArray(h)))return m=Qe(m),w.forEach(function(S,C){!(a.isUndefined(S)||S===null)&&t.append(i===!0?Re([m],C,o):i===null?m:m+"[]",l(S))}),!1}return ce(h)?!0:(t.append(Re(p,m,o),l(h)),!1)}const d=[],b=Object.assign(fn,{defaultVisitor:u,convertValue:l,isVisitable:ce});function g(h,m){if(!a.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+m.join("."));d.push(h),a.forEach(h,function(w,E){(!(a.isUndefined(w)||w===null)&&s.call(t,w,a.isString(E)?E.trim():E,m,b))===!0&&g(w,m?m.concat(E):[E])}),d.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Oe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function ye(e,t){this._pairs=[],e&&Y(e,this,t)}const Ge=ye.prototype;Ge.append=function(t,n){this._pairs.push([t,n])};Ge.toString=function(t){const n=t?function(r){return t.call(this,r,Oe)}:Oe;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function dn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Xe(e,t,n){if(!t)return e;const r=n&&n.encode||dn;a.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=a.isURLSearchParams(t)?t.toString():new ye(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Te{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Ze={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},hn=typeof URLSearchParams<"u"?URLSearchParams:ye,pn=typeof FormData<"u"?FormData:null,mn=typeof Blob<"u"?Blob:null,yn={isBrowser:!0,classes:{URLSearchParams:hn,FormData:pn,Blob:mn},protocols:["http","https","file","blob","url","data"]},be=typeof window<"u"&&typeof document<"u",ue=typeof navigator=="object"&&navigator||void 0,bn=be&&(!ue||["ReactNative","NativeScript","NS"].indexOf(ue.product)<0),wn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",En=be&&window.location.href||"http://localhost",gn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:be,hasStandardBrowserEnv:bn,hasStandardBrowserWebWorkerEnv:wn,navigator:ue,origin:En},Symbol.toStringTag,{value:"Module"})),T={...gn,...yn};function Sn(e,t){return Y(e,new T.classes.URLSearchParams,{visitor:function(n,r,s,o){return T.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function Rn(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function On(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Ye(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=n.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&a.isArray(s[i])&&(s[i]=On(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,s)=>{t(Rn(r),s,n,0)}),n}return null}function Tn(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const H={transitional:Ze,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(Ye(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Sn(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return Y(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Tn(t)):t}],transformResponse:[function(t){const n=this.transitional||H.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?y.from(c,y.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{H.headers[e]={}});const An=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),xn=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&An[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Ae=Symbol("internals");function B(e){return e&&String(e).trim().toLowerCase()}function J(e){return e===!1||e==null?e:a.isArray(e)?e.map(J):String(e)}function Cn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Fn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ne(e,t,n,r,s){if(a.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function Pn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Nn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let x=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,f,l){const u=B(f);if(!u)throw new Error("header name must be a non-empty string");const d=a.findKey(s,u);(!d||s[d]===void 0||l===!0||l===void 0&&s[d]!==!1)&&(s[d||f]=J(c))}const i=(c,f)=>a.forEach(c,(l,u)=>o(l,u,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!Fn(t))i(xn(t),n);else if(a.isObject(t)&&a.isIterable(t)){let c={},f,l;for(const u of t){if(!a.isArray(u))throw TypeError("Object iterator must return a key-value pair");c[l=u[0]]=(f=c[l])?a.isArray(f)?[...f,u[1]]:[f,u[1]]:u[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=B(t),t){const r=a.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Cn(s);if(a.isFunction(n))return n.call(this,s,r);if(a.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=B(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ne(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=B(i),i){const c=a.findKey(r,i);c&&(!n||ne(r,r[c],c,n))&&(delete r[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||ne(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return a.forEach(this,(s,o)=>{const i=a.findKey(r,o);if(i){n[i]=J(s),delete n[o];return}const c=t?Pn(o):String(o).trim();c!==o&&delete n[o],n[c]=J(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Ae]=this[Ae]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=B(i);r[c]||(Nn(s,i),r[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};x.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(x.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(x);function re(e,t){const n=this||H,r=t||n,s=x.from(r.headers);let o=r.data;return a.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function et(e){return!!(e&&e.__CANCEL__)}function D(e,t,n){y.call(this,e??"canceled",y.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(D,y,{__CANCEL__:!0});function tt(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new y("Request failed with status code "+n.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Ln(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function jn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const l=Date.now(),u=r[o];i||(i=l),n[s]=f,r[s]=l;let d=o,b=0;for(;d!==s;)b+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-i<t)return;const g=u&&l-u;return g?Math.round(b*1e3/g):void 0}}function _n(e,t){let n=0,r=1e3/t,s,o;const i=(l,u=Date.now())=>{n=u,s=null,o&&(clearTimeout(o),o=null),e(...l)};return[(...l)=>{const u=Date.now(),d=u-n;d>=r?i(l,u):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const V=(e,t,n=3)=>{let r=0;const s=jn(50,250);return _n(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-r,l=s(f),u=i<=c;r=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:l||void 0,estimated:l&&c&&u?(c-i)/l:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(d)},n)},xe=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ce=e=>(...t)=>a.asap(()=>e(...t)),Un=T.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,T.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,kn=T.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Dn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function nt(e,t,n){let r=!Dn(t);return e&&(r||n==!1)?Bn(e,t):t}const Fe=e=>e instanceof x?{...e}:e;function U(e,t){t=t||{};const n={};function r(l,u,d,b){return a.isPlainObject(l)&&a.isPlainObject(u)?a.merge.call({caseless:b},l,u):a.isPlainObject(u)?a.merge({},u):a.isArray(u)?u.slice():u}function s(l,u,d,b){if(a.isUndefined(u)){if(!a.isUndefined(l))return r(void 0,l,d,b)}else return r(l,u,d,b)}function o(l,u){if(!a.isUndefined(u))return r(void 0,u)}function i(l,u){if(a.isUndefined(u)){if(!a.isUndefined(l))return r(void 0,l)}else return r(void 0,u)}function c(l,u,d){if(d in t)return r(l,u);if(d in e)return r(void 0,l)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(l,u,d)=>s(Fe(l),Fe(u),d,!0)};return a.forEach(Object.keys({...e,...t}),function(u){const d=f[u]||s,b=d(e[u],t[u],u);a.isUndefined(b)&&d!==c||(n[u]=b)}),n}const rt=e=>{const t=U({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=x.from(i),t.url=Xe(nt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[l,...u]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...u].join("; "))}}if(T.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&Un(t.url))){const l=s&&o&&kn.read(o);l&&i.set(s,l)}return t},qn=typeof XMLHttpRequest<"u",vn=qn&&function(e){return new Promise(function(n,r){const s=rt(e);let o=s.data;const i=x.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:l}=s,u,d,b,g,h;function m(){g&&g(),h&&h(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function w(){if(!p)return;const S=x.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),O={data:!c||c==="text"||c==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:S,config:e,request:p};tt(function(L){n(L),m()},function(L){r(L),m()},O),p=null}"onloadend"in p?p.onloadend=w:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(w)},p.onabort=function(){p&&(r(new y("Request aborted",y.ECONNABORTED,e,p)),p=null)},p.onerror=function(){r(new y("Network Error",y.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let C=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const O=s.transitional||Ze;s.timeoutErrorMessage&&(C=s.timeoutErrorMessage),r(new y(C,O.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,p)),p=null},o===void 0&&i.setContentType(null),"setRequestHeader"in p&&a.forEach(i.toJSON(),function(C,O){p.setRequestHeader(O,C)}),a.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),c&&c!=="json"&&(p.responseType=s.responseType),l&&([b,h]=V(l,!0),p.addEventListener("progress",b)),f&&p.upload&&([d,g]=V(f),p.upload.addEventListener("progress",d),p.upload.addEventListener("loadend",g)),(s.cancelToken||s.signal)&&(u=S=>{p&&(r(!S||S.type?new D(null,e,p):S),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const E=Ln(s.url);if(E&&T.protocols.indexOf(E)===-1){r(new y("Unsupported protocol "+E+":",y.ERR_BAD_REQUEST,e));return}p.send(o||null)})},Mn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(l){if(!s){s=!0,c();const u=l instanceof Error?l:this.reason;r.abort(u instanceof y?u:new D(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),e=null)};e.forEach(l=>l.addEventListener("abort",o));const{signal:f}=r;return f.unsubscribe=()=>a.asap(c),f}},In=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},Hn=async function*(e,t){for await(const n of zn(e))yield*In(n,t)},zn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Pe=(e,t,n,r)=>{const s=Hn(e,t);let o=0,i,c=f=>{i||(i=!0,r&&r(f))};return new ReadableStream({async pull(f){try{const{done:l,value:u}=await s.next();if(l){c(),f.close();return}let d=u.byteLength;if(n){let b=o+=d;n(b)}f.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},ee=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",st=ee&&typeof ReadableStream=="function",$n=ee&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ot=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Jn=st&&ot(()=>{let e=!1;const t=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ne=64*1024,le=st&&ot(()=>a.isReadableStream(new Response("").body)),W={stream:le&&(e=>e.body)};ee&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!W[t]&&(W[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,r)})})})(new Response);const Kn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(T.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await $n(e)).byteLength},Vn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??Kn(t)},Wn=ee&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:b}=rt(e);l=l?(l+"").toLowerCase():"text";let g=Mn([s,o&&o.toAbortSignal()],i),h;const m=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let p;try{if(f&&Jn&&n!=="get"&&n!=="head"&&(p=await Vn(u,r))!==0){let O=new Request(t,{method:"POST",body:r,duplex:"half"}),N;if(a.isFormData(r)&&(N=O.headers.get("content-type"))&&u.setContentType(N),O.body){const[L,z]=xe(p,V(Ce(f)));r=Pe(O.body,Ne,L,z)}}a.isString(d)||(d=d?"include":"omit");const w="credentials"in Request.prototype;h=new Request(t,{...b,signal:g,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:w?d:void 0});let E=await fetch(h,b);const S=le&&(l==="stream"||l==="response");if(le&&(c||S&&m)){const O={};["status","statusText","headers"].forEach(we=>{O[we]=E[we]});const N=a.toFiniteNumber(E.headers.get("content-length")),[L,z]=c&&xe(N,V(Ce(c),!0))||[];E=new Response(Pe(E.body,Ne,L,()=>{z&&z(),m&&m()}),O)}l=l||"text";let C=await W[a.findKey(W,l)||"text"](E,e);return!S&&m&&m(),await new Promise((O,N)=>{tt(O,N,{data:C,headers:x.from(E.headers),status:E.status,statusText:E.statusText,config:e,request:h})})}catch(w){throw m&&m(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,h),{cause:w.cause||w}):y.from(w,w&&w.code,e,h)}}),fe={http:un,xhr:vn,fetch:Wn};a.forEach(fe,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Le=e=>`- ${e}`,Qn=e=>a.isFunction(e)||e===null||e===!1,it={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Qn(n)&&(r=fe[(i=String(n)).toLowerCase()],r===void 0))throw new y(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Le).join(`
`):" "+Le(o[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:fe};function se(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new D(null,e)}function je(e){return se(e),e.headers=x.from(e.headers),e.data=re.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),it.getAdapter(e.adapter||H.adapter)(e).then(function(r){return se(e),r.data=re.call(e,e.transformResponse,r),r.headers=x.from(r.headers),r},function(r){return et(r)||(se(e),r&&r.response&&(r.response.data=re.call(e,e.transformResponse,r.response),r.response.headers=x.from(r.response.headers))),Promise.reject(r)})}const at="1.11.0",te={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{te[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const _e={};te.transitional=function(t,n,r){function s(o,i){return"[Axios v"+at+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new y(s(i," has been removed"+(n?" in "+n:"")),y.ERR_DEPRECATED);return n&&!_e[i]&&(_e[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};te.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Gn(e,t,n){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new y("option "+o+" must be "+f,y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new y("Unknown option "+o,y.ERR_BAD_OPTION)}}const K={assertOptions:Gn,validators:te},P=K.validators;let _=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Te,response:new Te}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=U(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&K.assertOptions(r,{silentJSONParsing:P.transitional(P.boolean),forcedJSONParsing:P.transitional(P.boolean),clarifyTimeoutError:P.transitional(P.boolean)},!1),s!=null&&(a.isFunction(s)?n.paramsSerializer={serialize:s}:K.assertOptions(s,{encode:P.function,serialize:P.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),K.assertOptions(n,{baseUrl:P.spelling("baseURL"),withXsrfToken:P.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],h=>{delete o[h]}),n.headers=x.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(f=f&&m.synchronous,c.unshift(m.fulfilled,m.rejected))});const l=[];this.interceptors.response.forEach(function(m){l.push(m.fulfilled,m.rejected)});let u,d=0,b;if(!f){const h=[je.bind(this),void 0];for(h.unshift(...c),h.push(...l),b=h.length,u=Promise.resolve(n);d<b;)u=u.then(h[d++],h[d++]);return u}b=c.length;let g=n;for(d=0;d<b;){const h=c[d++],m=c[d++];try{g=h(g)}catch(p){m.call(this,p);break}}try{u=je.call(this,g)}catch(h){return Promise.reject(h)}for(d=0,b=l.length;d<b;)u=u.then(l[d++],l[d++]);return u}getUri(t){t=U(this.defaults,t);const n=nt(t.baseURL,t.url,t.allowAbsoluteUrls);return Xe(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){_.prototype[t]=function(n,r){return this.request(U(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(U(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}_.prototype[t]=n(),_.prototype[t+"Form"]=n(!0)});let Xn=class ct{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new D(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new ct(function(s){t=s}),cancel:t}}};function Zn(e){return function(n){return e.apply(null,n)}}function Yn(e){return a.isObject(e)&&e.isAxiosError===!0}const de={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(de).forEach(([e,t])=>{de[t]=e});function ut(e){const t=new _(e),n=ve(_.prototype.request,t);return a.extend(n,_.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return ut(U(e,s))},n}const R=ut(H);R.Axios=_;R.CanceledError=D;R.CancelToken=Xn;R.isCancel=et;R.VERSION=at;R.toFormData=Y;R.AxiosError=y;R.Cancel=R.CanceledError;R.all=function(t){return Promise.all(t)};R.spread=Zn;R.isAxiosError=Yn;R.mergeConfig=U;R.AxiosHeaders=x;R.formToJSON=e=>Ye(a.isHTMLForm(e)?new FormData(e):e);R.getAdapter=it.getAdapter;R.HttpStatusCode=de;R.default=R;const{Axios:Tr,AxiosError:Ar,CanceledError:xr,isCancel:Cr,CancelToken:Fr,VERSION:Pr,all:Nr,Cancel:Lr,isAxiosError:jr,spread:_r,toFormData:Ur,AxiosHeaders:kr,HttpStatusCode:Dr,formToJSON:Br,getAdapter:qr,mergeConfig:vr}=R,er=R.create({baseURL:`${window.location.protocol}//${window?.location.host}`}),tr=e=>{const t="EmailNotVerifiedError";if(typeof e=="string")return e.includes(t);if(typeof e=="object"&&e!==null){if("message"in e){const{message:n}=e;if(typeof n=="string")return n.includes(t);if(Array.isArray(n))return n.some(r=>typeof r=="string"&&r.includes(t))}return Object.values(e).some(n=>typeof n=="string"&&n.includes(t)||Array.isArray(n)&&n.some(r=>typeof r=="string"&&r.includes(t)))}return!1};er.interceptors.response.use(e=>e,e=>(e.response?.status===403&&tr(e.response?.data)&&window.location.pathname!=="/settings/user"&&window.location.reload(),Promise.reject(e)));export{kr as A,pe as B,wt as C,Q as D,ft as E,Sr as Q,Er as R,Ue as S,rr as a,ar as b,br as c,ir as d,pr as e,gt as f,ke as g,R as h,yr as i,lr as j,oe as k,mr as l,dt as m,wr as n,er as o,cr as p,ur as q,fr as r,pt as s,or as t,gr as u,hr as v,dr as w,yt as x,De as y,sr as z};
