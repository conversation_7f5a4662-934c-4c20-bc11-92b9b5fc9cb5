import{c as o,a as M,b as R,d as h}from"./browser-slice-DabBaamq.js";import{A as I}from"./agent-state-CFaY3go2.js";const r=o({name:"agent",initialState:{curAgentState:I.LOADING},reducers:{setCurrentAgentState:(e,t)=>{e.curAgentState=t.payload}}}),{setCurrentAgentState:G}=r.actions,_=r.reducer,b={code:"",path:"",refreshID:0,fileStates:[]},l=o({name:"code",initialState:b,reducers:{setCode:(e,t)=>{e.code=t.payload},setActiveFilepath:(e,t)=>{e.path=t.payload},setRefreshID:(e,t)=>{e.refreshID=t.payload},setFileStates:(e,t)=>{e.fileStates=t.payload},addOrUpdateFileState:(e,t)=>{const{path:a,unsavedContent:s,savedContent:i}=t.payload,n=e.fileStates.filter(f=>f.path!==a);n.push({path:a,savedContent:i,unsavedContent:s}),e.fileStates=n},removeFileState:(e,t)=>{const a=t.payload;e.fileStates=e.fileStates.filter(s=>s.path!==a)}}}),{setCode:Q,setActiveFilepath:E,setRefreshID:K,addOrUpdateFileState:q,removeFileState:B,setFileStates:X}=l.actions,F=l.reducer,O={changed:{}},c=o({name:"fileState",initialState:O,reducers:{setChanged(e,t){const{path:a,changed:s}=t.payload;e.changed[a]=s}}}),{setChanged:Y}=c.actions,C=c.reducer,V={files:[],initialPrompt:null,selectedRepository:null,selectedRepositoryProvider:null,replayJson:null},d=o({name:"initialQuery",initialState:V,reducers:{addFile(e,t){e.files.push(t.payload)},removeFile(e,t){e.files.splice(t.payload,1)},clearFiles(e){e.files=[]},setInitialPrompt(e,t){e.initialPrompt=t.payload},clearInitialPrompt(e){e.initialPrompt=null},setSelectedRepository(e,t){e.selectedRepository=t.payload},clearSelectedRepository(e){e.selectedRepository=null},setReplayJson(e,t){e.replayJson=t.payload}}}),{addFile:Z,removeFile:k,clearFiles:ee,setInitialPrompt:te,clearInitialPrompt:ae,setSelectedRepository:se,clearSelectedRepository:oe,setReplayJson:ie}=d.actions,A=d.reducer,v=[],p=o({name:"command",initialState:{commands:v},reducers:{appendInput:(e,t)=>{e.commands.push({content:t.payload,type:"input"})},appendOutput:(e,t)=>{e.commands.push({content:t.payload,type:"output"})},clearTerminal:e=>{e.commands=[]}}}),{appendInput:ne,appendOutput:re,clearTerminal:le}=p.actions,z=p.reducer,J=[],u=o({name:"jupyter",initialState:{cells:J},reducers:{appendJupyterInput:(e,t)=>{e.cells.push({content:t.payload,type:"input"})},appendJupyterOutput:(e,t)=>{e.cells.push({content:t.payload.content,type:"output",imageUrls:t.payload.imageUrls})},clearJupyter:e=>{e.cells=[]}}}),{appendJupyterInput:ce,appendJupyterOutput:de,clearJupyter:pe}=u.actions,P=u.reducer;var U=(e=>(e[e.UNKNOWN=-1]="UNKNOWN",e[e.LOW=0]="LOW",e[e.MEDIUM=1]="MEDIUM",e[e.HIGH=2]="HIGH",e))(U||{});const D=[],m=o({name:"securityAnalyzer",initialState:{logs:D},reducers:{appendSecurityAnalyzerInput:(e,t)=>{const a={id:t.payload.id,content:t.payload.args.command||t.payload.args.code||t.payload.args.content||t.payload.message,security_risk:t.payload.args.security_risk,confirmation_state:t.payload.args.confirmation_state,confirmed_changed:!1},s=e.logs.find(i=>i.id===a.id||i.confirmation_state==="awaiting_confirmation"&&i.content===a.content);s?s.confirmation_state!==a.confirmation_state&&(s.confirmation_state=a.confirmation_state,s.confirmed_changed=!0):e.logs.push(a)}}}),{appendSecurityAnalyzerInput:ue}=m.actions,N=m.reducer,T={status_update:!0,type:"info",id:"",message:""},y=o({name:"status",initialState:{curStatusMessage:T},reducers:{setCurStatusMessage:(e,t)=>{e.curStatusMessage=t.payload}}}),{setCurStatusMessage:me}=y.actions,j=y.reducer,x={cost:null,max_budget_per_task:null,usage:null},g=o({name:"metrics",initialState:x,reducers:{setMetrics:(e,t)=>{e.cost=t.payload.cost,e.max_budget_per_task=t.payload.max_budget_per_task,e.usage=t.payload.usage}}}),{setMetrics:ye}=g.actions,L=g.reducer,S=o({name:"microagentManagement",initialState:{addMicroagentModalVisible:!1,updateMicroagentModalVisible:!1,selectedRepository:null,personalRepositories:[],organizationRepositories:[],repositories:[],selectedMicroagentItem:null,learnThisRepoModalVisible:!1},reducers:{setAddMicroagentModalVisible:(e,t)=>{e.addMicroagentModalVisible=t.payload},setUpdateMicroagentModalVisible:(e,t)=>{e.updateMicroagentModalVisible=t.payload},setSelectedRepository:(e,t)=>{e.selectedRepository=t.payload},setPersonalRepositories:(e,t)=>{e.personalRepositories=t.payload},setOrganizationRepositories:(e,t)=>{e.organizationRepositories=t.payload},setRepositories:(e,t)=>{e.repositories=t.payload},setSelectedMicroagentItem:(e,t)=>{e.selectedMicroagentItem=t.payload},setLearnThisRepoModalVisible:(e,t)=>{e.learnThisRepoModalVisible=t.payload}}}),{setAddMicroagentModalVisible:ge,setUpdateMicroagentModalVisible:Se,setSelectedRepository:fe,setPersonalRepositories:Me,setOrganizationRepositories:Re,setRepositories:he,setSelectedMicroagentItem:Ie,setLearnThisRepoModalVisible:_e}=S.actions,w=S.reducer,H=M({fileState:C,initialQuery:A,browser:h,code:F,cmd:z,agent:_,jupyter:P,securityAnalyzer:N,status:j,metrics:L,microagentManagement:w}),be=R({reducer:H});export{U as A,G as a,de as b,re as c,ye as d,ne as e,ce as f,ue as g,me as h,Ie as i,fe as j,_e as k,ge as l,Me as m,Re as n,he as o,Se as p,le as q,pe as r,be as s};
