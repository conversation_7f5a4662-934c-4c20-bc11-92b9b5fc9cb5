import{r as p,j as e,R as d,w as M,c as N}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as v,B as w}from"./brand-button-3Z8FN4qR.js";import{O as S}from"./open-hands-Ce72Fmtl.js";import{u as b}from"./use-balance-BpOxWBAr.js";import{c as E}from"./utils-KsbccAr1.js";import{S as j}from"./settings-input-i4i_IemM.js";import{L as C}from"./loading-spinner-D6qcLhqr.js";import{I as a}from"./declaration-xyc84-tJ.js";import{u as c}from"./useTranslation-BG59QWH_.js";import{d as y,a as I}from"./custom-toast-handlers-CR9P-jKI.js";import"./mutation-B9dSlWD-.js";import"./open-hands-axios-CtirLpss.js";import"./useQuery-Cu2nkJ8V.js";import"./use-config-jdwF3W4-.js";import"./optional-tag-e1gRgM9y.js";import"./i18nInstance-DBIXdvxg.js";import"./index-cxP66Ws3.js";const A=()=>v({mutationFn:async t=>{const s=await S.createCheckoutSession(t.amount);window.location.href=s}}),T=t=>p.createElement("svg",{width:22,height:14,viewBox:"0 0 22 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t},p.createElement("path",{d:"M5 6C4.80222 6 4.60888 6.05865 4.44443 6.16853C4.27998 6.27841 4.15181 6.43459 4.07612 6.61732C4.00043 6.80004 3.98063 7.00111 4.01921 7.19509C4.0578 7.38907 4.15304 7.56725 4.29289 7.70711C4.43275 7.84696 4.61093 7.9422 4.80491 7.98079C4.99889 8.01937 5.19996 7.99957 5.38268 7.92388C5.56541 7.84819 5.72159 7.72002 5.83147 7.55557C5.94135 7.39112 6 7.19778 6 7C6 6.73478 5.89464 6.48043 5.70711 6.29289C5.51957 6.10536 5.26522 6 5 6ZM17 6C16.8022 6 16.6089 6.05865 16.4444 6.16853C16.28 6.27841 16.1518 6.43459 16.0761 6.61732C16.0004 6.80004 15.9806 7.00111 16.0192 7.19509C16.0578 7.38907 16.153 7.56725 16.2929 7.70711C16.4327 7.84696 16.6109 7.9422 16.8049 7.98079C16.9989 8.01937 17.2 7.99957 17.3827 7.92388C17.5654 7.84819 17.7216 7.72002 17.8315 7.55557C17.9414 7.39112 18 7.19778 18 7C18 6.73478 17.8946 6.48043 17.7071 6.29289C17.5196 6.10536 17.2652 6 17 6ZM19 0H3C2.20435 0 1.44129 0.316071 0.87868 0.87868C0.31607 1.44129 0 2.20435 0 3V11C0 11.7956 0.31607 12.5587 0.87868 13.1213C1.44129 13.6839 2.20435 14 3 14H19C19.7956 14 20.5587 13.6839 21.1213 13.1213C21.6839 12.5587 22 11.7956 22 11V3C22 2.20435 21.6839 1.44129 21.1213 0.87868C20.5587 0.316071 19.7956 0 19 0ZM20 11C20 11.2652 19.8946 11.5196 19.7071 11.7071C19.5196 11.8946 19.2652 12 19 12H3C2.73478 12 2.48043 11.8946 2.29289 11.7071C2.10536 11.5196 2 11.2652 2 11V3C2 2.73478 2.10536 2.48043 2.29289 2.29289C2.48043 2.10536 2.73478 2 3 2H19C19.2652 2 19.5196 2.10536 19.7071 2.29289C19.8946 2.48043 20 2.73478 20 3V11ZM11 4C10.4067 4 9.82664 4.17595 9.33329 4.50559C8.83994 4.83524 8.45542 5.30377 8.22836 5.85195C8.0013 6.40013 7.94189 7.00333 8.05764 7.58527C8.1734 8.16721 8.45912 8.70176 8.87868 9.12132C9.29824 9.54088 9.83279 9.8266 10.4147 9.94236C10.9967 10.0581 11.5999 9.9987 12.1481 9.77164C12.6962 9.54458 13.1648 9.16006 13.4944 8.66671C13.8241 8.17336 14 7.59334 14 7C14 6.20435 13.6839 5.44129 13.1213 4.87868C12.5587 4.31607 11.7956 4 11 4ZM11 8C10.8022 8 10.6089 7.94135 10.4444 7.83147C10.28 7.72159 10.1518 7.56541 10.0761 7.38268C10.0004 7.19996 9.98063 6.99889 10.0192 6.80491C10.0578 6.61093 10.153 6.43275 10.2929 6.29289C10.4327 6.15304 10.6109 6.0578 10.8049 6.01921C10.9989 5.98063 11.2 6.00043 11.3827 6.07612C11.5654 6.15181 11.7216 6.27998 11.8315 6.44443C11.9414 6.60888 12 6.80222 12 7C12 7.26522 11.8946 7.51957 11.7071 7.70711C11.5196 7.89464 11.2652 8 11 8Z",fill:"white"})),P=10,D=25e3,u=t=>{const s=parseInt(t,10);return!(Number.isNaN(s)||s<0||s<P||s>D||s!==parseFloat(t))},B="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xml:space='preserve'%20id='Layer_1'%20x='0'%20y='0'%20style='enable-background:new%200%200%20468%20222.5'%20version='1.1'%20viewBox='0%200%20468%20222.5'%3e%3cstyle%3e%20.st0{fill-rule:evenodd;clip-rule:evenodd;fill:%23635bff}%20%3c/style%3e%3cpath%20d='M414%20113.4c0-25.6-12.4-45.8-36.1-45.8-23.8%200-38.2%2020.2-38.2%2045.6%200%2030.1%2017%2045.3%2041.4%2045.3%2011.9%200%2020.9-2.7%2027.7-6.5v-20c-6.8%203.4-14.6%205.5-24.5%205.5-9.7%200-18.3-3.4-19.4-15.2h48.9c0-1.3.2-6.5.2-8.9zm-49.4-9.5c0-11.3%206.9-16%2013.2-16%206.1%200%2012.6%204.7%2012.6%2016h-25.8zM301.1%2067.6c-9.8%200-16.1%204.6-19.6%207.8l-1.3-6.2h-22v116.6l25-5.3.1-28.3c3.6%202.6%208.9%206.3%2017.7%206.3%2017.9%200%2034.2-14.4%2034.2-46.1-.1-29-16.6-44.8-34.1-44.8zm-6%2068.9c-5.9%200-9.4-2.1-11.8-4.7l-.1-37.1c2.6-2.9%206.2-4.9%2011.9-4.9%209.1%200%2015.4%2010.2%2015.4%2023.3%200%2013.4-6.2%2023.4-15.4%2023.4zM223.8%2061.7l25.1-5.4V36l-25.1%205.3zM223.8%2069.3h25.1v87.5h-25.1zM196.9%2076.7l-1.6-7.4h-21.6v87.5h25V97.5c5.9-7.7%2015.9-6.3%2019-5.2v-23c-3.2-1.2-14.9-3.4-20.8%207.4zM146.9%2047.6l-24.4%205.2-.1%2080.1c0%2014.8%2011.1%2025.7%2025.9%2025.7%208.2%200%2014.2-1.5%2017.5-3.3V135c-3.2%201.3-19%205.9-19-8.9V90.6h19V69.3h-19l.1-21.7zM79.3%2094.7c0-3.9%203.2-5.4%208.5-5.4%207.6%200%2017.2%202.3%2024.8%206.4V72.2c-8.3-3.3-16.5-4.6-24.8-4.6C67.5%2067.6%2054%2078.2%2054%2095.9c0%2027.6%2038%2023.2%2038%2035.1%200%204.6-4%206.1-9.6%206.1-8.3%200-18.9-3.4-27.3-8v23.8c9.3%204%2018.7%205.7%2027.3%205.7%2020.8%200%2035.1-10.3%2035.1-28.2-.1-29.8-38.2-24.5-38.2-35.7z'%20class='st0'/%3e%3c/svg%3e";function V(){const{t}=c();return e.jsxs("div",{className:"flex flex-row items-center",children:[e.jsx("span",{className:"text-medium font-semi-bold",children:t(a.BILLING$POWERED_BY)}),e.jsx("img",{src:B,alt:"Stripe",className:"h-8"})]})}function z(){const{t}=c(),{data:s,isLoading:i}=b(),{mutate:n,isPending:l}=A(),[f,m]=d.useState(!0),x=async r=>{const o=r.get("top-up-input")?.toString();if(o?.trim()){if(!u(o))return;const g=parseInt(o,10);n({amount:g})}m(!0)},h=r=>{m(!u(r))};return e.jsxs("form",{action:x,"data-testid":"billing-settings",className:"flex flex-col gap-6 px-11 py-9",children:[e.jsxs("div",{className:E("flex items-center justify-between w-[680px] bg-[#7F7445] rounded-sm px-3 py-2","text-[28px] leading-8 -tracking-[0.02em] font-bold"),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(T,{width:22,height:14}),e.jsx("span",{children:t(a.PAYMENT$MANAGE_CREDITS)})]}),!i&&e.jsxs("span",{"data-testid":"user-balance",children:["$",Number(s).toFixed(2)]}),i&&e.jsx(C,{size:"small"})]}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsx(j,{testId:"top-up-input",name:"top-up-input",onChange:h,type:"number",label:t(a.PAYMENT$ADD_FUNDS),placeholder:t(a.PAYMENT$SPECIFY_AMOUNT_USD),className:"w-[680px]",min:10,max:25e3,step:1}),e.jsxs("div",{className:"flex items-center w-[680px] gap-2",children:[e.jsx(w,{variant:"primary",type:"submit",isDisabled:l||f,children:t(a.PAYMENT$ADD_CREDIT)}),l&&e.jsx(C,{size:"small"}),e.jsx(V,{})]})]})]})}function U(){const{t}=c(),[s,i]=N(),n=s.get("checkout");return d.useEffect(()=>{n==="success"?y(t(a.PAYMENT$SUCCESS)):n==="cancel"&&I(t(a.PAYMENT$CANCELLED)),i({})},[n]),e.jsx(z,{})}const e1=M(U);export{e1 as default};
