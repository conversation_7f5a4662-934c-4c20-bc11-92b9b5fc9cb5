import{r as a,j as l}from"./chunk-C37GKA54-CBbYr_fP.js";import{g as p}from"./mutation-B9dSlWD-.js";import{S as b,j as m,k as c,n as d,u as f,a as y,l as v}from"./open-hands-axios-CtirLpss.js";import{c as g}from"./utils-KsbccAr1.js";var x=class extends b{#e;#i=void 0;#t;#s;constructor(t,s){super(),this.#e=t,this.setOptions(s),this.bindMethods(),this.#r()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const s=this.options;this.options=this.#e.defaultMutationOptions(t),m(this.options,s)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#t,observer:this}),s?.mutationKey&&this.options.mutationKey&&c(s.mutationKey)!==c(this.options.mutationKey)?this.reset():this.#t?.state.status==="pending"&&this.#t.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#t?.removeObserver(this)}onMutationUpdate(t){this.#r(),this.#o(t)}getCurrentResult(){return this.#i}reset(){this.#t?.removeObserver(this),this.#t=void 0,this.#r(),this.#o()}mutate(t,s){return this.#s=s,this.#t?.removeObserver(this),this.#t=this.#e.getMutationCache().build(this.#e,this.options),this.#t.addObserver(this),this.#t.execute(t)}#r(){const t=this.#t?.state??p();this.#i={...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset}}#o(t){d.batch(()=>{if(this.#s&&this.hasListeners()){const s=this.#i.variables,i=this.#i.context;t?.type==="success"?(this.#s.onSuccess?.(t.data,s,i),this.#s.onSettled?.(t.data,null,s,i)):t?.type==="error"&&(this.#s.onError?.(t.error,s,i),this.#s.onSettled?.(void 0,t.error,s,i))}this.listeners.forEach(s=>{s(this.#i)})})}};function w(t,s){const i=f(s),[e]=a.useState(()=>new x(i,t));a.useEffect(()=>{e.setOptions(t)},[e,t]);const r=a.useSyncExternalStore(a.useCallback(o=>e.subscribe(d.batchCalls(o)),[e]),()=>e.getCurrentResult(),()=>e.getCurrentResult()),n=a.useCallback((o,h)=>{e.mutate(o,h).catch(y)},[e]);if(r.error&&v(e.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:n,mutateAsync:r.mutate}}function C({testId:t,name:s,children:i,variant:e,type:r,isDisabled:n,className:o,onClick:h,startContent:u}){return l.jsxs("button",{name:s,"data-testid":t,disabled:n,type:r,onClick:h,className:g("w-fit p-2 text-sm rounded-sm disabled:opacity-30 disabled:cursor-not-allowed hover:opacity-80 cursor-pointer",e==="primary"&&"bg-primary text-[#0D0F11]",e==="secondary"&&"border border-primary text-primary",e==="danger"&&"bg-red-600 text-white hover:bg-red-700",u&&"flex items-center justify-center gap-2",o),children:[u,i]})}export{C as B,w as u};
