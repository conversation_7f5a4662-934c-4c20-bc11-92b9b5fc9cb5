import{o as a}from"./open-hands-axios-CtirLpss.js";class o{static async getSecrets(){const{data:t}=await a.get("/api/secrets");return t.custom_secrets}static async createSecret(t,e,s){const c={name:t,value:e,description:s},{status:r}=await a.post("/api/secrets",c);return r===201}static async updateSecret(t,e,s){const c={name:e,description:s},{status:r}=await a.put(`/api/secrets/${t}`,c);return r===200}static async deleteSecret(t){const{status:e}=await a.delete(`/api/secrets/${t}`);return e===200}static async addGitProvider(t){const e={provider_tokens:t},{data:s}=await a.post("/api/add-git-providers",e);return s}}export{o as S};
