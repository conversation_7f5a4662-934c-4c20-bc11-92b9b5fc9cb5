import{r as o}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as r}from"./use-conversation-id-0JHAicdF.js";import{u as a}from"./useQuery-Cu2nkJ8V.js";import{O as n}from"./open-hands-Ce72Fmtl.js";const u=1e3*60*5,i=1e3*60*15,c=(e,t)=>a({queryKey:["user","conversation",e],queryFn:async()=>await n.getConversation(e),enabled:!!e,retry:!1,refetchInterval:t,staleTime:u,gcTime:i}),v=1e3*60*5,T=()=>{const{conversationId:e}=r(),t=c(e,s=>s.state.data?.status==="STARTING"?3e3:v);return o.useEffect(()=>{const s=t.data;n.setCurrentConversation(s||null)},[e,t.isFetched,t?.data?.status]),t};export{c as a,T as u};
