const a=s=>typeof s.response?.data=="object"&&s.response?.data!==null&&"error"in s.response.data&&typeof s.response?.data?.error=="string",t=s=>typeof s.response?.data=="object"&&s.response?.data!==null&&"message"in s.response.data&&typeof s.response?.data?.message=="string",n=s=>{let e=null;return a(s)&&s.response?.data.error?e=s.response?.data.error:t(s)&&s.response?.data.message?e=s.response?.data.message:e=s.message,e};export{n as r};
