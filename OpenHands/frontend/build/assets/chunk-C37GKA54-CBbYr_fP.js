var gl=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function va(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ht={exports:{}},Ze={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fr;function ga(){if(Fr)return Ze;Fr=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(n,a,o){var l=null;if(o!==void 0&&(l=""+o),a.key!==void 0&&(l=""+a.key),"key"in a){o={};for(var u in a)u!=="key"&&(o[u]=a[u])}else o=a;return a=o.ref,{$$typeof:e,type:n,key:l,ref:a!==void 0?a:null,props:o}}return Ze.Fragment=t,Ze.jsx=r,Ze.jsxs=r,Ze}var jr;function wa(){return jr||(jr=1,Ht.exports=ga()),Ht.exports}var wl=wa(),zt={exports:{}},B={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ur;function Ea(){if(Ur)return B;Ur=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),i=Symbol.for("react.suspense"),s=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;function p(h){return h===null||typeof h!="object"?null:(h=m&&h[m]||h["@@iterator"],typeof h=="function"?h:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,b={};function S(h,x,k){this.props=h,this.context=x,this.refs=b,this.updater=k||w}S.prototype.isReactComponent={},S.prototype.setState=function(h,x){if(typeof h!="object"&&typeof h!="function"&&h!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,h,x,"setState")},S.prototype.forceUpdate=function(h){this.updater.enqueueForceUpdate(this,h,"forceUpdate")};function E(){}E.prototype=S.prototype;function _(h,x,k){this.props=h,this.context=x,this.refs=b,this.updater=k||w}var P=_.prototype=new E;P.constructor=_,R(P,S.prototype),P.isPureReactComponent=!0;var M=Array.isArray,C={H:null,A:null,T:null,S:null,V:null},y=Object.prototype.hasOwnProperty;function j(h,x,k,I,W,Z){return k=Z.ref,{$$typeof:e,type:h,key:x,ref:k!==void 0?k:null,props:Z}}function J(h,x){return j(h.type,x,void 0,void 0,void 0,h.props)}function H(h){return typeof h=="object"&&h!==null&&h.$$typeof===e}function q(h){var x={"=":"=0",":":"=2"};return"$"+h.replace(/[=:]/g,function(k){return x[k]})}var le=/\/+/g;function me(h,x){return typeof h=="object"&&h!==null&&h.key!=null?q(""+h.key):x.toString(36)}function X(){}function Q(h){switch(h.status){case"fulfilled":return h.value;case"rejected":throw h.reason;default:switch(typeof h.status=="string"?h.then(X,X):(h.status="pending",h.then(function(x){h.status==="pending"&&(h.status="fulfilled",h.value=x)},function(x){h.status==="pending"&&(h.status="rejected",h.reason=x)})),h.status){case"fulfilled":return h.value;case"rejected":throw h.reason}}throw h}function ae(h,x,k,I,W){var Z=typeof h;(Z==="undefined"||Z==="boolean")&&(h=null);var z=!1;if(h===null)z=!0;else switch(Z){case"bigint":case"string":case"number":z=!0;break;case"object":switch(h.$$typeof){case e:case t:z=!0;break;case f:return z=h._init,ae(z(h._payload),x,k,I,W)}}if(z)return W=W(h),z=I===""?"."+me(h,0):I,M(W)?(k="",z!=null&&(k=z.replace(le,"$&/")+"/"),ae(W,x,k,"",function(It){return It})):W!=null&&(H(W)&&(W=J(W,k+(W.key==null||h&&h.key===W.key?"":(""+W.key).replace(le,"$&/")+"/")+z)),x.push(W)),1;z=0;var xe=I===""?".":I+":";if(M(h))for(var oe=0;oe<h.length;oe++)I=h[oe],Z=xe+me(I,oe),z+=ae(I,x,k,Z,W);else if(oe=p(h),typeof oe=="function")for(h=oe.call(h),oe=0;!(I=h.next()).done;)I=I.value,Z=xe+me(I,oe++),z+=ae(I,x,k,Z,W);else if(Z==="object"){if(typeof h.then=="function")return ae(Q(h),x,k,I,W);throw x=String(h),Error("Objects are not valid as a React child (found: "+(x==="[object Object]"?"object with keys {"+Object.keys(h).join(", ")+"}":x)+"). If you meant to render a collection of children, use an array instead.")}return z}function Y(h,x,k){if(h==null)return h;var I=[],W=0;return ae(h,I,"","",function(Z){return x.call(k,Z,W++)}),I}function pe(h){if(h._status===-1){var x=h._result;x=x(),x.then(function(k){(h._status===0||h._status===-1)&&(h._status=1,h._result=k)},function(k){(h._status===0||h._status===-1)&&(h._status=2,h._result=k)}),h._status===-1&&(h._status=0,h._result=x)}if(h._status===1)return h._result.default;throw h._result}var se=typeof reportError=="function"?reportError:function(h){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var x=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof h=="object"&&h!==null&&typeof h.message=="string"?String(h.message):String(h),error:h});if(!window.dispatchEvent(x))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",h);return}console.error(h)};function ve(){}return B.Children={map:Y,forEach:function(h,x,k){Y(h,function(){x.apply(this,arguments)},k)},count:function(h){var x=0;return Y(h,function(){x++}),x},toArray:function(h){return Y(h,function(x){return x})||[]},only:function(h){if(!H(h))throw Error("React.Children.only expected to receive a single React element child.");return h}},B.Component=S,B.Fragment=r,B.Profiler=a,B.PureComponent=_,B.StrictMode=n,B.Suspense=i,B.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=C,B.__COMPILER_RUNTIME={__proto__:null,c:function(h){return C.H.useMemoCache(h)}},B.cache=function(h){return function(){return h.apply(null,arguments)}},B.cloneElement=function(h,x,k){if(h==null)throw Error("The argument must be a React element, but you passed "+h+".");var I=R({},h.props),W=h.key,Z=void 0;if(x!=null)for(z in x.ref!==void 0&&(Z=void 0),x.key!==void 0&&(W=""+x.key),x)!y.call(x,z)||z==="key"||z==="__self"||z==="__source"||z==="ref"&&x.ref===void 0||(I[z]=x[z]);var z=arguments.length-2;if(z===1)I.children=k;else if(1<z){for(var xe=Array(z),oe=0;oe<z;oe++)xe[oe]=arguments[oe+2];I.children=xe}return j(h.type,W,void 0,void 0,Z,I)},B.createContext=function(h){return h={$$typeof:l,_currentValue:h,_currentValue2:h,_threadCount:0,Provider:null,Consumer:null},h.Provider=h,h.Consumer={$$typeof:o,_context:h},h},B.createElement=function(h,x,k){var I,W={},Z=null;if(x!=null)for(I in x.key!==void 0&&(Z=""+x.key),x)y.call(x,I)&&I!=="key"&&I!=="__self"&&I!=="__source"&&(W[I]=x[I]);var z=arguments.length-2;if(z===1)W.children=k;else if(1<z){for(var xe=Array(z),oe=0;oe<z;oe++)xe[oe]=arguments[oe+2];W.children=xe}if(h&&h.defaultProps)for(I in z=h.defaultProps,z)W[I]===void 0&&(W[I]=z[I]);return j(h,Z,void 0,void 0,null,W)},B.createRef=function(){return{current:null}},B.forwardRef=function(h){return{$$typeof:u,render:h}},B.isValidElement=H,B.lazy=function(h){return{$$typeof:f,_payload:{_status:-1,_result:h},_init:pe}},B.memo=function(h,x){return{$$typeof:s,type:h,compare:x===void 0?null:x}},B.startTransition=function(h){var x=C.T,k={};C.T=k;try{var I=h(),W=C.S;W!==null&&W(k,I),typeof I=="object"&&I!==null&&typeof I.then=="function"&&I.then(ve,se)}catch(Z){se(Z)}finally{C.T=x}},B.unstable_useCacheRefresh=function(){return C.H.useCacheRefresh()},B.use=function(h){return C.H.use(h)},B.useActionState=function(h,x,k){return C.H.useActionState(h,x,k)},B.useCallback=function(h,x){return C.H.useCallback(h,x)},B.useContext=function(h){return C.H.useContext(h)},B.useDebugValue=function(){},B.useDeferredValue=function(h,x){return C.H.useDeferredValue(h,x)},B.useEffect=function(h,x,k){var I=C.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return I.useEffect(h,x)},B.useId=function(){return C.H.useId()},B.useImperativeHandle=function(h,x,k){return C.H.useImperativeHandle(h,x,k)},B.useInsertionEffect=function(h,x){return C.H.useInsertionEffect(h,x)},B.useLayoutEffect=function(h,x){return C.H.useLayoutEffect(h,x)},B.useMemo=function(h,x){return C.H.useMemo(h,x)},B.useOptimistic=function(h,x){return C.H.useOptimistic(h,x)},B.useReducer=function(h,x,k){return C.H.useReducer(h,x,k)},B.useRef=function(h){return C.H.useRef(h)},B.useState=function(h){return C.H.useState(h)},B.useSyncExternalStore=function(h,x,k){return C.H.useSyncExternalStore(h,x,k)},B.useTransition=function(){return C.H.useTransition()},B.version="19.1.1",B}var Hr;function Ra(){return Hr||(Hr=1,zt.exports=Ea()),zt.exports}var d=Ra();const El=va(d);/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var mn=e=>{throw TypeError(e)},ba=(e,t,r)=>t.has(e)||mn("Cannot "+r),Bt=(e,t,r)=>(ba(e,t,"read from private field"),r?r.call(e):t.get(e)),Sa=(e,t,r)=>t.has(e)?mn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),zr="popstate";function Rl(e={}){function t(n,a){let{pathname:o,search:l,hash:u}=n.location;return at("",{pathname:o,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Oe(a)}return La(t,r,null,e)}function V(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function te(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function xa(){return Math.random().toString(36).substring(2,10)}function Br(e,t){return{usr:e.state,key:e.key,idx:t}}function at(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Ne(t):t,state:r,key:t&&t.key||n||xa()}}function Oe({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ne(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function La(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:o=!1}=n,l=a.history,u="POP",i=null,s=f();s==null&&(s=0,l.replaceState({...l.state,idx:s},""));function f(){return(l.state||{idx:null}).idx}function m(){u="POP";let S=f(),E=S==null?null:S-s;s=S,i&&i({action:u,location:b.location,delta:E})}function p(S,E){u="PUSH";let _=at(b.location,S,E);s=f()+1;let P=Br(_,s),M=b.createHref(_);try{l.pushState(P,"",M)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;a.location.assign(M)}o&&i&&i({action:u,location:b.location,delta:1})}function w(S,E){u="REPLACE";let _=at(b.location,S,E);s=f();let P=Br(_,s),M=b.createHref(_);l.replaceState(P,"",M),o&&i&&i({action:u,location:b.location,delta:0})}function R(S){return pn(S)}let b={get action(){return u},get location(){return e(a,l)},listen(S){if(i)throw new Error("A history only accepts one active listener");return a.addEventListener(zr,m),i=S,()=>{a.removeEventListener(zr,m),i=null}},createHref(S){return t(a,S)},createURL:R,encodeLocation(S){let E=R(S);return{pathname:E.pathname,search:E.search,hash:E.hash}},push:p,replace:w,go(S){return l.go(S)}};return b}function pn(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),V(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:Oe(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var nt,Wr=class{constructor(e){if(Sa(this,nt,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Bt(this,nt).has(e))return Bt(this,nt).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Bt(this,nt).set(e,t)}};nt=new WeakMap;var Ca=new Set(["lazy","caseSensitive","path","id","index","children"]);function Pa(e){return Ca.has(e)}var Ta=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Ma(e){return Ta.has(e)}function _a(e){return e.index===!0}function ot(e,t,r=[],n={},a=!1){return e.map((o,l)=>{let u=[...r,String(l)],i=typeof o.id=="string"?o.id:u.join("-");if(V(o.index!==!0||!o.children,"Cannot specify children on an index route"),V(a||!n[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),_a(o)){let s={...o,...t(o),id:i};return n[i]=s,s}else{let s={...o,...t(o),id:i,children:void 0};return n[i]=s,o.children&&(s.children=ot(o.children,t,u,n,a)),s}})}function Te(e,t,r="/"){return St(e,t,r,!1)}function St(e,t,r,n){let a=typeof t=="string"?Ne(t):t,o=we(a.pathname||"/",r);if(o==null)return null;let l=vn(e);Da(l);let u=null;for(let i=0;u==null&&i<l.length;++i){let s=Ha(o);u=Ua(l[i],s,n)}return u}function yn(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function vn(e,t=[],r=[],n=""){let a=(o,l,u)=>{let i={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(V(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let s=Ce([n,i.relativePath]),f=r.concat(i);o.children&&o.children.length>0&&(V(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),vn(o.children,t,f,s)),!(o.path==null&&!o.index)&&t.push({path:s,score:Fa(s,o.index),routesMeta:f})};return e.forEach((o,l)=>{if(o.path===""||!o.path?.includes("?"))a(o,l);else for(let u of gn(o.path))a(o,l,u)}),t}function gn(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let l=gn(n.join("/")),u=[];return u.push(...l.map(i=>i===""?o:[o,i].join("/"))),a&&u.push(...l),u.map(i=>e.startsWith("/")&&i===""?"/":i)}function Da(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:ja(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var Aa=/^:[\w-]+$/,Oa=3,Na=2,ka=1,Ia=10,$a=-2,Yr=e=>e==="*";function Fa(e,t){let r=e.split("/"),n=r.length;return r.some(Yr)&&(n+=$a),t&&(n+=Na),r.filter(a=>!Yr(a)).reduce((a,o)=>a+(Aa.test(o)?Oa:o===""?ka:Ia),n)}function ja(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Ua(e,t,r=!1){let{routesMeta:n}=e,a={},o="/",l=[];for(let u=0;u<n.length;++u){let i=n[u],s=u===n.length-1,f=o==="/"?t:t.slice(o.length)||"/",m=Mt({path:i.relativePath,caseSensitive:i.caseSensitive,end:s},f),p=i.route;if(!m&&s&&r&&!n[n.length-1].route.index&&(m=Mt({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},f)),!m)return null;Object.assign(a,m.params),l.push({params:a,pathname:Ce([o,m.pathname]),pathnameBase:Ya(Ce([o,m.pathnameBase])),route:p}),m.pathnameBase!=="/"&&(o=Ce([o,m.pathnameBase]))}return l}function Mt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=wn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:n.reduce((s,{paramName:f,isOptional:m},p)=>{if(f==="*"){let R=u[p]||"";l=o.slice(0,o.length-R.length).replace(/(.)\/+$/,"$1")}const w=u[p];return m&&!w?s[f]=void 0:s[f]=(w||"").replace(/%2F/g,"/"),s},{}),pathname:o,pathnameBase:l,pattern:e}}function wn(e,t=!1,r=!0){te(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,i)=>(n.push({paramName:u,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Ha(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return te(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function we(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function za({basename:e,pathname:t}){return t==="/"?e:Ce([e,t])}function Ba(e,t="/"){let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?Ne(e):e;return{pathname:r?r.startsWith("/")?r:Wa(r,t):t,search:Va(n),hash:Ja(a)}}function Wa(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Wt(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function En(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function ur(e){let t=En(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function cr(e,t,r,n=!1){let a;typeof e=="string"?a=Ne(e):(a={...e},V(!a.pathname||!a.pathname.includes("?"),Wt("?","pathname","search",a)),V(!a.pathname||!a.pathname.includes("#"),Wt("#","pathname","hash",a)),V(!a.search||!a.search.includes("#"),Wt("#","search","hash",a)));let o=e===""||a.pathname==="",l=o?"/":a.pathname,u;if(l==null)u=r;else{let m=t.length-1;if(!n&&l.startsWith("..")){let p=l.split("/");for(;p[0]==="..";)p.shift(),m-=1;a.pathname=p.join("/")}u=m>=0?t[m]:"/"}let i=Ba(a,u),s=l&&l!=="/"&&l.endsWith("/"),f=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(s||f)&&(i.pathname+="/"),i}var Ce=e=>e.join("/").replace(/\/\/+/g,"/"),Ya=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Va=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ja=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Ga=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}};function Xa(e,t){return new Ga(e,typeof t=="number"?{status:t}:t)}var Ka=(e,t=302)=>{let r=t;typeof r=="number"?r={status:r}:typeof r.status>"u"&&(r.status=302);let n=new Headers(r.headers);return n.set("Location",e),new Response(null,{...r,headers:n})},He=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function ze(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Rn=["POST","PUT","PATCH","DELETE"],qa=new Set(Rn),Qa=["GET",...Rn],Za=new Set(Qa),eo=new Set([301,302,303,307,308]),to=new Set([307,308]),Yt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ro={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},et={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},no=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,dr=e=>no.test(e),ao=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),bn="remix-router-transitions",Sn=Symbol("ResetLoaderData");function bl(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";V(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],a=e.mapRouteProperties||ao,o={},l=ot(e.routes,a,void 0,o),u,i=e.basename||"/",s=e.dataStrategy||uo,f={unstable_middleware:!1,...e.future},m=null,p=new Set,w=null,R=null,b=null,S=e.hydrationData!=null,E=Te(l,e.history.location,i),_=!1,P=null,M;if(E==null&&!e.patchRoutesOnNavigation){let c=Ee(404,{pathname:e.history.location.pathname}),{matches:v,route:g}=nn(l);M=!0,E=v,P={[g.id]:c}}else if(E&&!e.hydrationData&&ft(E,l,e.history.location.pathname).active&&(E=null),E)if(E.some(c=>c.route.lazy))M=!1;else if(!E.some(c=>c.route.loader))M=!0;else{let c=e.hydrationData?e.hydrationData.loaderData:null,v=e.hydrationData?e.hydrationData.errors:null;if(v){let g=E.findIndex(L=>v[L.route.id]!==void 0);M=E.slice(0,g+1).every(L=>!Qt(L.route,c,v))}else M=E.every(g=>!Qt(g.route,c,v))}else{M=!1,E=[];let c=ft(null,l,e.history.location.pathname);c.active&&c.matches&&(_=!0,E=c.matches)}let C,y={historyAction:e.history.action,location:e.history.location,matches:E,initialized:M,navigation:Yt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||P,fetchers:new Map,blockers:new Map},j="POP",J=!1,H,q=!1,le=new Map,me=null,X=!1,Q=!1,ae=new Set,Y=new Map,pe=0,se=-1,ve=new Map,h=new Set,x=new Map,k=new Map,I=new Set,W=new Map,Z,z=null;function xe(){if(m=e.history.listen(({action:c,location:v,delta:g})=>{if(Z){Z(),Z=void 0;return}te(W.size===0||g!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let L=Nr({currentLocation:y.location,nextLocation:v,historyAction:c});if(L&&g!=null){let T=new Promise(A=>{Z=A});e.history.go(g*-1),dt(L,{state:"blocked",location:v,proceed(){dt(L,{state:"proceeding",proceed:void 0,reset:void 0,location:v}),T.then(()=>e.history.go(g))},reset(){let A=new Map(y.blockers);A.set(L,et),ue({blockers:A})}});return}return ke(c,v)}),r){Ro(t,le);let c=()=>bo(t,le);t.addEventListener("pagehide",c),me=()=>t.removeEventListener("pagehide",c)}return y.initialized||ke("POP",y.location,{initialHydration:!0}),C}function oe(){m&&m(),me&&me(),p.clear(),H&&H.abort(),y.fetchers.forEach((c,v)=>Ft(v)),y.blockers.forEach((c,v)=>Or(v))}function It(c){return p.add(c),()=>p.delete(c)}function ue(c,v={}){c.matches&&(c.matches=c.matches.map(T=>{let A=o[T.route.id],O=T.route;return O.element!==A.element||O.errorElement!==A.errorElement||O.hydrateFallbackElement!==A.hydrateFallbackElement?{...T,route:A}:T})),y={...y,...c};let g=[],L=[];y.fetchers.forEach((T,A)=>{T.state==="idle"&&(I.has(A)?g.push(A):L.push(A))}),I.forEach(T=>{!y.fetchers.has(T)&&!Y.has(T)&&g.push(T)}),[...p].forEach(T=>T(y,{deletedFetchers:g,viewTransitionOpts:v.viewTransitionOpts,flushSync:v.flushSync===!0})),g.forEach(T=>Ft(T)),L.forEach(T=>y.fetchers.delete(T))}function We(c,v,{flushSync:g}={}){let L=y.actionData!=null&&y.navigation.formMethod!=null&&ye(y.navigation.formMethod)&&y.navigation.state==="loading"&&c.state?._isRedirect!==!0,T;v.actionData?Object.keys(v.actionData).length>0?T=v.actionData:T=null:L?T=y.actionData:T=null;let A=v.loaderData?tn(y.loaderData,v.loaderData,v.matches||[],v.errors):y.loaderData,O=y.blockers;O.size>0&&(O=new Map(O),O.forEach((N,F)=>O.set(F,et)));let D=X?!1:Ir(c,v.matches||y.matches),$=J===!0||y.navigation.formMethod!=null&&ye(y.navigation.formMethod)&&c.state?._isRedirect!==!0;u&&(l=u,u=void 0),X||j==="POP"||(j==="PUSH"?e.history.push(c,c.state):j==="REPLACE"&&e.history.replace(c,c.state));let U;if(j==="POP"){let N=le.get(y.location.pathname);N&&N.has(c.pathname)?U={currentLocation:y.location,nextLocation:c}:le.has(c.pathname)&&(U={currentLocation:c,nextLocation:y.location})}else if(q){let N=le.get(y.location.pathname);N?N.add(c.pathname):(N=new Set([c.pathname]),le.set(y.location.pathname,N)),U={currentLocation:y.location,nextLocation:c}}ue({...v,actionData:T,loaderData:A,historyAction:j,location:c,initialized:!0,navigation:Yt,revalidation:"idle",restoreScrollPosition:D,preventScrollReset:$,blockers:O},{viewTransitionOpts:U,flushSync:g===!0}),j="POP",J=!1,q=!1,X=!1,Q=!1,z?.resolve(),z=null}async function Cr(c,v){if(typeof c=="number"){e.history.go(c);return}let g=qt(y.location,y.matches,i,c,v?.fromRouteId,v?.relative),{path:L,submission:T,error:A}=Vr(!1,g,v),O=y.location,D=at(y.location,L,v&&v.state);D={...D,...e.history.encodeLocation(D)};let $=v&&v.replace!=null?v.replace:void 0,U="PUSH";$===!0?U="REPLACE":$===!1||T!=null&&ye(T.formMethod)&&T.formAction===y.location.pathname+y.location.search&&(U="REPLACE");let N=v&&"preventScrollReset"in v?v.preventScrollReset===!0:void 0,F=(v&&v.flushSync)===!0,G=Nr({currentLocation:O,nextLocation:D,historyAction:U});if(G){dt(G,{state:"blocked",location:D,proceed(){dt(G,{state:"proceeding",proceed:void 0,reset:void 0,location:D}),Cr(c,v)},reset(){let re=new Map(y.blockers);re.set(G,et),ue({blockers:re})}});return}await ke(U,D,{submission:T,pendingError:A,preventScrollReset:N,replace:v&&v.replace,enableViewTransition:v&&v.viewTransition,flushSync:F})}function na(){z||(z=So()),$t(),ue({revalidation:"loading"});let c=z.promise;return y.navigation.state==="submitting"?c:y.navigation.state==="idle"?(ke(y.historyAction,y.location,{startUninterruptedRevalidation:!0}),c):(ke(j||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:q===!0}),c)}async function ke(c,v,g){H&&H.abort(),H=null,j=c,X=(g&&g.startUninterruptedRevalidation)===!0,ha(y.location,y.matches),J=(g&&g.preventScrollReset)===!0,q=(g&&g.enableViewTransition)===!0;let L=u||l,T=g&&g.overrideNavigation,A=g?.initialHydration&&y.matches&&y.matches.length>0&&!_?y.matches:Te(L,v,i),O=(g&&g.flushSync)===!0;if(A&&y.initialized&&!Q&&yo(y.location,v)&&!(g&&g.submission&&ye(g.submission.formMethod))){We(v,{matches:A},{flushSync:O});return}let D=ft(A,L,v.pathname);if(D.active&&D.matches&&(A=D.matches),!A){let{error:fe,notFoundMatches:ne,route:ee}=jt(v.pathname);We(v,{matches:ne,loaderData:{},errors:{[ee.id]:fe}},{flushSync:O});return}H=new AbortController;let $=Ye(e.history,v,H.signal,g&&g.submission),U=new Wr(e.unstable_getContext?await e.unstable_getContext():void 0),N;if(g&&g.pendingError)N=[je(A).route.id,{type:"error",error:g.pendingError}];else if(g&&g.submission&&ye(g.submission.formMethod)){let fe=await aa($,v,g.submission,A,U,D.active,g&&g.initialHydration===!0,{replace:g.replace,flushSync:O});if(fe.shortCircuited)return;if(fe.pendingActionResult){let[ne,ee]=fe.pendingActionResult;if(ge(ee)&&ze(ee.error)&&ee.error.status===404){H=null,We(v,{matches:fe.matches,loaderData:{},errors:{[ne]:ee.error}});return}}A=fe.matches||A,N=fe.pendingActionResult,T=Vt(v,g.submission),O=!1,D.active=!1,$=Ye(e.history,$.url,$.signal)}let{shortCircuited:F,matches:G,loaderData:re,errors:ce}=await oa($,v,A,U,D.active,T,g&&g.submission,g&&g.fetcherSubmission,g&&g.replace,g&&g.initialHydration===!0,O,N);F||(H=null,We(v,{matches:G||A,...rn(N),loaderData:re,errors:ce}))}async function aa(c,v,g,L,T,A,O,D={}){$t();let $=wo(v,g);if(ue({navigation:$},{flushSync:D.flushSync===!0}),A){let F=await ht(L,v.pathname,c.signal);if(F.type==="aborted")return{shortCircuited:!0};if(F.type==="error"){let G=je(F.partialMatches).route.id;return{matches:F.partialMatches,pendingActionResult:[G,{type:"error",error:F.error}]}}else if(F.matches)L=F.matches;else{let{notFoundMatches:G,error:re,route:ce}=jt(v.pathname);return{matches:G,pendingActionResult:[ce.id,{type:"error",error:re}]}}}let U,N=xt(L,v);if(!N.route.action&&!N.route.lazy)U={type:"error",error:Ee(405,{method:c.method,pathname:v.pathname,routeId:N.route.id})};else{let F=Ve(a,o,c,L,N,O?[]:n,T),G=await Xe(c,F,T,null);if(U=G[N.route.id],!U){for(let re of L)if(G[re.route.id]){U=G[re.route.id];break}}if(c.signal.aborted)return{shortCircuited:!0}}if(Ue(U)){let F;return D&&D.replace!=null?F=D.replace:F=Qr(U.response.headers.get("Location"),new URL(c.url),i)===y.location.pathname+y.location.search,await Ie(c,U,!0,{submission:g,replace:F}),{shortCircuited:!0}}if(ge(U)){let F=je(L,N.route.id);return(D&&D.replace)!==!0&&(j="PUSH"),{matches:L,pendingActionResult:[F.route.id,U,N.route.id]}}return{matches:L,pendingActionResult:[N.route.id,U]}}async function oa(c,v,g,L,T,A,O,D,$,U,N,F){let G=A||Vt(v,O),re=O||D||an(G),ce=!X&&!U;if(T){if(ce){let de=Pr(F);ue({navigation:G,...de!==void 0?{actionData:de}:{}},{flushSync:N})}let K=await ht(g,v.pathname,c.signal);if(K.type==="aborted")return{shortCircuited:!0};if(K.type==="error"){let de=je(K.partialMatches).route.id;return{matches:K.partialMatches,loaderData:{},errors:{[de]:K.error}}}else if(K.matches)g=K.matches;else{let{error:de,notFoundMatches:yt,route:Qe}=jt(v.pathname);return{matches:yt,loaderData:{},errors:{[Qe.id]:de}}}}let fe=u||l,{dsMatches:ne,revalidatingFetchers:ee}=Jr(c,L,a,o,e.history,y,g,re,v,U?[]:n,U===!0,Q,ae,I,x,h,fe,i,e.patchRoutesOnNavigation!=null,F);if(se=++pe,!e.dataStrategy&&!ne.some(K=>K.shouldLoad)&&ee.length===0){let K=Dr();return We(v,{matches:g,loaderData:{},errors:F&&ge(F[1])?{[F[0]]:F[1].error}:null,...rn(F),...K?{fetchers:new Map(y.fetchers)}:{}},{flushSync:N}),{shortCircuited:!0}}if(ce){let K={};if(!T){K.navigation=G;let de=Pr(F);de!==void 0&&(K.actionData=de)}ee.length>0&&(K.fetchers=ia(ee)),ue(K,{flushSync:N})}ee.forEach(K=>{_e(K.key),K.controller&&Y.set(K.key,K.controller)});let $e=()=>ee.forEach(K=>_e(K.key));H&&H.signal.addEventListener("abort",$e);let{loaderResults:Ke,fetcherResults:De}=await Tr(ne,ee,c,L);if(c.signal.aborted)return{shortCircuited:!0};H&&H.signal.removeEventListener("abort",$e),ee.forEach(K=>Y.delete(K.key));let Le=gt(Ke);if(Le)return await Ie(c,Le.result,!0,{replace:$}),{shortCircuited:!0};if(Le=gt(De),Le)return h.add(Le.key),await Ie(c,Le.result,!0,{replace:$}),{shortCircuited:!0};let{loaderData:Ut,errors:qe}=en(y,g,Ke,F,ee,De);U&&y.errors&&(qe={...y.errors,...qe});let Fe=Dr(),mt=Ar(se),pt=Fe||mt||ee.length>0;return{matches:g,loaderData:Ut,errors:qe,...pt?{fetchers:new Map(y.fetchers)}:{}}}function Pr(c){if(c&&!ge(c[1]))return{[c[0]]:c[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function ia(c){return c.forEach(v=>{let g=y.fetchers.get(v.key),L=tt(void 0,g?g.data:void 0);y.fetchers.set(v.key,L)}),new Map(y.fetchers)}async function la(c,v,g,L){_e(c);let T=(L&&L.flushSync)===!0,A=u||l,O=qt(y.location,y.matches,i,g,v,L?.relative),D=Te(A,O,i),$=ft(D,A,O);if($.active&&$.matches&&(D=$.matches),!D){Pe(c,v,Ee(404,{pathname:O}),{flushSync:T});return}let{path:U,submission:N,error:F}=Vr(!0,O,L);if(F){Pe(c,v,F,{flushSync:T});return}let G=new Wr(e.unstable_getContext?await e.unstable_getContext():void 0),re=(L&&L.preventScrollReset)===!0;if(N&&ye(N.formMethod)){await sa(c,v,U,D,G,$.active,T,re,N);return}x.set(c,{routeId:v,path:U}),await ua(c,v,U,D,G,$.active,T,re,N)}async function sa(c,v,g,L,T,A,O,D,$){$t(),x.delete(c);let U=y.fetchers.get(c);Me(c,Eo($,U),{flushSync:O});let N=new AbortController,F=Ye(e.history,g,N.signal,$);if(A){let ie=await ht(L,new URL(F.url).pathname,F.signal,c);if(ie.type==="aborted")return;if(ie.type==="error"){Pe(c,v,ie.error,{flushSync:O});return}else if(ie.matches)L=ie.matches;else{Pe(c,v,Ee(404,{pathname:g}),{flushSync:O});return}}let G=xt(L,g);if(!G.route.action&&!G.route.lazy){let ie=Ee(405,{method:$.formMethod,pathname:g,routeId:v});Pe(c,v,ie,{flushSync:O});return}Y.set(c,N);let re=pe,ce=Ve(a,o,F,L,G,n,T),ne=(await Xe(F,ce,T,c))[G.route.id];if(F.signal.aborted){Y.get(c)===N&&Y.delete(c);return}if(I.has(c)){if(Ue(ne)||ge(ne)){Me(c,Ae(void 0));return}}else{if(Ue(ne))if(Y.delete(c),se>re){Me(c,Ae(void 0));return}else return h.add(c),Me(c,tt($)),Ie(F,ne,!1,{fetcherSubmission:$,preventScrollReset:D});if(ge(ne)){Pe(c,v,ne.error);return}}let ee=y.navigation.location||y.location,$e=Ye(e.history,ee,N.signal),Ke=u||l,De=y.navigation.state!=="idle"?Te(Ke,y.navigation.location,i):y.matches;V(De,"Didn't find any matches after fetcher action");let Le=++pe;ve.set(c,Le);let Ut=tt($,ne.data);y.fetchers.set(c,Ut);let{dsMatches:qe,revalidatingFetchers:Fe}=Jr($e,T,a,o,e.history,y,De,$,ee,n,!1,Q,ae,I,x,h,Ke,i,e.patchRoutesOnNavigation!=null,[G.route.id,ne]);Fe.filter(ie=>ie.key!==c).forEach(ie=>{let vt=ie.key,$r=y.fetchers.get(vt),ya=tt(void 0,$r?$r.data:void 0);y.fetchers.set(vt,ya),_e(vt),ie.controller&&Y.set(vt,ie.controller)}),ue({fetchers:new Map(y.fetchers)});let mt=()=>Fe.forEach(ie=>_e(ie.key));N.signal.addEventListener("abort",mt);let{loaderResults:pt,fetcherResults:K}=await Tr(qe,Fe,$e,T);if(N.signal.aborted)return;if(N.signal.removeEventListener("abort",mt),ve.delete(c),Y.delete(c),Fe.forEach(ie=>Y.delete(ie.key)),y.fetchers.has(c)){let ie=Ae(ne.data);y.fetchers.set(c,ie)}let de=gt(pt);if(de)return Ie($e,de.result,!1,{preventScrollReset:D});if(de=gt(K),de)return h.add(de.key),Ie($e,de.result,!1,{preventScrollReset:D});let{loaderData:yt,errors:Qe}=en(y,De,pt,void 0,Fe,K);Ar(Le),y.navigation.state==="loading"&&Le>se?(V(j,"Expected pending action"),H&&H.abort(),We(y.navigation.location,{matches:De,loaderData:yt,errors:Qe,fetchers:new Map(y.fetchers)})):(ue({errors:Qe,loaderData:tn(y.loaderData,yt,De,Qe),fetchers:new Map(y.fetchers)}),Q=!1)}async function ua(c,v,g,L,T,A,O,D,$){let U=y.fetchers.get(c);Me(c,tt($,U?U.data:void 0),{flushSync:O});let N=new AbortController,F=Ye(e.history,g,N.signal);if(A){let ee=await ht(L,new URL(F.url).pathname,F.signal,c);if(ee.type==="aborted")return;if(ee.type==="error"){Pe(c,v,ee.error,{flushSync:O});return}else if(ee.matches)L=ee.matches;else{Pe(c,v,Ee(404,{pathname:g}),{flushSync:O});return}}let G=xt(L,g);Y.set(c,N);let re=pe,ce=Ve(a,o,F,L,G,n,T),ne=(await Xe(F,ce,T,c))[G.route.id];if(Y.get(c)===N&&Y.delete(c),!F.signal.aborted){if(I.has(c)){Me(c,Ae(void 0));return}if(Ue(ne))if(se>re){Me(c,Ae(void 0));return}else{h.add(c),await Ie(F,ne,!1,{preventScrollReset:D});return}if(ge(ne)){Pe(c,v,ne.error);return}Me(c,Ae(ne.data))}}async function Ie(c,v,g,{submission:L,fetcherSubmission:T,preventScrollReset:A,replace:O}={}){v.response.headers.has("X-Remix-Revalidate")&&(Q=!0);let D=v.response.headers.get("Location");V(D,"Expected a Location header on the redirect Response"),D=Qr(D,new URL(c.url),i);let $=at(y.location,D,{_isRedirect:!0});if(r){let ce=!1;if(v.response.headers.has("X-Remix-Reload-Document"))ce=!0;else if(dr(D)){const fe=pn(D,!0);ce=fe.origin!==t.location.origin||we(fe.pathname,i)==null}if(ce){O?t.location.replace(D):t.location.assign(D);return}}H=null;let U=O===!0||v.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:N,formAction:F,formEncType:G}=y.navigation;!L&&!T&&N&&F&&G&&(L=an(y.navigation));let re=L||T;if(to.has(v.response.status)&&re&&ye(re.formMethod))await ke(U,$,{submission:{...re,formAction:D},preventScrollReset:A||J,enableViewTransition:g?q:void 0});else{let ce=Vt($,L);await ke(U,$,{overrideNavigation:ce,fetcherSubmission:T,preventScrollReset:A||J,enableViewTransition:g?q:void 0})}}async function Xe(c,v,g,L){let T,A={};try{T=await co(s,c,v,L,g,!1)}catch(O){return v.filter(D=>D.shouldLoad).forEach(D=>{A[D.route.id]={type:"error",error:O}}),A}if(c.signal.aborted)return A;for(let[O,D]of Object.entries(T))if(vo(D)){let $=D.result;A[O]={type:"redirect",response:mo($,c,O,v,i)}}else A[O]=await ho(D);return A}async function Tr(c,v,g,L){let T=Xe(g,c,L,null),A=Promise.all(v.map(async $=>{if($.matches&&$.match&&$.request&&$.controller){let N=(await Xe($.request,$.matches,L,$.key))[$.match.route.id];return{[$.key]:N}}else return Promise.resolve({[$.key]:{type:"error",error:Ee(404,{pathname:$.path})}})})),O=await T,D=(await A).reduce(($,U)=>Object.assign($,U),{});return{loaderResults:O,fetcherResults:D}}function $t(){Q=!0,x.forEach((c,v)=>{Y.has(v)&&ae.add(v),_e(v)})}function Me(c,v,g={}){y.fetchers.set(c,v),ue({fetchers:new Map(y.fetchers)},{flushSync:(g&&g.flushSync)===!0})}function Pe(c,v,g,L={}){let T=je(y.matches,v);Ft(c),ue({errors:{[T.route.id]:g},fetchers:new Map(y.fetchers)},{flushSync:(L&&L.flushSync)===!0})}function Mr(c){return k.set(c,(k.get(c)||0)+1),I.has(c)&&I.delete(c),y.fetchers.get(c)||ro}function Ft(c){let v=y.fetchers.get(c);Y.has(c)&&!(v&&v.state==="loading"&&ve.has(c))&&_e(c),x.delete(c),ve.delete(c),h.delete(c),I.delete(c),ae.delete(c),y.fetchers.delete(c)}function ca(c){let v=(k.get(c)||0)-1;v<=0?(k.delete(c),I.add(c)):k.set(c,v),ue({fetchers:new Map(y.fetchers)})}function _e(c){let v=Y.get(c);v&&(v.abort(),Y.delete(c))}function _r(c){for(let v of c){let g=Mr(v),L=Ae(g.data);y.fetchers.set(v,L)}}function Dr(){let c=[],v=!1;for(let g of h){let L=y.fetchers.get(g);V(L,`Expected fetcher: ${g}`),L.state==="loading"&&(h.delete(g),c.push(g),v=!0)}return _r(c),v}function Ar(c){let v=[];for(let[g,L]of ve)if(L<c){let T=y.fetchers.get(g);V(T,`Expected fetcher: ${g}`),T.state==="loading"&&(_e(g),ve.delete(g),v.push(g))}return _r(v),v.length>0}function da(c,v){let g=y.blockers.get(c)||et;return W.get(c)!==v&&W.set(c,v),g}function Or(c){y.blockers.delete(c),W.delete(c)}function dt(c,v){let g=y.blockers.get(c)||et;V(g.state==="unblocked"&&v.state==="blocked"||g.state==="blocked"&&v.state==="blocked"||g.state==="blocked"&&v.state==="proceeding"||g.state==="blocked"&&v.state==="unblocked"||g.state==="proceeding"&&v.state==="unblocked",`Invalid blocker state transition: ${g.state} -> ${v.state}`);let L=new Map(y.blockers);L.set(c,v),ue({blockers:L})}function Nr({currentLocation:c,nextLocation:v,historyAction:g}){if(W.size===0)return;W.size>1&&te(!1,"A router only supports one blocker at a time");let L=Array.from(W.entries()),[T,A]=L[L.length-1],O=y.blockers.get(T);if(!(O&&O.state==="proceeding")&&A({currentLocation:c,nextLocation:v,historyAction:g}))return T}function jt(c){let v=Ee(404,{pathname:c}),g=u||l,{matches:L,route:T}=nn(g);return{notFoundMatches:L,route:T,error:v}}function fa(c,v,g){if(w=c,b=v,R=g||null,!S&&y.navigation===Yt){S=!0;let L=Ir(y.location,y.matches);L!=null&&ue({restoreScrollPosition:L})}return()=>{w=null,b=null,R=null}}function kr(c,v){return R&&R(c,v.map(L=>yn(L,y.loaderData)))||c.key}function ha(c,v){if(w&&b){let g=kr(c,v);w[g]=b()}}function Ir(c,v){if(w){let g=kr(c,v),L=w[g];if(typeof L=="number")return L}return null}function ft(c,v,g){if(e.patchRoutesOnNavigation)if(c){if(Object.keys(c[0].params).length>0)return{active:!0,matches:St(v,g,i,!0)}}else return{active:!0,matches:St(v,g,i,!0)||[]};return{active:!1,matches:null}}async function ht(c,v,g,L){if(!e.patchRoutesOnNavigation)return{type:"success",matches:c};let T=c;for(;;){let A=u==null,O=u||l,D=o;try{await e.patchRoutesOnNavigation({signal:g,path:v,matches:T,fetcherKey:L,patch:(N,F)=>{g.aborted||Gr(N,F,O,D,a,!1)}})}catch(N){return{type:"error",error:N,partialMatches:T}}finally{A&&!g.aborted&&(l=[...l])}if(g.aborted)return{type:"aborted"};let $=Te(O,v,i);if($)return{type:"success",matches:$};let U=St(O,v,i,!0);if(!U||T.length===U.length&&T.every((N,F)=>N.route.id===U[F].route.id))return{type:"success",matches:null};T=U}}function ma(c){o={},u=ot(c,a,void 0,o)}function pa(c,v,g=!1){let L=u==null;Gr(c,v,u||l,o,a,g),L&&(l=[...l],ue({}))}return C={get basename(){return i},get future(){return f},get state(){return y},get routes(){return l},get window(){return t},initialize:xe,subscribe:It,enableScrollRestoration:fa,navigate:Cr,fetch:la,revalidate:na,createHref:c=>e.history.createHref(c),encodeLocation:c=>e.history.encodeLocation(c),getFetcher:Mr,deleteFetcher:ca,dispose:oe,getBlocker:da,deleteBlocker:Or,patchRoutes:pa,_internalFetchControllers:Y,_internalSetRoutes:ma,_internalSetStateDoNotUseOrYouWillBreakYourApp(c){ue(c)}},C}function oo(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function qt(e,t,r,n,a,o){let l,u;if(a){l=[];for(let s of t)if(l.push(s),s.route.id===a){u=s;break}}else l=t,u=t[t.length-1];let i=cr(n||".",ur(l),we(e.pathname,r)||e.pathname,o==="path");if(n==null&&(i.search=e.search,i.hash=e.hash),(n==null||n===""||n===".")&&u){let s=hr(i.search);if(u.route.index&&!s)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&s){let f=new URLSearchParams(i.search),m=f.getAll("index");f.delete("index"),m.filter(w=>w).forEach(w=>f.append("index",w));let p=f.toString();i.search=p?`?${p}`:""}}return r!=="/"&&(i.pathname=za({basename:r,pathname:i.pathname})),Oe(i)}function Vr(e,t,r){if(!r||!oo(r))return{path:t};if(r.formMethod&&!go(r.formMethod))return{path:t,error:Ee(405,{method:r.formMethod})};let n=()=>({path:t,error:Ee(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=Mn(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!ye(o))return n();let m=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((p,[w,R])=>`${p}${w}=${R}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:m}}}else if(r.formEncType==="application/json"){if(!ye(o))return n();try{let m=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:m,text:void 0}}}catch{return n()}}}V(typeof FormData=="function","FormData is not available in this environment");let u,i;if(r.formData)u=er(r.formData),i=r.formData;else if(r.body instanceof FormData)u=er(r.body),i=r.body;else if(r.body instanceof URLSearchParams)u=r.body,i=Zr(u);else if(r.body==null)u=new URLSearchParams,i=new FormData;else try{u=new URLSearchParams(r.body),i=Zr(u)}catch{return n()}let s={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(ye(s.formMethod))return{path:t,submission:s};let f=Ne(t);return e&&f.search&&hr(f.search)&&u.append("index",""),f.search=`?${u}`,{path:Oe(f),submission:s}}function Jr(e,t,r,n,a,o,l,u,i,s,f,m,p,w,R,b,S,E,_,P){let M=P?ge(P[1])?P[1].error:P[1].data:void 0,C=a.createURL(o.location),y=a.createURL(i),j;if(f&&o.errors){let X=Object.keys(o.errors)[0];j=l.findIndex(Q=>Q.route.id===X)}else if(P&&ge(P[1])){let X=P[0];j=l.findIndex(Q=>Q.route.id===X)-1}let J=P?P[1].statusCode:void 0,H=J&&J>=400,q={currentUrl:C,currentParams:o.matches[0]?.params||{},nextUrl:y,nextParams:l[0].params,...u,actionResult:M,actionStatus:J},le=l.map((X,Q)=>{let{route:ae}=X,Y=null;if(j!=null&&Q>j?Y=!1:ae.lazy?Y=!0:ae.loader==null?Y=!1:f?Y=Qt(ae,o.loaderData,o.errors):io(o.loaderData,o.matches[Q],X)&&(Y=!0),Y!==null)return Zt(r,n,e,X,s,t,Y);let pe=H?!1:m||C.pathname+C.search===y.pathname+y.search||C.search!==y.search||lo(o.matches[Q],X),se={...q,defaultShouldRevalidate:pe},ve=_t(X,se);return Zt(r,n,e,X,s,t,ve,se)}),me=[];return R.forEach((X,Q)=>{if(f||!l.some(k=>k.route.id===X.routeId)||w.has(Q))return;let ae=o.fetchers.get(Q),Y=ae&&ae.state!=="idle"&&ae.data===void 0,pe=Te(S,X.path,E);if(!pe){if(_&&Y)return;me.push({key:Q,routeId:X.routeId,path:X.path,matches:null,match:null,request:null,controller:null});return}if(b.has(Q))return;let se=xt(pe,X.path),ve=new AbortController,h=Ye(a,X.path,ve.signal),x=null;if(p.has(Q))p.delete(Q),x=Ve(r,n,h,pe,se,s,t);else if(Y)m&&(x=Ve(r,n,h,pe,se,s,t));else{let k={...q,defaultShouldRevalidate:H?!1:m};_t(se,k)&&(x=Ve(r,n,h,pe,se,s,t,k))}x&&me.push({key:Q,routeId:X.routeId,path:X.path,matches:x,match:se,request:h,controller:ve})}),{dsMatches:le,revalidatingFetchers:me}}function Qt(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function io(e,t,r){let n=!t||r.route.id!==t.route.id,a=!e.hasOwnProperty(r.route.id);return n||a}function lo(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function _t(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Gr(e,t,r,n,a,o){let l;if(e){let s=n[e];V(s,`No route found to patch children into: routeId = ${e}`),s.children||(s.children=[]),l=s.children}else l=r;let u=[],i=[];if(t.forEach(s=>{let f=l.find(m=>xn(s,m));f?i.push({existingRoute:f,newRoute:s}):u.push(s)}),u.length>0){let s=ot(u,a,[e||"_","patch",String(l?.length||"0")],n);l.push(...s)}if(o&&i.length>0)for(let s=0;s<i.length;s++){let{existingRoute:f,newRoute:m}=i[s],p=f,[w]=ot([m],a,[],{},!0);Object.assign(p,{element:w.element?w.element:p.element,errorElement:w.errorElement?w.errorElement:p.errorElement,hydrateFallbackElement:w.hydrateFallbackElement?w.hydrateFallbackElement:p.hydrateFallbackElement})}}function xn(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>t.children?.some(a=>xn(r,a))):!1}var Xr=new WeakMap,Ln=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(V(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let o=a.lazy[e];if(!o)return;let l=Xr.get(a);l||(l={},Xr.set(a,l));let u=l[e];if(u)return u;let i=(async()=>{let s=Pa(e),m=a[e]!==void 0&&e!=="hasErrorBoundary";if(s)te(!s,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(m)te(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let p=await o();p!=null&&(Object.assign(a,{[e]:p}),Object.assign(a,n(a)))}typeof a.lazy=="object"&&(a.lazy[e]=void 0,Object.values(a.lazy).every(p=>p===void 0)&&(a.lazy=void 0))})();return l[e]=i,i},Kr=new WeakMap;function so(e,t,r,n,a){let o=r[e.id];if(V(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let f=Kr.get(o);if(f)return{lazyRoutePromise:f,lazyHandlerPromise:f};let m=(async()=>{V(typeof e.lazy=="function","No lazy route function found");let p=await e.lazy(),w={};for(let R in p){let b=p[R];if(b===void 0)continue;let S=Ma(R),_=o[R]!==void 0&&R!=="hasErrorBoundary";S?te(!S,"Route property "+R+" is not a supported property to be returned from a lazy route function. This property will be ignored."):_?te(!_,`Route "${o.id}" has a static property "${R}" defined but its lazy function is also returning a value for this property. The lazy route property "${R}" will be ignored.`):w[R]=b}Object.assign(o,w),Object.assign(o,{...n(o),lazy:void 0})})();return Kr.set(o,m),m.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:m}}let l=Object.keys(e.lazy),u=[],i;for(let f of l){if(a&&a.includes(f))continue;let m=Ln({key:f,route:e,manifest:r,mapRouteProperties:n});m&&(u.push(m),f===t&&(i=m))}let s=u.length>0?Promise.all(u).then(()=>{}):void 0;return s?.catch(()=>{}),i?.catch(()=>{}),{lazyRoutePromise:s,lazyHandlerPromise:i}}async function qr(e){let t=e.matches.filter(a=>a.shouldLoad),r={};return(await Promise.all(t.map(a=>a.resolve()))).forEach((a,o)=>{r[t[o].route.id]=a}),r}async function uo(e){return e.matches.some(t=>t.route.unstable_middleware)?Cn(e,!1,()=>qr(e),(t,r)=>({[r]:{type:"error",result:t}})):qr(e)}async function Cn(e,t,r,n){let{matches:a,request:o,params:l,context:u}=e,i={handlerResult:void 0};try{let s=a.flatMap(m=>m.route.unstable_middleware?m.route.unstable_middleware.map(p=>[m.route.id,p]):[]),f=await Pn({request:o,params:l,context:u},s,t,i,r);return t?f:i.handlerResult}catch(s){if(!i.middlewareError)throw s;let f=await n(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,f):f}}async function Pn(e,t,r,n,a,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let u=t[o];if(!u)return n.handlerResult=await a(),n.handlerResult;let[i,s]=u,f=!1,m,p=async()=>{if(f)throw new Error("You may only call `next()` once per middleware");f=!0,await Pn(e,t,r,n,a,o+1)};try{let w=await s({request:e.request,params:e.params,context:e.context},p);return f?w===void 0?m:w:p()}catch(w){throw n.middlewareError?n.middlewareError.error!==w&&(n.middlewareError={routeId:i,error:w}):n.middlewareError={routeId:i,error:w},w}}function Tn(e,t,r,n,a){let o=Ln({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),l=so(n.route,ye(r.method)?"action":"loader",t,e,a);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function Zt(e,t,r,n,a,o,l,u=null){let i=!1,s=Tn(e,t,r,n,a);return{...n,_lazyPromises:s,shouldLoad:l,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(f){return i=!0,u?typeof f=="boolean"?_t(n,{...u,defaultShouldRevalidate:f}):_t(n,u):l},resolve(f){return i||l||f&&!ye(r.method)&&(n.route.lazy||n.route.loader)?fo({request:r,match:n,lazyHandlerPromise:s?.handler,lazyRoutePromise:s?.route,handlerOverride:f,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Ve(e,t,r,n,a,o,l,u=null){return n.map(i=>i.route.id!==a.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Tn(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Zt(e,t,r,i,o,l,!0,u))}async function co(e,t,r,n,a,o){r.some(s=>s._lazyPromises?.middleware)&&await Promise.all(r.map(s=>s._lazyPromises?.middleware));let l={request:t,params:r[0].params,context:a,matches:r},i=await e({...l,fetcherKey:n,unstable_runClientMiddleware:s=>{let f=l;return Cn(f,!1,()=>s({...f,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(m,p)=>({[p]:{type:"error",result:m}}))}});try{await Promise.all(r.flatMap(s=>[s._lazyPromises?.handler,s._lazyPromises?.route]))}catch{}return i}async function fo({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:o}){let l,u,i=ye(e.method),s=i?"action":"loader",f=m=>{let p,w=new Promise((S,E)=>p=E);u=()=>p(),e.signal.addEventListener("abort",u);let R=S=>typeof m!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${s}" [routeId: ${t.route.id}]`)):m({request:e,params:t.params,context:o},...S!==void 0?[S]:[]),b=(async()=>{try{return{type:"data",result:await(a?a(E=>R(E)):R())}}catch(S){return{type:"error",result:S}}})();return Promise.race([b,w])};try{let m=i?t.route.action:t.route.loader;if(r||n)if(m){let p,[w]=await Promise.all([f(m).catch(R=>{p=R}),r,n]);if(p!==void 0)throw p;l=w}else{await r;let p=i?t.route.action:t.route.loader;if(p)[l]=await Promise.all([f(p),n]);else if(s==="action"){let w=new URL(e.url),R=w.pathname+w.search;throw Ee(405,{method:e.method,pathname:R,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(m)l=await f(m);else{let p=new URL(e.url),w=p.pathname+p.search;throw Ee(404,{pathname:w})}}catch(m){return{type:"error",result:m}}finally{u&&e.signal.removeEventListener("abort",u)}return l}async function ho(e){let{result:t,type:r}=e;if(fr(t)){let n;try{let a=t.headers.get("Content-Type");a&&/\bapplication\/json\b/.test(a)?t.body==null?n=null:n=await t.json():n=await t.text()}catch(a){return{type:"error",error:a}}return r==="error"?{type:"error",error:new He(t.status,t.statusText,n),statusCode:t.status,headers:t.headers}:{type:"data",data:n,statusCode:t.status,headers:t.headers}}return r==="error"?tr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new He(t.init?.status||500,void 0,t.data),statusCode:ze(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:ze(t)?t.status:void 0}:tr(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function mo(e,t,r,n,a){let o=e.headers.get("Location");if(V(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!dr(o)){let l=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=qt(new URL(t.url),l,a,o),e.headers.set("Location",o)}return e}function Qr(e,t,r){if(dr(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=we(a.pathname,r)!=null;if(a.origin===t.origin&&o)return a.pathname+a.search+a.hash}return e}function Ye(e,t,r,n){let a=e.createURL(Mn(t)).toString(),o={signal:r};if(n&&ye(n.formMethod)){let{formMethod:l,formEncType:u}=n;o.method=l.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=er(n.formData):o.body=n.formData}return new Request(a,o)}function er(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Zr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function po(e,t,r,n=!1,a=!1){let o={},l=null,u,i=!1,s={},f=r&&ge(r[1])?r[1].error:void 0;return e.forEach(m=>{if(!(m.route.id in t))return;let p=m.route.id,w=t[p];if(V(!Ue(w),"Cannot handle redirect results in processLoaderData"),ge(w)){let R=w.error;if(f!==void 0&&(R=f,f=void 0),l=l||{},a)l[p]=R;else{let b=je(e,p);l[b.route.id]==null&&(l[b.route.id]=R)}n||(o[p]=Sn),i||(i=!0,u=ze(w.error)?w.error.status:500),w.headers&&(s[p]=w.headers)}else o[p]=w.data,w.statusCode&&w.statusCode!==200&&!i&&(u=w.statusCode),w.headers&&(s[p]=w.headers)}),f!==void 0&&r&&(l={[r[0]]:f},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:u||200,loaderHeaders:s}}function en(e,t,r,n,a,o){let{loaderData:l,errors:u}=po(t,r,n);return a.filter(i=>!i.matches||i.matches.some(s=>s.shouldLoad)).forEach(i=>{let{key:s,match:f,controller:m}=i,p=o[s];if(V(p,"Did not find corresponding fetcher result"),!(m&&m.signal.aborted))if(ge(p)){let w=je(e.matches,f?.route.id);u&&u[w.route.id]||(u={...u,[w.route.id]:p.error}),e.fetchers.delete(s)}else if(Ue(p))V(!1,"Unhandled fetcher revalidation redirect");else{let w=Ae(p.data);e.fetchers.set(s,w)}}),{loaderData:l,errors:u}}function tn(e,t,r,n){let a=Object.entries(t).filter(([,o])=>o!==Sn).reduce((o,[l,u])=>(o[l]=u,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(a[l]=e[l]),n&&n.hasOwnProperty(l))break}return a}function rn(e){return e?ge(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function je(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function nn(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Ee(e,{pathname:t,routeId:r,method:n,type:a,message:o}={}){let l="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(l="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:a==="invalid-body"&&(u="Unable to encode submission body")):e===403?(l="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",u=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new He(e||500,l,new Error(u),!0)}function gt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(Ue(a))return{key:n,result:a}}}function Mn(e){let t=typeof e=="string"?Ne(e):e;return Oe({...t,hash:""})}function yo(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function vo(e){return fr(e.result)&&eo.has(e.result.status)}function ge(e){return e.type==="error"}function Ue(e){return(e&&e.type)==="redirect"}function tr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function fr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function go(e){return Za.has(e.toUpperCase())}function ye(e){return qa.has(e.toUpperCase())}function hr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function xt(e,t){let r=typeof t=="string"?Ne(t).search:t.search;if(e[e.length-1].route.index&&hr(r||""))return e[e.length-1];let n=En(e);return n[n.length-1]}function an(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:l}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:l,text:void 0}}}function Vt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function wo(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function tt(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Eo(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Ae(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Ro(e,t){try{let r=e.sessionStorage.getItem(bn);if(r){let n=JSON.parse(r);for(let[a,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(a,new Set(o||[]))}}catch{}}function bo(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(bn,JSON.stringify(r))}catch(n){te(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function So(){let e,t,r=new Promise((n,a)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{a(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Be=d.createContext(null);Be.displayName="DataRouter";var Je=d.createContext(null);Je.displayName="DataRouterState";var xo=d.createContext(!1);function Lo(){return d.useContext(xo)}var mr=d.createContext({isTransitioning:!1});mr.displayName="ViewTransition";var _n=d.createContext(new Map);_n.displayName="Fetchers";var Co=d.createContext(null);Co.displayName="Await";var Re=d.createContext(null);Re.displayName="Navigation";var At=d.createContext(null);At.displayName="Location";var be=d.createContext({outlet:null,matches:[],isDataRoute:!1});be.displayName="Route";var pr=d.createContext(null);pr.displayName="RouteError";function Po(e,{relative:t}={}){V(lt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=d.useContext(Re),{hash:a,pathname:o,search:l}=st(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:Ce([r,o])),n.createHref({pathname:u,search:l,hash:a})}function lt(){return d.useContext(At)!=null}function Se(){return V(lt(),"useLocation() may be used only in the context of a <Router> component."),d.useContext(At).location}var Dn="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function An(e){d.useContext(Re).static||d.useLayoutEffect(e)}function On(){let{isDataRoute:e}=d.useContext(be);return e?Ho():To()}function To(){V(lt(),"useNavigate() may be used only in the context of a <Router> component.");let e=d.useContext(Be),{basename:t,navigator:r}=d.useContext(Re),{matches:n}=d.useContext(be),{pathname:a}=Se(),o=JSON.stringify(ur(n)),l=d.useRef(!1);return An(()=>{l.current=!0}),d.useCallback((i,s={})=>{if(te(l.current,Dn),!l.current)return;if(typeof i=="number"){r.go(i);return}let f=cr(i,JSON.parse(o),a,s.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Ce([t,f.pathname])),(s.replace?r.replace:r.push)(f,s.state,s)},[t,r,o,a,e])}var Mo=d.createContext(null);function _o(e){let t=d.useContext(be).outlet;return t&&d.createElement(Mo.Provider,{value:e},t)}function Nn(){let{matches:e}=d.useContext(be),t=e[e.length-1];return t?t.params:{}}function st(e,{relative:t}={}){let{matches:r}=d.useContext(be),{pathname:n}=Se(),a=JSON.stringify(ur(r));return d.useMemo(()=>cr(e,JSON.parse(a),n,t==="path"),[e,a,n,t])}function Do(e,t,r,n){V(lt(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=d.useContext(Re),{matches:o}=d.useContext(be),l=o[o.length-1],u=l?l.params:{},i=l?l.pathname:"/",s=l?l.pathnameBase:"/",f=l&&l.route;{let E=f&&f.path||"";$n(i,!f||E.endsWith("*")||E.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${E}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${E}"> to <Route path="${E==="/"?"*":`${E}/*`}">.`)}let m=Se(),p;p=m;let w=p.pathname||"/",R=w;if(s!=="/"){let E=s.replace(/^\//,"").split("/");R="/"+w.replace(/^\//,"").split("/").slice(E.length).join("/")}let b=Te(e,{pathname:R});return te(f||b!=null,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),te(b==null||b[b.length-1].route.element!==void 0||b[b.length-1].route.Component!==void 0||b[b.length-1].route.lazy!==void 0,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Io(b&&b.map(E=>Object.assign({},E,{params:Object.assign({},u,E.params),pathname:Ce([s,a.encodeLocation?a.encodeLocation(E.pathname).pathname:E.pathname]),pathnameBase:E.pathnameBase==="/"?s:Ce([s,a.encodeLocation?a.encodeLocation(E.pathnameBase).pathname:E.pathnameBase])})),o,r,n)}function Ao(){let e=gr(),t=ze(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=d.createElement(d.Fragment,null,d.createElement("p",null,"💿 Hey developer 👋"),d.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",d.createElement("code",{style:o},"ErrorBoundary")," or"," ",d.createElement("code",{style:o},"errorElement")," prop on your route.")),d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},t),r?d.createElement("pre",{style:a},r):null,l)}var Oo=d.createElement(Ao,null),No=class extends d.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?d.createElement(be.Provider,{value:this.props.routeContext},d.createElement(pr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ko({routeContext:e,match:t,children:r}){let n=d.useContext(Be);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),d.createElement(be.Provider,{value:e},r)}function Io(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,o=r?.errors;if(o!=null){let i=a.findIndex(s=>s.route.id&&o?.[s.route.id]!==void 0);V(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,i+1))}let l=!1,u=-1;if(r)for(let i=0;i<a.length;i++){let s=a[i];if((s.route.HydrateFallback||s.route.hydrateFallbackElement)&&(u=i),s.route.id){let{loaderData:f,errors:m}=r,p=s.route.loader&&!f.hasOwnProperty(s.route.id)&&(!m||m[s.route.id]===void 0);if(s.route.lazy||p){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((i,s,f)=>{let m,p=!1,w=null,R=null;r&&(m=o&&s.route.id?o[s.route.id]:void 0,w=s.route.errorElement||Oo,l&&(u<0&&f===0?($n("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),p=!0,R=null):u===f&&(p=!0,R=s.route.hydrateFallbackElement||null)));let b=t.concat(a.slice(0,f+1)),S=()=>{let E;return m?E=w:p?E=R:s.route.Component?E=d.createElement(s.route.Component,null):s.route.element?E=s.route.element:E=i,d.createElement(ko,{match:s,routeContext:{outlet:i,matches:b,isDataRoute:r!=null},children:E})};return r&&(s.route.ErrorBoundary||s.route.errorElement||f===0)?d.createElement(No,{location:r.location,revalidation:r.revalidation,component:w,error:m,children:S(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):S()},null)}function yr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function $o(e){let t=d.useContext(Be);return V(t,yr(e)),t}function ut(e){let t=d.useContext(Je);return V(t,yr(e)),t}function Fo(e){let t=d.useContext(be);return V(t,yr(e)),t}function ct(e){let t=Fo(e),r=t.matches[t.matches.length-1];return V(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function jo(){return ct("useRouteId")}function Uo(){return ut("useNavigation").navigation}function vr(){let{matches:e,loaderData:t}=ut("useMatches");return d.useMemo(()=>e.map(r=>yn(r,t)),[e,t])}function kn(){let e=ut("useLoaderData"),t=ct("useLoaderData");return e.loaderData[t]}function In(){let e=ut("useActionData"),t=ct("useLoaderData");return e.actionData?e.actionData[t]:void 0}function gr(){let e=d.useContext(pr),t=ut("useRouteError"),r=ct("useRouteError");return e!==void 0?e:t.errors?.[r]}function Ho(){let{router:e}=$o("useNavigate"),t=ct("useNavigate"),r=d.useRef(!1);return An(()=>{r.current=!0}),d.useCallback(async(a,o={})=>{te(r.current,Dn),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...o}))},[e,t])}var on={};function $n(e,t,r){!t&&!on[e]&&(on[e]=!0,te(!1,r))}var ln={};function rr(e,t){!e&&!ln[t]&&(ln[t]=!0,console.warn(t))}function Sl(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&te(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:d.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&te(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:d.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&te(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:d.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var xl=["HydrateFallback","hydrateFallbackElement"],zo=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function Ll({router:e,flushSync:t}){let[r,n]=d.useState(e.state),[a,o]=d.useState(),[l,u]=d.useState({isTransitioning:!1}),[i,s]=d.useState(),[f,m]=d.useState(),[p,w]=d.useState(),R=d.useRef(new Map),b=d.useCallback((P,{deletedFetchers:M,flushSync:C,viewTransitionOpts:y})=>{P.fetchers.forEach((J,H)=>{J.data!==void 0&&R.current.set(H,J.data)}),M.forEach(J=>R.current.delete(J)),rr(C===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let j=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(rr(y==null||j,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!y||!j){t&&C?t(()=>n(P)):d.startTransition(()=>n(P));return}if(t&&C){t(()=>{f&&(i&&i.resolve(),f.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:y.currentLocation,nextLocation:y.nextLocation})});let J=e.window.document.startViewTransition(()=>{t(()=>n(P))});J.finished.finally(()=>{t(()=>{s(void 0),m(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>m(J));return}f?(i&&i.resolve(),f.skipTransition(),w({state:P,currentLocation:y.currentLocation,nextLocation:y.nextLocation})):(o(P),u({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}))},[e.window,t,f,i]);d.useLayoutEffect(()=>e.subscribe(b),[e,b]),d.useEffect(()=>{l.isTransitioning&&!l.flushSync&&s(new zo)},[l]),d.useEffect(()=>{if(i&&a&&e.window){let P=a,M=i.promise,C=e.window.document.startViewTransition(async()=>{d.startTransition(()=>n(P)),await M});C.finished.finally(()=>{s(void 0),m(void 0),o(void 0),u({isTransitioning:!1})}),m(C)}},[a,i,e.window]),d.useEffect(()=>{i&&a&&r.location.key===a.location.key&&i.resolve()},[i,f,r.location,a]),d.useEffect(()=>{!l.isTransitioning&&p&&(o(p.state),u({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}),w(void 0))},[l.isTransitioning,p]);let S=d.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:P=>e.navigate(P),push:(P,M,C)=>e.navigate(P,{state:M,preventScrollReset:C?.preventScrollReset}),replace:(P,M,C)=>e.navigate(P,{replace:!0,state:M,preventScrollReset:C?.preventScrollReset})}),[e]),E=e.basename||"/",_=d.useMemo(()=>({router:e,navigator:S,static:!1,basename:E}),[e,S,E]);return d.createElement(d.Fragment,null,d.createElement(Be.Provider,{value:_},d.createElement(Je.Provider,{value:r},d.createElement(_n.Provider,{value:R.current},d.createElement(mr.Provider,{value:l},d.createElement(Yo,{basename:E,location:r.location,navigationType:r.historyAction,navigator:S},d.createElement(Bo,{routes:e.routes,future:e.future,state:r})))))),null)}var Bo=d.memo(Wo);function Wo({routes:e,future:t,state:r}){return Do(e,void 0,r,t)}function Cl(e){return _o(e.context)}function Yo({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:a,static:o=!1}){V(!lt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),u=d.useMemo(()=>({basename:l,navigator:a,static:o,future:{}}),[l,a,o]);typeof r=="string"&&(r=Ne(r));let{pathname:i="/",search:s="",hash:f="",state:m=null,key:p="default"}=r,w=d.useMemo(()=>{let R=we(i,l);return R==null?null:{location:{pathname:R,search:s,hash:f,state:m,key:p},navigationType:n}},[l,i,s,f,m,p,n]);return te(w!=null,`<Router basename="${l}"> is not able to match the URL "${i}${s}${f}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:d.createElement(Re.Provider,{value:u},d.createElement(At.Provider,{children:t,value:w}))}function Vo(){return{params:Nn(),loaderData:kn(),actionData:In(),matches:vr()}}function Pl(e){return function(){const r=Vo();return d.createElement(e,r)}}function Jo(){return{params:Nn(),loaderData:kn(),actionData:In(),error:gr()}}function Tl(e){return function(){const r=Jo();return d.createElement(e,r)}}var Lt="get",Ct="application/x-www-form-urlencoded";function Ot(e){return e!=null&&typeof e.tagName=="string"}function Go(e){return Ot(e)&&e.tagName.toLowerCase()==="button"}function Xo(e){return Ot(e)&&e.tagName.toLowerCase()==="form"}function Ko(e){return Ot(e)&&e.tagName.toLowerCase()==="input"}function qo(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Qo(e,t){return e.button===0&&(!t||t==="_self")&&!qo(e)}function nr(e=""){return new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function Zo(e,t){let r=nr(e);return t&&t.forEach((n,a)=>{r.has(a)||t.getAll(a).forEach(o=>{r.append(a,o)})}),r}var wt=null;function ei(){if(wt===null)try{new FormData(document.createElement("form"),0),wt=!1}catch{wt=!0}return wt}var ti=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Jt(e){return e!=null&&!ti.has(e)?(te(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ct}"`),null):e}function ri(e,t){let r,n,a,o,l;if(Xo(e)){let u=e.getAttribute("action");n=u?we(u,t):null,r=e.getAttribute("method")||Lt,a=Jt(e.getAttribute("enctype"))||Ct,o=new FormData(e)}else if(Go(e)||Ko(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||u.getAttribute("action");if(n=i?we(i,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||Lt,a=Jt(e.getAttribute("formenctype"))||Jt(u.getAttribute("enctype"))||Ct,o=new FormData(u,e),!ei()){let{name:s,type:f,value:m}=e;if(f==="image"){let p=s?`${s}.`:"";o.append(`${p}x`,"0"),o.append(`${p}y`,"0")}else s&&o.append(s,m)}}else{if(Ot(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Lt,n=null,a=Ct,l=e}return o&&a==="text/plain"&&(l=o,o=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:o,body:l}}var ni=-1,ai=-2,oi=-3,ii=-4,li=-5,si=-6,ui=-7,ci="B",di="D",Fn="E",fi="M",hi="N",jn="P",mi="R",pi="S",yi="Y",vi="U",gi="Z",Un=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function wi(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,n){const a=e.decode(r,{stream:!0}),o=(t+a).split(`
`);t=o.pop()||"";for(const l of o)n.enqueue(l)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Gt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function ar(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return sn.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const n=r.length;for(const a of e)r.push(a);return t.length=r.length,sn.call(this,n)}function sn(e){const{hydrated:t,values:r,deferred:n,plugins:a}=this;let o;const l=[[e,i=>{o=i}]];let u=[];for(;l.length>0;){const[i,s]=l.pop();switch(i){case ui:s(void 0);continue;case li:s(null);continue;case ai:s(NaN);continue;case si:s(1/0);continue;case oi:s(-1/0);continue;case ii:s(-0);continue}if(t[i]){s(t[i]);continue}const f=r[i];if(!f||typeof f!="object"){t[i]=f,s(f);continue}if(Array.isArray(f))if(typeof f[0]=="string"){const[m,p,w]=f;switch(m){case di:s(t[i]=new Date(p));continue;case vi:s(t[i]=new URL(p));continue;case ci:s(t[i]=BigInt(p));continue;case mi:s(t[i]=new RegExp(p,w));continue;case yi:s(t[i]=Symbol.for(p));continue;case pi:const R=new Set;t[i]=R;for(let M=f.length-1;M>0;M--)l.push([f[M],C=>{R.add(C)}]);s(R);continue;case fi:const b=new Map;t[i]=b;for(let M=f.length-2;M>0;M-=2){const C=[];l.push([f[M+1],y=>{C[1]=y}]),l.push([f[M],y=>{C[0]=y}]),u.push(()=>{b.set(C[0],C[1])})}s(b);continue;case hi:const S=Object.create(null);t[i]=S;for(const M of Object.keys(p).reverse()){const C=[];l.push([p[M],y=>{C[1]=y}]),l.push([Number(M.slice(1)),y=>{C[0]=y}]),u.push(()=>{S[C[0]]=C[1]})}s(S);continue;case jn:if(t[p])s(t[i]=t[p]);else{const M=new Un;n[p]=M,s(t[i]=M.promise)}continue;case Fn:const[,E,_]=f;let P=_&&Gt&&Gt[_]?new Gt[_](E):new Error(E);t[i]=P,s(P);continue;case gi:s(t[i]=t[p]);continue;default:if(Array.isArray(a)){const M=[],C=f.slice(1);for(let y=0;y<C.length;y++){const j=C[y];l.push([j,J=>{M[y]=J}])}u.push(()=>{for(const y of a){const j=y(f[0],...M);if(j){s(t[i]=j.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const m=[];t[i]=m;for(let p=0;p<f.length;p++){const w=f[p];w!==ni&&l.push([w,R=>{m[p]=R}])}s(m);continue}else{const m={};t[i]=m;for(const p of Object.keys(f).reverse()){const w=[];l.push([f[p],R=>{w[1]=R}]),l.push([Number(p.slice(1)),R=>{w[0]=R}]),u.push(()=>{m[w[0]]=w[1]})}s(m);continue}}for(;u.length>0;)u.pop()();return o}async function Ei(e,t){const{plugins:r}=t??{},n=new Un,a=e.pipeThrough(wi()).getReader(),o={values:[],hydrated:[],deferred:{},plugins:r},l=await Ri.call(o,a);let u=n.promise;return l.done?n.resolve():u=bi.call(o,a).then(n.resolve).catch(i=>{for(const s of Object.values(o.deferred))s.reject(i);n.reject(i)}),{done:u.then(()=>a.closed),value:l.value}}async function Ri(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:ar.call(this,r)}}async function bi(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case jn:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),o=this.deferred[a];if(!o)throw new Error(`Deferred ID ${a} not found in stream`);const l=r.slice(n+1);let u;try{u=JSON.parse(l)}catch{throw new SyntaxError}const i=ar.call(this,u);o.resolve(i);break}case Fn:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),o=this.deferred[a];if(!o)throw new Error(`Deferred ID ${a} not found in stream`);const l=r.slice(n+1);let u;try{u=JSON.parse(l)}catch{throw new SyntaxError}const i=ar.call(this,u);o.reject(i);break}default:throw new SyntaxError}t=await e.read()}}async function Si(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}function un(e){return{__html:e}}function he(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}var or=Symbol("SingleFetchRedirect"),Hn=class extends Error{},xi=202,Li=new Set([100,101,204,205]);function Ml(e,t,r,n,a){let o=Ci(e,l=>{let u=t.routes[l.route.id];he(u,"Route not found in manifest");let i=r[l.route.id];return{hasLoader:u.hasLoader,hasClientLoader:u.hasClientLoader,hasShouldRevalidate:!!i?.shouldRevalidate}},Oi,n,a);return async l=>l.unstable_runClientMiddleware(o)}function Ci(e,t,r,n,a,o=()=>!0){return async l=>{let{request:u,matches:i,fetcherKey:s}=l,f=e();if(u.method!=="GET")return Pi(l,r,a);let m=i.some(p=>{let{hasLoader:w,hasClientLoader:R}=t(p);return p.unstable_shouldCallHandler()&&w&&!R});return!n&&!m?Ti(l,t,r,a):s?Di(l,r,a):Mi(l,f,t,r,n,a,o)}}async function Pi(e,t,r){let n=e.matches.find(l=>l.unstable_shouldCallHandler());he(n,"No action match found");let a,o=await n.resolve(async l=>await l(async()=>{let{data:i,status:s}=await t(e,r,[n.route.id]);return a=s,it(i,n.route.id)}));return fr(o.result)||ze(o.result)||tr(o.result)?{[n.route.id]:o}:{[n.route.id]:{type:o.type,result:Xa(o.result,a)}}}async function Ti(e,t,r,n){let a=e.matches.filter(l=>l.unstable_shouldCallHandler()),o={};return await Promise.all(a.map(l=>l.resolve(async u=>{try{let{hasClientLoader:i}=t(l),s=l.route.id,f=i?await u(async()=>{let{data:m}=await r(e,n,[s]);return it(m,s)}):await u();o[l.route.id]={type:"data",result:f}}catch(i){o[l.route.id]={type:"error",result:i}}}))),o}async function Mi(e,t,r,n,a,o,l=()=>!0){let u=new Set,i=!1,s=e.matches.map(()=>cn()),f=cn(),m={},p=Promise.all(e.matches.map(async(R,b)=>R.resolve(async S=>{s[b].resolve();let E=R.route.id,{hasLoader:_,hasClientLoader:P,hasShouldRevalidate:M}=r(R),C=!R.unstable_shouldRevalidateArgs||R.unstable_shouldRevalidateArgs.actionStatus==null||R.unstable_shouldRevalidateArgs.actionStatus<400;if(!R.unstable_shouldCallHandler(C)){i||(i=R.unstable_shouldRevalidateArgs!=null&&_&&M===!0);return}if(l(R)&&P){_&&(i=!0);try{let j=await S(async()=>{let{data:J}=await n(e,o,[E]);return it(J,E)});m[E]={type:"data",result:j}}catch(j){m[E]={type:"error",result:j}}return}_&&u.add(E);try{let j=await S(async()=>{let J=await f.promise;return it(J,E)});m[E]={type:"data",result:j}}catch(j){m[E]={type:"error",result:j}}})));if(await Promise.all(s.map(R=>R.promise)),(!t.state.initialized&&t.state.navigation.state==="idle"||u.size===0)&&!window.__reactRouterHdrActive)f.resolve({routes:{}});else{let R=a&&i&&u.size>0?[...u.keys()]:void 0;try{let b=await n(e,o,R);f.resolve(b.data)}catch(b){f.reject(b)}}return await p,await _i(f.promise,e.matches,u,m),m}async function _i(e,t,r,n){try{let a,o=await e;if("routes"in o){for(let l of t)if(l.route.id in o.routes){let u=o.routes[l.route.id];if("error"in u){a=u.error;break}}}a!==void 0&&Array.from(r.values()).forEach(l=>{n[l].result instanceof Hn&&(n[l].result=a)})}catch{}}async function Di(e,t,r){let n=e.matches.find(l=>l.unstable_shouldCallHandler());he(n,"No fetcher match found");let a=n.route.id,o=await n.resolve(async l=>l(async()=>{let{data:u}=await t(e,r,[a]);return it(u,a)}));return{[n.route.id]:o}}function Ai(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let n of t)n&&r.push(n);for(let n of r)e.searchParams.append("index",n);return e}function zn(e,t,r){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname=`_root.${r}`:t&&we(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.${r}`:n.pathname=`${n.pathname.replace(/\/$/,"")}.${r}`,n}async function Oi(e,t,r){let{request:n}=e,a=zn(n.url,t,"data");n.method==="GET"&&(a=Ai(a),r&&a.searchParams.set("_routes",r.join(",")));let o=await fetch(a,await Si(n));if(o.status===404&&!o.headers.has("X-Remix-Response"))throw new He(404,"Not Found",!0);if(o.status===204&&o.headers.has("X-Remix-Redirect"))return{status:xi,data:{redirect:{redirect:o.headers.get("X-Remix-Redirect"),status:Number(o.headers.get("X-Remix-Status")||"302"),revalidate:o.headers.get("X-Remix-Revalidate")==="true",reload:o.headers.get("X-Remix-Reload-Document")==="true",replace:o.headers.get("X-Remix-Replace")==="true"}}};if(Li.has(o.status)){let l={};return r&&n.method!=="GET"&&(l[r[0]]={data:void 0}),{status:o.status,data:{routes:l}}}he(o.body,"No response body to decode");try{let l=await Ni(o.body,window),u;if(n.method==="GET"){let i=l.value;or in i?u={redirect:i[or]}:u={routes:i}}else{let i=l.value,s=r?.[0];he(s,"No routeId found for single fetch call decoding"),"redirect"in i?u={redirect:i}:u={routes:{[s]:i}}}return{status:o.status,data:u}}catch{throw new Error("Unable to decode turbo-stream response")}}function Ni(e,t){return Ei(e,{plugins:[(r,...n)=>{if(r==="SanitizedError"){let[a,o,l]=n,u=Error;a&&a in t&&typeof t[a]=="function"&&(u=t[a]);let i=new u(o);return i.stack=l,{value:i}}if(r==="ErrorResponse"){let[a,o,l]=n;return{value:new He(o,l,a)}}if(r==="SingleFetchRedirect")return{value:{[or]:n[0]}};if(r==="SingleFetchClassInstance")return{value:n[0]};if(r==="SingleFetchFallback")return{value:void 0}}]})}function it(e,t){if("redirect"in e){let{redirect:n,revalidate:a,reload:o,replace:l,status:u}=e.redirect;throw Ka(n,{status:u,headers:{...a?{"X-Remix-Revalidate":"yes"}:null,...o?{"X-Remix-Reload-Document":"yes"}:null,...l?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if(r==null)throw new Hn(`No result found for routeId "${t}"`);if("error"in r)throw r.error;if("data"in r)return r.data;throw new Error(`Invalid response found for routeId "${t}"`)}function cn(){let e,t,r=new Promise((n,a)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{a(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}async function Bn(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ki(e,t,r){let n=e.map(o=>{let l=t[o.route.id],u=r.routes[o.route.id];return[u&&u.css?u.css.map(i=>({rel:"stylesheet",href:i})):[],l?.links?.()||[]]}).flat(2),a=Er(e,r);return Jn(n,a)}function Wn(e){return e.css?e.css.map(t=>({rel:"stylesheet",href:t})):[]}async function Ii(e){if(!e.css)return;let t=Wn(e);await Promise.all(t.map(Vn))}async function Yn(e,t){if(!e.css&&!t.links||!Hi())return;let r=[];if(e.css&&r.push(...Wn(e)),t.links&&r.push(...t.links()),r.length===0)return;let n=[];for(let a of r)!wr(a)&&a.rel==="stylesheet"&&n.push({...a,rel:"preload",as:"style"});await Promise.all(n.map(Vn))}async function Vn(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");Object.assign(r,e);function n(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{n(),t()},r.onerror=()=>{n(),t()},document.head.appendChild(r)})}function wr(e){return e!=null&&typeof e.page=="string"}function $i(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Fi(e,t,r){let n=await Promise.all(e.map(async a=>{let o=t.routes[a.route.id];if(o){let l=await Bn(o,r);return l.links?l.links():[]}return[]}));return Jn(n.flat(1).filter($i).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function dn(e,t,r,n,a,o){let l=(i,s)=>r[s]?i.route.id!==r[s].route.id:!0,u=(i,s)=>r[s].pathname!==i.pathname||r[s].route.path?.endsWith("*")&&r[s].params["*"]!==i.params["*"];return o==="assets"?t.filter((i,s)=>l(i,s)||u(i,s)):o==="data"?t.filter((i,s)=>{let f=n.routes[i.route.id];if(!f||!f.hasLoader)return!1;if(l(i,s)||u(i,s))return!0;if(i.route.shouldRevalidate){let m=i.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof m=="boolean")return m}return!0}):[]}function Er(e,t,{includeHydrateFallback:r}={}){return ji(e.map(n=>{let a=t.routes[n.route.id];if(!a)return[];let o=[a.module];return a.clientActionModule&&(o=o.concat(a.clientActionModule)),a.clientLoaderModule&&(o=o.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(o=o.concat(a.hydrateFallbackModule)),a.imports&&(o=o.concat(a.imports)),o}).flat(1))}function ji(e){return[...new Set(e)]}function Ui(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Jn(e,t){let r=new Set,n=new Set(t);return e.reduce((a,o)=>{if(t&&!wr(o)&&o.as==="script"&&o.href&&n.has(o.href))return a;let u=JSON.stringify(Ui(o));return r.has(u)||(r.add(u),a.push({key:u,link:o})),a},[])}var Et;function Hi(){if(Et!==void 0)return Et;let e=document.createElement("link");return Et=e.relList.supports("preload"),e=null,Et}function zi(){return d.createElement(ir,{title:"Loading...",renderScripts:!0},d.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback " +
                "for more information."
              );
            `}}))}function Gn(e){let t={};return Object.values(e).forEach(r=>{if(r){let n=r.parentId||"";t[n]||(t[n]=[]),t[n].push(r)}}),t}function Bi(e,t,r){let n=Xn(t),a=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?zi:void 0,o=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>d.createElement(Qn,{error:gr()}):void 0;return e.id==="root"&&t.Layout?{...n?{element:d.createElement(t.Layout,null,d.createElement(n,null))}:{Component:n},...o?{errorElement:d.createElement(t.Layout,null,d.createElement(o,null))}:{ErrorBoundary:o},...a?{hydrateFallbackElement:d.createElement(t.Layout,null,d.createElement(a,null))}:{HydrateFallback:a}}:{Component:n,ErrorBoundary:o,HydrateFallback:a}}function _l(e,t,r,n,a,o){return Rr(t,r,n,a,o,"",Gn(t),e)}function Rt(e,t){if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction){let n=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(n),new He(400,"Bad Request",new Error(n),!0)}}function Xt(e,t){let r=e==="clientAction"?"a":"an",n=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(n),new He(405,"Method Not Allowed",new Error(n),!0)}function Rr(e,t,r,n,a,o="",l=Gn(e),u){return(l[o]||[]).map(i=>{let s=t[i.id];function f(_){return he(typeof _=="function","No single fetch function available for route handler"),_()}function m(_){return i.hasLoader?f(_):Promise.resolve(null)}function p(_){if(!i.hasAction)throw Xt("action",i.id);return f(_)}function w(_){import(_)}function R(_){_.clientActionModule&&w(_.clientActionModule),_.clientLoaderModule&&w(_.clientLoaderModule)}async function b(_){let P=t[i.id],M=P?Yn(i,P):Promise.resolve();try{return _()}finally{await M}}let S={id:i.id,index:i.index,path:i.path};if(s){Object.assign(S,{...S,...Bi(i,s,a),unstable_middleware:s.unstable_clientMiddleware,handle:s.handle,shouldRevalidate:fn(S.path,s,i,n,u)});let _=r&&r.loaderData&&i.id in r.loaderData,P=_?r?.loaderData?.[i.id]:void 0,M=r&&r.errors&&i.id in r.errors,C=M?r?.errors?.[i.id]:void 0,y=u==null&&(s.clientLoader?.hydrate===!0||!i.hasLoader);S.loader=async({request:j,params:J,context:H},q)=>{try{return await b(async()=>(he(s,"No `routeModule` available for critical-route loader"),s.clientLoader?s.clientLoader({request:j,params:J,context:H,async serverLoader(){if(Rt("loader",i),y){if(_)return P;if(M)throw C}return m(q)}}):m(q)))}finally{y=!1}},S.loader.hydrate=Vi(i.id,s.clientLoader,i.hasLoader,a),S.action=({request:j,params:J,context:H},q)=>b(async()=>{if(he(s,"No `routeModule` available for critical-route action"),!s.clientAction){if(a)throw Xt("clientAction",i.id);return p(q)}return s.clientAction({request:j,params:J,context:H,async serverAction(){return Rt("action",i),p(q)}})})}else{i.hasClientLoader||(S.loader=(M,C)=>b(()=>m(C))),i.hasClientAction||(S.action=(M,C)=>b(()=>{if(a)throw Xt("clientAction",i.id);return p(C)}));let _;async function P(){return _?await _:(_=(async()=>{(i.clientLoaderModule||i.clientActionModule)&&await new Promise(C=>setTimeout(C,0));let M=Yi(i,t);return R(i),await M})(),await _)}S.lazy={loader:i.hasClientLoader?async()=>{let{clientLoader:M}=i.clientLoaderModule?await import(i.clientLoaderModule):await P();return he(M,"No `clientLoader` export found"),(C,y)=>M({...C,async serverLoader(){return Rt("loader",i),m(y)}})}:void 0,action:i.hasClientAction?async()=>{let M=i.clientActionModule?import(i.clientActionModule):P();R(i);let{clientAction:C}=await M;return he(C,"No `clientAction` export found"),(y,j)=>C({...y,async serverAction(){return Rt("action",i),p(j)}})}:void 0,unstable_middleware:i.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:M}=i.clientMiddlewareModule?await import(i.clientMiddlewareModule):await P();return he(M,"No `unstable_clientMiddleware` export found"),M}:void 0,shouldRevalidate:async()=>{let M=await P();return fn(S.path,M,i,n,u)},handle:async()=>(await P()).handle,Component:async()=>(await P()).Component,ErrorBoundary:i.hasErrorBoundary?async()=>(await P()).ErrorBoundary:void 0}}let E=Rr(e,t,r,n,a,i.id,l,u);return E.length>0&&(S.children=E),S})}function fn(e,t,r,n,a){if(a)return Wi(r.id,t.shouldRevalidate,a);if(!n&&r.hasLoader&&!r.hasClientLoader){let o=e?wn(e)[1].map(u=>u.paramName):[];const l=u=>o.some(i=>u.currentParams[i]!==u.nextParams[i]);if(t.shouldRevalidate){let u=t.shouldRevalidate;return i=>u({...i,defaultShouldRevalidate:l(i)})}else return u=>l(u)}if(n&&t.shouldRevalidate){let o=t.shouldRevalidate;return l=>o({...l,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function Wi(e,t,r){let n=!1;return a=>n?t?t(a):a.defaultShouldRevalidate:(n=!0,r.has(e))}async function Yi(e,t){let r=Bn(e,t),n=Ii(e),a=await r;return await Promise.all([n,Yn(e,a)]),{Component:Xn(a),ErrorBoundary:a.ErrorBoundary,unstable_clientMiddleware:a.unstable_clientMiddleware,clientAction:a.clientAction,clientLoader:a.clientLoader,handle:a.handle,links:a.links,meta:a.meta,shouldRevalidate:a.shouldRevalidate}}function Xn(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function Vi(e,t,r,n){return n&&e!=="root"||t!=null&&(t.hydrate===!0||r!==!0)}var Pt=new Set,Ji=1e3,Dt=new Set,Gi=7680;function br(e,t){return e.mode==="lazy"&&t===!0}function Xi({sri:e,...t},r){let n=new Set(r.state.matches.map(u=>u.route.id)),a=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(a.pop();a.length>0;)o.push(`/${a.join("/")}`),a.pop();o.forEach(u=>{let i=Te(r.routes,u,r.basename);i&&i.forEach(s=>n.add(s.route.id))});let l=[...n].reduce((u,i)=>Object.assign(u,{[i]:t.routes[i]}),{});return{...t,routes:l,sri:e?!0:void 0}}function Dl(e,t,r,n,a,o){if(br(n,r))return async({path:l,patch:u,signal:i,fetcherKey:s})=>{Dt.has(l)||await Kn([l],s?window.location.href:l,e,t,r,a,o,n.manifestPath,u,i)}}function Al(e,t,r,n,a,o){d.useEffect(()=>{if(!br(a,n)||window.navigator?.connection?.saveData===!0)return;function l(f){let m=f.tagName==="FORM"?f.getAttribute("action"):f.getAttribute("href");if(!m)return;let p=f.tagName==="A"?f.pathname:new URL(m,window.location.origin).pathname;Dt.has(p)||Pt.add(p)}async function u(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let f=Array.from(Pt.keys()).filter(m=>Dt.has(m)?(Pt.delete(m),!1):!0);if(f.length!==0)try{await Kn(f,null,t,r,n,o,e.basename,a.manifestPath,e.patchRoutes)}catch(m){console.error("Failed to fetch manifest patches",m)}}let i=Qi(u,100);u();let s=new MutationObserver(()=>i());return s.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>s.disconnect()},[n,o,t,r,e,a])}function Ki(e,t){let r=e||"/__manifest";return t==null?r:`${t}${r}`.replace(/\/+/g,"/")}var Kt="react-router-manifest-version";async function Kn(e,t,r,n,a,o,l,u,i,s){let f=new URL(Ki(u,l),window.location.origin);if(e.sort().forEach(b=>f.searchParams.append("p",b)),f.searchParams.set("version",r.version),f.toString().length>Gi){Pt.clear();return}let m;try{let b=await fetch(f,{signal:s});if(b.ok){if(b.status===204&&b.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(Kt)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}sessionStorage.setItem(Kt,r.version),window.location.href=t,console.warn("Detected manifest version mismatch, reloading..."),await new Promise(()=>{})}else if(b.status>=400)throw new Error(await b.text())}else throw new Error(`${b.status} ${b.statusText}`);sessionStorage.removeItem(Kt),m=await b.json()}catch(b){if(s?.aborted)return;throw b}let p=new Set(Object.keys(r.routes)),w=Object.values(m).reduce((b,S)=>(S&&!p.has(S.id)&&(b[S.id]=S),b),{});Object.assign(r.routes,w),e.forEach(b=>qi(b,Dt));let R=new Set;Object.values(w).forEach(b=>{b&&(!b.parentId||!w[b.parentId])&&R.add(b.parentId)}),R.forEach(b=>i(b||null,Rr(w,n,null,a,o,b)))}function qi(e,t){if(t.size>=Ji){let r=t.values().next().value;t.delete(r)}t.add(e)}function Qi(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}function Sr(){let e=d.useContext(Be);return he(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Nt(){let e=d.useContext(Je);return he(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var kt=d.createContext(void 0);kt.displayName="FrameworkContext";function Ge(){let e=d.useContext(kt);return he(e,"You must render this element inside a <HydratedRouter> element"),e}function Zi(e,t){let r=d.useContext(kt),[n,a]=d.useState(!1),[o,l]=d.useState(!1),{onFocus:u,onBlur:i,onMouseEnter:s,onMouseLeave:f,onTouchStart:m}=t,p=d.useRef(null);d.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let b=E=>{E.forEach(_=>{l(_.isIntersecting)})},S=new IntersectionObserver(b,{threshold:.5});return p.current&&S.observe(p.current),()=>{S.disconnect()}}},[e]),d.useEffect(()=>{if(n){let b=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(b)}}},[n]);let w=()=>{a(!0)},R=()=>{a(!1),l(!1)};return r?e!=="intent"?[o,p,{}]:[o,p,{onFocus:rt(u,w),onBlur:rt(i,R),onMouseEnter:rt(s,w),onMouseLeave:rt(f,R),onTouchStart:rt(m,w)}]:[!1,p,{}]}function rt(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function xr(e,t,r){if(r&&!Tt)return[e[0]];if(t){let n=e.findIndex(a=>t[a.route.id]!==void 0);return e.slice(0,n+1)}return e}var hn="data-react-router-critical-css";function Ol(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:n}=Ge(),{errors:a,matches:o}=Nt(),l=xr(o,a,e),u=d.useMemo(()=>ki(l,r,t),[l,r,t]);return d.createElement(d.Fragment,null,typeof n=="string"?d.createElement("style",{[hn]:"",dangerouslySetInnerHTML:{__html:n}}):null,typeof n=="object"?d.createElement("link",{[hn]:"",rel:"stylesheet",href:n.href}):null,u.map(({key:i,link:s})=>wr(s)?d.createElement(qn,{key:i,...s}):d.createElement("link",{key:i,...s})))}function qn({page:e,...t}){let{router:r}=Sr(),n=d.useMemo(()=>Te(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?d.createElement(tl,{page:e,matches:n,...t}):null}function el(e){let{manifest:t,routeModules:r}=Ge(),[n,a]=d.useState([]);return d.useEffect(()=>{let o=!1;return Fi(e,t,r).then(l=>{o||a(l)}),()=>{o=!0}},[e,t,r]),n}function tl({page:e,matches:t,...r}){let n=Se(),{manifest:a,routeModules:o}=Ge(),{basename:l}=Sr(),{loaderData:u,matches:i}=Nt(),s=d.useMemo(()=>dn(e,t,i,a,n,"data"),[e,t,i,a,n]),f=d.useMemo(()=>dn(e,t,i,a,n,"assets"),[e,t,i,a,n]),m=d.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let R=new Set,b=!1;if(t.forEach(E=>{let _=a.routes[E.route.id];!_||!_.hasLoader||(!s.some(P=>P.route.id===E.route.id)&&E.route.id in u&&o[E.route.id]?.shouldRevalidate||_.hasClientLoader?b=!0:R.add(E.route.id))}),R.size===0)return[];let S=zn(e,l,"data");return b&&R.size>0&&S.searchParams.set("_routes",t.filter(E=>R.has(E.route.id)).map(E=>E.route.id).join(",")),[S.pathname+S.search]},[l,u,n,a,s,t,e,o]),p=d.useMemo(()=>Er(f,a),[f,a]),w=el(f);return d.createElement(d.Fragment,null,m.map(R=>d.createElement("link",{key:R,rel:"prefetch",as:"fetch",href:R,...r})),p.map(R=>d.createElement("link",{key:R,rel:"modulepreload",href:R,...r})),w.map(({key:R,link:b})=>d.createElement("link",{key:R,...b})))}function Nl(){let{isSpaMode:e,routeModules:t}=Ge(),{errors:r,matches:n,loaderData:a}=Nt(),o=Se(),l=xr(n,r,e),u=null;r&&(u=r[l[l.length-1].route.id]);let i=[],s=null,f=[];for(let m=0;m<l.length;m++){let p=l[m],w=p.route.id,R=a[w],b=p.params,S=t[w],E=[],_={id:w,data:R,meta:[],params:p.params,pathname:p.pathname,handle:p.route.handle,error:u};if(f[m]=_,S?.meta?E=typeof S.meta=="function"?S.meta({data:R,params:b,location:o,matches:f,error:u}):Array.isArray(S.meta)?[...S.meta]:S.meta:s&&(E=[...s]),E=E||[],!Array.isArray(E))throw new Error("The route at "+p.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);_.meta=E,f[m]=_,i=[...E],s=i}return d.createElement(d.Fragment,null,i.flat().map(m=>{if(!m)return null;if("tagName"in m){let{tagName:p,...w}=m;if(!rl(p))return console.warn(`A meta object uses an invalid tagName: ${p}. Expected either 'link' or 'meta'`),null;let R=p;return d.createElement(R,{key:JSON.stringify(w),...w})}if("title"in m)return d.createElement("title",{key:"title"},String(m.title));if("charset"in m&&(m.charSet??(m.charSet=m.charset),delete m.charset),"charSet"in m&&m.charSet!=null)return typeof m.charSet=="string"?d.createElement("meta",{key:"charSet",charSet:m.charSet}):null;if("script:ld+json"in m)try{let p=JSON.stringify(m["script:ld+json"]);return d.createElement("script",{key:`script:ld+json:${p}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:p}})}catch{return null}return d.createElement("meta",{key:JSON.stringify(m),...m})}))}function rl(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}var Tt=!1;function nl(e){let{manifest:t,serverHandoffString:r,isSpaMode:n,renderMeta:a,routeDiscovery:o,ssr:l}=Ge(),{router:u,static:i,staticContext:s}=Sr(),{matches:f}=Nt(),m=Lo(),p=br(o,l);a&&(a.didRenderScripts=!0);let w=xr(f,null,n);d.useEffect(()=>{Tt=!0},[]);let R=d.useMemo(()=>{if(m)return null;let _=s?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",P=i?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${p?"":`import ${JSON.stringify(t.url)}`};
${w.map((M,C)=>{let y=`route${C}`,j=t.routes[M.route.id];he(j,`Route ${M.route.id} not found in manifest`);let{clientActionModule:J,clientLoaderModule:H,clientMiddlewareModule:q,hydrateFallbackModule:le,module:me}=j,X=[...J?[{module:J,varName:`${y}_clientAction`}]:[],...H?[{module:H,varName:`${y}_clientLoader`}]:[],...q?[{module:q,varName:`${y}_clientMiddleware`}]:[],...le?[{module:le,varName:`${y}_HydrateFallback`}]:[],{module:me,varName:`${y}_main`}];if(X.length===1)return`import * as ${y} from ${JSON.stringify(me)};`;let Q=X.map(Y=>`import * as ${Y.varName} from "${Y.module}";`).join(`
`),ae=`const ${y} = {${X.map(Y=>`...${Y.varName}`).join(",")}};`;return[Q,ae].join(`
`)}).join(`
`)}
  ${p?`window.__reactRouterManifest = ${JSON.stringify(Xi(t,u),null,2)};`:""}
  window.__reactRouterRouteModules = {${w.map((M,C)=>`${JSON.stringify(M.route.id)}:route${C}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return d.createElement(d.Fragment,null,d.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:un(_),type:void 0}),d.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:un(P),type:"module",async:!0}))},[]),b=Tt||m?[]:al(t.entry.imports.concat(Er(w,t,{includeHydrateFallback:!0}))),S=typeof t.sri=="object"?t.sri:{};return rr(!m,"The <Scripts /> element is a no-op when using RSC and can be safely removed."),Tt||m?null:d.createElement(d.Fragment,null,typeof t.sri=="object"?d.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:S})}}):null,p?null:d.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:S[t.url],suppressHydrationWarning:!0}),d.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:S[t.entry.module],suppressHydrationWarning:!0}),b.map(E=>d.createElement("link",{key:E,rel:"modulepreload",href:E,crossOrigin:e.crossOrigin,integrity:S[E],suppressHydrationWarning:!0})),R)}function al(e){return[...new Set(e)]}function ol(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var kl=class extends d.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?d.createElement(Qn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function Qn({error:e,isOutsideRemixApp:t}){console.error(e);let r=d.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."
        );
      `}});if(ze(e))return d.createElement(ir,{title:"Unhandled Thrown Response!"},d.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let n;if(e instanceof Error)n=e;else{let a=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);n=new Error(a)}return d.createElement(ir,{title:"Application Error!",isOutsideRemixApp:t},d.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),d.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},n.stack),r)}function ir({title:e,renderScripts:t,isOutsideRemixApp:r,children:n}){let{routeModules:a}=Ge();return a.root?.Layout&&!r?n:d.createElement("html",{lang:"en"},d.createElement("head",null,d.createElement("meta",{charSet:"utf-8"}),d.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),d.createElement("title",null,e)),d.createElement("body",null,d.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,t?d.createElement(nl,null):null)))}var Zn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Zn&&(window.__reactRouterVersion="7.7.1")}catch{}var ea=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ta=d.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:a,reloadDocument:o,replace:l,state:u,target:i,to:s,preventScrollReset:f,viewTransition:m,...p},w){let{basename:R}=d.useContext(Re),b=typeof s=="string"&&ea.test(s),S,E=!1;if(typeof s=="string"&&b&&(S=s,Zn))try{let H=new URL(window.location.href),q=s.startsWith("//")?new URL(H.protocol+s):new URL(s),le=we(q.pathname,R);q.origin===H.origin&&le!=null?s=le+q.search+q.hash:E=!0}catch{te(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let _=Po(s,{relative:a}),[P,M,C]=Zi(n,p),y=cl(s,{replace:l,state:u,target:i,preventScrollReset:f,relative:a,viewTransition:m});function j(H){t&&t(H),H.defaultPrevented||y(H)}let J=d.createElement("a",{...p,...C,href:S||_,onClick:E||o?t:j,ref:ol(w,M),target:i,"data-discover":!b&&r==="render"?"true":void 0});return P&&!b?d.createElement(d.Fragment,null,J,d.createElement(qn,{page:_})):J});ta.displayName="Link";var il=d.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:a=!1,style:o,to:l,viewTransition:u,children:i,...s},f){let m=st(l,{relative:s.relative}),p=Se(),w=d.useContext(Je),{navigator:R,basename:b}=d.useContext(Re),S=w!=null&&vl(m)&&u===!0,E=R.encodeLocation?R.encodeLocation(m).pathname:m.pathname,_=p.pathname,P=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;r||(_=_.toLowerCase(),P=P?P.toLowerCase():null,E=E.toLowerCase()),P&&b&&(P=we(P,b)||P);const M=E!=="/"&&E.endsWith("/")?E.length-1:E.length;let C=_===E||!a&&_.startsWith(E)&&_.charAt(M)==="/",y=P!=null&&(P===E||!a&&P.startsWith(E)&&P.charAt(E.length)==="/"),j={isActive:C,isPending:y,isTransitioning:S},J=C?t:void 0,H;typeof n=="function"?H=n(j):H=[n,C?"active":null,y?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let q=typeof o=="function"?o(j):o;return d.createElement(ta,{...s,"aria-current":J,className:H,ref:f,style:q,to:l,viewTransition:u},typeof i=="function"?i(j):i)});il.displayName="NavLink";var ll=d.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:a,state:o,method:l=Lt,action:u,onSubmit:i,relative:s,preventScrollReset:f,viewTransition:m,...p},w)=>{let R=hl(),b=ml(u,{relative:s}),S=l.toLowerCase()==="get"?"get":"post",E=typeof u=="string"&&ea.test(u),_=P=>{if(i&&i(P),P.defaultPrevented)return;P.preventDefault();let M=P.nativeEvent.submitter,C=M?.getAttribute("formmethod")||l;R(M||P.currentTarget,{fetcherKey:t,method:C,navigate:r,replace:a,state:o,relative:s,preventScrollReset:f,viewTransition:m})};return d.createElement("form",{ref:w,method:S,action:b,onSubmit:n?i:_,...p,"data-discover":!E&&e==="render"?"true":void 0})});ll.displayName="Form";function sl({getKey:e,storageKey:t,...r}){let n=d.useContext(kt),{basename:a}=d.useContext(Re),o=Se(),l=vr();pl({getKey:e,storageKey:t});let u=d.useMemo(()=>{if(!n||!e)return null;let s=sr(o,l,a,e);return s!==o.key?s:null},[]);if(!n||n.isSpaMode)return null;let i=((s,f)=>{if(!window.history.state||!window.history.state.key){let m=Math.random().toString(32).slice(2);window.history.replaceState({key:m},"")}try{let p=JSON.parse(sessionStorage.getItem(s)||"{}")[f||window.history.state.key];typeof p=="number"&&window.scrollTo(0,p)}catch(m){console.error(m),sessionStorage.removeItem(s)}}).toString();return d.createElement("script",{...r,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${i})(${JSON.stringify(t||lr)}, ${JSON.stringify(u)})`}})}sl.displayName="ScrollRestoration";function ra(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Lr(e){let t=d.useContext(Be);return V(t,ra(e)),t}function ul(e){let t=d.useContext(Je);return V(t,ra(e)),t}function cl(e,{target:t,replace:r,state:n,preventScrollReset:a,relative:o,viewTransition:l}={}){let u=On(),i=Se(),s=st(e,{relative:o});return d.useCallback(f=>{if(Qo(f,t)){f.preventDefault();let m=r!==void 0?r:Oe(i)===Oe(s);u(e,{replace:m,state:n,preventScrollReset:a,relative:o,viewTransition:l})}},[i,u,s,r,n,t,e,a,o,l])}function Il(e){te(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=d.useRef(nr(e)),r=d.useRef(!1),n=Se(),a=d.useMemo(()=>Zo(n.search,r.current?null:t.current),[n.search]),o=On(),l=d.useCallback((u,i)=>{const s=nr(typeof u=="function"?u(new URLSearchParams(a)):u);r.current=!0,o("?"+s,i)},[o,a]);return[a,l]}var dl=0,fl=()=>`__${String(++dl)}__`;function hl(){let{router:e}=Lr("useSubmit"),{basename:t}=d.useContext(Re),r=jo();return d.useCallback(async(n,a={})=>{let{action:o,method:l,encType:u,formData:i,body:s}=ri(n,t);if(a.navigate===!1){let f=a.fetcherKey||fl();await e.fetch(f,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:s,formMethod:a.method||l,formEncType:a.encType||u,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:s,formMethod:a.method||l,formEncType:a.encType||u,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function ml(e,{relative:t}={}){let{basename:r}=d.useContext(Re),n=d.useContext(be);V(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),o={...st(e||".",{relative:t})},l=Se();if(e==null){o.search=l.search;let u=new URLSearchParams(o.search),i=u.getAll("index");if(i.some(f=>f==="")){u.delete("index"),i.filter(m=>m).forEach(m=>u.append("index",m));let f=u.toString();o.search=f?`?${f}`:""}}return(!e||e===".")&&a.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:Ce([r,o.pathname])),Oe(o)}var lr="react-router-scroll-positions",bt={};function sr(e,t,r,n){let a=null;return n&&(r!=="/"?a=n({...e,pathname:we(e.pathname,r)||e.pathname},t):a=n(e,t)),a==null&&(a=e.key),a}function pl({getKey:e,storageKey:t}={}){let{router:r}=Lr("useScrollRestoration"),{restoreScrollPosition:n,preventScrollReset:a}=ul("useScrollRestoration"),{basename:o}=d.useContext(Re),l=Se(),u=vr(),i=Uo();d.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),yl(d.useCallback(()=>{if(i.state==="idle"){let s=sr(l,u,o,e);bt[s]=window.scrollY}try{sessionStorage.setItem(t||lr,JSON.stringify(bt))}catch(s){te(!1,`Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${s}).`)}window.history.scrollRestoration="auto"},[i.state,e,o,l,u,t])),typeof document<"u"&&(d.useLayoutEffect(()=>{try{let s=sessionStorage.getItem(t||lr);s&&(bt=JSON.parse(s))}catch{}},[t]),d.useLayoutEffect(()=>{let s=r?.enableScrollRestoration(bt,()=>window.scrollY,e?(f,m)=>sr(f,m,o,e):void 0);return()=>s&&s()},[r,o,e]),d.useLayoutEffect(()=>{if(n!==!1){if(typeof n=="number"){window.scrollTo(0,n);return}try{if(l.hash){let s=document.getElementById(decodeURIComponent(l.hash.slice(1)));if(s){s.scrollIntoView();return}}}catch{te(!1,`"${l.hash.slice(1)}" is not a decodable element ID. The view will not scroll to it.`)}a!==!0&&window.scrollTo(0,0)}},[l,n,a]))}function yl(e,t){let{capture:r}={};d.useEffect(()=>{let n=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,n),()=>{window.removeEventListener("pagehide",e,n)}},[e,r])}function vl(e,{relative:t}={}){let r=d.useContext(mr);V(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Lr("useViewTransitionState"),a=st(e,{relative:t});if(!r.isTransitioning)return!1;let o=we(r.currentLocation.pathname,n)||r.currentLocation.pathname,l=we(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Mt(a.pathname,l)!=null||Mt(a.pathname,o)!=null}export{Rl as A,_l as B,Sl as C,xl as D,He as E,kt as F,Uo as G,Tl as H,gr as I,Ol as L,Nl as M,il as N,Cl as O,El as R,sl as S,Ka as a,On as b,Il as c,Se as d,nl as e,gl as f,va as g,Ra as h,ta as i,wl as j,Li as k,ze as l,Te as m,V as n,Al as o,kl as p,Ll as q,d as r,Vi as s,Ni as t,Nn as u,Rr as v,Pl as w,bl as x,Dl as y,Ml as z};
