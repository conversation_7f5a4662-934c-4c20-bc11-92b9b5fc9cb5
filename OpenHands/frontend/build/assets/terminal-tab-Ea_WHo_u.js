const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/terminal-D2BKm852.js","assets/chunk-C37GKA54-CBbYr_fP.js","assets/react-redux-B5osdedR.js","assets/agent-state-CFaY3go2.js","assets/ws-client-provider-Dmsj8lkD.js","assets/open-hands-axios-CtirLpss.js","assets/module-5laXsVNO.js","assets/custom-toast-handlers-CR9P-jKI.js","assets/index-cxP66Ws3.js","assets/store-Bya9Reqe.js","assets/browser-slice-DabBaamq.js","assets/query-client-config-CJn-5u6A.js","assets/i18next-CO45VQzB.js","assets/declaration-xyc84-tJ.js","assets/retrieve-axios-error-message-CYr77e_f.js","assets/open-hands-Ce72Fmtl.js","assets/mutation-B9dSlWD-.js","assets/use-user-providers-CVWOd-tS.js","assets/use-settings-CSlhfPqo.js","assets/useQuery-Cu2nkJ8V.js","assets/use-config-jdwF3W4-.js","assets/use-active-conversation-B8Aw3kE2.js","assets/use-conversation-id-0JHAicdF.js","assets/use-optimistic-user-message-tdysaQ5t.js","assets/useTranslation-BG59QWH_.js","assets/i18nInstance-DBIXdvxg.js","assets/terminal-DhzMnp0v.css"])))=>i.map(i=>d[i]);
import{_ as l}from"./preload-helper-BXl3LOEh.js";import{w as o,R as s,j as e}from"./chunk-C37GKA54-CBbYr_fP.js";function r(){const a=s.useMemo(()=>s.lazy(()=>l(()=>import("./terminal-D2BKm852.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]))),[]);return e.jsx("div",{className:"h-full flex flex-col",children:e.jsx("div",{className:"flex-grow overflow-auto",children:e.jsx(s.Suspense,{fallback:e.jsx("div",{className:"h-full"}),children:e.jsx(a,{})})})})}const n=o(r);export{n as default};
