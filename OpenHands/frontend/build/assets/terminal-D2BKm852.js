import{R as ne,j as pe}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as Se}from"./react-redux-B5osdedR.js";import{R as ye}from"./agent-state-CFaY3go2.js";import{A as Le,u as De}from"./ws-client-provider-Dmsj8lkD.js";import{u as Re}from"./useTranslation-BG59QWH_.js";import"./open-hands-axios-CtirLpss.js";import"./module-5laXsVNO.js";import"./custom-toast-handlers-CR9P-jKI.js";import"./index-cxP66Ws3.js";import"./store-Bya9Reqe.js";import"./browser-slice-DabBaamq.js";import"./query-client-config-CJn-5u6A.js";import"./i18next-CO45VQzB.js";import"./declaration-xyc84-tJ.js";import"./retrieve-axios-error-message-CYr77e_f.js";import"./open-hands-Ce72Fmtl.js";import"./mutation-B9dSlWD-.js";import"./use-user-providers-CVWOd-tS.js";import"./use-settings-CSlhfPqo.js";import"./useQuery-Cu2nkJ8V.js";import"./use-config-jdwF3W4-.js";import"./use-active-conversation-B8Aw3kE2.js";import"./use-conversation-id-0JHAicdF.js";import"./use-optimistic-user-message-tdysaQ5t.js";import"./i18nInstance-DBIXdvxg.js";var ge={exports:{}},Ce;function xe(){return Ce||(Ce=1,function(V,te){(function(Z,z){V.exports=z()})(self,()=>(()=>{var Z={};return(()=>{var z=Z;Object.defineProperty(z,"__esModule",{value:!0}),z.FitAddon=void 0,z.FitAddon=class{activate(q){this._terminal=q}dispose(){}fit(){const q=this.proposeDimensions();if(!q||!this._terminal||isNaN(q.cols)||isNaN(q.rows))return;const Y=this._terminal._core;this._terminal.rows===q.rows&&this._terminal.cols===q.cols||(Y._renderService.clear(),this._terminal.resize(q.cols,q.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const q=this._terminal._core,Y=q._renderService.dimensions;if(Y.css.cell.width===0||Y.css.cell.height===0)return;const B=this._terminal.options.scrollback===0?0:q.viewport.scrollBarWidth,r=window.getComputedStyle(this._terminal.element.parentElement),o=parseInt(r.getPropertyValue("height")),l=Math.max(0,parseInt(r.getPropertyValue("width"))),u=window.getComputedStyle(this._terminal.element),n=o-(parseInt(u.getPropertyValue("padding-top"))+parseInt(u.getPropertyValue("padding-bottom"))),d=l-(parseInt(u.getPropertyValue("padding-right"))+parseInt(u.getPropertyValue("padding-left")))-B;return{cols:Math.max(2,Math.floor(d/Y.css.cell.width)),rows:Math.max(1,Math.floor(n/Y.css.cell.height))}}}})(),Z})())}(ge)),ge.exports}var Ae=xe(),me={exports:{}},be;function Be(){return be||(be=1,function(V,te){(function(Z,z){V.exports=z()})(globalThis,()=>(()=>{var Z={4567:function(B,r,o){var l=this&&this.__decorate||function(e,i,a,v){var _,g=arguments.length,c=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,a):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")c=Reflect.decorate(e,i,a,v);else for(var m=e.length-1;m>=0;m--)(_=e[m])&&(c=(g<3?_(c):g>3?_(i,a,c):_(i,a))||c);return g>3&&c&&Object.defineProperty(i,a,c),c},u=this&&this.__param||function(e,i){return function(a,v){i(a,v,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.AccessibilityManager=void 0;const n=o(9042),d=o(9924),f=o(844),p=o(4725),h=o(2585),t=o(3656);let s=r.AccessibilityManager=class extends f.Disposable{constructor(e,i,a,v){super(),this._terminal=e,this._coreBrowserService=a,this._renderService=v,this._rowColumns=new WeakMap,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=this._coreBrowserService.mainDocument.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=this._coreBrowserService.mainDocument.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let _=0;_<this._terminal.rows;_++)this._rowElements[_]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[_]);if(this._topBoundaryFocusListener=_=>this._handleBoundaryFocus(_,0),this._bottomBoundaryFocusListener=_=>this._handleBoundaryFocus(_,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=this._coreBrowserService.mainDocument.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new d.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(_=>this._handleResize(_.rows))),this.register(this._terminal.onRender(_=>this._refreshRows(_.start,_.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(_=>this._handleChar(_))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(_=>this._handleTab(_))),this.register(this._terminal.onKey(_=>this._handleKey(_.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this.register((0,t.addDisposableDomListener)(document,"selectionchange",()=>this._handleSelectionChange())),this.register(this._coreBrowserService.onDprChange(()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,f.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(e){for(let i=0;i<e;i++)this._handleChar(" ")}_handleChar(e){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==e&&(this._charsToAnnounce+=e):this._charsToAnnounce+=e,e===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=n.tooMuchOutput)))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0}_handleKey(e){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(e)||this._charsToConsume.push(e)}_refreshRows(e,i){this._liveRegionDebouncer.refresh(e,i,this._terminal.rows)}_renderRows(e,i){const a=this._terminal.buffer,v=a.lines.length.toString();for(let _=e;_<=i;_++){const g=a.lines.get(a.ydisp+_),c=[],m=g?.translateToString(!0,void 0,void 0,c)||"",E=(a.ydisp+_+1).toString(),k=this._rowElements[_];k&&(m.length===0?(k.innerText=" ",this._rowColumns.set(k,[0,1])):(k.textContent=m,this._rowColumns.set(k,c)),k.setAttribute("aria-posinset",E),k.setAttribute("aria-setsize",v))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(e,i){const a=e.target,v=this._rowElements[i===0?1:this._rowElements.length-2];if(a.getAttribute("aria-posinset")===(i===0?"1":`${this._terminal.buffer.lines.length}`)||e.relatedTarget!==v)return;let _,g;if(i===0?(_=a,g=this._rowElements.pop(),this._rowContainer.removeChild(g)):(_=this._rowElements.shift(),g=a,this._rowContainer.removeChild(_)),_.removeEventListener("focus",this._topBoundaryFocusListener),g.removeEventListener("focus",this._bottomBoundaryFocusListener),i===0){const c=this._createAccessibilityTreeNode();this._rowElements.unshift(c),this._rowContainer.insertAdjacentElement("afterbegin",c)}else{const c=this._createAccessibilityTreeNode();this._rowElements.push(c),this._rowContainer.appendChild(c)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(i===0?-1:1),this._rowElements[i===0?1:this._rowElements.length-2].focus(),e.preventDefault(),e.stopImmediatePropagation()}_handleSelectionChange(){if(this._rowElements.length===0)return;const e=document.getSelection();if(!e)return;if(e.isCollapsed)return void(this._rowContainer.contains(e.anchorNode)&&this._terminal.clearSelection());if(!e.anchorNode||!e.focusNode)return void console.error("anchorNode and/or focusNode are null");let i={node:e.anchorNode,offset:e.anchorOffset},a={node:e.focusNode,offset:e.focusOffset};if((i.node.compareDocumentPosition(a.node)&Node.DOCUMENT_POSITION_PRECEDING||i.node===a.node&&i.offset>a.offset)&&([i,a]=[a,i]),i.node.compareDocumentPosition(this._rowElements[0])&(Node.DOCUMENT_POSITION_CONTAINED_BY|Node.DOCUMENT_POSITION_FOLLOWING)&&(i={node:this._rowElements[0].childNodes[0],offset:0}),!this._rowContainer.contains(i.node))return;const v=this._rowElements.slice(-1)[0];if(a.node.compareDocumentPosition(v)&(Node.DOCUMENT_POSITION_CONTAINED_BY|Node.DOCUMENT_POSITION_PRECEDING)&&(a={node:v,offset:v.textContent?.length??0}),!this._rowContainer.contains(a.node))return;const _=({node:m,offset:E})=>{const k=m instanceof Text?m.parentNode:m;let D=parseInt(k?.getAttribute("aria-posinset"),10)-1;if(isNaN(D))return console.warn("row is invalid. Race condition?"),null;const b=this._rowColumns.get(k);if(!b)return console.warn("columns is null. Race condition?"),null;let x=E<b.length?b[E]:b.slice(-1)[0]+1;return x>=this._terminal.cols&&(++D,x=0),{row:D,column:x}},g=_(i),c=_(a);if(g&&c){if(g.row>c.row||g.row===c.row&&g.column>=c.column)throw new Error("invalid range");this._terminal.select(g.column,g.row,(c.row-g.row)*this._terminal.cols-g.column+c.column)}}_handleResize(e){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let i=this._rowContainer.children.length;i<this._terminal.rows;i++)this._rowElements[i]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[i]);for(;this._rowElements.length>e;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const e=this._coreBrowserService.mainDocument.createElement("div");return e.setAttribute("role","listitem"),e.tabIndex=-1,this._refreshRowDimensions(e),e}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let e=0;e<this._terminal.rows;e++)this._refreshRowDimensions(this._rowElements[e])}}_refreshRowDimensions(e){e.style.height=`${this._renderService.dimensions.css.cell.height}px`}};r.AccessibilityManager=s=l([u(1,h.IInstantiationService),u(2,p.ICoreBrowserService),u(3,p.IRenderService)],s)},3614:(B,r)=>{function o(d){return d.replace(/\r?\n/g,"\r")}function l(d,f){return f?"\x1B[200~"+d+"\x1B[201~":d}function u(d,f,p,h){d=l(d=o(d),p.decPrivateModes.bracketedPasteMode&&h.rawOptions.ignoreBracketedPasteMode!==!0),p.triggerDataEvent(d,!0),f.value=""}function n(d,f,p){const h=p.getBoundingClientRect(),t=d.clientX-h.left-10,s=d.clientY-h.top-10;f.style.width="20px",f.style.height="20px",f.style.left=`${t}px`,f.style.top=`${s}px`,f.style.zIndex="1000",f.focus()}Object.defineProperty(r,"__esModule",{value:!0}),r.rightClickHandler=r.moveTextAreaUnderMouseCursor=r.paste=r.handlePasteEvent=r.copyHandler=r.bracketTextForPaste=r.prepareTextForTerminal=void 0,r.prepareTextForTerminal=o,r.bracketTextForPaste=l,r.copyHandler=function(d,f){d.clipboardData&&d.clipboardData.setData("text/plain",f.selectionText),d.preventDefault()},r.handlePasteEvent=function(d,f,p,h){d.stopPropagation(),d.clipboardData&&u(d.clipboardData.getData("text/plain"),f,p,h)},r.paste=u,r.moveTextAreaUnderMouseCursor=n,r.rightClickHandler=function(d,f,p,h,t){n(d,f,p),t&&h.rightClickSelect(d),f.value=h.selectionText,f.select()}},7239:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ColorContrastCache=void 0;const l=o(1505);r.ColorContrastCache=class{constructor(){this._color=new l.TwoKeyMap,this._css=new l.TwoKeyMap}setCss(u,n,d){this._css.set(u,n,d)}getCss(u,n){return this._css.get(u,n)}setColor(u,n,d){this._color.set(u,n,d)}getColor(u,n){return this._color.get(u,n)}clear(){this._color.clear(),this._css.clear()}}},3656:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.addDisposableDomListener=void 0,r.addDisposableDomListener=function(o,l,u,n){o.addEventListener(l,u,n);let d=!1;return{dispose:()=>{d||(d=!0,o.removeEventListener(l,u,n))}}}},3551:function(B,r,o){var l=this&&this.__decorate||function(s,e,i,a){var v,_=arguments.length,g=_<3?e:a===null?a=Object.getOwnPropertyDescriptor(e,i):a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(s,e,i,a);else for(var c=s.length-1;c>=0;c--)(v=s[c])&&(g=(_<3?v(g):_>3?v(e,i,g):v(e,i))||g);return _>3&&g&&Object.defineProperty(e,i,g),g},u=this&&this.__param||function(s,e){return function(i,a){e(i,a,s)}};Object.defineProperty(r,"__esModule",{value:!0}),r.Linkifier=void 0;const n=o(3656),d=o(8460),f=o(844),p=o(2585),h=o(4725);let t=r.Linkifier=class extends f.Disposable{get currentLink(){return this._currentLink}constructor(s,e,i,a,v){super(),this._element=s,this._mouseService=e,this._renderService=i,this._bufferService=a,this._linkProviderService=v,this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new d.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new d.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,f.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,f.toDisposable)(()=>{this._lastMouseEvent=void 0,this._activeProviderReplies?.clear()})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0})),this.register((0,n.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,n.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,n.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,n.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(s){this._lastMouseEvent=s;const e=this._positionFromMouseEvent(s,this._element,this._mouseService);if(!e)return;this._isMouseOut=!1;const i=s.composedPath();for(let a=0;a<i.length;a++){const v=i[a];if(v.classList.contains("xterm"))break;if(v.classList.contains("xterm-hover"))return}this._lastBufferCell&&e.x===this._lastBufferCell.x&&e.y===this._lastBufferCell.y||(this._handleHover(e),this._lastBufferCell=e)}_handleHover(s){if(this._activeLine!==s.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(s,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,s)||(this._clearCurrentLink(),this._askForLink(s,!0))}_askForLink(s,e){this._activeProviderReplies&&e||(this._activeProviderReplies?.forEach(a=>{a?.forEach(v=>{v.link.dispose&&v.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=s.y);let i=!1;for(const[a,v]of this._linkProviderService.linkProviders.entries())e?this._activeProviderReplies?.get(a)&&(i=this._checkLinkProviderResult(a,s,i)):v.provideLinks(s.y,_=>{if(this._isMouseOut)return;const g=_?.map(c=>({link:c}));this._activeProviderReplies?.set(a,g),i=this._checkLinkProviderResult(a,s,i),this._activeProviderReplies?.size===this._linkProviderService.linkProviders.length&&this._removeIntersectingLinks(s.y,this._activeProviderReplies)})}_removeIntersectingLinks(s,e){const i=new Set;for(let a=0;a<e.size;a++){const v=e.get(a);if(v)for(let _=0;_<v.length;_++){const g=v[_],c=g.link.range.start.y<s?0:g.link.range.start.x,m=g.link.range.end.y>s?this._bufferService.cols:g.link.range.end.x;for(let E=c;E<=m;E++){if(i.has(E)){v.splice(_--,1);break}i.add(E)}}}}_checkLinkProviderResult(s,e,i){if(!this._activeProviderReplies)return i;const a=this._activeProviderReplies.get(s);let v=!1;for(let _=0;_<s;_++)this._activeProviderReplies.has(_)&&!this._activeProviderReplies.get(_)||(v=!0);if(!v&&a){const _=a.find(g=>this._linkAtPosition(g.link,e));_&&(i=!0,this._handleNewLink(_))}if(this._activeProviderReplies.size===this._linkProviderService.linkProviders.length&&!i)for(let _=0;_<this._activeProviderReplies.size;_++){const g=this._activeProviderReplies.get(_)?.find(c=>this._linkAtPosition(c.link,e));if(g){i=!0,this._handleNewLink(g);break}}return i}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(s){if(!this._currentLink)return;const e=this._positionFromMouseEvent(s,this._element,this._mouseService);e&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,e)&&this._currentLink.link.activate(s,this._currentLink.link.text)}_clearCurrentLink(s,e){this._currentLink&&this._lastMouseEvent&&(!s||!e||this._currentLink.link.range.start.y>=s&&this._currentLink.link.range.end.y<=e)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,f.disposeArray)(this._linkCacheDisposables))}_handleNewLink(s){if(!this._lastMouseEvent)return;const e=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);e&&this._linkAtPosition(s.link,e)&&(this._currentLink=s,this._currentLink.state={decorations:{underline:s.link.decorations===void 0||s.link.decorations.underline,pointerCursor:s.link.decorations===void 0||s.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,s.link,this._lastMouseEvent),s.link.decorations={},Object.defineProperties(s.link.decorations,{pointerCursor:{get:()=>this._currentLink?.state?.decorations.pointerCursor,set:i=>{this._currentLink?.state&&this._currentLink.state.decorations.pointerCursor!==i&&(this._currentLink.state.decorations.pointerCursor=i,this._currentLink.state.isHovered&&this._element.classList.toggle("xterm-cursor-pointer",i))}},underline:{get:()=>this._currentLink?.state?.decorations.underline,set:i=>{this._currentLink?.state&&this._currentLink?.state?.decorations.underline!==i&&(this._currentLink.state.decorations.underline=i,this._currentLink.state.isHovered&&this._fireUnderlineEvent(s.link,i))}}}),this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(i=>{if(!this._currentLink)return;const a=i.start===0?0:i.start+1+this._bufferService.buffer.ydisp,v=this._bufferService.buffer.ydisp+1+i.end;if(this._currentLink.link.range.start.y>=a&&this._currentLink.link.range.end.y<=v&&(this._clearCurrentLink(a,v),this._lastMouseEvent)){const _=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);_&&this._askForLink(_,!1)}})))}_linkHover(s,e,i){this._currentLink?.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(e,!0),this._currentLink.state.decorations.pointerCursor&&s.classList.add("xterm-cursor-pointer")),e.hover&&e.hover(i,e.text)}_fireUnderlineEvent(s,e){const i=s.range,a=this._bufferService.buffer.ydisp,v=this._createLinkUnderlineEvent(i.start.x-1,i.start.y-a-1,i.end.x,i.end.y-a-1,void 0);(e?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(v)}_linkLeave(s,e,i){this._currentLink?.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(e,!1),this._currentLink.state.decorations.pointerCursor&&s.classList.remove("xterm-cursor-pointer")),e.leave&&e.leave(i,e.text)}_linkAtPosition(s,e){const i=s.range.start.y*this._bufferService.cols+s.range.start.x,a=s.range.end.y*this._bufferService.cols+s.range.end.x,v=e.y*this._bufferService.cols+e.x;return i<=v&&v<=a}_positionFromMouseEvent(s,e,i){const a=i.getCoords(s,e,this._bufferService.cols,this._bufferService.rows);if(a)return{x:a[0],y:a[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(s,e,i,a,v){return{x1:s,y1:e,x2:i,y2:a,cols:this._bufferService.cols,fg:v}}};r.Linkifier=t=l([u(1,h.IMouseService),u(2,h.IRenderService),u(3,p.IBufferService),u(4,h.ILinkProviderService)],t)},9042:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.tooMuchOutput=r.promptLabel=void 0,r.promptLabel="Terminal input",r.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(B,r,o){var l=this&&this.__decorate||function(h,t,s,e){var i,a=arguments.length,v=a<3?t:e===null?e=Object.getOwnPropertyDescriptor(t,s):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(h,t,s,e);else for(var _=h.length-1;_>=0;_--)(i=h[_])&&(v=(a<3?i(v):a>3?i(t,s,v):i(t,s))||v);return a>3&&v&&Object.defineProperty(t,s,v),v},u=this&&this.__param||function(h,t){return function(s,e){t(s,e,h)}};Object.defineProperty(r,"__esModule",{value:!0}),r.OscLinkProvider=void 0;const n=o(511),d=o(2585);let f=r.OscLinkProvider=class{constructor(h,t,s){this._bufferService=h,this._optionsService=t,this._oscLinkService=s}provideLinks(h,t){const s=this._bufferService.buffer.lines.get(h-1);if(!s)return void t(void 0);const e=[],i=this._optionsService.rawOptions.linkHandler,a=new n.CellData,v=s.getTrimmedLength();let _=-1,g=-1,c=!1;for(let m=0;m<v;m++)if(g!==-1||s.hasContent(m)){if(s.loadCell(m,a),a.hasExtendedAttrs()&&a.extended.urlId){if(g===-1){g=m,_=a.extended.urlId;continue}c=a.extended.urlId!==_}else g!==-1&&(c=!0);if(c||g!==-1&&m===v-1){const E=this._oscLinkService.getLinkData(_)?.uri;if(E){const k={start:{x:g+1,y:h},end:{x:m+(c||m!==v-1?0:1),y:h}};let D=!1;if(!i?.allowNonHttpProtocols)try{const b=new URL(E);["http:","https:"].includes(b.protocol)||(D=!0)}catch{D=!0}D||e.push({text:E,range:k,activate:(b,x)=>i?i.activate(b,x,k):p(0,x),hover:(b,x)=>i?.hover?.(b,x,k),leave:(b,x)=>i?.leave?.(b,x,k)})}c=!1,a.hasExtendedAttrs()&&a.extended.urlId?(g=m,_=a.extended.urlId):(g=-1,_=-1)}}t(e)}};function p(h,t){if(confirm(`Do you want to navigate to ${t}?

WARNING: This link could potentially be dangerous`)){const s=window.open();if(s){try{s.opener=null}catch{}s.location.href=t}else console.warn("Opening link blocked as opener could not be cleared")}}r.OscLinkProvider=f=l([u(0,d.IBufferService),u(1,d.IOptionsService),u(2,d.IOscLinkService)],f)},6193:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.RenderDebouncer=void 0,r.RenderDebouncer=class{constructor(o,l){this._renderCallback=o,this._coreBrowserService=l,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._coreBrowserService.window.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(o){return this._refreshCallbacks.push(o),this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(o,l,u){this._rowCount=u,o=o!==void 0?o:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,o):o,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l,this._animationFrame||(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const o=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(o,l),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const o of this._refreshCallbacks)o(0);this._refreshCallbacks=[]}}},3236:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Terminal=void 0;const l=o(3614),u=o(3656),n=o(3551),d=o(9042),f=o(3730),p=o(1680),h=o(3107),t=o(5744),s=o(2950),e=o(1296),i=o(428),a=o(4269),v=o(5114),_=o(8934),g=o(3230),c=o(9312),m=o(4725),E=o(6731),k=o(8055),D=o(8969),b=o(8460),x=o(844),M=o(6114),I=o(8437),N=o(2584),P=o(7399),S=o(5941),w=o(9074),y=o(2585),L=o(5435),T=o(4567),H=o(779);class U extends D.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(R={}){super(R),this.browser=M,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new x.MutableDisposable),this._onCursorMove=this.register(new b.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new b.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new b.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new b.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new b.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new b.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new b.EventEmitter),this._onBlur=this.register(new b.EventEmitter),this._onA11yCharEmitter=this.register(new b.EventEmitter),this._onA11yTabEmitter=this.register(new b.EventEmitter),this._onWillOpen=this.register(new b.EventEmitter),this._setup(),this._decorationService=this._instantiationService.createInstance(w.DecorationService),this._instantiationService.setService(y.IDecorationService,this._decorationService),this._linkProviderService=this._instantiationService.createInstance(H.LinkProviderService),this._instantiationService.setService(m.ILinkProviderService,this._linkProviderService),this._linkProviderService.registerLinkProvider(this._instantiationService.createInstance(f.OscLinkProvider)),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((C,A)=>this.refresh(C,A))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(C=>this._reportWindowsOptions(C))),this.register(this._inputHandler.onColor(C=>this._handleColorEvent(C))),this.register((0,b.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,b.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,b.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,b.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(C=>this._afterResize(C.cols,C.rows))),this.register((0,x.toDisposable)(()=>{this._customKeyEventHandler=void 0,this.element?.parentNode?.removeChild(this.element)}))}_handleColorEvent(R){if(this._themeService)for(const C of R){let A,O="";switch(C.index){case 256:A="foreground",O="10";break;case 257:A="background",O="11";break;case 258:A="cursor",O="12";break;default:A="ansi",O="4;"+C.index}switch(C.type){case 0:const W=k.color.toColorRGB(A==="ansi"?this._themeService.colors.ansi[C.index]:this._themeService.colors[A]);this.coreService.triggerDataEvent(`${N.C0.ESC}]${O};${(0,S.toRgbString)(W)}${N.C1_ESCAPED.ST}`);break;case 1:if(A==="ansi")this._themeService.modifyColors(F=>F.ansi[C.index]=k.channels.toColor(...C.color));else{const F=A;this._themeService.modifyColors(K=>K[F]=k.channels.toColor(...C.color))}break;case 2:this._themeService.restoreColor(C.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(R){R?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(T.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(R){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(N.C0.ESC+"[I"),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){return this.textarea?.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(N.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const R=this.buffer.ybase+this.buffer.y,C=this.buffer.lines.get(R);if(!C)return;const A=Math.min(this.buffer.x,this.cols-1),O=this._renderService.dimensions.css.cell.height,W=C.getWidth(A),F=this._renderService.dimensions.css.cell.width*W,K=this.buffer.y*this._renderService.dimensions.css.cell.height,X=A*this._renderService.dimensions.css.cell.width;this.textarea.style.left=X+"px",this.textarea.style.top=K+"px",this.textarea.style.width=F+"px",this.textarea.style.height=O+"px",this.textarea.style.lineHeight=O+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,u.addDisposableDomListener)(this.element,"copy",C=>{this.hasSelection()&&(0,l.copyHandler)(C,this._selectionService)}));const R=C=>(0,l.handlePasteEvent)(C,this.textarea,this.coreService,this.optionsService);this.register((0,u.addDisposableDomListener)(this.textarea,"paste",R)),this.register((0,u.addDisposableDomListener)(this.element,"paste",R)),M.isFirefox?this.register((0,u.addDisposableDomListener)(this.element,"mousedown",C=>{C.button===2&&(0,l.rightClickHandler)(C,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,u.addDisposableDomListener)(this.element,"contextmenu",C=>{(0,l.rightClickHandler)(C,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),M.isLinux&&this.register((0,u.addDisposableDomListener)(this.element,"auxclick",C=>{C.button===1&&(0,l.moveTextAreaUnderMouseCursor)(C,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,u.addDisposableDomListener)(this.textarea,"keyup",R=>this._keyUp(R),!0)),this.register((0,u.addDisposableDomListener)(this.textarea,"keydown",R=>this._keyDown(R),!0)),this.register((0,u.addDisposableDomListener)(this.textarea,"keypress",R=>this._keyPress(R),!0)),this.register((0,u.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,u.addDisposableDomListener)(this.textarea,"compositionupdate",R=>this._compositionHelper.compositionupdate(R))),this.register((0,u.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,u.addDisposableDomListener)(this.textarea,"input",R=>this._inputEvent(R),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(R){if(!R)throw new Error("Terminal requires a parent element.");if(R.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),this.element?.ownerDocument.defaultView&&this._coreBrowserService)return void(this.element.ownerDocument.defaultView!==this._coreBrowserService.window&&(this._coreBrowserService.window=this.element.ownerDocument.defaultView));this._document=R.ownerDocument,this.options.documentOverride&&this.options.documentOverride instanceof Document&&(this._document=this.optionsService.rawOptions.documentOverride),this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),R.appendChild(this.element);const C=this._document.createDocumentFragment();this._viewportElement=this._document.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),C.appendChild(this._viewportElement),this._viewportScrollArea=this._document.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=this._document.createElement("div"),this.screenElement.classList.add("xterm-screen"),this.register((0,u.addDisposableDomListener)(this.screenElement,"mousemove",A=>this.updateCursorStyle(A))),this._helperContainer=this._document.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),C.appendChild(this.screenElement),this.textarea=this._document.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",d.promptLabel),M.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this.register(this._instantiationService.createInstance(v.CoreBrowserService,this.textarea,R.ownerDocument.defaultView??window,this._document??typeof window<"u"?window.document:null)),this._instantiationService.setService(m.ICoreBrowserService,this._coreBrowserService),this.register((0,u.addDisposableDomListener)(this.textarea,"focus",A=>this._handleTextAreaFocus(A))),this.register((0,u.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(i.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(m.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(E.ThemeService),this._instantiationService.setService(m.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(a.CharacterJoinerService),this._instantiationService.setService(m.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(g.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(m.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(A=>this._onRender.fire(A))),this.onResize(A=>this._renderService.resize(A.cols,A.rows)),this._compositionView=this._document.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(s.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this._mouseService=this._instantiationService.createInstance(_.MouseService),this._instantiationService.setService(m.IMouseService,this._mouseService),this.linkifier=this.register(this._instantiationService.createInstance(n.Linkifier,this.screenElement)),this.element.appendChild(C);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this.viewport=this._instantiationService.createInstance(p.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(A=>this.scrollLines(A.amount,A.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(c.SelectionService,this.element,this.screenElement,this.linkifier)),this._instantiationService.setService(m.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(A=>this.scrollLines(A.amount,A.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(A=>this._renderService.handleSelectionChanged(A.start,A.end,A.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(A=>{this.textarea.value=A,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(A=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,u.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.register(this._instantiationService.createInstance(h.BufferDecorationRenderer,this.screenElement)),this.register((0,u.addDisposableDomListener)(this.element,"mousedown",A=>this._selectionService.handleMouseDown(A))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(T.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",A=>this._handleScreenReaderModeOptionChange(A))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(t.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",A=>{!this._overviewRulerRenderer&&A&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(t.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(e.DomRenderer,this,this._document,this.element,this.screenElement,this._viewportElement,this._helperContainer,this.linkifier)}bindMouse(){const R=this,C=this.element;function A(F){const K=R._mouseService.getMouseReportCoords(F,R.screenElement);if(!K)return!1;let X,J;switch(F.overrideType||F.type){case"mousemove":J=32,F.buttons===void 0?(X=3,F.button!==void 0&&(X=F.button<3?F.button:3)):X=1&F.buttons?0:4&F.buttons?1:2&F.buttons?2:3;break;case"mouseup":J=0,X=F.button<3?F.button:3;break;case"mousedown":J=1,X=F.button<3?F.button:3;break;case"wheel":if(R._customWheelEventHandler&&R._customWheelEventHandler(F)===!1||R.viewport.getLinesScrolled(F)===0)return!1;J=F.deltaY<0?0:1,X=4;break;default:return!1}return!(J===void 0||X===void 0||X>4)&&R.coreMouseService.triggerMouseEvent({col:K.col,row:K.row,x:K.x,y:K.y,button:X,action:J,ctrl:F.ctrlKey,alt:F.altKey,shift:F.shiftKey})}const O={mouseup:null,wheel:null,mousedrag:null,mousemove:null},W={mouseup:F=>(A(F),F.buttons||(this._document.removeEventListener("mouseup",O.mouseup),O.mousedrag&&this._document.removeEventListener("mousemove",O.mousedrag)),this.cancel(F)),wheel:F=>(A(F),this.cancel(F,!0)),mousedrag:F=>{F.buttons&&A(F)},mousemove:F=>{F.buttons||A(F)}};this.register(this.coreMouseService.onProtocolChange(F=>{F?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(F)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&F?O.mousemove||(C.addEventListener("mousemove",W.mousemove),O.mousemove=W.mousemove):(C.removeEventListener("mousemove",O.mousemove),O.mousemove=null),16&F?O.wheel||(C.addEventListener("wheel",W.wheel,{passive:!1}),O.wheel=W.wheel):(C.removeEventListener("wheel",O.wheel),O.wheel=null),2&F?O.mouseup||(O.mouseup=W.mouseup):(this._document.removeEventListener("mouseup",O.mouseup),O.mouseup=null),4&F?O.mousedrag||(O.mousedrag=W.mousedrag):(this._document.removeEventListener("mousemove",O.mousedrag),O.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,u.addDisposableDomListener)(C,"mousedown",F=>{if(F.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(F))return A(F),O.mouseup&&this._document.addEventListener("mouseup",O.mouseup),O.mousedrag&&this._document.addEventListener("mousemove",O.mousedrag),this.cancel(F)})),this.register((0,u.addDisposableDomListener)(C,"wheel",F=>{if(!O.wheel){if(this._customWheelEventHandler&&this._customWheelEventHandler(F)===!1)return!1;if(!this.buffer.hasScrollback){const K=this.viewport.getLinesScrolled(F);if(K===0)return;const X=N.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(F.deltaY<0?"A":"B");let J="";for(let Q=0;Q<Math.abs(K);Q++)J+=X;return this.coreService.triggerDataEvent(J,!0),this.cancel(F,!0)}return this.viewport.handleWheel(F)?this.cancel(F):void 0}},{passive:!1})),this.register((0,u.addDisposableDomListener)(C,"touchstart",F=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(F),this.cancel(F)},{passive:!0})),this.register((0,u.addDisposableDomListener)(C,"touchmove",F=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(F)?void 0:this.cancel(F)},{passive:!1}))}refresh(R,C){this._renderService?.refreshRows(R,C)}updateCursorStyle(R){this._selectionService?.shouldColumnSelect(R)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(R,C,A=0){A===1?(super.scrollLines(R,C,A),this.refresh(0,this.rows-1)):this.viewport?.scrollLines(R)}paste(R){(0,l.paste)(R,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(R){this._customKeyEventHandler=R}attachCustomWheelEventHandler(R){this._customWheelEventHandler=R}registerLinkProvider(R){return this._linkProviderService.registerLinkProvider(R)}registerCharacterJoiner(R){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const C=this._characterJoinerService.register(R);return this.refresh(0,this.rows-1),C}deregisterCharacterJoiner(R){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(R)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(R){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+R)}registerDecoration(R){return this._decorationService.registerDecoration(R)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(R,C,A){this._selectionService.setSelection(R,C,A)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){this._selectionService?.clearSelection()}selectAll(){this._selectionService?.selectAll()}selectLines(R,C){this._selectionService?.selectLines(R,C)}_keyDown(R){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(R)===!1)return!1;const C=this.browser.isMac&&this.options.macOptionIsMeta&&R.altKey;if(!C&&!this._compositionHelper.keydown(R))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;C||R.key!=="Dead"&&R.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const A=(0,P.evaluateKeyboardEvent)(R,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(R),A.type===3||A.type===2){const O=this.rows-1;return this.scrollLines(A.type===2?-O:O),this.cancel(R,!0)}return A.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,R)||(A.cancel&&this.cancel(R,!0),!A.key||!!(R.key&&!R.ctrlKey&&!R.altKey&&!R.metaKey&&R.key.length===1&&R.key.charCodeAt(0)>=65&&R.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(A.key!==N.C0.ETX&&A.key!==N.C0.CR||(this.textarea.value=""),this._onKey.fire({key:A.key,domEvent:R}),this._showCursor(),this.coreService.triggerDataEvent(A.key,!0),!this.optionsService.rawOptions.screenReaderMode||R.altKey||R.ctrlKey?this.cancel(R,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(R,C){const A=R.isMac&&!this.options.macOptionIsMeta&&C.altKey&&!C.ctrlKey&&!C.metaKey||R.isWindows&&C.altKey&&C.ctrlKey&&!C.metaKey||R.isWindows&&C.getModifierState("AltGraph");return C.type==="keypress"?A:A&&(!C.keyCode||C.keyCode>47)}_keyUp(R){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(R)===!1||(function(C){return C.keyCode===16||C.keyCode===17||C.keyCode===18}(R)||this.focus(),this.updateCursorStyle(R),this._keyPressHandled=!1)}_keyPress(R){let C;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(R)===!1)return!1;if(this.cancel(R),R.charCode)C=R.charCode;else if(R.which===null||R.which===void 0)C=R.keyCode;else{if(R.which===0||R.charCode===0)return!1;C=R.which}return!(!C||(R.altKey||R.ctrlKey||R.metaKey)&&!this._isThirdLevelShift(this.browser,R)||(C=String.fromCharCode(C),this._onKey.fire({key:C,domEvent:R}),this._showCursor(),this.coreService.triggerDataEvent(C,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(R){if(R.data&&R.inputType==="insertText"&&(!R.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const C=R.data;return this.coreService.triggerDataEvent(C,!0),this.cancel(R),!0}return!1}resize(R,C){R!==this.cols||C!==this.rows?super.resize(R,C):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(R,C){this._charSizeService?.measure(),this.viewport?.syncScrollArea(!0)}clear(){if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let R=1;R<this.rows;R++)this.buffer.lines.push(this.buffer.getBlankLine(I.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),this.viewport?.reset(),this.refresh(0,this.rows-1)}}reset(){this.options.rows=this.rows,this.options.cols=this.cols;const R=this._customKeyEventHandler;this._setup(),super.reset(),this._selectionService?.reset(),this._decorationService.reset(),this.viewport?.reset(),this._customKeyEventHandler=R,this.refresh(0,this.rows-1)}clearTextureAtlas(){this._renderService?.clearTextureAtlas()}_reportFocus(){this.element?.classList.contains("focus")?this.coreService.triggerDataEvent(N.C0.ESC+"[I"):this.coreService.triggerDataEvent(N.C0.ESC+"[O")}_reportWindowsOptions(R){if(this._renderService)switch(R){case L.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const C=this._renderService.dimensions.css.canvas.width.toFixed(0),A=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${N.C0.ESC}[4;${A};${C}t`);break;case L.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const O=this._renderService.dimensions.css.cell.width.toFixed(0),W=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${N.C0.ESC}[6;${W};${O}t`)}}cancel(R,C){if(this.options.cancelEvents||C)return R.preventDefault(),R.stopPropagation(),!1}}r.Terminal=U},9924:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.TimeBasedDebouncer=void 0,r.TimeBasedDebouncer=class{constructor(o,l=1e3){this._renderCallback=o,this._debounceThresholdMS=l,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(o,l,u){this._rowCount=u,o=o!==void 0?o:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,o):o,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l;const n=Date.now();if(n-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=n,this._innerRefresh();else if(!this._additionalRefreshRequested){const d=n-this._lastRefreshMs,f=this._debounceThresholdMS-d;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},f)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const o=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(o,l)}}},1680:function(B,r,o){var l=this&&this.__decorate||function(s,e,i,a){var v,_=arguments.length,g=_<3?e:a===null?a=Object.getOwnPropertyDescriptor(e,i):a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(s,e,i,a);else for(var c=s.length-1;c>=0;c--)(v=s[c])&&(g=(_<3?v(g):_>3?v(e,i,g):v(e,i))||g);return _>3&&g&&Object.defineProperty(e,i,g),g},u=this&&this.__param||function(s,e){return function(i,a){e(i,a,s)}};Object.defineProperty(r,"__esModule",{value:!0}),r.Viewport=void 0;const n=o(3656),d=o(4725),f=o(8460),p=o(844),h=o(2585);let t=r.Viewport=class extends p.Disposable{constructor(s,e,i,a,v,_,g,c){super(),this._viewportElement=s,this._scrollArea=e,this._bufferService=i,this._optionsService=a,this._charSizeService=v,this._renderService=_,this._coreBrowserService=g,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new f.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,n.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(m=>this._activeBuffer=m.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(m=>this._renderDimensions=m)),this._handleThemeChange(c.colors),this.register(c.onChangeColors(m=>this._handleThemeChange(m))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(s){this._viewportElement.style.backgroundColor=s.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(s){if(s)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderDimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderDimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const e=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderDimensions.css.canvas.height);this._lastRecordedBufferHeight!==e&&(this._lastRecordedBufferHeight=e,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const s=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==s&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=s),this._refreshAnimationFrame=null}syncScrollArea(s=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(s);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(s)}_handleScroll(s){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const e=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:e,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const s=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(s*(this._smoothScrollState.target-this._smoothScrollState.origin)),s<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(s,e){const i=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(e<0&&this._viewportElement.scrollTop!==0||e>0&&i<this._lastRecordedBufferHeight)||(s.cancelable&&s.preventDefault(),!1)}handleWheel(s){const e=this._getPixelsScrolled(s);return e!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+e:this._smoothScrollState.target+=e,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=e,this._bubbleScroll(s,e))}scrollLines(s){if(s!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const e=s*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+e,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:s,suppressScrollEvent:!1})}_getPixelsScrolled(s){if(s.deltaY===0||s.shiftKey)return 0;let e=this._applyScrollModifier(s.deltaY,s);return s.deltaMode===WheelEvent.DOM_DELTA_LINE?e*=this._currentRowHeight:s.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(e*=this._currentRowHeight*this._bufferService.rows),e}getBufferElements(s,e){let i,a="";const v=[],_=e??this._bufferService.buffer.lines.length,g=this._bufferService.buffer.lines;for(let c=s;c<_;c++){const m=g.get(c);if(!m)continue;const E=g.get(c+1)?.isWrapped;if(a+=m.translateToString(!E),!E||c===g.length-1){const k=document.createElement("div");k.textContent=a,v.push(k),a.length>0&&(i=k),a=""}}return{bufferElements:v,cursorElement:i}}getLinesScrolled(s){if(s.deltaY===0||s.shiftKey)return 0;let e=this._applyScrollModifier(s.deltaY,s);return s.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(e/=this._currentRowHeight+0,this._wheelPartialScroll+=e,e=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):s.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(e*=this._bufferService.rows),e}_applyScrollModifier(s,e){const i=this._optionsService.rawOptions.fastScrollModifier;return i==="alt"&&e.altKey||i==="ctrl"&&e.ctrlKey||i==="shift"&&e.shiftKey?s*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:s*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(s){this._lastTouchY=s.touches[0].pageY}handleTouchMove(s){const e=this._lastTouchY-s.touches[0].pageY;return this._lastTouchY=s.touches[0].pageY,e!==0&&(this._viewportElement.scrollTop+=e,this._bubbleScroll(s,e))}};r.Viewport=t=l([u(2,h.IBufferService),u(3,h.IOptionsService),u(4,d.ICharSizeService),u(5,d.IRenderService),u(6,d.ICoreBrowserService),u(7,d.IThemeService)],t)},3107:function(B,r,o){var l=this&&this.__decorate||function(h,t,s,e){var i,a=arguments.length,v=a<3?t:e===null?e=Object.getOwnPropertyDescriptor(t,s):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(h,t,s,e);else for(var _=h.length-1;_>=0;_--)(i=h[_])&&(v=(a<3?i(v):a>3?i(t,s,v):i(t,s))||v);return a>3&&v&&Object.defineProperty(t,s,v),v},u=this&&this.__param||function(h,t){return function(s,e){t(s,e,h)}};Object.defineProperty(r,"__esModule",{value:!0}),r.BufferDecorationRenderer=void 0;const n=o(4725),d=o(844),f=o(2585);let p=r.BufferDecorationRenderer=class extends d.Disposable{constructor(h,t,s,e,i){super(),this._screenElement=h,this._bufferService=t,this._coreBrowserService=s,this._decorationService=e,this._renderService=i,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register(this._coreBrowserService.onDprChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(a=>this._removeDecoration(a))),this.register((0,d.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const h of this._decorationService.decorations)this._renderDecoration(h);this._dimensionsChanged=!1}_renderDecoration(h){this._refreshStyle(h),this._dimensionsChanged&&this._refreshXPosition(h)}_createElement(h){const t=this._coreBrowserService.mainDocument.createElement("div");t.classList.add("xterm-decoration"),t.classList.toggle("xterm-decoration-top-layer",h?.options?.layer==="top"),t.style.width=`${Math.round((h.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,t.style.height=(h.options.height||1)*this._renderService.dimensions.css.cell.height+"px",t.style.top=(h.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",t.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const s=h.options.x??0;return s&&s>this._bufferService.cols&&(t.style.display="none"),this._refreshXPosition(h,t),t}_refreshStyle(h){const t=h.marker.line-this._bufferService.buffers.active.ydisp;if(t<0||t>=this._bufferService.rows)h.element&&(h.element.style.display="none",h.onRenderEmitter.fire(h.element));else{let s=this._decorationElements.get(h);s||(s=this._createElement(h),h.element=s,this._decorationElements.set(h,s),this._container.appendChild(s),h.onDispose(()=>{this._decorationElements.delete(h),s.remove()})),s.style.top=t*this._renderService.dimensions.css.cell.height+"px",s.style.display=this._altBufferIsActive?"none":"block",h.onRenderEmitter.fire(s)}}_refreshXPosition(h,t=h.element){if(!t)return;const s=h.options.x??0;(h.options.anchor||"left")==="right"?t.style.right=s?s*this._renderService.dimensions.css.cell.width+"px":"":t.style.left=s?s*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(h){this._decorationElements.get(h)?.remove(),this._decorationElements.delete(h),h.dispose()}};r.BufferDecorationRenderer=p=l([u(1,f.IBufferService),u(2,n.ICoreBrowserService),u(3,f.IDecorationService),u(4,n.IRenderService)],p)},5871:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ColorZoneStore=void 0,r.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(o){if(o.options.overviewRulerOptions){for(const l of this._zones)if(l.color===o.options.overviewRulerOptions.color&&l.position===o.options.overviewRulerOptions.position){if(this._lineIntersectsZone(l,o.marker.line))return;if(this._lineAdjacentToZone(l,o.marker.line,o.options.overviewRulerOptions.position))return void this._addLineToZone(l,o.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=o.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=o.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=o.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=o.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:o.options.overviewRulerOptions.color,position:o.options.overviewRulerOptions.position,startBufferLine:o.marker.line,endBufferLine:o.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(o){this._linePadding=o}_lineIntersectsZone(o,l){return l>=o.startBufferLine&&l<=o.endBufferLine}_lineAdjacentToZone(o,l,u){return l>=o.startBufferLine-this._linePadding[u||"full"]&&l<=o.endBufferLine+this._linePadding[u||"full"]}_addLineToZone(o,l){o.startBufferLine=Math.min(o.startBufferLine,l),o.endBufferLine=Math.max(o.endBufferLine,l)}}},5744:function(B,r,o){var l=this&&this.__decorate||function(i,a,v,_){var g,c=arguments.length,m=c<3?a:_===null?_=Object.getOwnPropertyDescriptor(a,v):_;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")m=Reflect.decorate(i,a,v,_);else for(var E=i.length-1;E>=0;E--)(g=i[E])&&(m=(c<3?g(m):c>3?g(a,v,m):g(a,v))||m);return c>3&&m&&Object.defineProperty(a,v,m),m},u=this&&this.__param||function(i,a){return function(v,_){a(v,_,i)}};Object.defineProperty(r,"__esModule",{value:!0}),r.OverviewRulerRenderer=void 0;const n=o(5871),d=o(4725),f=o(844),p=o(2585),h={full:0,left:0,center:0,right:0},t={full:0,left:0,center:0,right:0},s={full:0,left:0,center:0,right:0};let e=r.OverviewRulerRenderer=class extends f.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(i,a,v,_,g,c,m){super(),this._viewportElement=i,this._screenElement=a,this._bufferService=v,this._decorationService=_,this._renderService=g,this._optionsService=c,this._coreBrowserService=m,this._colorZoneStore=new n.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=this._coreBrowserService.mainDocument.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),this._viewportElement.parentElement?.insertBefore(this._canvas,this._viewportElement);const E=this._canvas.getContext("2d");if(!E)throw new Error("Ctx cannot be null");this._ctx=E,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,f.toDisposable)(()=>{this._canvas?.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register(this._coreBrowserService.onDprChange(()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const i=Math.floor(this._canvas.width/3),a=Math.ceil(this._canvas.width/3);t.full=this._canvas.width,t.left=i,t.center=a,t.right=i,this._refreshDrawHeightConstants(),s.full=0,s.left=0,s.center=t.left,s.right=t.left+t.center}_refreshDrawHeightConstants(){h.full=Math.round(2*this._coreBrowserService.dpr);const i=this._canvas.height/this._bufferService.buffer.lines.length,a=Math.round(Math.max(Math.min(i,12),6)*this._coreBrowserService.dpr);h.left=a,h.center=a,h.right=a}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*h.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*h.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*h.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*h.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowserService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowserService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const a of this._decorationService.decorations)this._colorZoneStore.addDecoration(a);this._ctx.lineWidth=1;const i=this._colorZoneStore.zones;for(const a of i)a.position!=="full"&&this._renderColorZone(a);for(const a of i)a.position==="full"&&this._renderColorZone(a);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(i){this._ctx.fillStyle=i.color,this._ctx.fillRect(s[i.position||"full"],Math.round((this._canvas.height-1)*(i.startBufferLine/this._bufferService.buffers.active.lines.length)-h[i.position||"full"]/2),t[i.position||"full"],Math.round((this._canvas.height-1)*((i.endBufferLine-i.startBufferLine)/this._bufferService.buffers.active.lines.length)+h[i.position||"full"]))}_queueRefresh(i,a){this._shouldUpdateDimensions=i||this._shouldUpdateDimensions,this._shouldUpdateAnchor=a||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};r.OverviewRulerRenderer=e=l([u(2,p.IBufferService),u(3,p.IDecorationService),u(4,d.IRenderService),u(5,p.IOptionsService),u(6,d.ICoreBrowserService)],e)},2950:function(B,r,o){var l=this&&this.__decorate||function(h,t,s,e){var i,a=arguments.length,v=a<3?t:e===null?e=Object.getOwnPropertyDescriptor(t,s):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(h,t,s,e);else for(var _=h.length-1;_>=0;_--)(i=h[_])&&(v=(a<3?i(v):a>3?i(t,s,v):i(t,s))||v);return a>3&&v&&Object.defineProperty(t,s,v),v},u=this&&this.__param||function(h,t){return function(s,e){t(s,e,h)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CompositionHelper=void 0;const n=o(4725),d=o(2585),f=o(2584);let p=r.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(h,t,s,e,i,a){this._textarea=h,this._compositionView=t,this._bufferService=s,this._optionsService=e,this._coreService=i,this._renderService=a,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(h){this._compositionView.textContent=h.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(h){if(this._isComposing||this._isSendingComposition){if(h.keyCode===229||h.keyCode===16||h.keyCode===17||h.keyCode===18)return!1;this._finalizeComposition(!1)}return h.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(h){if(this._compositionView.classList.remove("active"),this._isComposing=!1,h){const t={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let s;this._isSendingComposition=!1,t.start+=this._dataAlreadySent.length,s=this._isComposing?this._textarea.value.substring(t.start,t.end):this._textarea.value.substring(t.start),s.length>0&&this._coreService.triggerDataEvent(s,!0)}},0)}else{this._isSendingComposition=!1;const t=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(t,!0)}}_handleAnyTextareaChanges(){const h=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const t=this._textarea.value,s=t.replace(h,"");this._dataAlreadySent=s,t.length>h.length?this._coreService.triggerDataEvent(s,!0):t.length<h.length?this._coreService.triggerDataEvent(`${f.C0.DEL}`,!0):t.length===h.length&&t!==h&&this._coreService.triggerDataEvent(t,!0)}},0)}updateCompositionElements(h){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const t=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),s=this._renderService.dimensions.css.cell.height,e=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,i=t*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=i+"px",this._compositionView.style.top=e+"px",this._compositionView.style.height=s+"px",this._compositionView.style.lineHeight=s+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const a=this._compositionView.getBoundingClientRect();this._textarea.style.left=i+"px",this._textarea.style.top=e+"px",this._textarea.style.width=Math.max(a.width,1)+"px",this._textarea.style.height=Math.max(a.height,1)+"px",this._textarea.style.lineHeight=a.height+"px"}h||setTimeout(()=>this.updateCompositionElements(!0),0)}}};r.CompositionHelper=p=l([u(2,d.IBufferService),u(3,d.IOptionsService),u(4,d.ICoreService),u(5,n.IRenderService)],p)},9806:(B,r)=>{function o(l,u,n){const d=n.getBoundingClientRect(),f=l.getComputedStyle(n),p=parseInt(f.getPropertyValue("padding-left")),h=parseInt(f.getPropertyValue("padding-top"));return[u.clientX-d.left-p,u.clientY-d.top-h]}Object.defineProperty(r,"__esModule",{value:!0}),r.getCoords=r.getCoordsRelativeToElement=void 0,r.getCoordsRelativeToElement=o,r.getCoords=function(l,u,n,d,f,p,h,t,s){if(!p)return;const e=o(l,u,n);return e?(e[0]=Math.ceil((e[0]+(s?h/2:0))/h),e[1]=Math.ceil(e[1]/t),e[0]=Math.min(Math.max(e[0],1),d+(s?1:0)),e[1]=Math.min(Math.max(e[1],1),f),e):void 0}},9504:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.moveToCellSequence=void 0;const l=o(2584);function u(t,s,e,i){const a=t-n(t,e),v=s-n(s,e),_=Math.abs(a-v)-function(g,c,m){let E=0;const k=g-n(g,m),D=c-n(c,m);for(let b=0;b<Math.abs(k-D);b++){const x=d(g,c)==="A"?-1:1;m.buffer.lines.get(k+x*b)?.isWrapped&&E++}return E}(t,s,e);return h(_,p(d(t,s),i))}function n(t,s){let e=0,i=s.buffer.lines.get(t),a=i?.isWrapped;for(;a&&t>=0&&t<s.rows;)e++,i=s.buffer.lines.get(--t),a=i?.isWrapped;return e}function d(t,s){return t>s?"A":"B"}function f(t,s,e,i,a,v){let _=t,g=s,c="";for(;_!==e||g!==i;)_+=a?1:-1,a&&_>v.cols-1?(c+=v.buffer.translateBufferLineToString(g,!1,t,_),_=0,t=0,g++):!a&&_<0&&(c+=v.buffer.translateBufferLineToString(g,!1,0,t+1),_=v.cols-1,t=_,g--);return c+v.buffer.translateBufferLineToString(g,!1,t,_)}function p(t,s){const e=s?"O":"[";return l.C0.ESC+e+t}function h(t,s){t=Math.floor(t);let e="";for(let i=0;i<t;i++)e+=s;return e}r.moveToCellSequence=function(t,s,e,i){const a=e.buffer.x,v=e.buffer.y;if(!e.buffer.hasScrollback)return function(c,m,E,k,D,b){return u(m,k,D,b).length===0?"":h(f(c,m,c,m-n(m,D),!1,D).length,p("D",b))}(a,v,0,s,e,i)+u(v,s,e,i)+function(c,m,E,k,D,b){let x;x=u(m,k,D,b).length>0?k-n(k,D):m;const M=k,I=function(N,P,S,w,y,L){let T;return T=u(S,w,y,L).length>0?w-n(w,y):P,N<S&&T<=w||N>=S&&T<w?"C":"D"}(c,m,E,k,D,b);return h(f(c,x,E,M,I==="C",D).length,p(I,b))}(a,v,t,s,e,i);let _;if(v===s)return _=a>t?"D":"C",h(Math.abs(a-t),p(_,i));_=v>s?"D":"C";const g=Math.abs(v-s);return h(function(c,m){return m.cols-c}(v>s?t:a,e)+(g-1)*e.cols+1+((v>s?a:t)-1),p(_,i))}},1296:function(B,r,o){var l=this&&this.__decorate||function(b,x,M,I){var N,P=arguments.length,S=P<3?x:I===null?I=Object.getOwnPropertyDescriptor(x,M):I;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(b,x,M,I);else for(var w=b.length-1;w>=0;w--)(N=b[w])&&(S=(P<3?N(S):P>3?N(x,M,S):N(x,M))||S);return P>3&&S&&Object.defineProperty(x,M,S),S},u=this&&this.__param||function(b,x){return function(M,I){x(M,I,b)}};Object.defineProperty(r,"__esModule",{value:!0}),r.DomRenderer=void 0;const n=o(3787),d=o(2550),f=o(2223),p=o(6171),h=o(6052),t=o(4725),s=o(8055),e=o(8460),i=o(844),a=o(2585),v="xterm-dom-renderer-owner-",_="xterm-rows",g="xterm-fg-",c="xterm-bg-",m="xterm-focus",E="xterm-selection";let k=1,D=r.DomRenderer=class extends i.Disposable{constructor(b,x,M,I,N,P,S,w,y,L,T,H,U){super(),this._terminal=b,this._document=x,this._element=M,this._screenElement=I,this._viewportElement=N,this._helperContainer=P,this._linkifier2=S,this._charSizeService=y,this._optionsService=L,this._bufferService=T,this._coreBrowserService=H,this._themeService=U,this._terminalClass=k++,this._rowElements=[],this._selectionRenderModel=(0,h.createSelectionRenderModel)(),this.onRequestRedraw=this.register(new e.EventEmitter).event,this._rowContainer=this._document.createElement("div"),this._rowContainer.classList.add(_),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=this._document.createElement("div"),this._selectionContainer.classList.add(E),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,p.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors($=>this._injectCss($))),this._injectCss(this._themeService.colors),this._rowFactory=w.createInstance(n.DomRendererRowFactory,document),this._element.classList.add(v+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline($=>this._handleLinkHover($))),this.register(this._linkifier2.onHideLinkUnderline($=>this._handleLinkLeave($))),this.register((0,i.toDisposable)(()=>{this._element.classList.remove(v+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new d.WidthCache(this._document,this._helperContainer),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const b=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*b,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*b),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/b),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/b),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const M of this._rowElements)M.style.width=`${this.dimensions.css.canvas.width}px`,M.style.height=`${this.dimensions.css.cell.height}px`,M.style.lineHeight=`${this.dimensions.css.cell.height}px`,M.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=this._document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const x=`${this._terminalSelector} .${_} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=x,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(b){this._themeStyleElement||(this._themeStyleElement=this._document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let x=`${this._terminalSelector} .${_} { color: ${b.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;x+=`${this._terminalSelector} .${_} .xterm-dim { color: ${s.color.multiplyOpacity(b.foreground,.5).css};}`,x+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`;const M=`blink_underline_${this._terminalClass}`,I=`blink_bar_${this._terminalClass}`,N=`blink_block_${this._terminalClass}`;x+=`@keyframes ${M} { 50% {  border-bottom-style: hidden; }}`,x+=`@keyframes ${I} { 50% {  box-shadow: none; }}`,x+=`@keyframes ${N} { 0% {  background-color: ${b.cursor.css};  color: ${b.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${b.cursor.css}; }}`,x+=`${this._terminalSelector} .${_}.${m} .xterm-cursor.xterm-cursor-blink.xterm-cursor-underline { animation: ${M} 1s step-end infinite;}${this._terminalSelector} .${_}.${m} .xterm-cursor.xterm-cursor-blink.xterm-cursor-bar { animation: ${I} 1s step-end infinite;}${this._terminalSelector} .${_}.${m} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: ${N} 1s step-end infinite;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-block { background-color: ${b.cursor.css}; color: ${b.cursorAccent.css};}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-block:not(.xterm-cursor-blink) { background-color: ${b.cursor.css} !important; color: ${b.cursorAccent.css} !important;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${b.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${b.cursor.css} inset;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${b.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,x+=`${this._terminalSelector} .${E} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${E} div { position: absolute; background-color: ${b.selectionBackgroundOpaque.css};}${this._terminalSelector} .${E} div { position: absolute; background-color: ${b.selectionInactiveBackgroundOpaque.css};}`;for(const[P,S]of b.ansi.entries())x+=`${this._terminalSelector} .${g}${P} { color: ${S.css}; }${this._terminalSelector} .${g}${P}.xterm-dim { color: ${s.color.multiplyOpacity(S,.5).css}; }${this._terminalSelector} .${c}${P} { background-color: ${S.css}; }`;x+=`${this._terminalSelector} .${g}${f.INVERTED_DEFAULT_COLOR} { color: ${s.color.opaque(b.background).css}; }${this._terminalSelector} .${g}${f.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${s.color.multiplyOpacity(s.color.opaque(b.background),.5).css}; }${this._terminalSelector} .${c}${f.INVERTED_DEFAULT_COLOR} { background-color: ${b.foreground.css}; }`,this._themeStyleElement.textContent=x}_setDefaultSpacing(){const b=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${b}px`,this._rowFactory.defaultSpacing=b}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(b,x){for(let M=this._rowElements.length;M<=x;M++){const I=this._document.createElement("div");this._rowContainer.appendChild(I),this._rowElements.push(I)}for(;this._rowElements.length>x;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(b,x){this._refreshRowElements(b,x),this._updateDimensions(),this.handleSelectionChanged(this._selectionRenderModel.selectionStart,this._selectionRenderModel.selectionEnd,this._selectionRenderModel.columnSelectMode)}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(m),this.renderRows(0,this._bufferService.rows-1)}handleFocus(){this._rowContainer.classList.add(m),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(b,x,M){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(b,x,M),this.renderRows(0,this._bufferService.rows-1),!b||!x)return;this._selectionRenderModel.update(this._terminal,b,x,M);const I=this._selectionRenderModel.viewportStartRow,N=this._selectionRenderModel.viewportEndRow,P=this._selectionRenderModel.viewportCappedStartRow,S=this._selectionRenderModel.viewportCappedEndRow;if(P>=this._bufferService.rows||S<0)return;const w=this._document.createDocumentFragment();if(M){const y=b[0]>x[0];w.appendChild(this._createSelectionElement(P,y?x[0]:b[0],y?b[0]:x[0],S-P+1))}else{const y=I===P?b[0]:0,L=P===N?x[0]:this._bufferService.cols;w.appendChild(this._createSelectionElement(P,y,L));const T=S-P-1;if(w.appendChild(this._createSelectionElement(P+1,0,this._bufferService.cols,T)),P!==S){const H=N===S?x[0]:this._bufferService.cols;w.appendChild(this._createSelectionElement(S,0,H))}}this._selectionContainer.appendChild(w)}_createSelectionElement(b,x,M,I=1){const N=this._document.createElement("div"),P=x*this.dimensions.css.cell.width;let S=this.dimensions.css.cell.width*(M-x);return P+S>this.dimensions.css.canvas.width&&(S=this.dimensions.css.canvas.width-P),N.style.height=I*this.dimensions.css.cell.height+"px",N.style.top=b*this.dimensions.css.cell.height+"px",N.style.left=`${P}px`,N.style.width=`${S}px`,N}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const b of this._rowElements)b.replaceChildren()}renderRows(b,x){const M=this._bufferService.buffer,I=M.ybase+M.y,N=Math.min(M.x,this._bufferService.cols-1),P=this._optionsService.rawOptions.cursorBlink,S=this._optionsService.rawOptions.cursorStyle,w=this._optionsService.rawOptions.cursorInactiveStyle;for(let y=b;y<=x;y++){const L=y+M.ydisp,T=this._rowElements[y],H=M.lines.get(L);if(!T||!H)break;T.replaceChildren(...this._rowFactory.createRow(H,L,L===I,S,w,N,P,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${v}${this._terminalClass}`}_handleLinkHover(b){this._setCellUnderline(b.x1,b.x2,b.y1,b.y2,b.cols,!0)}_handleLinkLeave(b){this._setCellUnderline(b.x1,b.x2,b.y1,b.y2,b.cols,!1)}_setCellUnderline(b,x,M,I,N,P){M<0&&(b=0),I<0&&(x=0);const S=this._bufferService.rows-1;M=Math.max(Math.min(M,S),0),I=Math.max(Math.min(I,S),0),N=Math.min(N,this._bufferService.cols);const w=this._bufferService.buffer,y=w.ybase+w.y,L=Math.min(w.x,N-1),T=this._optionsService.rawOptions.cursorBlink,H=this._optionsService.rawOptions.cursorStyle,U=this._optionsService.rawOptions.cursorInactiveStyle;for(let $=M;$<=I;++$){const R=$+w.ydisp,C=this._rowElements[$],A=w.lines.get(R);if(!C||!A)break;C.replaceChildren(...this._rowFactory.createRow(A,R,R===y,H,U,L,T,this.dimensions.css.cell.width,this._widthCache,P?$===M?b:0:-1,P?($===I?x:N)-1:-1))}}};r.DomRenderer=D=l([u(7,a.IInstantiationService),u(8,t.ICharSizeService),u(9,a.IOptionsService),u(10,a.IBufferService),u(11,t.ICoreBrowserService),u(12,t.IThemeService)],D)},3787:function(B,r,o){var l=this&&this.__decorate||function(_,g,c,m){var E,k=arguments.length,D=k<3?g:m===null?m=Object.getOwnPropertyDescriptor(g,c):m;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")D=Reflect.decorate(_,g,c,m);else for(var b=_.length-1;b>=0;b--)(E=_[b])&&(D=(k<3?E(D):k>3?E(g,c,D):E(g,c))||D);return k>3&&D&&Object.defineProperty(g,c,D),D},u=this&&this.__param||function(_,g){return function(c,m){g(c,m,_)}};Object.defineProperty(r,"__esModule",{value:!0}),r.DomRendererRowFactory=void 0;const n=o(2223),d=o(643),f=o(511),p=o(2585),h=o(8055),t=o(4725),s=o(4269),e=o(6171),i=o(3734);let a=r.DomRendererRowFactory=class{constructor(_,g,c,m,E,k,D){this._document=_,this._characterJoinerService=g,this._optionsService=c,this._coreBrowserService=m,this._coreService=E,this._decorationService=k,this._themeService=D,this._workCell=new f.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(_,g,c){this._selectionStart=_,this._selectionEnd=g,this._columnSelectMode=c}createRow(_,g,c,m,E,k,D,b,x,M,I){const N=[],P=this._characterJoinerService.getJoinedCharacters(g),S=this._themeService.colors;let w,y=_.getNoBgTrimmedLength();c&&y<k+1&&(y=k+1);let L=0,T="",H=0,U=0,$=0,R=!1,C=0,A=!1,O=0;const W=[],F=M!==-1&&I!==-1;for(let K=0;K<y;K++){_.loadCell(K,this._workCell);let X=this._workCell.getWidth();if(X===0)continue;let J=!1,Q=K,j=this._workCell;if(P.length>0&&K===P[0][0]){J=!0;const G=P.shift();j=new s.JoinedCellData(this._workCell,_.translateToString(!0,G[0],G[1]),G[1]-G[0]),Q=G[1]-1,X=j.getWidth()}const oe=this._isCellInSelection(K,g),ue=c&&K===k,_e=F&&K>=M&&K<=I;let fe=!1;this._decorationService.forEachDecorationAtCell(K,g,void 0,G=>{fe=!0});let le=j.getChars()||d.WHITESPACE_CELL_CHAR;if(le===" "&&(j.isUnderline()||j.isOverline())&&(le=" "),O=X*b-x.get(le,j.isBold(),j.isItalic()),w){if(L&&(oe&&A||!oe&&!A&&j.bg===H)&&(oe&&A&&S.selectionForeground||j.fg===U)&&j.extended.ext===$&&_e===R&&O===C&&!ue&&!J&&!fe){j.isInvisible()?T+=d.WHITESPACE_CELL_CHAR:T+=le,L++;continue}L&&(w.textContent=T),w=this._document.createElement("span"),L=0,T=""}else w=this._document.createElement("span");if(H=j.bg,U=j.fg,$=j.extended.ext,R=_e,C=O,A=oe,J&&k>=K&&k<=Q&&(k=K),!this._coreService.isCursorHidden&&ue&&this._coreService.isCursorInitialized){if(W.push("xterm-cursor"),this._coreBrowserService.isFocused)D&&W.push("xterm-cursor-blink"),W.push(m==="bar"?"xterm-cursor-bar":m==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(E)switch(E){case"outline":W.push("xterm-cursor-outline");break;case"block":W.push("xterm-cursor-block");break;case"bar":W.push("xterm-cursor-bar");break;case"underline":W.push("xterm-cursor-underline")}}if(j.isBold()&&W.push("xterm-bold"),j.isItalic()&&W.push("xterm-italic"),j.isDim()&&W.push("xterm-dim"),T=j.isInvisible()?d.WHITESPACE_CELL_CHAR:j.getChars()||d.WHITESPACE_CELL_CHAR,j.isUnderline()&&(W.push(`xterm-underline-${j.extended.underlineStyle}`),T===" "&&(T=" "),!j.isUnderlineColorDefault()))if(j.isUnderlineColorRGB())w.style.textDecorationColor=`rgb(${i.AttributeData.toColorRGB(j.getUnderlineColor()).join(",")})`;else{let G=j.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&j.isBold()&&G<8&&(G+=8),w.style.textDecorationColor=S.ansi[G].css}j.isOverline()&&(W.push("xterm-overline"),T===" "&&(T=" ")),j.isStrikethrough()&&W.push("xterm-strikethrough"),_e&&(w.style.textDecoration="underline");let ee=j.getFgColor(),ae=j.getFgColorMode(),ie=j.getBgColor(),he=j.getBgColorMode();const ve=!!j.isInverse();if(ve){const G=ee;ee=ie,ie=G;const ke=ae;ae=he,he=ke}let se,de,re,ce=!1;switch(this._decorationService.forEachDecorationAtCell(K,g,void 0,G=>{G.options.layer!=="top"&&ce||(G.backgroundColorRGB&&(he=50331648,ie=G.backgroundColorRGB.rgba>>8&16777215,se=G.backgroundColorRGB),G.foregroundColorRGB&&(ae=50331648,ee=G.foregroundColorRGB.rgba>>8&16777215,de=G.foregroundColorRGB),ce=G.options.layer==="top")}),!ce&&oe&&(se=this._coreBrowserService.isFocused?S.selectionBackgroundOpaque:S.selectionInactiveBackgroundOpaque,ie=se.rgba>>8&16777215,he=50331648,ce=!0,S.selectionForeground&&(ae=50331648,ee=S.selectionForeground.rgba>>8&16777215,de=S.selectionForeground)),ce&&W.push("xterm-decoration-top"),he){case 16777216:case 33554432:re=S.ansi[ie],W.push(`xterm-bg-${ie}`);break;case 50331648:re=h.channels.toColor(ie>>16,ie>>8&255,255&ie),this._addStyle(w,`background-color:#${v((ie>>>0).toString(16),"0",6)}`);break;default:ve?(re=S.foreground,W.push(`xterm-bg-${n.INVERTED_DEFAULT_COLOR}`)):re=S.background}switch(se||j.isDim()&&(se=h.color.multiplyOpacity(re,.5)),ae){case 16777216:case 33554432:j.isBold()&&ee<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(ee+=8),this._applyMinimumContrast(w,re,S.ansi[ee],j,se,void 0)||W.push(`xterm-fg-${ee}`);break;case 50331648:const G=h.channels.toColor(ee>>16&255,ee>>8&255,255&ee);this._applyMinimumContrast(w,re,G,j,se,de)||this._addStyle(w,`color:#${v(ee.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(w,re,S.foreground,j,se,de)||ve&&W.push(`xterm-fg-${n.INVERTED_DEFAULT_COLOR}`)}W.length&&(w.className=W.join(" "),W.length=0),ue||J||fe?w.textContent=T:L++,O!==this.defaultSpacing&&(w.style.letterSpacing=`${O}px`),N.push(w),K=Q}return w&&L&&(w.textContent=T),N}_applyMinimumContrast(_,g,c,m,E,k){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,e.treatGlyphAsBackgroundColor)(m.getCode()))return!1;const D=this._getContrastCache(m);let b;if(E||k||(b=D.getColor(g.rgba,c.rgba)),b===void 0){const x=this._optionsService.rawOptions.minimumContrastRatio/(m.isDim()?2:1);b=h.color.ensureContrastRatio(E||g,k||c,x),D.setColor((E||g).rgba,(k||c).rgba,b??null)}return!!b&&(this._addStyle(_,`color:${b.css}`),!0)}_getContrastCache(_){return _.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(_,g){_.setAttribute("style",`${_.getAttribute("style")||""}${g};`)}_isCellInSelection(_,g){const c=this._selectionStart,m=this._selectionEnd;return!(!c||!m)&&(this._columnSelectMode?c[0]<=m[0]?_>=c[0]&&g>=c[1]&&_<m[0]&&g<=m[1]:_<c[0]&&g>=c[1]&&_>=m[0]&&g<=m[1]:g>c[1]&&g<m[1]||c[1]===m[1]&&g===c[1]&&_>=c[0]&&_<m[0]||c[1]<m[1]&&g===m[1]&&_<m[0]||c[1]<m[1]&&g===c[1]&&_>=c[0])}};function v(_,g,c){for(;_.length<c;)_=g+_;return _}r.DomRendererRowFactory=a=l([u(1,t.ICharacterJoinerService),u(2,p.IOptionsService),u(3,t.ICoreBrowserService),u(4,p.ICoreService),u(5,p.IDecorationService),u(6,t.IThemeService)],a)},2550:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.WidthCache=void 0,r.WidthCache=class{constructor(o,l){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=o.createElement("div"),this._container.classList.add("xterm-width-cache-measure-container"),this._container.setAttribute("aria-hidden","true"),this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const u=o.createElement("span");u.classList.add("xterm-char-measure-element");const n=o.createElement("span");n.classList.add("xterm-char-measure-element"),n.style.fontWeight="bold";const d=o.createElement("span");d.classList.add("xterm-char-measure-element"),d.style.fontStyle="italic";const f=o.createElement("span");f.classList.add("xterm-char-measure-element"),f.style.fontWeight="bold",f.style.fontStyle="italic",this._measureElements=[u,n,d,f],this._container.appendChild(u),this._container.appendChild(n),this._container.appendChild(d),this._container.appendChild(f),l.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(o,l,u,n){o===this._font&&l===this._fontSize&&u===this._weight&&n===this._weightBold||(this._font=o,this._fontSize=l,this._weight=u,this._weightBold=n,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${u}`,this._measureElements[1].style.fontWeight=`${n}`,this._measureElements[2].style.fontWeight=`${u}`,this._measureElements[3].style.fontWeight=`${n}`,this.clear())}get(o,l,u){let n=0;if(!l&&!u&&o.length===1&&(n=o.charCodeAt(0))<256){if(this._flat[n]!==-9999)return this._flat[n];const p=this._measure(o,0);return p>0&&(this._flat[n]=p),p}let d=o;l&&(d+="B"),u&&(d+="I");let f=this._holey.get(d);if(f===void 0){let p=0;l&&(p|=1),u&&(p|=2),f=this._measure(o,p),f>0&&this._holey.set(d,f)}return f}_measure(o,l){const u=this._measureElements[l];return u.textContent=o.repeat(32),u.offsetWidth/32}}},2223:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.TEXT_BASELINE=r.DIM_OPACITY=r.INVERTED_DEFAULT_COLOR=void 0;const l=o(6114);r.INVERTED_DEFAULT_COLOR=257,r.DIM_OPACITY=.5,r.TEXT_BASELINE=l.isFirefox||l.isLegacyEdge?"bottom":"ideographic"},6171:(B,r)=>{function o(u){return 57508<=u&&u<=57558}function l(u){return u>=128512&&u<=128591||u>=127744&&u<=128511||u>=128640&&u<=128767||u>=9728&&u<=9983||u>=9984&&u<=10175||u>=65024&&u<=65039||u>=129280&&u<=129535||u>=127462&&u<=127487}Object.defineProperty(r,"__esModule",{value:!0}),r.computeNextVariantOffset=r.createRenderDimensions=r.treatGlyphAsBackgroundColor=r.allowRescaling=r.isEmoji=r.isRestrictedPowerlineGlyph=r.isPowerlineGlyph=r.throwIfFalsy=void 0,r.throwIfFalsy=function(u){if(!u)throw new Error("value must not be falsy");return u},r.isPowerlineGlyph=o,r.isRestrictedPowerlineGlyph=function(u){return 57520<=u&&u<=57527},r.isEmoji=l,r.allowRescaling=function(u,n,d,f){return n===1&&d>Math.ceil(1.5*f)&&u!==void 0&&u>255&&!l(u)&&!o(u)&&!function(p){return 57344<=p&&p<=63743}(u)},r.treatGlyphAsBackgroundColor=function(u){return o(u)||function(n){return 9472<=n&&n<=9631}(u)},r.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}},r.computeNextVariantOffset=function(u,n,d=0){return(u-(2*Math.round(n)-d))%(2*Math.round(n))}},6052:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.createSelectionRenderModel=void 0;class o{constructor(){this.clear()}clear(){this.hasSelection=!1,this.columnSelectMode=!1,this.viewportStartRow=0,this.viewportEndRow=0,this.viewportCappedStartRow=0,this.viewportCappedEndRow=0,this.startCol=0,this.endCol=0,this.selectionStart=void 0,this.selectionEnd=void 0}update(u,n,d,f=!1){if(this.selectionStart=n,this.selectionEnd=d,!n||!d||n[0]===d[0]&&n[1]===d[1])return void this.clear();const p=u.buffers.active.ydisp,h=n[1]-p,t=d[1]-p,s=Math.max(h,0),e=Math.min(t,u.rows-1);s>=u.rows||e<0?this.clear():(this.hasSelection=!0,this.columnSelectMode=f,this.viewportStartRow=h,this.viewportEndRow=t,this.viewportCappedStartRow=s,this.viewportCappedEndRow=e,this.startCol=n[0],this.endCol=d[0])}isCellSelected(u,n,d){return!!this.hasSelection&&(d-=u.buffer.active.viewportY,this.columnSelectMode?this.startCol<=this.endCol?n>=this.startCol&&d>=this.viewportCappedStartRow&&n<this.endCol&&d<=this.viewportCappedEndRow:n<this.startCol&&d>=this.viewportCappedStartRow&&n>=this.endCol&&d<=this.viewportCappedEndRow:d>this.viewportStartRow&&d<this.viewportEndRow||this.viewportStartRow===this.viewportEndRow&&d===this.viewportStartRow&&n>=this.startCol&&n<this.endCol||this.viewportStartRow<this.viewportEndRow&&d===this.viewportEndRow&&n<this.endCol||this.viewportStartRow<this.viewportEndRow&&d===this.viewportStartRow&&n>=this.startCol)}}r.createSelectionRenderModel=function(){return new o}},456:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.SelectionModel=void 0,r.SelectionModel=class{constructor(o){this._bufferService=o,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const o=this.selectionStart[0]+this.selectionStartLength;return o>this._bufferService.cols?o%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)-1]:[o%this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)]:[o,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const o=this.selectionStart[0]+this.selectionStartLength;return o>this._bufferService.cols?[o%this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)]:[Math.max(o,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const o=this.selectionStart,l=this.selectionEnd;return!(!o||!l)&&(o[1]>l[1]||o[1]===l[1]&&o[0]>l[0])}handleTrim(o){return this.selectionStart&&(this.selectionStart[1]-=o),this.selectionEnd&&(this.selectionEnd[1]-=o),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(B,r,o){var l=this&&this.__decorate||function(e,i,a,v){var _,g=arguments.length,c=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,a):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")c=Reflect.decorate(e,i,a,v);else for(var m=e.length-1;m>=0;m--)(_=e[m])&&(c=(g<3?_(c):g>3?_(i,a,c):_(i,a))||c);return g>3&&c&&Object.defineProperty(i,a,c),c},u=this&&this.__param||function(e,i){return function(a,v){i(a,v,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CharSizeService=void 0;const n=o(2585),d=o(8460),f=o(844);let p=r.CharSizeService=class extends f.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(e,i,a){super(),this._optionsService=a,this.width=0,this.height=0,this._onCharSizeChange=this.register(new d.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event;try{this._measureStrategy=this.register(new s(this._optionsService))}catch{this._measureStrategy=this.register(new t(e,i,this._optionsService))}this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const e=this._measureStrategy.measure();e.width===this.width&&e.height===this.height||(this.width=e.width,this.height=e.height,this._onCharSizeChange.fire())}};r.CharSizeService=p=l([u(2,n.IOptionsService)],p);class h extends f.Disposable{constructor(){super(...arguments),this._result={width:0,height:0}}_validateAndSet(i,a){i!==void 0&&i>0&&a!==void 0&&a>0&&(this._result.width=i,this._result.height=a)}}class t extends h{constructor(i,a,v){super(),this._document=i,this._parentElement=a,this._optionsService=v,this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){return this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`,this._validateAndSet(Number(this._measureElement.offsetWidth)/32,Number(this._measureElement.offsetHeight)),this._result}}class s extends h{constructor(i){super(),this._optionsService=i,this._canvas=new OffscreenCanvas(100,100),this._ctx=this._canvas.getContext("2d");const a=this._ctx.measureText("W");if(!("width"in a&&"fontBoundingBoxAscent"in a&&"fontBoundingBoxDescent"in a))throw new Error("Required font metrics not supported")}measure(){this._ctx.font=`${this._optionsService.rawOptions.fontSize}px ${this._optionsService.rawOptions.fontFamily}`;const i=this._ctx.measureText("W");return this._validateAndSet(i.width,i.fontBoundingBoxAscent+i.fontBoundingBoxDescent),this._result}}},4269:function(B,r,o){var l=this&&this.__decorate||function(s,e,i,a){var v,_=arguments.length,g=_<3?e:a===null?a=Object.getOwnPropertyDescriptor(e,i):a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")g=Reflect.decorate(s,e,i,a);else for(var c=s.length-1;c>=0;c--)(v=s[c])&&(g=(_<3?v(g):_>3?v(e,i,g):v(e,i))||g);return _>3&&g&&Object.defineProperty(e,i,g),g},u=this&&this.__param||function(s,e){return function(i,a){e(i,a,s)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CharacterJoinerService=r.JoinedCellData=void 0;const n=o(3734),d=o(643),f=o(511),p=o(2585);class h extends n.AttributeData{constructor(e,i,a){super(),this.content=0,this.combinedData="",this.fg=e.fg,this.bg=e.bg,this.combinedData=i,this._width=a}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(e){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}r.JoinedCellData=h;let t=r.CharacterJoinerService=class Ee{constructor(e){this._bufferService=e,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new f.CellData}register(e){const i={id:this._nextCharacterJoinerId++,handler:e};return this._characterJoiners.push(i),i.id}deregister(e){for(let i=0;i<this._characterJoiners.length;i++)if(this._characterJoiners[i].id===e)return this._characterJoiners.splice(i,1),!0;return!1}getJoinedCharacters(e){if(this._characterJoiners.length===0)return[];const i=this._bufferService.buffer.lines.get(e);if(!i||i.length===0)return[];const a=[],v=i.translateToString(!0);let _=0,g=0,c=0,m=i.getFg(0),E=i.getBg(0);for(let k=0;k<i.getTrimmedLength();k++)if(i.loadCell(k,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==m||this._workCell.bg!==E){if(k-_>1){const D=this._getJoinedRanges(v,c,g,i,_);for(let b=0;b<D.length;b++)a.push(D[b])}_=k,c=g,m=this._workCell.fg,E=this._workCell.bg}g+=this._workCell.getChars().length||d.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-_>1){const k=this._getJoinedRanges(v,c,g,i,_);for(let D=0;D<k.length;D++)a.push(k[D])}return a}_getJoinedRanges(e,i,a,v,_){const g=e.substring(i,a);let c=[];try{c=this._characterJoiners[0].handler(g)}catch(m){console.error(m)}for(let m=1;m<this._characterJoiners.length;m++)try{const E=this._characterJoiners[m].handler(g);for(let k=0;k<E.length;k++)Ee._mergeRanges(c,E[k])}catch(E){console.error(E)}return this._stringRangesToCellRanges(c,v,_),c}_stringRangesToCellRanges(e,i,a){let v=0,_=!1,g=0,c=e[v];if(c){for(let m=a;m<this._bufferService.cols;m++){const E=i.getWidth(m),k=i.getString(m).length||d.WHITESPACE_CELL_CHAR.length;if(E!==0){if(!_&&c[0]<=g&&(c[0]=m,_=!0),c[1]<=g){if(c[1]=m,c=e[++v],!c)break;c[0]<=g?(c[0]=m,_=!0):_=!1}g+=k}}c&&(c[1]=this._bufferService.cols)}}static _mergeRanges(e,i){let a=!1;for(let v=0;v<e.length;v++){const _=e[v];if(a){if(i[1]<=_[0])return e[v-1][1]=i[1],e;if(i[1]<=_[1])return e[v-1][1]=Math.max(i[1],_[1]),e.splice(v,1),e;e.splice(v,1),v--}else{if(i[1]<=_[0])return e.splice(v,0,i),e;if(i[1]<=_[1])return _[0]=Math.min(i[0],_[0]),e;i[0]<_[1]&&(_[0]=Math.min(i[0],_[0]),a=!0)}}return a?e[e.length-1][1]=i[1]:e.push(i),e}};r.CharacterJoinerService=t=l([u(0,p.IBufferService)],t)},5114:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CoreBrowserService=void 0;const l=o(844),u=o(8460),n=o(3656);class d extends l.Disposable{constructor(h,t,s){super(),this._textarea=h,this._window=t,this.mainDocument=s,this._isFocused=!1,this._cachedIsFocused=void 0,this._screenDprMonitor=new f(this._window),this._onDprChange=this.register(new u.EventEmitter),this.onDprChange=this._onDprChange.event,this._onWindowChange=this.register(new u.EventEmitter),this.onWindowChange=this._onWindowChange.event,this.register(this.onWindowChange(e=>this._screenDprMonitor.setWindow(e))),this.register((0,u.forwardEvent)(this._screenDprMonitor.onDprChange,this._onDprChange)),this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get window(){return this._window}set window(h){this._window!==h&&(this._window=h,this._onWindowChange.fire(this._window))}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}r.CoreBrowserService=d;class f extends l.Disposable{constructor(h){super(),this._parentWindow=h,this._windowResizeListener=this.register(new l.MutableDisposable),this._onDprChange=this.register(new u.EventEmitter),this.onDprChange=this._onDprChange.event,this._outerListener=()=>this._setDprAndFireIfDiffers(),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._updateDpr(),this._setWindowResizeListener(),this.register((0,l.toDisposable)(()=>this.clearListener()))}setWindow(h){this._parentWindow=h,this._setWindowResizeListener(),this._setDprAndFireIfDiffers()}_setWindowResizeListener(){this._windowResizeListener.value=(0,n.addDisposableDomListener)(this._parentWindow,"resize",()=>this._setDprAndFireIfDiffers())}_setDprAndFireIfDiffers(){this._parentWindow.devicePixelRatio!==this._currentDevicePixelRatio&&this._onDprChange.fire(this._parentWindow.devicePixelRatio),this._updateDpr()}_updateDpr(){this._outerListener&&(this._resolutionMediaMatchList?.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._outerListener=void 0)}}},779:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.LinkProviderService=void 0;const l=o(844);class u extends l.Disposable{constructor(){super(),this.linkProviders=[],this.register((0,l.toDisposable)(()=>this.linkProviders.length=0))}registerLinkProvider(d){return this.linkProviders.push(d),{dispose:()=>{const f=this.linkProviders.indexOf(d);f!==-1&&this.linkProviders.splice(f,1)}}}}r.LinkProviderService=u},8934:function(B,r,o){var l=this&&this.__decorate||function(p,h,t,s){var e,i=arguments.length,a=i<3?h:s===null?s=Object.getOwnPropertyDescriptor(h,t):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")a=Reflect.decorate(p,h,t,s);else for(var v=p.length-1;v>=0;v--)(e=p[v])&&(a=(i<3?e(a):i>3?e(h,t,a):e(h,t))||a);return i>3&&a&&Object.defineProperty(h,t,a),a},u=this&&this.__param||function(p,h){return function(t,s){h(t,s,p)}};Object.defineProperty(r,"__esModule",{value:!0}),r.MouseService=void 0;const n=o(4725),d=o(9806);let f=r.MouseService=class{constructor(p,h){this._renderService=p,this._charSizeService=h}getCoords(p,h,t,s,e){return(0,d.getCoords)(window,p,h,t,s,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,e)}getMouseReportCoords(p,h){const t=(0,d.getCoordsRelativeToElement)(window,p,h);if(this._charSizeService.hasValidSize)return t[0]=Math.min(Math.max(t[0],0),this._renderService.dimensions.css.canvas.width-1),t[1]=Math.min(Math.max(t[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(t[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(t[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(t[0]),y:Math.floor(t[1])}}};r.MouseService=f=l([u(0,n.IRenderService),u(1,n.ICharSizeService)],f)},3230:function(B,r,o){var l=this&&this.__decorate||function(e,i,a,v){var _,g=arguments.length,c=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,a):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")c=Reflect.decorate(e,i,a,v);else for(var m=e.length-1;m>=0;m--)(_=e[m])&&(c=(g<3?_(c):g>3?_(i,a,c):_(i,a))||c);return g>3&&c&&Object.defineProperty(i,a,c),c},u=this&&this.__param||function(e,i){return function(a,v){i(a,v,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.RenderService=void 0;const n=o(6193),d=o(4725),f=o(8460),p=o(844),h=o(7226),t=o(2585);let s=r.RenderService=class extends p.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(e,i,a,v,_,g,c,m){super(),this._rowCount=e,this._charSizeService=v,this._renderer=this.register(new p.MutableDisposable),this._pausedResizeTask=new h.DebouncedIdleTask,this._observerDisposable=this.register(new p.MutableDisposable),this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new f.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new f.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new f.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new f.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new n.RenderDebouncer((E,k)=>this._renderRows(E,k),c),this.register(this._renderDebouncer),this.register(c.onDprChange(()=>this.handleDevicePixelRatioChange())),this.register(g.onResize(()=>this._fullRefresh())),this.register(g.buffers.onBufferActivate(()=>this._renderer.value?.clear())),this.register(a.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(_.onDecorationRegistered(()=>this._fullRefresh())),this.register(_.onDecorationRemoved(()=>this._fullRefresh())),this.register(a.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio","rescaleOverlappingGlyphs"],()=>{this.clear(),this.handleResize(g.cols,g.rows),this._fullRefresh()})),this.register(a.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(g.buffer.y,g.buffer.y,!0))),this.register(m.onChangeColors(()=>this._fullRefresh())),this._registerIntersectionObserver(c.window,i),this.register(c.onWindowChange(E=>this._registerIntersectionObserver(E,i)))}_registerIntersectionObserver(e,i){if("IntersectionObserver"in e){const a=new e.IntersectionObserver(v=>this._handleIntersectionChange(v[v.length-1]),{threshold:0});a.observe(i),this._observerDisposable.value=(0,p.toDisposable)(()=>a.disconnect())}}_handleIntersectionChange(e){this._isPaused=e.isIntersecting===void 0?e.intersectionRatio===0:!e.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(e,i,a=!1){this._isPaused?this._needsFullRefresh=!0:(a||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(e,i,this._rowCount))}_renderRows(e,i){this._renderer.value&&(e=Math.min(e,this._rowCount-1),i=Math.min(i,this._rowCount-1),this._renderer.value.renderRows(e,i),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:e,end:i}),this._onRender.fire({start:e,end:i}),this._isNextRenderRedrawOnly=!0)}resize(e,i){this._rowCount=i,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(e){this._renderer.value=e,this._renderer.value&&(this._renderer.value.onRequestRedraw(i=>this.refreshRows(i.start,i.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh())}addRefreshCallback(e){return this._renderDebouncer.addRefreshCallback(e)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){this._renderer.value&&(this._renderer.value.clearTextureAtlas?.(),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(e,i){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>this._renderer.value?.handleResize(e,i)):this._renderer.value.handleResize(e,i),this._fullRefresh())}handleCharSizeChanged(){this._renderer.value?.handleCharSizeChanged()}handleBlur(){this._renderer.value?.handleBlur()}handleFocus(){this._renderer.value?.handleFocus()}handleSelectionChanged(e,i,a){this._selectionState.start=e,this._selectionState.end=i,this._selectionState.columnSelectMode=a,this._renderer.value?.handleSelectionChanged(e,i,a)}handleCursorMove(){this._renderer.value?.handleCursorMove()}clear(){this._renderer.value?.clear()}};r.RenderService=s=l([u(2,t.IOptionsService),u(3,d.ICharSizeService),u(4,t.IDecorationService),u(5,t.IBufferService),u(6,d.ICoreBrowserService),u(7,d.IThemeService)],s)},9312:function(B,r,o){var l=this&&this.__decorate||function(c,m,E,k){var D,b=arguments.length,x=b<3?m:k===null?k=Object.getOwnPropertyDescriptor(m,E):k;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")x=Reflect.decorate(c,m,E,k);else for(var M=c.length-1;M>=0;M--)(D=c[M])&&(x=(b<3?D(x):b>3?D(m,E,x):D(m,E))||x);return b>3&&x&&Object.defineProperty(m,E,x),x},u=this&&this.__param||function(c,m){return function(E,k){m(E,k,c)}};Object.defineProperty(r,"__esModule",{value:!0}),r.SelectionService=void 0;const n=o(9806),d=o(9504),f=o(456),p=o(4725),h=o(8460),t=o(844),s=o(6114),e=o(4841),i=o(511),a=o(2585),v=" ",_=new RegExp(v,"g");let g=r.SelectionService=class extends t.Disposable{constructor(c,m,E,k,D,b,x,M,I){super(),this._element=c,this._screenElement=m,this._linkifier=E,this._bufferService=k,this._coreService=D,this._mouseService=b,this._optionsService=x,this._renderService=M,this._coreBrowserService=I,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new i.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new h.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new h.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new h.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new h.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=N=>this._handleMouseMove(N),this._mouseUpListener=N=>this._handleMouseUp(N),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(N=>this._handleTrim(N)),this.register(this._bufferService.buffers.onBufferActivate(N=>this._handleBufferActivate(N))),this.enable(),this._model=new f.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,t.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const c=this._model.finalSelectionStart,m=this._model.finalSelectionEnd;return!(!c||!m||c[0]===m[0]&&c[1]===m[1])}get selectionText(){const c=this._model.finalSelectionStart,m=this._model.finalSelectionEnd;if(!c||!m)return"";const E=this._bufferService.buffer,k=[];if(this._activeSelectionMode===3){if(c[0]===m[0])return"";const D=c[0]<m[0]?c[0]:m[0],b=c[0]<m[0]?m[0]:c[0];for(let x=c[1];x<=m[1];x++){const M=E.translateBufferLineToString(x,!0,D,b);k.push(M)}}else{const D=c[1]===m[1]?m[0]:void 0;k.push(E.translateBufferLineToString(c[1],!0,c[0],D));for(let b=c[1]+1;b<=m[1]-1;b++){const x=E.lines.get(b),M=E.translateBufferLineToString(b,!0);x?.isWrapped?k[k.length-1]+=M:k.push(M)}if(c[1]!==m[1]){const b=E.lines.get(m[1]),x=E.translateBufferLineToString(m[1],!0,0,m[0]);b&&b.isWrapped?k[k.length-1]+=x:k.push(x)}}return k.map(D=>D.replace(_," ")).join(s.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(c){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),s.isLinux&&c&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(c){const m=this._getMouseBufferCoords(c),E=this._model.finalSelectionStart,k=this._model.finalSelectionEnd;return!!(E&&k&&m)&&this._areCoordsInSelection(m,E,k)}isCellInSelection(c,m){const E=this._model.finalSelectionStart,k=this._model.finalSelectionEnd;return!(!E||!k)&&this._areCoordsInSelection([c,m],E,k)}_areCoordsInSelection(c,m,E){return c[1]>m[1]&&c[1]<E[1]||m[1]===E[1]&&c[1]===m[1]&&c[0]>=m[0]&&c[0]<E[0]||m[1]<E[1]&&c[1]===E[1]&&c[0]<E[0]||m[1]<E[1]&&c[1]===m[1]&&c[0]>=m[0]}_selectWordAtCursor(c,m){const E=this._linkifier.currentLink?.link?.range;if(E)return this._model.selectionStart=[E.start.x-1,E.start.y-1],this._model.selectionStartLength=(0,e.getRangeLength)(E,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const k=this._getMouseBufferCoords(c);return!!k&&(this._selectWordAt(k,m),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(c,m){this._model.clearSelection(),c=Math.max(c,0),m=Math.min(m,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,c],this._model.selectionEnd=[this._bufferService.cols,m],this.refresh(),this._onSelectionChange.fire()}_handleTrim(c){this._model.handleTrim(c)&&this.refresh()}_getMouseBufferCoords(c){const m=this._mouseService.getCoords(c,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(m)return m[0]--,m[1]--,m[1]+=this._bufferService.buffer.ydisp,m}_getMouseEventScrollAmount(c){let m=(0,n.getCoordsRelativeToElement)(this._coreBrowserService.window,c,this._screenElement)[1];const E=this._renderService.dimensions.css.canvas.height;return m>=0&&m<=E?0:(m>E&&(m-=E),m=Math.min(Math.max(m,-50),50),m/=50,m/Math.abs(m)+Math.round(14*m))}shouldForceSelection(c){return s.isMac?c.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:c.shiftKey}handleMouseDown(c){if(this._mouseDownTimeStamp=c.timeStamp,(c.button!==2||!this.hasSelection)&&c.button===0){if(!this._enabled){if(!this.shouldForceSelection(c))return;c.stopPropagation()}c.preventDefault(),this._dragScrollAmount=0,this._enabled&&c.shiftKey?this._handleIncrementalClick(c):c.detail===1?this._handleSingleClick(c):c.detail===2?this._handleDoubleClick(c):c.detail===3&&this._handleTripleClick(c),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(c){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(c))}_handleSingleClick(c){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(c)?3:0,this._model.selectionStart=this._getMouseBufferCoords(c),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const m=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);m&&m.length!==this._model.selectionStart[0]&&m.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(c){this._selectWordAtCursor(c,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(c){const m=this._getMouseBufferCoords(c);m&&(this._activeSelectionMode=2,this._selectLineAt(m[1]))}shouldColumnSelect(c){return c.altKey&&!(s.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(c){if(c.stopImmediatePropagation(),!this._model.selectionStart)return;const m=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(c),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(c),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const E=this._bufferService.buffer;if(this._model.selectionEnd[1]<E.lines.length){const k=E.lines.get(this._model.selectionEnd[1]);k&&k.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]<this._bufferService.cols&&this._model.selectionEnd[0]++}m&&m[0]===this._model.selectionEnd[0]&&m[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const c=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(c.ydisp+this._bufferService.rows,c.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=c.ydisp),this.refresh()}}_handleMouseUp(c){const m=c.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&m<500&&c.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const E=this._mouseService.getCoords(c,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(E&&E[0]!==void 0&&E[1]!==void 0){const k=(0,d.moveToCellSequence)(E[0]-1,E[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(k,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const c=this._model.finalSelectionStart,m=this._model.finalSelectionEnd,E=!(!c||!m||c[0]===m[0]&&c[1]===m[1]);E?c&&m&&(this._oldSelectionStart&&this._oldSelectionEnd&&c[0]===this._oldSelectionStart[0]&&c[1]===this._oldSelectionStart[1]&&m[0]===this._oldSelectionEnd[0]&&m[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(c,m,E)):this._oldHasSelection&&this._fireOnSelectionChange(c,m,E)}_fireOnSelectionChange(c,m,E){this._oldSelectionStart=c,this._oldSelectionEnd=m,this._oldHasSelection=E,this._onSelectionChange.fire()}_handleBufferActivate(c){this.clearSelection(),this._trimListener.dispose(),this._trimListener=c.activeBuffer.lines.onTrim(m=>this._handleTrim(m))}_convertViewportColToCharacterIndex(c,m){let E=m;for(let k=0;m>=k;k++){const D=c.loadCell(k,this._workCell).getChars().length;this._workCell.getWidth()===0?E--:D>1&&m!==k&&(E+=D-1)}return E}setSelection(c,m,E){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[c,m],this._model.selectionStartLength=E,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(c){this._isClickInSelection(c)||(this._selectWordAtCursor(c,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(c,m,E=!0,k=!0){if(c[0]>=this._bufferService.cols)return;const D=this._bufferService.buffer,b=D.lines.get(c[1]);if(!b)return;const x=D.translateBufferLineToString(c[1],!1);let M=this._convertViewportColToCharacterIndex(b,c[0]),I=M;const N=c[0]-M;let P=0,S=0,w=0,y=0;if(x.charAt(M)===" "){for(;M>0&&x.charAt(M-1)===" ";)M--;for(;I<x.length&&x.charAt(I+1)===" ";)I++}else{let H=c[0],U=c[0];b.getWidth(H)===0&&(P++,H--),b.getWidth(U)===2&&(S++,U++);const $=b.getString(U).length;for($>1&&(y+=$-1,I+=$-1);H>0&&M>0&&!this._isCharWordSeparator(b.loadCell(H-1,this._workCell));){b.loadCell(H-1,this._workCell);const R=this._workCell.getChars().length;this._workCell.getWidth()===0?(P++,H--):R>1&&(w+=R-1,M-=R-1),M--,H--}for(;U<b.length&&I+1<x.length&&!this._isCharWordSeparator(b.loadCell(U+1,this._workCell));){b.loadCell(U+1,this._workCell);const R=this._workCell.getChars().length;this._workCell.getWidth()===2?(S++,U++):R>1&&(y+=R-1,I+=R-1),I++,U++}}I++;let L=M+N-P+w,T=Math.min(this._bufferService.cols,I-M+P+S-w-y);if(m||x.slice(M,I).trim()!==""){if(E&&L===0&&b.getCodePoint(0)!==32){const H=D.lines.get(c[1]-1);if(H&&b.isWrapped&&H.getCodePoint(this._bufferService.cols-1)!==32){const U=this._getWordAt([this._bufferService.cols-1,c[1]-1],!1,!0,!1);if(U){const $=this._bufferService.cols-U.start;L-=$,T+=$}}}if(k&&L+T===this._bufferService.cols&&b.getCodePoint(this._bufferService.cols-1)!==32){const H=D.lines.get(c[1]+1);if(H?.isWrapped&&H.getCodePoint(0)!==32){const U=this._getWordAt([0,c[1]+1],!1,!1,!0);U&&(T+=U.length)}}return{start:L,length:T}}}_selectWordAt(c,m){const E=this._getWordAt(c,m);if(E){for(;E.start<0;)E.start+=this._bufferService.cols,c[1]--;this._model.selectionStart=[E.start,c[1]],this._model.selectionStartLength=E.length}}_selectToWordAt(c){const m=this._getWordAt(c,!0);if(m){let E=c[1];for(;m.start<0;)m.start+=this._bufferService.cols,E--;if(!this._model.areSelectionValuesReversed())for(;m.start+m.length>this._bufferService.cols;)m.length-=this._bufferService.cols,E++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?m.start:m.start+m.length,E]}}_isCharWordSeparator(c){return c.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(c.getChars())>=0}_selectLineAt(c){const m=this._bufferService.buffer.getWrappedRangeForLine(c),E={start:{x:0,y:m.first},end:{x:this._bufferService.cols-1,y:m.last}};this._model.selectionStart=[0,m.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,e.getRangeLength)(E,this._bufferService.cols)}};r.SelectionService=g=l([u(3,a.IBufferService),u(4,a.ICoreService),u(5,p.IMouseService),u(6,a.IOptionsService),u(7,p.IRenderService),u(8,p.ICoreBrowserService)],g)},4725:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ILinkProviderService=r.IThemeService=r.ICharacterJoinerService=r.ISelectionService=r.IRenderService=r.IMouseService=r.ICoreBrowserService=r.ICharSizeService=void 0;const l=o(8343);r.ICharSizeService=(0,l.createDecorator)("CharSizeService"),r.ICoreBrowserService=(0,l.createDecorator)("CoreBrowserService"),r.IMouseService=(0,l.createDecorator)("MouseService"),r.IRenderService=(0,l.createDecorator)("RenderService"),r.ISelectionService=(0,l.createDecorator)("SelectionService"),r.ICharacterJoinerService=(0,l.createDecorator)("CharacterJoinerService"),r.IThemeService=(0,l.createDecorator)("ThemeService"),r.ILinkProviderService=(0,l.createDecorator)("LinkProviderService")},6731:function(B,r,o){var l=this&&this.__decorate||function(g,c,m,E){var k,D=arguments.length,b=D<3?c:E===null?E=Object.getOwnPropertyDescriptor(c,m):E;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(g,c,m,E);else for(var x=g.length-1;x>=0;x--)(k=g[x])&&(b=(D<3?k(b):D>3?k(c,m,b):k(c,m))||b);return D>3&&b&&Object.defineProperty(c,m,b),b},u=this&&this.__param||function(g,c){return function(m,E){c(m,E,g)}};Object.defineProperty(r,"__esModule",{value:!0}),r.ThemeService=r.DEFAULT_ANSI_COLORS=void 0;const n=o(7239),d=o(8055),f=o(8460),p=o(844),h=o(2585),t=d.css.toColor("#ffffff"),s=d.css.toColor("#000000"),e=d.css.toColor("#ffffff"),i=d.css.toColor("#000000"),a={css:"rgba(255, 255, 255, 0.3)",rgba:**********};r.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const g=[d.css.toColor("#2e3436"),d.css.toColor("#cc0000"),d.css.toColor("#4e9a06"),d.css.toColor("#c4a000"),d.css.toColor("#3465a4"),d.css.toColor("#75507b"),d.css.toColor("#06989a"),d.css.toColor("#d3d7cf"),d.css.toColor("#555753"),d.css.toColor("#ef2929"),d.css.toColor("#8ae234"),d.css.toColor("#fce94f"),d.css.toColor("#729fcf"),d.css.toColor("#ad7fa8"),d.css.toColor("#34e2e2"),d.css.toColor("#eeeeec")],c=[0,95,135,175,215,255];for(let m=0;m<216;m++){const E=c[m/36%6|0],k=c[m/6%6|0],D=c[m%6];g.push({css:d.channels.toCss(E,k,D),rgba:d.channels.toRgba(E,k,D)})}for(let m=0;m<24;m++){const E=8+10*m;g.push({css:d.channels.toCss(E,E,E),rgba:d.channels.toRgba(E,E,E)})}return g})());let v=r.ThemeService=class extends p.Disposable{get colors(){return this._colors}constructor(g){super(),this._optionsService=g,this._contrastCache=new n.ColorContrastCache,this._halfContrastCache=new n.ColorContrastCache,this._onChangeColors=this.register(new f.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:t,background:s,cursor:e,cursorAccent:i,selectionForeground:void 0,selectionBackgroundTransparent:a,selectionBackgroundOpaque:d.color.blend(s,a),selectionInactiveBackgroundTransparent:a,selectionInactiveBackgroundOpaque:d.color.blend(s,a),ansi:r.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(g={}){const c=this._colors;if(c.foreground=_(g.foreground,t),c.background=_(g.background,s),c.cursor=_(g.cursor,e),c.cursorAccent=_(g.cursorAccent,i),c.selectionBackgroundTransparent=_(g.selectionBackground,a),c.selectionBackgroundOpaque=d.color.blend(c.background,c.selectionBackgroundTransparent),c.selectionInactiveBackgroundTransparent=_(g.selectionInactiveBackground,c.selectionBackgroundTransparent),c.selectionInactiveBackgroundOpaque=d.color.blend(c.background,c.selectionInactiveBackgroundTransparent),c.selectionForeground=g.selectionForeground?_(g.selectionForeground,d.NULL_COLOR):void 0,c.selectionForeground===d.NULL_COLOR&&(c.selectionForeground=void 0),d.color.isOpaque(c.selectionBackgroundTransparent)&&(c.selectionBackgroundTransparent=d.color.opacity(c.selectionBackgroundTransparent,.3)),d.color.isOpaque(c.selectionInactiveBackgroundTransparent)&&(c.selectionInactiveBackgroundTransparent=d.color.opacity(c.selectionInactiveBackgroundTransparent,.3)),c.ansi=r.DEFAULT_ANSI_COLORS.slice(),c.ansi[0]=_(g.black,r.DEFAULT_ANSI_COLORS[0]),c.ansi[1]=_(g.red,r.DEFAULT_ANSI_COLORS[1]),c.ansi[2]=_(g.green,r.DEFAULT_ANSI_COLORS[2]),c.ansi[3]=_(g.yellow,r.DEFAULT_ANSI_COLORS[3]),c.ansi[4]=_(g.blue,r.DEFAULT_ANSI_COLORS[4]),c.ansi[5]=_(g.magenta,r.DEFAULT_ANSI_COLORS[5]),c.ansi[6]=_(g.cyan,r.DEFAULT_ANSI_COLORS[6]),c.ansi[7]=_(g.white,r.DEFAULT_ANSI_COLORS[7]),c.ansi[8]=_(g.brightBlack,r.DEFAULT_ANSI_COLORS[8]),c.ansi[9]=_(g.brightRed,r.DEFAULT_ANSI_COLORS[9]),c.ansi[10]=_(g.brightGreen,r.DEFAULT_ANSI_COLORS[10]),c.ansi[11]=_(g.brightYellow,r.DEFAULT_ANSI_COLORS[11]),c.ansi[12]=_(g.brightBlue,r.DEFAULT_ANSI_COLORS[12]),c.ansi[13]=_(g.brightMagenta,r.DEFAULT_ANSI_COLORS[13]),c.ansi[14]=_(g.brightCyan,r.DEFAULT_ANSI_COLORS[14]),c.ansi[15]=_(g.brightWhite,r.DEFAULT_ANSI_COLORS[15]),g.extendedAnsi){const m=Math.min(c.ansi.length-16,g.extendedAnsi.length);for(let E=0;E<m;E++)c.ansi[E+16]=_(g.extendedAnsi[E],r.DEFAULT_ANSI_COLORS[E+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(g){this._restoreColor(g),this._onChangeColors.fire(this.colors)}_restoreColor(g){if(g!==void 0)switch(g){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[g]=this._restoreColors.ansi[g]}else for(let c=0;c<this._restoreColors.ansi.length;++c)this._colors.ansi[c]=this._restoreColors.ansi[c]}modifyColors(g){g(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function _(g,c){if(g!==void 0)try{return d.css.toColor(g)}catch{}return c}r.ThemeService=v=l([u(0,h.IOptionsService)],v)},6349:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CircularList=void 0;const l=o(8460),u=o(844);class n extends u.Disposable{constructor(f){super(),this._maxLength=f,this.onDeleteEmitter=this.register(new l.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new l.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new l.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(f){if(this._maxLength===f)return;const p=new Array(f);for(let h=0;h<Math.min(f,this.length);h++)p[h]=this._array[this._getCyclicIndex(h)];this._array=p,this._maxLength=f,this._startIndex=0}get length(){return this._length}set length(f){if(f>this._length)for(let p=this._length;p<f;p++)this._array[p]=void 0;this._length=f}get(f){return this._array[this._getCyclicIndex(f)]}set(f,p){this._array[this._getCyclicIndex(f)]=p}push(f){this._array[this._getCyclicIndex(this._length)]=f,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(f,p,...h){if(p){for(let t=f;t<this._length-p;t++)this._array[this._getCyclicIndex(t)]=this._array[this._getCyclicIndex(t+p)];this._length-=p,this.onDeleteEmitter.fire({index:f,amount:p})}for(let t=this._length-1;t>=f;t--)this._array[this._getCyclicIndex(t+h.length)]=this._array[this._getCyclicIndex(t)];for(let t=0;t<h.length;t++)this._array[this._getCyclicIndex(f+t)]=h[t];if(h.length&&this.onInsertEmitter.fire({index:f,amount:h.length}),this._length+h.length>this._maxLength){const t=this._length+h.length-this._maxLength;this._startIndex+=t,this._length=this._maxLength,this.onTrimEmitter.fire(t)}else this._length+=h.length}trimStart(f){f>this._length&&(f=this._length),this._startIndex+=f,this._length-=f,this.onTrimEmitter.fire(f)}shiftElements(f,p,h){if(!(p<=0)){if(f<0||f>=this._length)throw new Error("start argument out of range");if(f+h<0)throw new Error("Cannot shift elements in list beyond index 0");if(h>0){for(let s=p-1;s>=0;s--)this.set(f+s+h,this.get(f+s));const t=f+p+h-this._length;if(t>0)for(this._length+=t;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let t=0;t<p;t++)this.set(f+t+h,this.get(f+t))}}_getCyclicIndex(f){return(this._startIndex+f)%this._maxLength}}r.CircularList=n},1439:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.clone=void 0,r.clone=function o(l,u=5){if(typeof l!="object")return l;const n=Array.isArray(l)?[]:{};for(const d in l)n[d]=u<=1?l[d]:l[d]&&o(l[d],u-1);return n}},8055:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.contrastRatio=r.toPaddedHex=r.rgba=r.rgb=r.css=r.color=r.channels=r.NULL_COLOR=void 0;let o=0,l=0,u=0,n=0;var d,f,p,h,t;function s(i){const a=i.toString(16);return a.length<2?"0"+a:a}function e(i,a){return i<a?(a+.05)/(i+.05):(i+.05)/(a+.05)}r.NULL_COLOR={css:"#00000000",rgba:0},function(i){i.toCss=function(a,v,_,g){return g!==void 0?`#${s(a)}${s(v)}${s(_)}${s(g)}`:`#${s(a)}${s(v)}${s(_)}`},i.toRgba=function(a,v,_,g=255){return(a<<24|v<<16|_<<8|g)>>>0},i.toColor=function(a,v,_,g){return{css:i.toCss(a,v,_,g),rgba:i.toRgba(a,v,_,g)}}}(d||(r.channels=d={})),function(i){function a(v,_){return n=Math.round(255*_),[o,l,u]=t.toChannels(v.rgba),{css:d.toCss(o,l,u,n),rgba:d.toRgba(o,l,u,n)}}i.blend=function(v,_){if(n=(255&_.rgba)/255,n===1)return{css:_.css,rgba:_.rgba};const g=_.rgba>>24&255,c=_.rgba>>16&255,m=_.rgba>>8&255,E=v.rgba>>24&255,k=v.rgba>>16&255,D=v.rgba>>8&255;return o=E+Math.round((g-E)*n),l=k+Math.round((c-k)*n),u=D+Math.round((m-D)*n),{css:d.toCss(o,l,u),rgba:d.toRgba(o,l,u)}},i.isOpaque=function(v){return(255&v.rgba)==255},i.ensureContrastRatio=function(v,_,g){const c=t.ensureContrastRatio(v.rgba,_.rgba,g);if(c)return d.toColor(c>>24&255,c>>16&255,c>>8&255)},i.opaque=function(v){const _=(255|v.rgba)>>>0;return[o,l,u]=t.toChannels(_),{css:d.toCss(o,l,u),rgba:_}},i.opacity=a,i.multiplyOpacity=function(v,_){return n=255&v.rgba,a(v,n*_/255)},i.toColorRGB=function(v){return[v.rgba>>24&255,v.rgba>>16&255,v.rgba>>8&255]}}(f||(r.color=f={})),function(i){let a,v;try{const _=document.createElement("canvas");_.width=1,_.height=1;const g=_.getContext("2d",{willReadFrequently:!0});g&&(a=g,a.globalCompositeOperation="copy",v=a.createLinearGradient(0,0,1,1))}catch{}i.toColor=function(_){if(_.match(/#[\da-f]{3,8}/i))switch(_.length){case 4:return o=parseInt(_.slice(1,2).repeat(2),16),l=parseInt(_.slice(2,3).repeat(2),16),u=parseInt(_.slice(3,4).repeat(2),16),d.toColor(o,l,u);case 5:return o=parseInt(_.slice(1,2).repeat(2),16),l=parseInt(_.slice(2,3).repeat(2),16),u=parseInt(_.slice(3,4).repeat(2),16),n=parseInt(_.slice(4,5).repeat(2),16),d.toColor(o,l,u,n);case 7:return{css:_,rgba:(parseInt(_.slice(1),16)<<8|255)>>>0};case 9:return{css:_,rgba:parseInt(_.slice(1),16)>>>0}}const g=_.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(g)return o=parseInt(g[1]),l=parseInt(g[2]),u=parseInt(g[3]),n=Math.round(255*(g[5]===void 0?1:parseFloat(g[5]))),d.toColor(o,l,u,n);if(!a||!v)throw new Error("css.toColor: Unsupported css format");if(a.fillStyle=v,a.fillStyle=_,typeof a.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(a.fillRect(0,0,1,1),[o,l,u,n]=a.getImageData(0,0,1,1).data,n!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:d.toRgba(o,l,u,n),css:_}}}(p||(r.css=p={})),function(i){function a(v,_,g){const c=v/255,m=_/255,E=g/255;return .2126*(c<=.03928?c/12.92:Math.pow((c+.055)/1.055,2.4))+.7152*(m<=.03928?m/12.92:Math.pow((m+.055)/1.055,2.4))+.0722*(E<=.03928?E/12.92:Math.pow((E+.055)/1.055,2.4))}i.relativeLuminance=function(v){return a(v>>16&255,v>>8&255,255&v)},i.relativeLuminance2=a}(h||(r.rgb=h={})),function(i){function a(_,g,c){const m=_>>24&255,E=_>>16&255,k=_>>8&255;let D=g>>24&255,b=g>>16&255,x=g>>8&255,M=e(h.relativeLuminance2(D,b,x),h.relativeLuminance2(m,E,k));for(;M<c&&(D>0||b>0||x>0);)D-=Math.max(0,Math.ceil(.1*D)),b-=Math.max(0,Math.ceil(.1*b)),x-=Math.max(0,Math.ceil(.1*x)),M=e(h.relativeLuminance2(D,b,x),h.relativeLuminance2(m,E,k));return(D<<24|b<<16|x<<8|255)>>>0}function v(_,g,c){const m=_>>24&255,E=_>>16&255,k=_>>8&255;let D=g>>24&255,b=g>>16&255,x=g>>8&255,M=e(h.relativeLuminance2(D,b,x),h.relativeLuminance2(m,E,k));for(;M<c&&(D<255||b<255||x<255);)D=Math.min(255,D+Math.ceil(.1*(255-D))),b=Math.min(255,b+Math.ceil(.1*(255-b))),x=Math.min(255,x+Math.ceil(.1*(255-x))),M=e(h.relativeLuminance2(D,b,x),h.relativeLuminance2(m,E,k));return(D<<24|b<<16|x<<8|255)>>>0}i.blend=function(_,g){if(n=(255&g)/255,n===1)return g;const c=g>>24&255,m=g>>16&255,E=g>>8&255,k=_>>24&255,D=_>>16&255,b=_>>8&255;return o=k+Math.round((c-k)*n),l=D+Math.round((m-D)*n),u=b+Math.round((E-b)*n),d.toRgba(o,l,u)},i.ensureContrastRatio=function(_,g,c){const m=h.relativeLuminance(_>>8),E=h.relativeLuminance(g>>8);if(e(m,E)<c){if(E<m){const b=a(_,g,c),x=e(m,h.relativeLuminance(b>>8));if(x<c){const M=v(_,g,c);return x>e(m,h.relativeLuminance(M>>8))?b:M}return b}const k=v(_,g,c),D=e(m,h.relativeLuminance(k>>8));if(D<c){const b=a(_,g,c);return D>e(m,h.relativeLuminance(b>>8))?k:b}return k}},i.reduceLuminance=a,i.increaseLuminance=v,i.toChannels=function(_){return[_>>24&255,_>>16&255,_>>8&255,255&_]}}(t||(r.rgba=t={})),r.toPaddedHex=s,r.contrastRatio=e},8969:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CoreTerminal=void 0;const l=o(844),u=o(2585),n=o(4348),d=o(7866),f=o(744),p=o(7302),h=o(6975),t=o(8460),s=o(1753),e=o(1480),i=o(7994),a=o(9282),v=o(5435),_=o(5981),g=o(2660);let c=!1;class m extends l.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new t.EventEmitter),this._onScroll.event(k=>{this._onScrollApi?.fire(k.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(k){for(const D in k)this.optionsService.options[D]=k[D]}constructor(k){super(),this._windowsWrappingHeuristics=this.register(new l.MutableDisposable),this._onBinary=this.register(new t.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new t.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new t.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new t.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new t.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new t.EventEmitter),this._instantiationService=new n.InstantiationService,this.optionsService=this.register(new p.OptionsService(k)),this._instantiationService.setService(u.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(f.BufferService)),this._instantiationService.setService(u.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(d.LogService)),this._instantiationService.setService(u.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(h.CoreService)),this._instantiationService.setService(u.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(s.CoreMouseService)),this._instantiationService.setService(u.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(e.UnicodeService)),this._instantiationService.setService(u.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(i.CharsetService),this._instantiationService.setService(u.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(g.OscLinkService),this._instantiationService.setService(u.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new v.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,t.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,t.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,t.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,t.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(D=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(D=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new _.WriteBuffer((D,b)=>this._inputHandler.parse(D,b))),this.register((0,t.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(k,D){this._writeBuffer.write(k,D)}writeSync(k,D){this._logService.logLevel<=u.LogLevelEnum.WARN&&!c&&(this._logService.warn("writeSync is unreliable and will be removed soon."),c=!0),this._writeBuffer.writeSync(k,D)}input(k,D=!0){this.coreService.triggerDataEvent(k,D)}resize(k,D){isNaN(k)||isNaN(D)||(k=Math.max(k,f.MINIMUM_COLS),D=Math.max(D,f.MINIMUM_ROWS),this._bufferService.resize(k,D))}scroll(k,D=!1){this._bufferService.scroll(k,D)}scrollLines(k,D,b){this._bufferService.scrollLines(k,D,b)}scrollPages(k){this.scrollLines(k*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(k){const D=k-this._bufferService.buffer.ydisp;D!==0&&this.scrollLines(D)}registerEscHandler(k,D){return this._inputHandler.registerEscHandler(k,D)}registerDcsHandler(k,D){return this._inputHandler.registerDcsHandler(k,D)}registerCsiHandler(k,D){return this._inputHandler.registerCsiHandler(k,D)}registerOscHandler(k,D){return this._inputHandler.registerOscHandler(k,D)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let k=!1;const D=this.optionsService.rawOptions.windowsPty;D&&D.buildNumber!==void 0&&D.buildNumber!==void 0?k=D.backend==="conpty"&&D.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(k=!0),k?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const k=[];k.push(this.onLineFeed(a.updateWindowsModeWrappedState.bind(null,this._bufferService))),k.push(this.registerCsiHandler({final:"H"},()=>((0,a.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,l.toDisposable)(()=>{for(const D of k)D.dispose()})}}}r.CoreTerminal=m},8460:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.runAndSubscribe=r.forwardEvent=r.EventEmitter=void 0,r.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=o=>(this._listeners.push(o),{dispose:()=>{if(!this._disposed){for(let l=0;l<this._listeners.length;l++)if(this._listeners[l]===o)return void this._listeners.splice(l,1)}}})),this._event}fire(o,l){const u=[];for(let n=0;n<this._listeners.length;n++)u.push(this._listeners[n]);for(let n=0;n<u.length;n++)u[n].call(void 0,o,l)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},r.forwardEvent=function(o,l){return o(u=>l.fire(u))},r.runAndSubscribe=function(o,l){return l(void 0),o(u=>l(u))}},5435:function(B,r,o){var l=this&&this.__decorate||function(P,S,w,y){var L,T=arguments.length,H=T<3?S:y===null?y=Object.getOwnPropertyDescriptor(S,w):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")H=Reflect.decorate(P,S,w,y);else for(var U=P.length-1;U>=0;U--)(L=P[U])&&(H=(T<3?L(H):T>3?L(S,w,H):L(S,w))||H);return T>3&&H&&Object.defineProperty(S,w,H),H},u=this&&this.__param||function(P,S){return function(w,y){S(w,y,P)}};Object.defineProperty(r,"__esModule",{value:!0}),r.InputHandler=r.WindowsOptionsReportType=void 0;const n=o(2584),d=o(7116),f=o(2015),p=o(844),h=o(482),t=o(8437),s=o(8460),e=o(643),i=o(511),a=o(3734),v=o(2585),_=o(1480),g=o(6242),c=o(6351),m=o(5941),E={"(":0,")":1,"*":2,"+":3,"-":1,".":2},k=131072;function D(P,S){if(P>24)return S.setWinLines||!1;switch(P){case 1:return!!S.restoreWin;case 2:return!!S.minimizeWin;case 3:return!!S.setWinPosition;case 4:return!!S.setWinSizePixels;case 5:return!!S.raiseWin;case 6:return!!S.lowerWin;case 7:return!!S.refreshWin;case 8:return!!S.setWinSizeChars;case 9:return!!S.maximizeWin;case 10:return!!S.fullscreenWin;case 11:return!!S.getWinState;case 13:return!!S.getWinPosition;case 14:return!!S.getWinSizePixels;case 15:return!!S.getScreenSizePixels;case 16:return!!S.getCellSizePixels;case 18:return!!S.getWinSizeChars;case 19:return!!S.getScreenSizeChars;case 20:return!!S.getIconTitle;case 21:return!!S.getWinTitle;case 22:return!!S.pushTitle;case 23:return!!S.popTitle;case 24:return!!S.setWinLines}return!1}var b;(function(P){P[P.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",P[P.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(b||(r.WindowsOptionsReportType=b={}));let x=0;class M extends p.Disposable{getAttrData(){return this._curAttrData}constructor(S,w,y,L,T,H,U,$,R=new f.EscapeSequenceParser){super(),this._bufferService=S,this._charsetService=w,this._coreService=y,this._logService=L,this._optionsService=T,this._oscLinkService=H,this._coreMouseService=U,this._unicodeService=$,this._parser=R,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new h.StringToUtf32,this._utf8Decoder=new h.Utf8ToUtf32,this._workCell=new i.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=t.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=t.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new s.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new s.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new s.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new s.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new s.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new s.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new s.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new s.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new s.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new s.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new s.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new s.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new s.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new I(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(C=>this._activeBuffer=C.activeBuffer)),this._parser.setCsiHandlerFallback((C,A)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(C),params:A.toArray()})}),this._parser.setEscHandlerFallback(C=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(C)})}),this._parser.setExecuteHandlerFallback(C=>{this._logService.debug("Unknown EXECUTE code: ",{code:C})}),this._parser.setOscHandlerFallback((C,A,O)=>{this._logService.debug("Unknown OSC code: ",{identifier:C,action:A,data:O})}),this._parser.setDcsHandlerFallback((C,A,O)=>{A==="HOOK"&&(O=O.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(C),action:A,payload:O})}),this._parser.setPrintHandler((C,A,O)=>this.print(C,A,O)),this._parser.registerCsiHandler({final:"@"},C=>this.insertChars(C)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},C=>this.scrollLeft(C)),this._parser.registerCsiHandler({final:"A"},C=>this.cursorUp(C)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},C=>this.scrollRight(C)),this._parser.registerCsiHandler({final:"B"},C=>this.cursorDown(C)),this._parser.registerCsiHandler({final:"C"},C=>this.cursorForward(C)),this._parser.registerCsiHandler({final:"D"},C=>this.cursorBackward(C)),this._parser.registerCsiHandler({final:"E"},C=>this.cursorNextLine(C)),this._parser.registerCsiHandler({final:"F"},C=>this.cursorPrecedingLine(C)),this._parser.registerCsiHandler({final:"G"},C=>this.cursorCharAbsolute(C)),this._parser.registerCsiHandler({final:"H"},C=>this.cursorPosition(C)),this._parser.registerCsiHandler({final:"I"},C=>this.cursorForwardTab(C)),this._parser.registerCsiHandler({final:"J"},C=>this.eraseInDisplay(C,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},C=>this.eraseInDisplay(C,!0)),this._parser.registerCsiHandler({final:"K"},C=>this.eraseInLine(C,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},C=>this.eraseInLine(C,!0)),this._parser.registerCsiHandler({final:"L"},C=>this.insertLines(C)),this._parser.registerCsiHandler({final:"M"},C=>this.deleteLines(C)),this._parser.registerCsiHandler({final:"P"},C=>this.deleteChars(C)),this._parser.registerCsiHandler({final:"S"},C=>this.scrollUp(C)),this._parser.registerCsiHandler({final:"T"},C=>this.scrollDown(C)),this._parser.registerCsiHandler({final:"X"},C=>this.eraseChars(C)),this._parser.registerCsiHandler({final:"Z"},C=>this.cursorBackwardTab(C)),this._parser.registerCsiHandler({final:"`"},C=>this.charPosAbsolute(C)),this._parser.registerCsiHandler({final:"a"},C=>this.hPositionRelative(C)),this._parser.registerCsiHandler({final:"b"},C=>this.repeatPrecedingCharacter(C)),this._parser.registerCsiHandler({final:"c"},C=>this.sendDeviceAttributesPrimary(C)),this._parser.registerCsiHandler({prefix:">",final:"c"},C=>this.sendDeviceAttributesSecondary(C)),this._parser.registerCsiHandler({final:"d"},C=>this.linePosAbsolute(C)),this._parser.registerCsiHandler({final:"e"},C=>this.vPositionRelative(C)),this._parser.registerCsiHandler({final:"f"},C=>this.hVPosition(C)),this._parser.registerCsiHandler({final:"g"},C=>this.tabClear(C)),this._parser.registerCsiHandler({final:"h"},C=>this.setMode(C)),this._parser.registerCsiHandler({prefix:"?",final:"h"},C=>this.setModePrivate(C)),this._parser.registerCsiHandler({final:"l"},C=>this.resetMode(C)),this._parser.registerCsiHandler({prefix:"?",final:"l"},C=>this.resetModePrivate(C)),this._parser.registerCsiHandler({final:"m"},C=>this.charAttributes(C)),this._parser.registerCsiHandler({final:"n"},C=>this.deviceStatus(C)),this._parser.registerCsiHandler({prefix:"?",final:"n"},C=>this.deviceStatusPrivate(C)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},C=>this.softReset(C)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},C=>this.setCursorStyle(C)),this._parser.registerCsiHandler({final:"r"},C=>this.setScrollRegion(C)),this._parser.registerCsiHandler({final:"s"},C=>this.saveCursor(C)),this._parser.registerCsiHandler({final:"t"},C=>this.windowOptions(C)),this._parser.registerCsiHandler({final:"u"},C=>this.restoreCursor(C)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},C=>this.insertColumns(C)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},C=>this.deleteColumns(C)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},C=>this.selectProtected(C)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},C=>this.requestMode(C,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},C=>this.requestMode(C,!1)),this._parser.setExecuteHandler(n.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(n.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(n.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(n.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(n.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(n.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(n.C1.IND,()=>this.index()),this._parser.setExecuteHandler(n.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(n.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new g.OscHandler(C=>(this.setTitle(C),this.setIconName(C),!0))),this._parser.registerOscHandler(1,new g.OscHandler(C=>this.setIconName(C))),this._parser.registerOscHandler(2,new g.OscHandler(C=>this.setTitle(C))),this._parser.registerOscHandler(4,new g.OscHandler(C=>this.setOrReportIndexedColor(C))),this._parser.registerOscHandler(8,new g.OscHandler(C=>this.setHyperlink(C))),this._parser.registerOscHandler(10,new g.OscHandler(C=>this.setOrReportFgColor(C))),this._parser.registerOscHandler(11,new g.OscHandler(C=>this.setOrReportBgColor(C))),this._parser.registerOscHandler(12,new g.OscHandler(C=>this.setOrReportCursorColor(C))),this._parser.registerOscHandler(104,new g.OscHandler(C=>this.restoreIndexedColor(C))),this._parser.registerOscHandler(110,new g.OscHandler(C=>this.restoreFgColor(C))),this._parser.registerOscHandler(111,new g.OscHandler(C=>this.restoreBgColor(C))),this._parser.registerOscHandler(112,new g.OscHandler(C=>this.restoreCursorColor(C))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const C in d.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:C},()=>this.selectCharset("("+C)),this._parser.registerEscHandler({intermediates:")",final:C},()=>this.selectCharset(")"+C)),this._parser.registerEscHandler({intermediates:"*",final:C},()=>this.selectCharset("*"+C)),this._parser.registerEscHandler({intermediates:"+",final:C},()=>this.selectCharset("+"+C)),this._parser.registerEscHandler({intermediates:"-",final:C},()=>this.selectCharset("-"+C)),this._parser.registerEscHandler({intermediates:".",final:C},()=>this.selectCharset("."+C)),this._parser.registerEscHandler({intermediates:"/",final:C},()=>this.selectCharset("/"+C));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(C=>(this._logService.error("Parsing error: ",C),C)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new c.DcsHandler((C,A)=>this.requestStatusString(C,A)))}_preserveStack(S,w,y,L){this._parseStack.paused=!0,this._parseStack.cursorStartX=S,this._parseStack.cursorStartY=w,this._parseStack.decodedLength=y,this._parseStack.position=L}_logSlowResolvingAsync(S){this._logService.logLevel<=v.LogLevelEnum.WARN&&Promise.race([S,new Promise((w,y)=>setTimeout(()=>y("#SLOW_TIMEOUT"),5e3))]).catch(w=>{if(w!=="#SLOW_TIMEOUT")throw w;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(S,w){let y,L=this._activeBuffer.x,T=this._activeBuffer.y,H=0;const U=this._parseStack.paused;if(U){if(y=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,w))return this._logSlowResolvingAsync(y),y;L=this._parseStack.cursorStartX,T=this._parseStack.cursorStartY,this._parseStack.paused=!1,S.length>k&&(H=this._parseStack.position+k)}if(this._logService.logLevel<=v.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof S=="string"?` "${S}"`:` "${Array.prototype.map.call(S,C=>String.fromCharCode(C)).join("")}"`),typeof S=="string"?S.split("").map(C=>C.charCodeAt(0)):S),this._parseBuffer.length<S.length&&this._parseBuffer.length<k&&(this._parseBuffer=new Uint32Array(Math.min(S.length,k))),U||this._dirtyRowTracker.clearRange(),S.length>k)for(let C=H;C<S.length;C+=k){const A=C+k<S.length?C+k:S.length,O=typeof S=="string"?this._stringDecoder.decode(S.substring(C,A),this._parseBuffer):this._utf8Decoder.decode(S.subarray(C,A),this._parseBuffer);if(y=this._parser.parse(this._parseBuffer,O))return this._preserveStack(L,T,O,C),this._logSlowResolvingAsync(y),y}else if(!U){const C=typeof S=="string"?this._stringDecoder.decode(S,this._parseBuffer):this._utf8Decoder.decode(S,this._parseBuffer);if(y=this._parser.parse(this._parseBuffer,C))return this._preserveStack(L,T,C,0),this._logSlowResolvingAsync(y),y}this._activeBuffer.x===L&&this._activeBuffer.y===T||this._onCursorMove.fire();const $=this._dirtyRowTracker.end+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp),R=this._dirtyRowTracker.start+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp);R<this._bufferService.rows&&this._onRequestRefreshRows.fire(Math.min(R,this._bufferService.rows-1),Math.min($,this._bufferService.rows-1))}print(S,w,y){let L,T;const H=this._charsetService.charset,U=this._optionsService.rawOptions.screenReaderMode,$=this._bufferService.cols,R=this._coreService.decPrivateModes.wraparound,C=this._coreService.modes.insertMode,A=this._curAttrData;let O=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&y-w>0&&O.getWidth(this._activeBuffer.x-1)===2&&O.setCellFromCodepoint(this._activeBuffer.x-1,0,1,A);let W=this._parser.precedingJoinState;for(let F=w;F<y;++F){if(L=S[F],L<127&&H){const Q=H[String.fromCharCode(L)];Q&&(L=Q.charCodeAt(0))}const K=this._unicodeService.charProperties(L,W);T=_.UnicodeService.extractWidth(K);const X=_.UnicodeService.extractShouldJoin(K),J=X?_.UnicodeService.extractWidth(W):0;if(W=K,U&&this._onA11yChar.fire((0,h.stringFromCodePoint)(L)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),this._activeBuffer.x+T-J>$){if(R){const Q=O;let j=this._activeBuffer.x-J;for(this._activeBuffer.x=J,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),O=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y),J>0&&O instanceof t.BufferLine&&O.copyCellsFrom(Q,j,0,J,!1);j<$;)Q.setCellFromCodepoint(j++,0,1,A)}else if(this._activeBuffer.x=$-1,T===2)continue}if(X&&this._activeBuffer.x){const Q=O.getWidth(this._activeBuffer.x-1)?1:2;O.addCodepointToCell(this._activeBuffer.x-Q,L,T);for(let j=T-J;--j>=0;)O.setCellFromCodepoint(this._activeBuffer.x++,0,0,A)}else if(C&&(O.insertCells(this._activeBuffer.x,T-J,this._activeBuffer.getNullCell(A)),O.getWidth($-1)===2&&O.setCellFromCodepoint($-1,e.NULL_CELL_CODE,e.NULL_CELL_WIDTH,A)),O.setCellFromCodepoint(this._activeBuffer.x++,L,T,A),T>0)for(;--T;)O.setCellFromCodepoint(this._activeBuffer.x++,0,0,A)}this._parser.precedingJoinState=W,this._activeBuffer.x<$&&y-w>0&&O.getWidth(this._activeBuffer.x)===0&&!O.hasContent(this._activeBuffer.x)&&O.setCellFromCodepoint(this._activeBuffer.x,0,1,A),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(S,w){return S.final!=="t"||S.prefix||S.intermediates?this._parser.registerCsiHandler(S,w):this._parser.registerCsiHandler(S,y=>!D(y.params[0],this._optionsService.rawOptions.windowOptions)||w(y))}registerDcsHandler(S,w){return this._parser.registerDcsHandler(S,new c.DcsHandler(w))}registerEscHandler(S,w){return this._parser.registerEscHandler(S,w)}registerOscHandler(S,w){return this._parser.registerOscHandler(S,new g.OscHandler(w))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)?.isWrapped){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const S=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);S.hasWidth(this._activeBuffer.x)&&!S.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const S=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-S),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(S=this._bufferService.cols-1){this._activeBuffer.x=Math.min(S,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(S,w){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=S,this._activeBuffer.y=this._activeBuffer.scrollTop+w):(this._activeBuffer.x=S,this._activeBuffer.y=w),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(S,w){this._restrictCursor(),this._setCursor(this._activeBuffer.x+S,this._activeBuffer.y+w)}cursorUp(S){const w=this._activeBuffer.y-this._activeBuffer.scrollTop;return w>=0?this._moveCursor(0,-Math.min(w,S.params[0]||1)):this._moveCursor(0,-(S.params[0]||1)),!0}cursorDown(S){const w=this._activeBuffer.scrollBottom-this._activeBuffer.y;return w>=0?this._moveCursor(0,Math.min(w,S.params[0]||1)):this._moveCursor(0,S.params[0]||1),!0}cursorForward(S){return this._moveCursor(S.params[0]||1,0),!0}cursorBackward(S){return this._moveCursor(-(S.params[0]||1),0),!0}cursorNextLine(S){return this.cursorDown(S),this._activeBuffer.x=0,!0}cursorPrecedingLine(S){return this.cursorUp(S),this._activeBuffer.x=0,!0}cursorCharAbsolute(S){return this._setCursor((S.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(S){return this._setCursor(S.length>=2?(S.params[1]||1)-1:0,(S.params[0]||1)-1),!0}charPosAbsolute(S){return this._setCursor((S.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(S){return this._moveCursor(S.params[0]||1,0),!0}linePosAbsolute(S){return this._setCursor(this._activeBuffer.x,(S.params[0]||1)-1),!0}vPositionRelative(S){return this._moveCursor(0,S.params[0]||1),!0}hVPosition(S){return this.cursorPosition(S),!0}tabClear(S){const w=S.params[0];return w===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:w===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(S){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let w=S.params[0]||1;for(;w--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(S){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let w=S.params[0]||1;for(;w--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(S){const w=S.params[0];return w===1&&(this._curAttrData.bg|=536870912),w!==2&&w!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(S,w,y,L=!1,T=!1){const H=this._activeBuffer.lines.get(this._activeBuffer.ybase+S);H.replaceCells(w,y,this._activeBuffer.getNullCell(this._eraseAttrData()),T),L&&(H.isWrapped=!1)}_resetBufferLine(S,w=!1){const y=this._activeBuffer.lines.get(this._activeBuffer.ybase+S);y&&(y.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),w),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+S),y.isWrapped=!1)}eraseInDisplay(S,w=!1){let y;switch(this._restrictCursor(this._bufferService.cols),S.params[0]){case 0:for(y=this._activeBuffer.y,this._dirtyRowTracker.markDirty(y),this._eraseInBufferLine(y++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,w);y<this._bufferService.rows;y++)this._resetBufferLine(y,w);this._dirtyRowTracker.markDirty(y);break;case 1:for(y=this._activeBuffer.y,this._dirtyRowTracker.markDirty(y),this._eraseInBufferLine(y,0,this._activeBuffer.x+1,!0,w),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(y+1).isWrapped=!1);y--;)this._resetBufferLine(y,w);this._dirtyRowTracker.markDirty(0);break;case 2:for(y=this._bufferService.rows,this._dirtyRowTracker.markDirty(y-1);y--;)this._resetBufferLine(y,w);this._dirtyRowTracker.markDirty(0);break;case 3:const L=this._activeBuffer.lines.length-this._bufferService.rows;L>0&&(this._activeBuffer.lines.trimStart(L),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-L,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-L,0),this._onScroll.fire(0))}return!0}eraseInLine(S,w=!1){switch(this._restrictCursor(this._bufferService.cols),S.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,w);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,w);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,w)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(S){this._restrictCursor();let w=S.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const y=this._activeBuffer.ybase+this._activeBuffer.y,L=this._bufferService.rows-1-this._activeBuffer.scrollBottom,T=this._bufferService.rows-1+this._activeBuffer.ybase-L+1;for(;w--;)this._activeBuffer.lines.splice(T-1,1),this._activeBuffer.lines.splice(y,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(S){this._restrictCursor();let w=S.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const y=this._activeBuffer.ybase+this._activeBuffer.y;let L;for(L=this._bufferService.rows-1-this._activeBuffer.scrollBottom,L=this._bufferService.rows-1+this._activeBuffer.ybase-L;w--;)this._activeBuffer.lines.splice(y,1),this._activeBuffer.lines.splice(L,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(S){this._restrictCursor();const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return w&&(w.insertCells(this._activeBuffer.x,S.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(S){this._restrictCursor();const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return w&&(w.deleteCells(this._activeBuffer.x,S.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(S){let w=S.params[0]||1;for(;w--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(S){let w=S.params[0]||1;for(;w--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(t.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=S.params[0]||1;for(let y=this._activeBuffer.scrollTop;y<=this._activeBuffer.scrollBottom;++y){const L=this._activeBuffer.lines.get(this._activeBuffer.ybase+y);L.deleteCells(0,w,this._activeBuffer.getNullCell(this._eraseAttrData())),L.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=S.params[0]||1;for(let y=this._activeBuffer.scrollTop;y<=this._activeBuffer.scrollBottom;++y){const L=this._activeBuffer.lines.get(this._activeBuffer.ybase+y);L.insertCells(0,w,this._activeBuffer.getNullCell(this._eraseAttrData())),L.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=S.params[0]||1;for(let y=this._activeBuffer.scrollTop;y<=this._activeBuffer.scrollBottom;++y){const L=this._activeBuffer.lines.get(this._activeBuffer.ybase+y);L.insertCells(this._activeBuffer.x,w,this._activeBuffer.getNullCell(this._eraseAttrData())),L.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(S){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=S.params[0]||1;for(let y=this._activeBuffer.scrollTop;y<=this._activeBuffer.scrollBottom;++y){const L=this._activeBuffer.lines.get(this._activeBuffer.ybase+y);L.deleteCells(this._activeBuffer.x,w,this._activeBuffer.getNullCell(this._eraseAttrData())),L.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(S){this._restrictCursor();const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return w&&(w.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(S.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(S){const w=this._parser.precedingJoinState;if(!w)return!0;const y=S.params[0]||1,L=_.UnicodeService.extractWidth(w),T=this._activeBuffer.x-L,H=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).getString(T),U=new Uint32Array(H.length*y);let $=0;for(let C=0;C<H.length;){const A=H.codePointAt(C)||0;U[$++]=A,C+=A>65535?2:1}let R=$;for(let C=1;C<y;++C)U.copyWithin(R,0,$),R+=$;return this.print(U,0,R),!0}sendDeviceAttributesPrimary(S){return S.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(n.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(n.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(S){return S.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(n.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(n.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(S.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(n.C0.ESC+"[>83;40003;0c")),!0}_is(S){return(this._optionsService.rawOptions.termName+"").indexOf(S)===0}setMode(S){for(let w=0;w<S.length;w++)switch(S.params[w]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(S){for(let w=0;w<S.length;w++)switch(S.params[w]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,d.DEFAULT_CHARSET),this._charsetService.setgCharset(1,d.DEFAULT_CHARSET),this._charsetService.setgCharset(2,d.DEFAULT_CHARSET),this._charsetService.setgCharset(3,d.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(S){for(let w=0;w<S.length;w++)switch(S.params[w]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(S){for(let w=0;w<S.length;w++)switch(S.params[w]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),S.params[w]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(S,w){const y=this._coreService.decPrivateModes,{activeProtocol:L,activeEncoding:T}=this._coreMouseService,H=this._coreService,{buffers:U,cols:$}=this._bufferService,{active:R,alt:C}=U,A=this._optionsService.rawOptions,O=X=>X?1:2,W=S.params[0];return F=W,K=w?W===2?4:W===4?O(H.modes.insertMode):W===12?3:W===20?O(A.convertEol):0:W===1?O(y.applicationCursorKeys):W===3?A.windowOptions.setWinLines?$===80?2:$===132?1:0:0:W===6?O(y.origin):W===7?O(y.wraparound):W===8?3:W===9?O(L==="X10"):W===12?O(A.cursorBlink):W===25?O(!H.isCursorHidden):W===45?O(y.reverseWraparound):W===66?O(y.applicationKeypad):W===67?4:W===1e3?O(L==="VT200"):W===1002?O(L==="DRAG"):W===1003?O(L==="ANY"):W===1004?O(y.sendFocus):W===1005?4:W===1006?O(T==="SGR"):W===1015?4:W===1016?O(T==="SGR_PIXELS"):W===1048?1:W===47||W===1047||W===1049?O(R===C):W===2004?O(y.bracketedPasteMode):0,H.triggerDataEvent(`${n.C0.ESC}[${w?"":"?"}${F};${K}$y`),!0;var F,K}_updateAttrColor(S,w,y,L,T){return w===2?(S|=50331648,S&=-16777216,S|=a.AttributeData.fromColorRGB([y,L,T])):w===5&&(S&=-50331904,S|=33554432|255&y),S}_extractColor(S,w,y){const L=[0,0,-1,0,0,0];let T=0,H=0;do{if(L[H+T]=S.params[w+H],S.hasSubParams(w+H)){const U=S.getSubParams(w+H);let $=0;do L[1]===5&&(T=1),L[H+$+1+T]=U[$];while(++$<U.length&&$+H+1+T<L.length);break}if(L[1]===5&&H+T>=2||L[1]===2&&H+T>=5)break;L[1]&&(T=1)}while(++H+w<S.length&&H+T<L.length);for(let U=2;U<L.length;++U)L[U]===-1&&(L[U]=0);switch(L[0]){case 38:y.fg=this._updateAttrColor(y.fg,L[1],L[3],L[4],L[5]);break;case 48:y.bg=this._updateAttrColor(y.bg,L[1],L[3],L[4],L[5]);break;case 58:y.extended=y.extended.clone(),y.extended.underlineColor=this._updateAttrColor(y.extended.underlineColor,L[1],L[3],L[4],L[5])}return H}_processUnderline(S,w){w.extended=w.extended.clone(),(!~S||S>5)&&(S=1),w.extended.underlineStyle=S,w.fg|=268435456,S===0&&(w.fg&=-268435457),w.updateExtended()}_processSGR0(S){S.fg=t.DEFAULT_ATTR_DATA.fg,S.bg=t.DEFAULT_ATTR_DATA.bg,S.extended=S.extended.clone(),S.extended.underlineStyle=0,S.extended.underlineColor&=-67108864,S.updateExtended()}charAttributes(S){if(S.length===1&&S.params[0]===0)return this._processSGR0(this._curAttrData),!0;const w=S.length;let y;const L=this._curAttrData;for(let T=0;T<w;T++)y=S.params[T],y>=30&&y<=37?(L.fg&=-50331904,L.fg|=16777216|y-30):y>=40&&y<=47?(L.bg&=-50331904,L.bg|=16777216|y-40):y>=90&&y<=97?(L.fg&=-50331904,L.fg|=16777224|y-90):y>=100&&y<=107?(L.bg&=-50331904,L.bg|=16777224|y-100):y===0?this._processSGR0(L):y===1?L.fg|=134217728:y===3?L.bg|=67108864:y===4?(L.fg|=268435456,this._processUnderline(S.hasSubParams(T)?S.getSubParams(T)[0]:1,L)):y===5?L.fg|=536870912:y===7?L.fg|=67108864:y===8?L.fg|=1073741824:y===9?L.fg|=2147483648:y===2?L.bg|=134217728:y===21?this._processUnderline(2,L):y===22?(L.fg&=-134217729,L.bg&=-134217729):y===23?L.bg&=-67108865:y===24?(L.fg&=-268435457,this._processUnderline(0,L)):y===25?L.fg&=-536870913:y===27?L.fg&=-67108865:y===28?L.fg&=-1073741825:y===29?L.fg&=2147483647:y===39?(L.fg&=-67108864,L.fg|=16777215&t.DEFAULT_ATTR_DATA.fg):y===49?(L.bg&=-67108864,L.bg|=16777215&t.DEFAULT_ATTR_DATA.bg):y===38||y===48||y===58?T+=this._extractColor(S,T,L):y===53?L.bg|=1073741824:y===55?L.bg&=-1073741825:y===59?(L.extended=L.extended.clone(),L.extended.underlineColor=-1,L.updateExtended()):y===100?(L.fg&=-67108864,L.fg|=16777215&t.DEFAULT_ATTR_DATA.fg,L.bg&=-67108864,L.bg|=16777215&t.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",y);return!0}deviceStatus(S){switch(S.params[0]){case 5:this._coreService.triggerDataEvent(`${n.C0.ESC}[0n`);break;case 6:const w=this._activeBuffer.y+1,y=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${n.C0.ESC}[${w};${y}R`)}return!0}deviceStatusPrivate(S){if(S.params[0]===6){const w=this._activeBuffer.y+1,y=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${n.C0.ESC}[?${w};${y}R`)}return!0}softReset(S){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=t.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(S){const w=S.params[0]||1;switch(w){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const y=w%2==1;return this._optionsService.options.cursorBlink=y,!0}setScrollRegion(S){const w=S.params[0]||1;let y;return(S.length<2||(y=S.params[1])>this._bufferService.rows||y===0)&&(y=this._bufferService.rows),y>w&&(this._activeBuffer.scrollTop=w-1,this._activeBuffer.scrollBottom=y-1,this._setCursor(0,0)),!0}windowOptions(S){if(!D(S.params[0],this._optionsService.rawOptions.windowOptions))return!0;const w=S.length>1?S.params[1]:0;switch(S.params[0]){case 14:w!==2&&this._onRequestWindowsOptionsReport.fire(b.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(b.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${n.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:w!==0&&w!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),w!==0&&w!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:w!==0&&w!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),w!==0&&w!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(S){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(S){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(S){return this._windowTitle=S,this._onTitleChange.fire(S),!0}setIconName(S){return this._iconName=S,!0}setOrReportIndexedColor(S){const w=[],y=S.split(";");for(;y.length>1;){const L=y.shift(),T=y.shift();if(/^\d+$/.exec(L)){const H=parseInt(L);if(N(H))if(T==="?")w.push({type:0,index:H});else{const U=(0,m.parseColor)(T);U&&w.push({type:1,index:H,color:U})}}}return w.length&&this._onColor.fire(w),!0}setHyperlink(S){const w=S.split(";");return!(w.length<2)&&(w[1]?this._createHyperlink(w[0],w[1]):!w[0]&&this._finishHyperlink())}_createHyperlink(S,w){this._getCurrentLinkId()&&this._finishHyperlink();const y=S.split(":");let L;const T=y.findIndex(H=>H.startsWith("id="));return T!==-1&&(L=y[T].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:L,uri:w}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(S,w){const y=S.split(";");for(let L=0;L<y.length&&!(w>=this._specialColors.length);++L,++w)if(y[L]==="?")this._onColor.fire([{type:0,index:this._specialColors[w]}]);else{const T=(0,m.parseColor)(y[L]);T&&this._onColor.fire([{type:1,index:this._specialColors[w],color:T}])}return!0}setOrReportFgColor(S){return this._setOrReportSpecialColor(S,0)}setOrReportBgColor(S){return this._setOrReportSpecialColor(S,1)}setOrReportCursorColor(S){return this._setOrReportSpecialColor(S,2)}restoreIndexedColor(S){if(!S)return this._onColor.fire([{type:2}]),!0;const w=[],y=S.split(";");for(let L=0;L<y.length;++L)if(/^\d+$/.exec(y[L])){const T=parseInt(y[L]);N(T)&&w.push({type:2,index:T})}return w.length&&this._onColor.fire(w),!0}restoreFgColor(S){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(S){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(S){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,d.DEFAULT_CHARSET),!0}selectCharset(S){return S.length!==2?(this.selectDefaultCharset(),!0):(S[0]==="/"||this._charsetService.setgCharset(E[S[0]],d.CHARSETS[S[1]]||d.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const S=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,S,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=t.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=t.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(S){return this._charsetService.setgLevel(S),!0}screenAlignmentPattern(){const S=new i.CellData;S.content=4194373,S.fg=this._curAttrData.fg,S.bg=this._curAttrData.bg,this._setCursor(0,0);for(let w=0;w<this._bufferService.rows;++w){const y=this._activeBuffer.ybase+this._activeBuffer.y+w,L=this._activeBuffer.lines.get(y);L&&(L.fill(S),L.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(S,w){const y=this._bufferService.buffer,L=this._optionsService.rawOptions;return(T=>(this._coreService.triggerDataEvent(`${n.C0.ESC}${T}${n.C0.ESC}\\`),!0))(S==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:S==='"p'?'P1$r61;1"p':S==="r"?`P1$r${y.scrollTop+1};${y.scrollBottom+1}r`:S==="m"?"P1$r0m":S===" q"?`P1$r${{block:2,underline:4,bar:6}[L.cursorStyle]-(L.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(S,w){this._dirtyRowTracker.markRangeDirty(S,w)}}r.InputHandler=M;let I=class{constructor(P){this._bufferService=P,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(P){P<this.start?this.start=P:P>this.end&&(this.end=P)}markRangeDirty(P,S){P>S&&(x=P,P=S,S=x),P<this.start&&(this.start=P),S>this.end&&(this.end=S)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function N(P){return 0<=P&&P<256}I=l([u(0,v.IBufferService)],I)},844:(B,r)=>{function o(l){for(const u of l)u.dispose();l.length=0}Object.defineProperty(r,"__esModule",{value:!0}),r.getDisposeArrayDisposable=r.disposeArray=r.toDisposable=r.MutableDisposable=r.Disposable=void 0,r.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const l of this._disposables)l.dispose();this._disposables.length=0}register(l){return this._disposables.push(l),l}unregister(l){const u=this._disposables.indexOf(l);u!==-1&&this._disposables.splice(u,1)}},r.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(l){this._isDisposed||l===this._value||(this._value?.dispose(),this._value=l)}clear(){this.value=void 0}dispose(){this._isDisposed=!0,this._value?.dispose(),this._value=void 0}},r.toDisposable=function(l){return{dispose:l}},r.disposeArray=o,r.getDisposeArrayDisposable=function(l){return{dispose:()=>o(l)}}},1505:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.FourKeyMap=r.TwoKeyMap=void 0;class o{constructor(){this._data={}}set(u,n,d){this._data[u]||(this._data[u]={}),this._data[u][n]=d}get(u,n){return this._data[u]?this._data[u][n]:void 0}clear(){this._data={}}}r.TwoKeyMap=o,r.FourKeyMap=class{constructor(){this._data=new o}set(l,u,n,d,f){this._data.get(l,u)||this._data.set(l,u,new o),this._data.get(l,u).set(n,d,f)}get(l,u,n,d){return this._data.get(l,u)?.get(n,d)}clear(){this._data.clear()}}},6114:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.isChromeOS=r.isLinux=r.isWindows=r.isIphone=r.isIpad=r.isMac=r.getSafariVersion=r.isSafari=r.isLegacyEdge=r.isFirefox=r.isNode=void 0,r.isNode=typeof process<"u"&&"title"in process;const o=r.isNode?"node":navigator.userAgent,l=r.isNode?"node":navigator.platform;r.isFirefox=o.includes("Firefox"),r.isLegacyEdge=o.includes("Edge"),r.isSafari=/^((?!chrome|android).)*safari/i.test(o),r.getSafariVersion=function(){if(!r.isSafari)return 0;const u=o.match(/Version\/(\d+)/);return u===null||u.length<2?0:parseInt(u[1])},r.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(l),r.isIpad=l==="iPad",r.isIphone=l==="iPhone",r.isWindows=["Windows","Win16","Win32","WinCE"].includes(l),r.isLinux=l.indexOf("Linux")>=0,r.isChromeOS=/\bCrOS\b/.test(o)},6106:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.SortedList=void 0;let o=0;r.SortedList=class{constructor(l){this._getKey=l,this._array=[]}clear(){this._array.length=0}insert(l){this._array.length!==0?(o=this._search(this._getKey(l)),this._array.splice(o,0,l)):this._array.push(l)}delete(l){if(this._array.length===0)return!1;const u=this._getKey(l);if(u===void 0||(o=this._search(u),o===-1)||this._getKey(this._array[o])!==u)return!1;do if(this._array[o]===l)return this._array.splice(o,1),!0;while(++o<this._array.length&&this._getKey(this._array[o])===u);return!1}*getKeyIterator(l){if(this._array.length!==0&&(o=this._search(l),!(o<0||o>=this._array.length)&&this._getKey(this._array[o])===l))do yield this._array[o];while(++o<this._array.length&&this._getKey(this._array[o])===l)}forEachByKey(l,u){if(this._array.length!==0&&(o=this._search(l),!(o<0||o>=this._array.length)&&this._getKey(this._array[o])===l))do u(this._array[o]);while(++o<this._array.length&&this._getKey(this._array[o])===l)}values(){return[...this._array].values()}_search(l){let u=0,n=this._array.length-1;for(;n>=u;){let d=u+n>>1;const f=this._getKey(this._array[d]);if(f>l)n=d-1;else{if(!(f<l)){for(;d>0&&this._getKey(this._array[d-1])===l;)d--;return d}u=d+1}}return u}}},7226:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DebouncedIdleTask=r.IdleTaskQueue=r.PriorityTaskQueue=void 0;const l=o(6114);class u{constructor(){this._tasks=[],this._i=0}enqueue(f){this._tasks.push(f),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(f){this._idleCallback=void 0;let p=0,h=0,t=f.timeRemaining(),s=0;for(;this._i<this._tasks.length;){if(p=Date.now(),this._tasks[this._i]()||this._i++,p=Math.max(1,Date.now()-p),h=Math.max(p,h),s=f.timeRemaining(),1.5*h>s)return t-p<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(t-p))}ms`),void this._start();t=s}this.clear()}}class n extends u{_requestCallback(f){return setTimeout(()=>f(this._createDeadline(16)))}_cancelCallback(f){clearTimeout(f)}_createDeadline(f){const p=Date.now()+f;return{timeRemaining:()=>Math.max(0,p-Date.now())}}}r.PriorityTaskQueue=n,r.IdleTaskQueue=!l.isNode&&"requestIdleCallback"in window?class extends u{_requestCallback(d){return requestIdleCallback(d)}_cancelCallback(d){cancelIdleCallback(d)}}:n,r.DebouncedIdleTask=class{constructor(){this._queue=new r.IdleTaskQueue}set(d){this._queue.clear(),this._queue.enqueue(d)}flush(){this._queue.flush()}}},9282:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.updateWindowsModeWrappedState=void 0;const l=o(643);r.updateWindowsModeWrappedState=function(u){const n=u.buffer.lines.get(u.buffer.ybase+u.buffer.y-1),d=n?.get(u.cols-1),f=u.buffer.lines.get(u.buffer.ybase+u.buffer.y);f&&d&&(f.isWrapped=d[l.CHAR_DATA_CODE_INDEX]!==l.NULL_CELL_CODE&&d[l.CHAR_DATA_CODE_INDEX]!==l.WHITESPACE_CELL_CODE)}},3734:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ExtendedAttrs=r.AttributeData=void 0;class o{constructor(){this.fg=0,this.bg=0,this.extended=new l}static toColorRGB(n){return[n>>>16&255,n>>>8&255,255&n]}static fromColorRGB(n){return(255&n[0])<<16|(255&n[1])<<8|255&n[2]}clone(){const n=new o;return n.fg=this.fg,n.bg=this.bg,n.extended=this.extended.clone(),n}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}getUnderlineVariantOffset(){return this.extended.underlineVariantOffset}}r.AttributeData=o;class l{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(n){this._ext=n}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(n){this._ext&=-469762049,this._ext|=n<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(n){this._ext&=-67108864,this._ext|=67108863&n}get urlId(){return this._urlId}set urlId(n){this._urlId=n}get underlineVariantOffset(){const n=(3758096384&this._ext)>>29;return n<0?4294967288^n:n}set underlineVariantOffset(n){this._ext&=536870911,this._ext|=n<<29&3758096384}constructor(n=0,d=0){this._ext=0,this._urlId=0,this._ext=n,this._urlId=d}clone(){return new l(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}r.ExtendedAttrs=l},9092:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Buffer=r.MAX_BUFFER_SIZE=void 0;const l=o(6349),u=o(7226),n=o(3734),d=o(8437),f=o(4634),p=o(511),h=o(643),t=o(4863),s=o(7116);r.MAX_BUFFER_SIZE=4294967295,r.Buffer=class{constructor(e,i,a){this._hasScrollback=e,this._optionsService=i,this._bufferService=a,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=d.DEFAULT_ATTR_DATA.clone(),this.savedCharset=s.DEFAULT_CHARSET,this.markers=[],this._nullCell=p.CellData.fromCharData([0,h.NULL_CELL_CHAR,h.NULL_CELL_WIDTH,h.NULL_CELL_CODE]),this._whitespaceCell=p.CellData.fromCharData([0,h.WHITESPACE_CELL_CHAR,h.WHITESPACE_CELL_WIDTH,h.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new u.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(e){return e?(this._nullCell.fg=e.fg,this._nullCell.bg=e.bg,this._nullCell.extended=e.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new n.ExtendedAttrs),this._nullCell}getWhitespaceCell(e){return e?(this._whitespaceCell.fg=e.fg,this._whitespaceCell.bg=e.bg,this._whitespaceCell.extended=e.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new n.ExtendedAttrs),this._whitespaceCell}getBlankLine(e,i){return new d.BufferLine(this._bufferService.cols,this.getNullCell(e),i)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const e=this.ybase+this.y-this.ydisp;return e>=0&&e<this._rows}_getCorrectBufferLength(e){if(!this._hasScrollback)return e;const i=e+this._optionsService.rawOptions.scrollback;return i>r.MAX_BUFFER_SIZE?r.MAX_BUFFER_SIZE:i}fillViewportRows(e){if(this.lines.length===0){e===void 0&&(e=d.DEFAULT_ATTR_DATA);let i=this._rows;for(;i--;)this.lines.push(this.getBlankLine(e))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(e,i){const a=this.getNullCell(d.DEFAULT_ATTR_DATA);let v=0;const _=this._getCorrectBufferLength(i);if(_>this.lines.maxLength&&(this.lines.maxLength=_),this.lines.length>0){if(this._cols<e)for(let c=0;c<this.lines.length;c++)v+=+this.lines.get(c).resize(e,a);let g=0;if(this._rows<i)for(let c=this._rows;c<i;c++)this.lines.length<i+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new d.BufferLine(e,a)):this.ybase>0&&this.lines.length<=this.ybase+this.y+g+1?(this.ybase--,g++,this.ydisp>0&&this.ydisp--):this.lines.push(new d.BufferLine(e,a)));else for(let c=this._rows;c>i;c--)this.lines.length>i+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(_<this.lines.maxLength){const c=this.lines.length-_;c>0&&(this.lines.trimStart(c),this.ybase=Math.max(this.ybase-c,0),this.ydisp=Math.max(this.ydisp-c,0),this.savedY=Math.max(this.savedY-c,0)),this.lines.maxLength=_}this.x=Math.min(this.x,e-1),this.y=Math.min(this.y,i-1),g&&(this.y+=g),this.savedX=Math.min(this.savedX,e-1),this.scrollTop=0}if(this.scrollBottom=i-1,this._isReflowEnabled&&(this._reflow(e,i),this._cols>e))for(let g=0;g<this.lines.length;g++)v+=+this.lines.get(g).resize(e,a);this._cols=e,this._rows=i,this._memoryCleanupQueue.clear(),v>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let e=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,e=!1);let i=0;for(;this._memoryCleanupPosition<this.lines.length;)if(i+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),i>100)return!0;return e}get _isReflowEnabled(){const e=this._optionsService.rawOptions.windowsPty;return e&&e.buildNumber?this._hasScrollback&&e.backend==="conpty"&&e.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(e,i){this._cols!==e&&(e>this._cols?this._reflowLarger(e,i):this._reflowSmaller(e,i))}_reflowLarger(e,i){const a=(0,f.reflowLargerGetLinesToRemove)(this.lines,this._cols,e,this.ybase+this.y,this.getNullCell(d.DEFAULT_ATTR_DATA));if(a.length>0){const v=(0,f.reflowLargerCreateNewLayout)(this.lines,a);(0,f.reflowLargerApplyNewLayout)(this.lines,v.layout),this._reflowLargerAdjustViewport(e,i,v.countRemoved)}}_reflowLargerAdjustViewport(e,i,a){const v=this.getNullCell(d.DEFAULT_ATTR_DATA);let _=a;for(;_-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<i&&this.lines.push(new d.BufferLine(e,v))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-a,0)}_reflowSmaller(e,i){const a=this.getNullCell(d.DEFAULT_ATTR_DATA),v=[];let _=0;for(let g=this.lines.length-1;g>=0;g--){let c=this.lines.get(g);if(!c||!c.isWrapped&&c.getTrimmedLength()<=e)continue;const m=[c];for(;c.isWrapped&&g>0;)c=this.lines.get(--g),m.unshift(c);const E=this.ybase+this.y;if(E>=g&&E<g+m.length)continue;const k=m[m.length-1].getTrimmedLength(),D=(0,f.reflowSmallerGetNewLineLengths)(m,this._cols,e),b=D.length-m.length;let x;x=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+b):Math.max(0,this.lines.length-this.lines.maxLength+b);const M=[];for(let y=0;y<b;y++){const L=this.getBlankLine(d.DEFAULT_ATTR_DATA,!0);M.push(L)}M.length>0&&(v.push({start:g+m.length+_,newLines:M}),_+=M.length),m.push(...M);let I=D.length-1,N=D[I];N===0&&(I--,N=D[I]);let P=m.length-b-1,S=k;for(;P>=0;){const y=Math.min(S,N);if(m[I]===void 0)break;if(m[I].copyCellsFrom(m[P],S-y,N-y,y,!0),N-=y,N===0&&(I--,N=D[I]),S-=y,S===0){P--;const L=Math.max(P,0);S=(0,f.getWrappedLineTrimmedLength)(m,L,this._cols)}}for(let y=0;y<m.length;y++)D[y]<e&&m[y].setCell(D[y],a);let w=b-x;for(;w-- >0;)this.ybase===0?this.y<i-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+_)-i&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+b,this.ybase+i-1)}if(v.length>0){const g=[],c=[];for(let I=0;I<this.lines.length;I++)c.push(this.lines.get(I));const m=this.lines.length;let E=m-1,k=0,D=v[k];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+_);let b=0;for(let I=Math.min(this.lines.maxLength-1,m+_-1);I>=0;I--)if(D&&D.start>E+b){for(let N=D.newLines.length-1;N>=0;N--)this.lines.set(I--,D.newLines[N]);I++,g.push({index:E+1,amount:D.newLines.length}),b+=D.newLines.length,D=v[++k]}else this.lines.set(I,c[E--]);let x=0;for(let I=g.length-1;I>=0;I--)g[I].index+=x,this.lines.onInsertEmitter.fire(g[I]),x+=g[I].amount;const M=Math.max(0,m+_-this.lines.maxLength);M>0&&this.lines.onTrimEmitter.fire(M)}}translateBufferLineToString(e,i,a=0,v){const _=this.lines.get(e);return _?_.translateToString(i,a,v):""}getWrappedRangeForLine(e){let i=e,a=e;for(;i>0&&this.lines.get(i).isWrapped;)i--;for(;a+1<this.lines.length&&this.lines.get(a+1).isWrapped;)a++;return{first:i,last:a}}setupTabStops(e){for(e!=null?this.tabs[e]||(e=this.prevStop(e)):(this.tabs={},e=0);e<this._cols;e+=this._optionsService.rawOptions.tabStopWidth)this.tabs[e]=!0}prevStop(e){for(e==null&&(e=this.x);!this.tabs[--e]&&e>0;);return e>=this._cols?this._cols-1:e<0?0:e}nextStop(e){for(e==null&&(e=this.x);!this.tabs[++e]&&e<this._cols;);return e>=this._cols?this._cols-1:e<0?0:e}clearMarkers(e){this._isClearing=!0;for(let i=0;i<this.markers.length;i++)this.markers[i].line===e&&(this.markers[i].dispose(),this.markers.splice(i--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let e=0;e<this.markers.length;e++)this.markers[e].dispose(),this.markers.splice(e--,1);this._isClearing=!1}addMarker(e){const i=new t.Marker(e);return this.markers.push(i),i.register(this.lines.onTrim(a=>{i.line-=a,i.line<0&&i.dispose()})),i.register(this.lines.onInsert(a=>{i.line>=a.index&&(i.line+=a.amount)})),i.register(this.lines.onDelete(a=>{i.line>=a.index&&i.line<a.index+a.amount&&i.dispose(),i.line>a.index&&(i.line-=a.amount)})),i.register(i.onDispose(()=>this._removeMarker(i))),i}_removeMarker(e){this._isClearing||this.markers.splice(this.markers.indexOf(e),1)}}},8437:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferLine=r.DEFAULT_ATTR_DATA=void 0;const l=o(3734),u=o(511),n=o(643),d=o(482);r.DEFAULT_ATTR_DATA=Object.freeze(new l.AttributeData);let f=0;class p{constructor(t,s,e=!1){this.isWrapped=e,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*t);const i=s||u.CellData.fromCharData([0,n.NULL_CELL_CHAR,n.NULL_CELL_WIDTH,n.NULL_CELL_CODE]);for(let a=0;a<t;++a)this.setCell(a,i);this.length=t}get(t){const s=this._data[3*t+0],e=2097151&s;return[this._data[3*t+1],2097152&s?this._combined[t]:e?(0,d.stringFromCodePoint)(e):"",s>>22,2097152&s?this._combined[t].charCodeAt(this._combined[t].length-1):e]}set(t,s){this._data[3*t+1]=s[n.CHAR_DATA_ATTR_INDEX],s[n.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[t]=s[1],this._data[3*t+0]=2097152|t|s[n.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*t+0]=s[n.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|s[n.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(t){return this._data[3*t+0]>>22}hasWidth(t){return 12582912&this._data[3*t+0]}getFg(t){return this._data[3*t+1]}getBg(t){return this._data[3*t+2]}hasContent(t){return 4194303&this._data[3*t+0]}getCodePoint(t){const s=this._data[3*t+0];return 2097152&s?this._combined[t].charCodeAt(this._combined[t].length-1):2097151&s}isCombined(t){return 2097152&this._data[3*t+0]}getString(t){const s=this._data[3*t+0];return 2097152&s?this._combined[t]:2097151&s?(0,d.stringFromCodePoint)(2097151&s):""}isProtected(t){return 536870912&this._data[3*t+2]}loadCell(t,s){return f=3*t,s.content=this._data[f+0],s.fg=this._data[f+1],s.bg=this._data[f+2],2097152&s.content&&(s.combinedData=this._combined[t]),268435456&s.bg&&(s.extended=this._extendedAttrs[t]),s}setCell(t,s){2097152&s.content&&(this._combined[t]=s.combinedData),268435456&s.bg&&(this._extendedAttrs[t]=s.extended),this._data[3*t+0]=s.content,this._data[3*t+1]=s.fg,this._data[3*t+2]=s.bg}setCellFromCodepoint(t,s,e,i){268435456&i.bg&&(this._extendedAttrs[t]=i.extended),this._data[3*t+0]=s|e<<22,this._data[3*t+1]=i.fg,this._data[3*t+2]=i.bg}addCodepointToCell(t,s,e){let i=this._data[3*t+0];2097152&i?this._combined[t]+=(0,d.stringFromCodePoint)(s):2097151&i?(this._combined[t]=(0,d.stringFromCodePoint)(2097151&i)+(0,d.stringFromCodePoint)(s),i&=-2097152,i|=2097152):i=s|4194304,e&&(i&=-12582913,i|=e<<22),this._data[3*t+0]=i}insertCells(t,s,e){if((t%=this.length)&&this.getWidth(t-1)===2&&this.setCellFromCodepoint(t-1,0,1,e),s<this.length-t){const i=new u.CellData;for(let a=this.length-t-s-1;a>=0;--a)this.setCell(t+s+a,this.loadCell(t+a,i));for(let a=0;a<s;++a)this.setCell(t+a,e)}else for(let i=t;i<this.length;++i)this.setCell(i,e);this.getWidth(this.length-1)===2&&this.setCellFromCodepoint(this.length-1,0,1,e)}deleteCells(t,s,e){if(t%=this.length,s<this.length-t){const i=new u.CellData;for(let a=0;a<this.length-t-s;++a)this.setCell(t+a,this.loadCell(t+s+a,i));for(let a=this.length-s;a<this.length;++a)this.setCell(a,e)}else for(let i=t;i<this.length;++i)this.setCell(i,e);t&&this.getWidth(t-1)===2&&this.setCellFromCodepoint(t-1,0,1,e),this.getWidth(t)!==0||this.hasContent(t)||this.setCellFromCodepoint(t,0,1,e)}replaceCells(t,s,e,i=!1){if(i)for(t&&this.getWidth(t-1)===2&&!this.isProtected(t-1)&&this.setCellFromCodepoint(t-1,0,1,e),s<this.length&&this.getWidth(s-1)===2&&!this.isProtected(s)&&this.setCellFromCodepoint(s,0,1,e);t<s&&t<this.length;)this.isProtected(t)||this.setCell(t,e),t++;else for(t&&this.getWidth(t-1)===2&&this.setCellFromCodepoint(t-1,0,1,e),s<this.length&&this.getWidth(s-1)===2&&this.setCellFromCodepoint(s,0,1,e);t<s&&t<this.length;)this.setCell(t++,e)}resize(t,s){if(t===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const e=3*t;if(t>this.length){if(this._data.buffer.byteLength>=4*e)this._data=new Uint32Array(this._data.buffer,0,e);else{const i=new Uint32Array(e);i.set(this._data),this._data=i}for(let i=this.length;i<t;++i)this.setCell(i,s)}else{this._data=this._data.subarray(0,e);const i=Object.keys(this._combined);for(let v=0;v<i.length;v++){const _=parseInt(i[v],10);_>=t&&delete this._combined[_]}const a=Object.keys(this._extendedAttrs);for(let v=0;v<a.length;v++){const _=parseInt(a[v],10);_>=t&&delete this._extendedAttrs[_]}}return this.length=t,4*e*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const t=new Uint32Array(this._data.length);return t.set(this._data),this._data=t,1}return 0}fill(t,s=!1){if(s)for(let e=0;e<this.length;++e)this.isProtected(e)||this.setCell(e,t);else{this._combined={},this._extendedAttrs={};for(let e=0;e<this.length;++e)this.setCell(e,t)}}copyFrom(t){this.length!==t.length?this._data=new Uint32Array(t._data):this._data.set(t._data),this.length=t.length,this._combined={};for(const s in t._combined)this._combined[s]=t._combined[s];this._extendedAttrs={};for(const s in t._extendedAttrs)this._extendedAttrs[s]=t._extendedAttrs[s];this.isWrapped=t.isWrapped}clone(){const t=new p(0);t._data=new Uint32Array(this._data),t.length=this.length;for(const s in this._combined)t._combined[s]=this._combined[s];for(const s in this._extendedAttrs)t._extendedAttrs[s]=this._extendedAttrs[s];return t.isWrapped=this.isWrapped,t}getTrimmedLength(){for(let t=this.length-1;t>=0;--t)if(4194303&this._data[3*t+0])return t+(this._data[3*t+0]>>22);return 0}getNoBgTrimmedLength(){for(let t=this.length-1;t>=0;--t)if(4194303&this._data[3*t+0]||50331648&this._data[3*t+2])return t+(this._data[3*t+0]>>22);return 0}copyCellsFrom(t,s,e,i,a){const v=t._data;if(a)for(let g=i-1;g>=0;g--){for(let c=0;c<3;c++)this._data[3*(e+g)+c]=v[3*(s+g)+c];268435456&v[3*(s+g)+2]&&(this._extendedAttrs[e+g]=t._extendedAttrs[s+g])}else for(let g=0;g<i;g++){for(let c=0;c<3;c++)this._data[3*(e+g)+c]=v[3*(s+g)+c];268435456&v[3*(s+g)+2]&&(this._extendedAttrs[e+g]=t._extendedAttrs[s+g])}const _=Object.keys(t._combined);for(let g=0;g<_.length;g++){const c=parseInt(_[g],10);c>=s&&(this._combined[c-s+e]=t._combined[c])}}translateToString(t,s,e,i){s=s??0,e=e??this.length,t&&(e=Math.min(e,this.getTrimmedLength())),i&&(i.length=0);let a="";for(;s<e;){const v=this._data[3*s+0],_=2097151&v,g=2097152&v?this._combined[s]:_?(0,d.stringFromCodePoint)(_):n.WHITESPACE_CELL_CHAR;if(a+=g,i)for(let c=0;c<g.length;++c)i.push(s);s+=v>>22||1}return i&&i.push(s),a}}r.BufferLine=p},4841:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.getRangeLength=void 0,r.getRangeLength=function(o,l){if(o.start.y>o.end.y)throw new Error(`Buffer range end (${o.end.x}, ${o.end.y}) cannot be before start (${o.start.x}, ${o.start.y})`);return l*(o.end.y-o.start.y)+(o.end.x-o.start.x+1)}},4634:(B,r)=>{function o(l,u,n){if(u===l.length-1)return l[u].getTrimmedLength();const d=!l[u].hasContent(n-1)&&l[u].getWidth(n-1)===1,f=l[u+1].getWidth(0)===2;return d&&f?n-1:n}Object.defineProperty(r,"__esModule",{value:!0}),r.getWrappedLineTrimmedLength=r.reflowSmallerGetNewLineLengths=r.reflowLargerApplyNewLayout=r.reflowLargerCreateNewLayout=r.reflowLargerGetLinesToRemove=void 0,r.reflowLargerGetLinesToRemove=function(l,u,n,d,f){const p=[];for(let h=0;h<l.length-1;h++){let t=h,s=l.get(++t);if(!s.isWrapped)continue;const e=[l.get(h)];for(;t<l.length&&s.isWrapped;)e.push(s),s=l.get(++t);if(d>=h&&d<t){h+=e.length-1;continue}let i=0,a=o(e,i,u),v=1,_=0;for(;v<e.length;){const c=o(e,v,u),m=c-_,E=n-a,k=Math.min(m,E);e[i].copyCellsFrom(e[v],_,a,k,!1),a+=k,a===n&&(i++,a=0),_+=k,_===c&&(v++,_=0),a===0&&i!==0&&e[i-1].getWidth(n-1)===2&&(e[i].copyCellsFrom(e[i-1],n-1,a++,1,!1),e[i-1].setCell(n-1,f))}e[i].replaceCells(a,n,f);let g=0;for(let c=e.length-1;c>0&&(c>i||e[c].getTrimmedLength()===0);c--)g++;g>0&&(p.push(h+e.length-g),p.push(g)),h+=e.length-1}return p},r.reflowLargerCreateNewLayout=function(l,u){const n=[];let d=0,f=u[d],p=0;for(let h=0;h<l.length;h++)if(f===h){const t=u[++d];l.onDeleteEmitter.fire({index:h-p,amount:t}),h+=t-1,p+=t,f=u[++d]}else n.push(h);return{layout:n,countRemoved:p}},r.reflowLargerApplyNewLayout=function(l,u){const n=[];for(let d=0;d<u.length;d++)n.push(l.get(u[d]));for(let d=0;d<n.length;d++)l.set(d,n[d]);l.length=u.length},r.reflowSmallerGetNewLineLengths=function(l,u,n){const d=[],f=l.map((s,e)=>o(l,e,u)).reduce((s,e)=>s+e);let p=0,h=0,t=0;for(;t<f;){if(f-t<n){d.push(f-t);break}p+=n;const s=o(l,h,u);p>s&&(p-=s,h++);const e=l[h].getWidth(p-1)===2;e&&p--;const i=e?n-1:n;d.push(i),t+=i}return d},r.getWrappedLineTrimmedLength=o},5295:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferSet=void 0;const l=o(8460),u=o(844),n=o(9092);class d extends u.Disposable{constructor(p,h){super(),this._optionsService=p,this._bufferService=h,this._onBufferActivate=this.register(new l.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new n.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new n.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(p){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(p),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(p,h){this._normal.resize(p,h),this._alt.resize(p,h),this.setupTabStops(p)}setupTabStops(p){this._normal.setupTabStops(p),this._alt.setupTabStops(p)}}r.BufferSet=d},511:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CellData=void 0;const l=o(482),u=o(643),n=o(3734);class d extends n.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new n.ExtendedAttrs,this.combinedData=""}static fromCharData(p){const h=new d;return h.setFromCharData(p),h}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,l.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(p){this.fg=p[u.CHAR_DATA_ATTR_INDEX],this.bg=0;let h=!1;if(p[u.CHAR_DATA_CHAR_INDEX].length>2)h=!0;else if(p[u.CHAR_DATA_CHAR_INDEX].length===2){const t=p[u.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=t&&t<=56319){const s=p[u.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=s&&s<=57343?this.content=1024*(t-55296)+s-56320+65536|p[u.CHAR_DATA_WIDTH_INDEX]<<22:h=!0}else h=!0}else this.content=p[u.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|p[u.CHAR_DATA_WIDTH_INDEX]<<22;h&&(this.combinedData=p[u.CHAR_DATA_CHAR_INDEX],this.content=2097152|p[u.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}r.CellData=d},643:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.WHITESPACE_CELL_CODE=r.WHITESPACE_CELL_WIDTH=r.WHITESPACE_CELL_CHAR=r.NULL_CELL_CODE=r.NULL_CELL_WIDTH=r.NULL_CELL_CHAR=r.CHAR_DATA_CODE_INDEX=r.CHAR_DATA_WIDTH_INDEX=r.CHAR_DATA_CHAR_INDEX=r.CHAR_DATA_ATTR_INDEX=r.DEFAULT_EXT=r.DEFAULT_ATTR=r.DEFAULT_COLOR=void 0,r.DEFAULT_COLOR=0,r.DEFAULT_ATTR=256|r.DEFAULT_COLOR<<9,r.DEFAULT_EXT=0,r.CHAR_DATA_ATTR_INDEX=0,r.CHAR_DATA_CHAR_INDEX=1,r.CHAR_DATA_WIDTH_INDEX=2,r.CHAR_DATA_CODE_INDEX=3,r.NULL_CELL_CHAR="",r.NULL_CELL_WIDTH=1,r.NULL_CELL_CODE=0,r.WHITESPACE_CELL_CHAR=" ",r.WHITESPACE_CELL_WIDTH=1,r.WHITESPACE_CELL_CODE=32},4863:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Marker=void 0;const l=o(8460),u=o(844);class n{get id(){return this._id}constructor(f){this.line=f,this.isDisposed=!1,this._disposables=[],this._id=n._nextId++,this._onDispose=this.register(new l.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,u.disposeArray)(this._disposables),this._disposables.length=0)}register(f){return this._disposables.push(f),f}}r.Marker=n,n._nextId=1},7116:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DEFAULT_CHARSET=r.CHARSETS=void 0,r.CHARSETS={},r.DEFAULT_CHARSET=r.CHARSETS.B,r.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},r.CHARSETS.A={"#":"£"},r.CHARSETS.B=void 0,r.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},r.CHARSETS.C=r.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},r.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},r.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},r.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},r.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},r.CHARSETS.E=r.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},r.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},r.CHARSETS.H=r.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},r.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(B,r)=>{var o,l,u;Object.defineProperty(r,"__esModule",{value:!0}),r.C1_ESCAPED=r.C1=r.C0=void 0,function(n){n.NUL="\0",n.SOH="",n.STX="",n.ETX="",n.EOT="",n.ENQ="",n.ACK="",n.BEL="\x07",n.BS="\b",n.HT="	",n.LF=`
`,n.VT="\v",n.FF="\f",n.CR="\r",n.SO="",n.SI="",n.DLE="",n.DC1="",n.DC2="",n.DC3="",n.DC4="",n.NAK="",n.SYN="",n.ETB="",n.CAN="",n.EM="",n.SUB="",n.ESC="\x1B",n.FS="",n.GS="",n.RS="",n.US="",n.SP=" ",n.DEL=""}(o||(r.C0=o={})),function(n){n.PAD="",n.HOP="",n.BPH="",n.NBH="",n.IND="",n.NEL="",n.SSA="",n.ESA="",n.HTS="",n.HTJ="",n.VTS="",n.PLD="",n.PLU="",n.RI="",n.SS2="",n.SS3="",n.DCS="",n.PU1="",n.PU2="",n.STS="",n.CCH="",n.MW="",n.SPA="",n.EPA="",n.SOS="",n.SGCI="",n.SCI="",n.CSI="",n.ST="",n.OSC="",n.PM="",n.APC=""}(l||(r.C1=l={})),function(n){n.ST=`${o.ESC}\\`}(u||(r.C1_ESCAPED=u={}))},7399:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.evaluateKeyboardEvent=void 0;const l=o(2584),u={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};r.evaluateKeyboardEvent=function(n,d,f,p){const h={type:0,cancel:!1,key:void 0},t=(n.shiftKey?1:0)|(n.altKey?2:0)|(n.ctrlKey?4:0)|(n.metaKey?8:0);switch(n.keyCode){case 0:n.key==="UIKeyInputUpArrow"?h.key=d?l.C0.ESC+"OA":l.C0.ESC+"[A":n.key==="UIKeyInputLeftArrow"?h.key=d?l.C0.ESC+"OD":l.C0.ESC+"[D":n.key==="UIKeyInputRightArrow"?h.key=d?l.C0.ESC+"OC":l.C0.ESC+"[C":n.key==="UIKeyInputDownArrow"&&(h.key=d?l.C0.ESC+"OB":l.C0.ESC+"[B");break;case 8:h.key=n.ctrlKey?"\b":l.C0.DEL,n.altKey&&(h.key=l.C0.ESC+h.key);break;case 9:if(n.shiftKey){h.key=l.C0.ESC+"[Z";break}h.key=l.C0.HT,h.cancel=!0;break;case 13:h.key=n.altKey?l.C0.ESC+l.C0.CR:l.C0.CR,h.cancel=!0;break;case 27:h.key=l.C0.ESC,n.altKey&&(h.key=l.C0.ESC+l.C0.ESC),h.cancel=!0;break;case 37:if(n.metaKey)break;t?(h.key=l.C0.ESC+"[1;"+(t+1)+"D",h.key===l.C0.ESC+"[1;3D"&&(h.key=l.C0.ESC+(f?"b":"[1;5D"))):h.key=d?l.C0.ESC+"OD":l.C0.ESC+"[D";break;case 39:if(n.metaKey)break;t?(h.key=l.C0.ESC+"[1;"+(t+1)+"C",h.key===l.C0.ESC+"[1;3C"&&(h.key=l.C0.ESC+(f?"f":"[1;5C"))):h.key=d?l.C0.ESC+"OC":l.C0.ESC+"[C";break;case 38:if(n.metaKey)break;t?(h.key=l.C0.ESC+"[1;"+(t+1)+"A",f||h.key!==l.C0.ESC+"[1;3A"||(h.key=l.C0.ESC+"[1;5A")):h.key=d?l.C0.ESC+"OA":l.C0.ESC+"[A";break;case 40:if(n.metaKey)break;t?(h.key=l.C0.ESC+"[1;"+(t+1)+"B",f||h.key!==l.C0.ESC+"[1;3B"||(h.key=l.C0.ESC+"[1;5B")):h.key=d?l.C0.ESC+"OB":l.C0.ESC+"[B";break;case 45:n.shiftKey||n.ctrlKey||(h.key=l.C0.ESC+"[2~");break;case 46:h.key=t?l.C0.ESC+"[3;"+(t+1)+"~":l.C0.ESC+"[3~";break;case 36:h.key=t?l.C0.ESC+"[1;"+(t+1)+"H":d?l.C0.ESC+"OH":l.C0.ESC+"[H";break;case 35:h.key=t?l.C0.ESC+"[1;"+(t+1)+"F":d?l.C0.ESC+"OF":l.C0.ESC+"[F";break;case 33:n.shiftKey?h.type=2:n.ctrlKey?h.key=l.C0.ESC+"[5;"+(t+1)+"~":h.key=l.C0.ESC+"[5~";break;case 34:n.shiftKey?h.type=3:n.ctrlKey?h.key=l.C0.ESC+"[6;"+(t+1)+"~":h.key=l.C0.ESC+"[6~";break;case 112:h.key=t?l.C0.ESC+"[1;"+(t+1)+"P":l.C0.ESC+"OP";break;case 113:h.key=t?l.C0.ESC+"[1;"+(t+1)+"Q":l.C0.ESC+"OQ";break;case 114:h.key=t?l.C0.ESC+"[1;"+(t+1)+"R":l.C0.ESC+"OR";break;case 115:h.key=t?l.C0.ESC+"[1;"+(t+1)+"S":l.C0.ESC+"OS";break;case 116:h.key=t?l.C0.ESC+"[15;"+(t+1)+"~":l.C0.ESC+"[15~";break;case 117:h.key=t?l.C0.ESC+"[17;"+(t+1)+"~":l.C0.ESC+"[17~";break;case 118:h.key=t?l.C0.ESC+"[18;"+(t+1)+"~":l.C0.ESC+"[18~";break;case 119:h.key=t?l.C0.ESC+"[19;"+(t+1)+"~":l.C0.ESC+"[19~";break;case 120:h.key=t?l.C0.ESC+"[20;"+(t+1)+"~":l.C0.ESC+"[20~";break;case 121:h.key=t?l.C0.ESC+"[21;"+(t+1)+"~":l.C0.ESC+"[21~";break;case 122:h.key=t?l.C0.ESC+"[23;"+(t+1)+"~":l.C0.ESC+"[23~";break;case 123:h.key=t?l.C0.ESC+"[24;"+(t+1)+"~":l.C0.ESC+"[24~";break;default:if(!n.ctrlKey||n.shiftKey||n.altKey||n.metaKey)if(f&&!p||!n.altKey||n.metaKey)!f||n.altKey||n.ctrlKey||n.shiftKey||!n.metaKey?n.key&&!n.ctrlKey&&!n.altKey&&!n.metaKey&&n.keyCode>=48&&n.key.length===1?h.key=n.key:n.key&&n.ctrlKey&&(n.key==="_"&&(h.key=l.C0.US),n.key==="@"&&(h.key=l.C0.NUL)):n.keyCode===65&&(h.type=1);else{const s=u[n.keyCode],e=s?.[n.shiftKey?1:0];if(e)h.key=l.C0.ESC+e;else if(n.keyCode>=65&&n.keyCode<=90){const i=n.ctrlKey?n.keyCode-64:n.keyCode+32;let a=String.fromCharCode(i);n.shiftKey&&(a=a.toUpperCase()),h.key=l.C0.ESC+a}else if(n.keyCode===32)h.key=l.C0.ESC+(n.ctrlKey?l.C0.NUL:" ");else if(n.key==="Dead"&&n.code.startsWith("Key")){let i=n.code.slice(3,4);n.shiftKey||(i=i.toLowerCase()),h.key=l.C0.ESC+i,h.cancel=!0}}else n.keyCode>=65&&n.keyCode<=90?h.key=String.fromCharCode(n.keyCode-64):n.keyCode===32?h.key=l.C0.NUL:n.keyCode>=51&&n.keyCode<=55?h.key=String.fromCharCode(n.keyCode-51+27):n.keyCode===56?h.key=l.C0.DEL:n.keyCode===219?h.key=l.C0.ESC:n.keyCode===220?h.key=l.C0.FS:n.keyCode===221&&(h.key=l.C0.GS)}return h}},482:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Utf8ToUtf32=r.StringToUtf32=r.utf32ToString=r.stringFromCodePoint=void 0,r.stringFromCodePoint=function(o){return o>65535?(o-=65536,String.fromCharCode(55296+(o>>10))+String.fromCharCode(o%1024+56320)):String.fromCharCode(o)},r.utf32ToString=function(o,l=0,u=o.length){let n="";for(let d=l;d<u;++d){let f=o[d];f>65535?(f-=65536,n+=String.fromCharCode(55296+(f>>10))+String.fromCharCode(f%1024+56320)):n+=String.fromCharCode(f)}return n},r.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(o,l){const u=o.length;if(!u)return 0;let n=0,d=0;if(this._interim){const f=o.charCodeAt(d++);56320<=f&&f<=57343?l[n++]=1024*(this._interim-55296)+f-56320+65536:(l[n++]=this._interim,l[n++]=f),this._interim=0}for(let f=d;f<u;++f){const p=o.charCodeAt(f);if(55296<=p&&p<=56319){if(++f>=u)return this._interim=p,n;const h=o.charCodeAt(f);56320<=h&&h<=57343?l[n++]=1024*(p-55296)+h-56320+65536:(l[n++]=p,l[n++]=h)}else p!==65279&&(l[n++]=p)}return n}},r.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(o,l){const u=o.length;if(!u)return 0;let n,d,f,p,h=0,t=0,s=0;if(this.interim[0]){let a=!1,v=this.interim[0];v&=(224&v)==192?31:(240&v)==224?15:7;let _,g=0;for(;(_=63&this.interim[++g])&&g<4;)v<<=6,v|=_;const c=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,m=c-g;for(;s<m;){if(s>=u)return 0;if(_=o[s++],(192&_)!=128){s--,a=!0;break}this.interim[g++]=_,v<<=6,v|=63&_}a||(c===2?v<128?s--:l[h++]=v:c===3?v<2048||v>=55296&&v<=57343||v===65279||(l[h++]=v):v<65536||v>1114111||(l[h++]=v)),this.interim.fill(0)}const e=u-4;let i=s;for(;i<u;){for(;!(!(i<e)||128&(n=o[i])||128&(d=o[i+1])||128&(f=o[i+2])||128&(p=o[i+3]));)l[h++]=n,l[h++]=d,l[h++]=f,l[h++]=p,i+=4;if(n=o[i++],n<128)l[h++]=n;else if((224&n)==192){if(i>=u)return this.interim[0]=n,h;if(d=o[i++],(192&d)!=128){i--;continue}if(t=(31&n)<<6|63&d,t<128){i--;continue}l[h++]=t}else if((240&n)==224){if(i>=u)return this.interim[0]=n,h;if(d=o[i++],(192&d)!=128){i--;continue}if(i>=u)return this.interim[0]=n,this.interim[1]=d,h;if(f=o[i++],(192&f)!=128){i--;continue}if(t=(15&n)<<12|(63&d)<<6|63&f,t<2048||t>=55296&&t<=57343||t===65279)continue;l[h++]=t}else if((248&n)==240){if(i>=u)return this.interim[0]=n,h;if(d=o[i++],(192&d)!=128){i--;continue}if(i>=u)return this.interim[0]=n,this.interim[1]=d,h;if(f=o[i++],(192&f)!=128){i--;continue}if(i>=u)return this.interim[0]=n,this.interim[1]=d,this.interim[2]=f,h;if(p=o[i++],(192&p)!=128){i--;continue}if(t=(7&n)<<18|(63&d)<<12|(63&f)<<6|63&p,t<65536||t>1114111)continue;l[h++]=t}}return h}}},225:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.UnicodeV6=void 0;const l=o(1480),u=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],n=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let d;r.UnicodeV6=class{constructor(){if(this.version="6",!d){d=new Uint8Array(65536),d.fill(1),d[0]=0,d.fill(0,1,32),d.fill(0,127,160),d.fill(2,4352,4448),d[9001]=2,d[9002]=2,d.fill(2,11904,42192),d[12351]=1,d.fill(2,44032,55204),d.fill(2,63744,64256),d.fill(2,65040,65050),d.fill(2,65072,65136),d.fill(2,65280,65377),d.fill(2,65504,65511);for(let f=0;f<u.length;++f)d.fill(0,u[f][0],u[f][1]+1)}}wcwidth(f){return f<32?0:f<127?1:f<65536?d[f]:function(p,h){let t,s=0,e=h.length-1;if(p<h[0][0]||p>h[e][1])return!1;for(;e>=s;)if(t=s+e>>1,p>h[t][1])s=t+1;else{if(!(p<h[t][0]))return!0;e=t-1}return!1}(f,n)?0:f>=131072&&f<=196605||f>=196608&&f<=262141?2:1}charProperties(f,p){let h=this.wcwidth(f),t=h===0&&p!==0;if(t){const s=l.UnicodeService.extractWidth(p);s===0?t=!1:s>h&&(h=s)}return l.UnicodeService.createPropertyValue(0,h,t)}}},5981:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.WriteBuffer=void 0;const l=o(8460),u=o(844);class n extends u.Disposable{constructor(f){super(),this._action=f,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new l.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(f,p){if(p!==void 0&&this._syncCalls>p)return void(this._syncCalls=0);if(this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let h;for(this._isSyncWriting=!0;h=this._writeBuffer.shift();){this._action(h);const t=this._callbacks.shift();t&&t()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(f,p){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(p),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(p)}_innerWrite(f=0,p=!0){const h=f||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const t=this._writeBuffer[this._bufferOffset],s=this._action(t,p);if(s){const i=a=>Date.now()-h>=12?setTimeout(()=>this._innerWrite(0,a)):this._innerWrite(h,a);return void s.catch(a=>(queueMicrotask(()=>{throw a}),Promise.resolve(!1))).then(i)}const e=this._callbacks[this._bufferOffset];if(e&&e(),this._bufferOffset++,this._pendingData-=t.length,Date.now()-h>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}r.WriteBuffer=n},5941:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.toRgbString=r.parseColor=void 0;const o=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,l=/^[\da-f]+$/;function u(n,d){const f=n.toString(16),p=f.length<2?"0"+f:f;switch(d){case 4:return f[0];case 8:return p;case 12:return(p+p).slice(0,3);default:return p+p}}r.parseColor=function(n){if(!n)return;let d=n.toLowerCase();if(d.indexOf("rgb:")===0){d=d.slice(4);const f=o.exec(d);if(f){const p=f[1]?15:f[4]?255:f[7]?4095:65535;return[Math.round(parseInt(f[1]||f[4]||f[7]||f[10],16)/p*255),Math.round(parseInt(f[2]||f[5]||f[8]||f[11],16)/p*255),Math.round(parseInt(f[3]||f[6]||f[9]||f[12],16)/p*255)]}}else if(d.indexOf("#")===0&&(d=d.slice(1),l.exec(d)&&[3,6,9,12].includes(d.length))){const f=d.length/3,p=[0,0,0];for(let h=0;h<3;++h){const t=parseInt(d.slice(f*h,f*h+f),16);p[h]=f===1?t<<4:f===2?t:f===3?t>>4:t>>8}return p}},r.toRgbString=function(n,d=16){const[f,p,h]=n;return`rgb:${u(f,d)}/${u(p,d)}/${u(h,d)}`}},5770:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.PAYLOAD_LIMIT=void 0,r.PAYLOAD_LIMIT=1e7},6351:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DcsHandler=r.DcsParser=void 0;const l=o(482),u=o(8742),n=o(5770),d=[];r.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=d,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=d}registerHandler(p,h){this._handlers[p]===void 0&&(this._handlers[p]=[]);const t=this._handlers[p];return t.push(h),{dispose:()=>{const s=t.indexOf(h);s!==-1&&t.splice(s,1)}}}clearHandler(p){this._handlers[p]&&delete this._handlers[p]}setHandlerFallback(p){this._handlerFb=p}reset(){if(this._active.length)for(let p=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;p>=0;--p)this._active[p].unhook(!1);this._stack.paused=!1,this._active=d,this._ident=0}hook(p,h){if(this.reset(),this._ident=p,this._active=this._handlers[p]||d,this._active.length)for(let t=this._active.length-1;t>=0;t--)this._active[t].hook(h);else this._handlerFb(this._ident,"HOOK",h)}put(p,h,t){if(this._active.length)for(let s=this._active.length-1;s>=0;s--)this._active[s].put(p,h,t);else this._handlerFb(this._ident,"PUT",(0,l.utf32ToString)(p,h,t))}unhook(p,h=!0){if(this._active.length){let t=!1,s=this._active.length-1,e=!1;if(this._stack.paused&&(s=this._stack.loopPosition-1,t=h,e=this._stack.fallThrough,this._stack.paused=!1),!e&&t===!1){for(;s>=0&&(t=this._active[s].unhook(p),t!==!0);s--)if(t instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=s,this._stack.fallThrough=!1,t;s--}for(;s>=0;s--)if(t=this._active[s].unhook(!1),t instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=s,this._stack.fallThrough=!0,t}else this._handlerFb(this._ident,"UNHOOK",p);this._active=d,this._ident=0}};const f=new u.Params;f.addParam(0),r.DcsHandler=class{constructor(p){this._handler=p,this._data="",this._params=f,this._hitLimit=!1}hook(p){this._params=p.length>1||p.params[0]?p.clone():f,this._data="",this._hitLimit=!1}put(p,h,t){this._hitLimit||(this._data+=(0,l.utf32ToString)(p,h,t),this._data.length>n.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(p){let h=!1;if(this._hitLimit)h=!1;else if(p&&(h=this._handler(this._data,this._params),h instanceof Promise))return h.then(t=>(this._params=f,this._data="",this._hitLimit=!1,t));return this._params=f,this._data="",this._hitLimit=!1,h}}},2015:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.EscapeSequenceParser=r.VT500_TRANSITION_TABLE=r.TransitionTable=void 0;const l=o(844),u=o(8742),n=o(6242),d=o(6351);class f{constructor(s){this.table=new Uint8Array(s)}setDefault(s,e){this.table.fill(s<<4|e)}add(s,e,i,a){this.table[e<<8|s]=i<<4|a}addMany(s,e,i,a){for(let v=0;v<s.length;v++)this.table[e<<8|s[v]]=i<<4|a}}r.TransitionTable=f;const p=160;r.VT500_TRANSITION_TABLE=function(){const t=new f(4095),s=Array.apply(null,Array(256)).map((g,c)=>c),e=(g,c)=>s.slice(g,c),i=e(32,127),a=e(0,24);a.push(25),a.push.apply(a,e(28,32));const v=e(0,14);let _;for(_ in t.setDefault(1,0),t.addMany(i,0,2,0),v)t.addMany([24,26,153,154],_,3,0),t.addMany(e(128,144),_,3,0),t.addMany(e(144,152),_,3,0),t.add(156,_,0,0),t.add(27,_,11,1),t.add(157,_,4,8),t.addMany([152,158,159],_,0,7),t.add(155,_,11,3),t.add(144,_,11,9);return t.addMany(a,0,3,0),t.addMany(a,1,3,1),t.add(127,1,0,1),t.addMany(a,8,0,8),t.addMany(a,3,3,3),t.add(127,3,0,3),t.addMany(a,4,3,4),t.add(127,4,0,4),t.addMany(a,6,3,6),t.addMany(a,5,3,5),t.add(127,5,0,5),t.addMany(a,2,3,2),t.add(127,2,0,2),t.add(93,1,4,8),t.addMany(i,8,5,8),t.add(127,8,5,8),t.addMany([156,27,24,26,7],8,6,0),t.addMany(e(28,32),8,0,8),t.addMany([88,94,95],1,0,7),t.addMany(i,7,0,7),t.addMany(a,7,0,7),t.add(156,7,0,0),t.add(127,7,0,7),t.add(91,1,11,3),t.addMany(e(64,127),3,7,0),t.addMany(e(48,60),3,8,4),t.addMany([60,61,62,63],3,9,4),t.addMany(e(48,60),4,8,4),t.addMany(e(64,127),4,7,0),t.addMany([60,61,62,63],4,0,6),t.addMany(e(32,64),6,0,6),t.add(127,6,0,6),t.addMany(e(64,127),6,0,0),t.addMany(e(32,48),3,9,5),t.addMany(e(32,48),5,9,5),t.addMany(e(48,64),5,0,6),t.addMany(e(64,127),5,7,0),t.addMany(e(32,48),4,9,5),t.addMany(e(32,48),1,9,2),t.addMany(e(32,48),2,9,2),t.addMany(e(48,127),2,10,0),t.addMany(e(48,80),1,10,0),t.addMany(e(81,88),1,10,0),t.addMany([89,90,92],1,10,0),t.addMany(e(96,127),1,10,0),t.add(80,1,11,9),t.addMany(a,9,0,9),t.add(127,9,0,9),t.addMany(e(28,32),9,0,9),t.addMany(e(32,48),9,9,12),t.addMany(e(48,60),9,8,10),t.addMany([60,61,62,63],9,9,10),t.addMany(a,11,0,11),t.addMany(e(32,128),11,0,11),t.addMany(e(28,32),11,0,11),t.addMany(a,10,0,10),t.add(127,10,0,10),t.addMany(e(28,32),10,0,10),t.addMany(e(48,60),10,8,10),t.addMany([60,61,62,63],10,0,11),t.addMany(e(32,48),10,9,12),t.addMany(a,12,0,12),t.add(127,12,0,12),t.addMany(e(28,32),12,0,12),t.addMany(e(32,48),12,9,12),t.addMany(e(48,64),12,0,11),t.addMany(e(64,127),12,12,13),t.addMany(e(64,127),10,12,13),t.addMany(e(64,127),9,12,13),t.addMany(a,13,13,13),t.addMany(i,13,13,13),t.add(127,13,0,13),t.addMany([27,156,24,26],13,14,0),t.add(p,0,2,0),t.add(p,8,5,8),t.add(p,6,0,6),t.add(p,11,0,11),t.add(p,13,13,13),t}();class h extends l.Disposable{constructor(s=r.VT500_TRANSITION_TABLE){super(),this._transitions=s,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new u.Params,this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._printHandlerFb=(e,i,a)=>{},this._executeHandlerFb=e=>{},this._csiHandlerFb=(e,i)=>{},this._escHandlerFb=e=>{},this._errorHandlerFb=e=>e,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,l.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new n.OscParser),this._dcsParser=this.register(new d.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(s,e=[64,126]){let i=0;if(s.prefix){if(s.prefix.length>1)throw new Error("only one byte as prefix supported");if(i=s.prefix.charCodeAt(0),i&&60>i||i>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(s.intermediates){if(s.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let v=0;v<s.intermediates.length;++v){const _=s.intermediates.charCodeAt(v);if(32>_||_>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");i<<=8,i|=_}}if(s.final.length!==1)throw new Error("final must be a single byte");const a=s.final.charCodeAt(0);if(e[0]>a||a>e[1])throw new Error(`final must be in range ${e[0]} .. ${e[1]}`);return i<<=8,i|=a,i}identToString(s){const e=[];for(;s;)e.push(String.fromCharCode(255&s)),s>>=8;return e.reverse().join("")}setPrintHandler(s){this._printHandler=s}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(s,e){const i=this._identifier(s,[48,126]);this._escHandlers[i]===void 0&&(this._escHandlers[i]=[]);const a=this._escHandlers[i];return a.push(e),{dispose:()=>{const v=a.indexOf(e);v!==-1&&a.splice(v,1)}}}clearEscHandler(s){this._escHandlers[this._identifier(s,[48,126])]&&delete this._escHandlers[this._identifier(s,[48,126])]}setEscHandlerFallback(s){this._escHandlerFb=s}setExecuteHandler(s,e){this._executeHandlers[s.charCodeAt(0)]=e}clearExecuteHandler(s){this._executeHandlers[s.charCodeAt(0)]&&delete this._executeHandlers[s.charCodeAt(0)]}setExecuteHandlerFallback(s){this._executeHandlerFb=s}registerCsiHandler(s,e){const i=this._identifier(s);this._csiHandlers[i]===void 0&&(this._csiHandlers[i]=[]);const a=this._csiHandlers[i];return a.push(e),{dispose:()=>{const v=a.indexOf(e);v!==-1&&a.splice(v,1)}}}clearCsiHandler(s){this._csiHandlers[this._identifier(s)]&&delete this._csiHandlers[this._identifier(s)]}setCsiHandlerFallback(s){this._csiHandlerFb=s}registerDcsHandler(s,e){return this._dcsParser.registerHandler(this._identifier(s),e)}clearDcsHandler(s){this._dcsParser.clearHandler(this._identifier(s))}setDcsHandlerFallback(s){this._dcsParser.setHandlerFallback(s)}registerOscHandler(s,e){return this._oscParser.registerHandler(s,e)}clearOscHandler(s){this._oscParser.clearHandler(s)}setOscHandlerFallback(s){this._oscParser.setHandlerFallback(s)}setErrorHandler(s){this._errorHandler=s}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(s,e,i,a,v){this._parseStack.state=s,this._parseStack.handlers=e,this._parseStack.handlerPos=i,this._parseStack.transition=a,this._parseStack.chunkPos=v}parse(s,e,i){let a,v=0,_=0,g=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,g=this._parseStack.chunkPos+1;else{if(i===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const c=this._parseStack.handlers;let m=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(i===!1&&m>-1){for(;m>=0&&(a=c[m](this._params),a!==!0);m--)if(a instanceof Promise)return this._parseStack.handlerPos=m,a}this._parseStack.handlers=[];break;case 4:if(i===!1&&m>-1){for(;m>=0&&(a=c[m](),a!==!0);m--)if(a instanceof Promise)return this._parseStack.handlerPos=m,a}this._parseStack.handlers=[];break;case 6:if(v=s[this._parseStack.chunkPos],a=this._dcsParser.unhook(v!==24&&v!==26,i),a)return a;v===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(v=s[this._parseStack.chunkPos],a=this._oscParser.end(v!==24&&v!==26,i),a)return a;v===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,g=this._parseStack.chunkPos+1,this.precedingJoinState=0,this.currentState=15&this._parseStack.transition}for(let c=g;c<e;++c){switch(v=s[c],_=this._transitions.table[this.currentState<<8|(v<160?v:p)],_>>4){case 2:for(let b=c+1;;++b){if(b>=e||(v=s[b])<32||v>126&&v<p){this._printHandler(s,c,b),c=b-1;break}if(++b>=e||(v=s[b])<32||v>126&&v<p){this._printHandler(s,c,b),c=b-1;break}if(++b>=e||(v=s[b])<32||v>126&&v<p){this._printHandler(s,c,b),c=b-1;break}if(++b>=e||(v=s[b])<32||v>126&&v<p){this._printHandler(s,c,b),c=b-1;break}}break;case 3:this._executeHandlers[v]?this._executeHandlers[v]():this._executeHandlerFb(v),this.precedingJoinState=0;break;case 0:break;case 1:if(this._errorHandler({position:c,code:v,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const m=this._csiHandlers[this._collect<<8|v];let E=m?m.length-1:-1;for(;E>=0&&(a=m[E](this._params),a!==!0);E--)if(a instanceof Promise)return this._preserveStack(3,m,E,_,c),a;E<0&&this._csiHandlerFb(this._collect<<8|v,this._params),this.precedingJoinState=0;break;case 8:do switch(v){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(v-48)}while(++c<e&&(v=s[c])>47&&v<60);c--;break;case 9:this._collect<<=8,this._collect|=v;break;case 10:const k=this._escHandlers[this._collect<<8|v];let D=k?k.length-1:-1;for(;D>=0&&(a=k[D](),a!==!0);D--)if(a instanceof Promise)return this._preserveStack(4,k,D,_,c),a;D<0&&this._escHandlerFb(this._collect<<8|v),this.precedingJoinState=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|v,this._params);break;case 13:for(let b=c+1;;++b)if(b>=e||(v=s[b])===24||v===26||v===27||v>127&&v<p){this._dcsParser.put(s,c,b),c=b-1;break}break;case 14:if(a=this._dcsParser.unhook(v!==24&&v!==26),a)return this._preserveStack(6,[],0,_,c),a;v===27&&(_|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0;break;case 4:this._oscParser.start();break;case 5:for(let b=c+1;;b++)if(b>=e||(v=s[b])<32||v>127&&v<p){this._oscParser.put(s,c,b),c=b-1;break}break;case 6:if(a=this._oscParser.end(v!==24&&v!==26),a)return this._preserveStack(5,[],0,_,c),a;v===27&&(_|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0}this.currentState=15&_}}}r.EscapeSequenceParser=h},6242:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.OscHandler=r.OscParser=void 0;const l=o(5770),u=o(482),n=[];r.OscParser=class{constructor(){this._state=0,this._active=n,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(d,f){this._handlers[d]===void 0&&(this._handlers[d]=[]);const p=this._handlers[d];return p.push(f),{dispose:()=>{const h=p.indexOf(f);h!==-1&&p.splice(h,1)}}}clearHandler(d){this._handlers[d]&&delete this._handlers[d]}setHandlerFallback(d){this._handlerFb=d}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=n}reset(){if(this._state===2)for(let d=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;d>=0;--d)this._active[d].end(!1);this._stack.paused=!1,this._active=n,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||n,this._active.length)for(let d=this._active.length-1;d>=0;d--)this._active[d].start();else this._handlerFb(this._id,"START")}_put(d,f,p){if(this._active.length)for(let h=this._active.length-1;h>=0;h--)this._active[h].put(d,f,p);else this._handlerFb(this._id,"PUT",(0,u.utf32ToString)(d,f,p))}start(){this.reset(),this._state=1}put(d,f,p){if(this._state!==3){if(this._state===1)for(;f<p;){const h=d[f++];if(h===59){this._state=2,this._start();break}if(h<48||57<h)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+h-48}this._state===2&&p-f>0&&this._put(d,f,p)}}end(d,f=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let p=!1,h=this._active.length-1,t=!1;if(this._stack.paused&&(h=this._stack.loopPosition-1,p=f,t=this._stack.fallThrough,this._stack.paused=!1),!t&&p===!1){for(;h>=0&&(p=this._active[h].end(d),p!==!0);h--)if(p instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=h,this._stack.fallThrough=!1,p;h--}for(;h>=0;h--)if(p=this._active[h].end(!1),p instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=h,this._stack.fallThrough=!0,p}else this._handlerFb(this._id,"END",d);this._active=n,this._id=-1,this._state=0}}},r.OscHandler=class{constructor(d){this._handler=d,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(d,f,p){this._hitLimit||(this._data+=(0,u.utf32ToString)(d,f,p),this._data.length>l.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(d){let f=!1;if(this._hitLimit)f=!1;else if(d&&(f=this._handler(this._data),f instanceof Promise))return f.then(p=>(this._data="",this._hitLimit=!1,p));return this._data="",this._hitLimit=!1,f}}},8742:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Params=void 0;const o=2147483647;class l{static fromArray(n){const d=new l;if(!n.length)return d;for(let f=Array.isArray(n[0])?1:0;f<n.length;++f){const p=n[f];if(Array.isArray(p))for(let h=0;h<p.length;++h)d.addSubParam(p[h]);else d.addParam(p)}return d}constructor(n=32,d=32){if(this.maxLength=n,this.maxSubParamsLength=d,d>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(n),this.length=0,this._subParams=new Int32Array(d),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(n),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const n=new l(this.maxLength,this.maxSubParamsLength);return n.params.set(this.params),n.length=this.length,n._subParams.set(this._subParams),n._subParamsLength=this._subParamsLength,n._subParamsIdx.set(this._subParamsIdx),n._rejectDigits=this._rejectDigits,n._rejectSubDigits=this._rejectSubDigits,n._digitIsSub=this._digitIsSub,n}toArray(){const n=[];for(let d=0;d<this.length;++d){n.push(this.params[d]);const f=this._subParamsIdx[d]>>8,p=255&this._subParamsIdx[d];p-f>0&&n.push(Array.prototype.slice.call(this._subParams,f,p))}return n}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(n){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(n<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=n>o?o:n}}addSubParam(n){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(n<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=n>o?o:n,this._subParamsIdx[this.length-1]++}}hasSubParams(n){return(255&this._subParamsIdx[n])-(this._subParamsIdx[n]>>8)>0}getSubParams(n){const d=this._subParamsIdx[n]>>8,f=255&this._subParamsIdx[n];return f-d>0?this._subParams.subarray(d,f):null}getSubParamsAll(){const n={};for(let d=0;d<this.length;++d){const f=this._subParamsIdx[d]>>8,p=255&this._subParamsIdx[d];p-f>0&&(n[d]=this._subParams.slice(f,p))}return n}addDigit(n){let d;if(this._rejectDigits||!(d=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const f=this._digitIsSub?this._subParams:this.params,p=f[d-1];f[d-1]=~p?Math.min(10*p+n,o):n}}r.Params=l},5741:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.AddonManager=void 0,r.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let o=this._addons.length-1;o>=0;o--)this._addons[o].instance.dispose()}loadAddon(o,l){const u={instance:l,dispose:l.dispose,isDisposed:!1};this._addons.push(u),l.dispose=()=>this._wrappedAddonDispose(u),l.activate(o)}_wrappedAddonDispose(o){if(o.isDisposed)return;let l=-1;for(let u=0;u<this._addons.length;u++)if(this._addons[u]===o){l=u;break}if(l===-1)throw new Error("Could not dispose an addon that has not been loaded");o.isDisposed=!0,o.dispose.apply(o.instance),this._addons.splice(l,1)}}},8771:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferApiView=void 0;const l=o(3785),u=o(511);r.BufferApiView=class{constructor(n,d){this._buffer=n,this.type=d}init(n){return this._buffer=n,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(n){const d=this._buffer.lines.get(n);if(d)return new l.BufferLineApiView(d)}getNullCell(){return new u.CellData}}},3785:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferLineApiView=void 0;const l=o(511);r.BufferLineApiView=class{constructor(u){this._line=u}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(u,n){if(!(u<0||u>=this._line.length))return n?(this._line.loadCell(u,n),n):this._line.loadCell(u,new l.CellData)}translateToString(u,n,d){return this._line.translateToString(u,n,d)}}},8285:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferNamespaceApi=void 0;const l=o(8771),u=o(8460),n=o(844);class d extends n.Disposable{constructor(p){super(),this._core=p,this._onBufferChange=this.register(new u.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new l.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new l.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}r.BufferNamespaceApi=d},7975:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ParserApi=void 0,r.ParserApi=class{constructor(o){this._core=o}registerCsiHandler(o,l){return this._core.registerCsiHandler(o,u=>l(u.toArray()))}addCsiHandler(o,l){return this.registerCsiHandler(o,l)}registerDcsHandler(o,l){return this._core.registerDcsHandler(o,(u,n)=>l(u,n.toArray()))}addDcsHandler(o,l){return this.registerDcsHandler(o,l)}registerEscHandler(o,l){return this._core.registerEscHandler(o,l)}addEscHandler(o,l){return this.registerEscHandler(o,l)}registerOscHandler(o,l){return this._core.registerOscHandler(o,l)}addOscHandler(o,l){return this.registerOscHandler(o,l)}}},7090:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.UnicodeApi=void 0,r.UnicodeApi=class{constructor(o){this._core=o}register(o){this._core.unicodeService.register(o)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(o){this._core.unicodeService.activeVersion=o}}},744:function(B,r,o){var l=this&&this.__decorate||function(t,s,e,i){var a,v=arguments.length,_=v<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,e):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")_=Reflect.decorate(t,s,e,i);else for(var g=t.length-1;g>=0;g--)(a=t[g])&&(_=(v<3?a(_):v>3?a(s,e,_):a(s,e))||_);return v>3&&_&&Object.defineProperty(s,e,_),_},u=this&&this.__param||function(t,s){return function(e,i){s(e,i,t)}};Object.defineProperty(r,"__esModule",{value:!0}),r.BufferService=r.MINIMUM_ROWS=r.MINIMUM_COLS=void 0;const n=o(8460),d=o(844),f=o(5295),p=o(2585);r.MINIMUM_COLS=2,r.MINIMUM_ROWS=1;let h=r.BufferService=class extends d.Disposable{get buffer(){return this.buffers.active}constructor(t){super(),this.isUserScrolling=!1,this._onResize=this.register(new n.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new n.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(t.rawOptions.cols||0,r.MINIMUM_COLS),this.rows=Math.max(t.rawOptions.rows||0,r.MINIMUM_ROWS),this.buffers=this.register(new f.BufferSet(t,this))}resize(t,s){this.cols=t,this.rows=s,this.buffers.resize(t,s),this._onResize.fire({cols:t,rows:s})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(t,s=!1){const e=this.buffer;let i;i=this._cachedBlankLine,i&&i.length===this.cols&&i.getFg(0)===t.fg&&i.getBg(0)===t.bg||(i=e.getBlankLine(t,s),this._cachedBlankLine=i),i.isWrapped=s;const a=e.ybase+e.scrollTop,v=e.ybase+e.scrollBottom;if(e.scrollTop===0){const _=e.lines.isFull;v===e.lines.length-1?_?e.lines.recycle().copyFrom(i):e.lines.push(i.clone()):e.lines.splice(v+1,0,i.clone()),_?this.isUserScrolling&&(e.ydisp=Math.max(e.ydisp-1,0)):(e.ybase++,this.isUserScrolling||e.ydisp++)}else{const _=v-a+1;e.lines.shiftElements(a+1,_-1,-1),e.lines.set(v,i.clone())}this.isUserScrolling||(e.ydisp=e.ybase),this._onScroll.fire(e.ydisp)}scrollLines(t,s,e){const i=this.buffer;if(t<0){if(i.ydisp===0)return;this.isUserScrolling=!0}else t+i.ydisp>=i.ybase&&(this.isUserScrolling=!1);const a=i.ydisp;i.ydisp=Math.max(Math.min(i.ydisp+t,i.ybase),0),a!==i.ydisp&&(s||this._onScroll.fire(i.ydisp))}};r.BufferService=h=l([u(0,p.IOptionsService)],h)},7994:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CharsetService=void 0,r.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(o){this.glevel=o,this.charset=this._charsets[o]}setgCharset(o,l){this._charsets[o]=l,this.glevel===o&&(this.charset=l)}}},1753:function(B,r,o){var l=this&&this.__decorate||function(i,a,v,_){var g,c=arguments.length,m=c<3?a:_===null?_=Object.getOwnPropertyDescriptor(a,v):_;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")m=Reflect.decorate(i,a,v,_);else for(var E=i.length-1;E>=0;E--)(g=i[E])&&(m=(c<3?g(m):c>3?g(a,v,m):g(a,v))||m);return c>3&&m&&Object.defineProperty(a,v,m),m},u=this&&this.__param||function(i,a){return function(v,_){a(v,_,i)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CoreMouseService=void 0;const n=o(2585),d=o(8460),f=o(844),p={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:i=>i.button!==4&&i.action===1&&(i.ctrl=!1,i.alt=!1,i.shift=!1,!0)},VT200:{events:19,restrict:i=>i.action!==32},DRAG:{events:23,restrict:i=>i.action!==32||i.button!==3},ANY:{events:31,restrict:i=>!0}};function h(i,a){let v=(i.ctrl?16:0)|(i.shift?4:0)|(i.alt?8:0);return i.button===4?(v|=64,v|=i.action):(v|=3&i.button,4&i.button&&(v|=64),8&i.button&&(v|=128),i.action===32?v|=32:i.action!==0||a||(v|=3)),v}const t=String.fromCharCode,s={DEFAULT:i=>{const a=[h(i,!1)+32,i.col+32,i.row+32];return a[0]>255||a[1]>255||a[2]>255?"":`\x1B[M${t(a[0])}${t(a[1])}${t(a[2])}`},SGR:i=>{const a=i.action===0&&i.button!==4?"m":"M";return`\x1B[<${h(i,!0)};${i.col};${i.row}${a}`},SGR_PIXELS:i=>{const a=i.action===0&&i.button!==4?"m":"M";return`\x1B[<${h(i,!0)};${i.x};${i.y}${a}`}};let e=r.CoreMouseService=class extends f.Disposable{constructor(i,a){super(),this._bufferService=i,this._coreService=a,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new d.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const v of Object.keys(p))this.addProtocol(v,p[v]);for(const v of Object.keys(s))this.addEncoding(v,s[v]);this.reset()}addProtocol(i,a){this._protocols[i]=a}addEncoding(i,a){this._encodings[i]=a}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(i){if(!this._protocols[i])throw new Error(`unknown protocol "${i}"`);this._activeProtocol=i,this._onProtocolChange.fire(this._protocols[i].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(i){if(!this._encodings[i])throw new Error(`unknown encoding "${i}"`);this._activeEncoding=i}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(i){if(i.col<0||i.col>=this._bufferService.cols||i.row<0||i.row>=this._bufferService.rows||i.button===4&&i.action===32||i.button===3&&i.action!==32||i.button!==4&&(i.action===2||i.action===3)||(i.col++,i.row++,i.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,i,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(i))return!1;const a=this._encodings[this._activeEncoding](i);return a&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(a):this._coreService.triggerDataEvent(a,!0)),this._lastEvent=i,!0}explainEvents(i){return{down:!!(1&i),up:!!(2&i),drag:!!(4&i),move:!!(8&i),wheel:!!(16&i)}}_equalEvents(i,a,v){if(v){if(i.x!==a.x||i.y!==a.y)return!1}else if(i.col!==a.col||i.row!==a.row)return!1;return i.button===a.button&&i.action===a.action&&i.ctrl===a.ctrl&&i.alt===a.alt&&i.shift===a.shift}};r.CoreMouseService=e=l([u(0,n.IBufferService),u(1,n.ICoreService)],e)},6975:function(B,r,o){var l=this&&this.__decorate||function(e,i,a,v){var _,g=arguments.length,c=g<3?i:v===null?v=Object.getOwnPropertyDescriptor(i,a):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")c=Reflect.decorate(e,i,a,v);else for(var m=e.length-1;m>=0;m--)(_=e[m])&&(c=(g<3?_(c):g>3?_(i,a,c):_(i,a))||c);return g>3&&c&&Object.defineProperty(i,a,c),c},u=this&&this.__param||function(e,i){return function(a,v){i(a,v,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CoreService=void 0;const n=o(1439),d=o(8460),f=o(844),p=o(2585),h=Object.freeze({insertMode:!1}),t=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let s=r.CoreService=class extends f.Disposable{constructor(e,i,a){super(),this._bufferService=e,this._logService=i,this._optionsService=a,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new d.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new d.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new d.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new d.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,n.clone)(h),this.decPrivateModes=(0,n.clone)(t)}reset(){this.modes=(0,n.clone)(h),this.decPrivateModes=(0,n.clone)(t)}triggerDataEvent(e,i=!1){if(this._optionsService.rawOptions.disableStdin)return;const a=this._bufferService.buffer;i&&this._optionsService.rawOptions.scrollOnUserInput&&a.ybase!==a.ydisp&&this._onRequestScrollToBottom.fire(),i&&this._onUserInput.fire(),this._logService.debug(`sending data "${e}"`,()=>e.split("").map(v=>v.charCodeAt(0))),this._onData.fire(e)}triggerBinaryEvent(e){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${e}"`,()=>e.split("").map(i=>i.charCodeAt(0))),this._onBinary.fire(e))}};r.CoreService=s=l([u(0,p.IBufferService),u(1,p.ILogService),u(2,p.IOptionsService)],s)},9074:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DecorationService=void 0;const l=o(8055),u=o(8460),n=o(844),d=o(6106);let f=0,p=0;class h extends n.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new d.SortedList(e=>e?.marker.line),this._onDecorationRegistered=this.register(new u.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new u.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,n.toDisposable)(()=>this.reset()))}registerDecoration(e){if(e.marker.isDisposed)return;const i=new t(e);if(i){const a=i.marker.onDispose(()=>i.dispose());i.onDispose(()=>{i&&(this._decorations.delete(i)&&this._onDecorationRemoved.fire(i),a.dispose())}),this._decorations.insert(i),this._onDecorationRegistered.fire(i)}return i}reset(){for(const e of this._decorations.values())e.dispose();this._decorations.clear()}*getDecorationsAtCell(e,i,a){let v=0,_=0;for(const g of this._decorations.getKeyIterator(i))v=g.options.x??0,_=v+(g.options.width??1),e>=v&&e<_&&(!a||(g.options.layer??"bottom")===a)&&(yield g)}forEachDecorationAtCell(e,i,a,v){this._decorations.forEachByKey(i,_=>{f=_.options.x??0,p=f+(_.options.width??1),e>=f&&e<p&&(!a||(_.options.layer??"bottom")===a)&&v(_)})}}r.DecorationService=h;class t extends n.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=l.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=l.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(e){super(),this.options=e,this.onRenderEmitter=this.register(new u.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new u.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=e.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.InstantiationService=r.ServiceCollection=void 0;const l=o(2585),u=o(8343);class n{constructor(...f){this._entries=new Map;for(const[p,h]of f)this.set(p,h)}set(f,p){const h=this._entries.get(f);return this._entries.set(f,p),h}forEach(f){for(const[p,h]of this._entries.entries())f(p,h)}has(f){return this._entries.has(f)}get(f){return this._entries.get(f)}}r.ServiceCollection=n,r.InstantiationService=class{constructor(){this._services=new n,this._services.set(l.IInstantiationService,this)}setService(d,f){this._services.set(d,f)}getService(d){return this._services.get(d)}createInstance(d,...f){const p=(0,u.getServiceDependencies)(d).sort((s,e)=>s.index-e.index),h=[];for(const s of p){const e=this._services.get(s.id);if(!e)throw new Error(`[createInstance] ${d.name} depends on UNKNOWN service ${s.id}.`);h.push(e)}const t=p.length>0?p[0].index:f.length;if(f.length!==t)throw new Error(`[createInstance] First service dependency of ${d.name} at position ${t+1} conflicts with ${f.length} static arguments`);return new d(...f,...h)}}},7866:function(B,r,o){var l=this&&this.__decorate||function(t,s,e,i){var a,v=arguments.length,_=v<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,e):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")_=Reflect.decorate(t,s,e,i);else for(var g=t.length-1;g>=0;g--)(a=t[g])&&(_=(v<3?a(_):v>3?a(s,e,_):a(s,e))||_);return v>3&&_&&Object.defineProperty(s,e,_),_},u=this&&this.__param||function(t,s){return function(e,i){s(e,i,t)}};Object.defineProperty(r,"__esModule",{value:!0}),r.traceCall=r.setTraceLogger=r.LogService=void 0;const n=o(844),d=o(2585),f={trace:d.LogLevelEnum.TRACE,debug:d.LogLevelEnum.DEBUG,info:d.LogLevelEnum.INFO,warn:d.LogLevelEnum.WARN,error:d.LogLevelEnum.ERROR,off:d.LogLevelEnum.OFF};let p,h=r.LogService=class extends n.Disposable{get logLevel(){return this._logLevel}constructor(t){super(),this._optionsService=t,this._logLevel=d.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),p=this}_updateLogLevel(){this._logLevel=f[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(t){for(let s=0;s<t.length;s++)typeof t[s]=="function"&&(t[s]=t[s]())}_log(t,s,e){this._evalLazyOptionalParams(e),t.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+s,...e)}trace(t,...s){this._logLevel<=d.LogLevelEnum.TRACE&&this._log(this._optionsService.options.logger?.trace.bind(this._optionsService.options.logger)??console.log,t,s)}debug(t,...s){this._logLevel<=d.LogLevelEnum.DEBUG&&this._log(this._optionsService.options.logger?.debug.bind(this._optionsService.options.logger)??console.log,t,s)}info(t,...s){this._logLevel<=d.LogLevelEnum.INFO&&this._log(this._optionsService.options.logger?.info.bind(this._optionsService.options.logger)??console.info,t,s)}warn(t,...s){this._logLevel<=d.LogLevelEnum.WARN&&this._log(this._optionsService.options.logger?.warn.bind(this._optionsService.options.logger)??console.warn,t,s)}error(t,...s){this._logLevel<=d.LogLevelEnum.ERROR&&this._log(this._optionsService.options.logger?.error.bind(this._optionsService.options.logger)??console.error,t,s)}};r.LogService=h=l([u(0,d.IOptionsService)],h),r.setTraceLogger=function(t){p=t},r.traceCall=function(t,s,e){if(typeof e.value!="function")throw new Error("not supported");const i=e.value;e.value=function(...a){if(p.logLevel!==d.LogLevelEnum.TRACE)return i.apply(this,a);p.trace(`GlyphRenderer#${i.name}(${a.map(_=>JSON.stringify(_)).join(", ")})`);const v=i.apply(this,a);return p.trace(`GlyphRenderer#${i.name} return`,v),v}}},7302:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.OptionsService=r.DEFAULT_OPTIONS=void 0;const l=o(8460),u=o(844),n=o(6114);r.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,documentOverride:null,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rescaleOverlappingGlyphs:!1,rightClickSelectsWord:n.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const d=["normal","bold","100","200","300","400","500","600","700","800","900"];class f extends u.Disposable{constructor(h){super(),this._onOptionChange=this.register(new l.EventEmitter),this.onOptionChange=this._onOptionChange.event;const t={...r.DEFAULT_OPTIONS};for(const s in h)if(s in t)try{const e=h[s];t[s]=this._sanitizeAndValidateOption(s,e)}catch(e){console.error(e)}this.rawOptions=t,this.options={...t},this._setupOptions(),this.register((0,u.toDisposable)(()=>{this.rawOptions.linkHandler=null,this.rawOptions.documentOverride=null}))}onSpecificOptionChange(h,t){return this.onOptionChange(s=>{s===h&&t(this.rawOptions[h])})}onMultipleOptionChange(h,t){return this.onOptionChange(s=>{h.indexOf(s)!==-1&&t()})}_setupOptions(){const h=s=>{if(!(s in r.DEFAULT_OPTIONS))throw new Error(`No option with key "${s}"`);return this.rawOptions[s]},t=(s,e)=>{if(!(s in r.DEFAULT_OPTIONS))throw new Error(`No option with key "${s}"`);e=this._sanitizeAndValidateOption(s,e),this.rawOptions[s]!==e&&(this.rawOptions[s]=e,this._onOptionChange.fire(s))};for(const s in this.rawOptions){const e={get:h.bind(this,s),set:t.bind(this,s)};Object.defineProperty(this.options,s,e)}}_sanitizeAndValidateOption(h,t){switch(h){case"cursorStyle":if(t||(t=r.DEFAULT_OPTIONS[h]),!function(s){return s==="block"||s==="underline"||s==="bar"}(t))throw new Error(`"${t}" is not a valid value for ${h}`);break;case"wordSeparator":t||(t=r.DEFAULT_OPTIONS[h]);break;case"fontWeight":case"fontWeightBold":if(typeof t=="number"&&1<=t&&t<=1e3)break;t=d.includes(t)?t:r.DEFAULT_OPTIONS[h];break;case"cursorWidth":t=Math.floor(t);case"lineHeight":case"tabStopWidth":if(t<1)throw new Error(`${h} cannot be less than 1, value: ${t}`);break;case"minimumContrastRatio":t=Math.max(1,Math.min(21,Math.round(10*t)/10));break;case"scrollback":if((t=Math.min(t,4294967295))<0)throw new Error(`${h} cannot be less than 0, value: ${t}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(t<=0)throw new Error(`${h} cannot be less than or equal to 0, value: ${t}`);break;case"rows":case"cols":if(!t&&t!==0)throw new Error(`${h} must be numeric, value: ${t}`);break;case"windowsPty":t=t??{}}return t}}r.OptionsService=f},2660:function(B,r,o){var l=this&&this.__decorate||function(f,p,h,t){var s,e=arguments.length,i=e<3?p:t===null?t=Object.getOwnPropertyDescriptor(p,h):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(f,p,h,t);else for(var a=f.length-1;a>=0;a--)(s=f[a])&&(i=(e<3?s(i):e>3?s(p,h,i):s(p,h))||i);return e>3&&i&&Object.defineProperty(p,h,i),i},u=this&&this.__param||function(f,p){return function(h,t){p(h,t,f)}};Object.defineProperty(r,"__esModule",{value:!0}),r.OscLinkService=void 0;const n=o(2585);let d=r.OscLinkService=class{constructor(f){this._bufferService=f,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(f){const p=this._bufferService.buffer;if(f.id===void 0){const a=p.addMarker(p.ybase+p.y),v={data:f,id:this._nextId++,lines:[a]};return a.onDispose(()=>this._removeMarkerFromLink(v,a)),this._dataByLinkId.set(v.id,v),v.id}const h=f,t=this._getEntryIdKey(h),s=this._entriesWithId.get(t);if(s)return this.addLineToLink(s.id,p.ybase+p.y),s.id;const e=p.addMarker(p.ybase+p.y),i={id:this._nextId++,key:this._getEntryIdKey(h),data:h,lines:[e]};return e.onDispose(()=>this._removeMarkerFromLink(i,e)),this._entriesWithId.set(i.key,i),this._dataByLinkId.set(i.id,i),i.id}addLineToLink(f,p){const h=this._dataByLinkId.get(f);if(h&&h.lines.every(t=>t.line!==p)){const t=this._bufferService.buffer.addMarker(p);h.lines.push(t),t.onDispose(()=>this._removeMarkerFromLink(h,t))}}getLinkData(f){return this._dataByLinkId.get(f)?.data}_getEntryIdKey(f){return`${f.id};;${f.uri}`}_removeMarkerFromLink(f,p){const h=f.lines.indexOf(p);h!==-1&&(f.lines.splice(h,1),f.lines.length===0&&(f.data.id!==void 0&&this._entriesWithId.delete(f.key),this._dataByLinkId.delete(f.id)))}};r.OscLinkService=d=l([u(0,n.IBufferService)],d)},8343:(B,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.createDecorator=r.getServiceDependencies=r.serviceRegistry=void 0;const o="di$target",l="di$dependencies";r.serviceRegistry=new Map,r.getServiceDependencies=function(u){return u[l]||[]},r.createDecorator=function(u){if(r.serviceRegistry.has(u))return r.serviceRegistry.get(u);const n=function(d,f,p){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(h,t,s){t[o]===t?t[l].push({id:h,index:s}):(t[l]=[{id:h,index:s}],t[o]=t)})(n,d,p)};return n.toString=()=>u,r.serviceRegistry.set(u,n),n}},2585:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.IDecorationService=r.IUnicodeService=r.IOscLinkService=r.IOptionsService=r.ILogService=r.LogLevelEnum=r.IInstantiationService=r.ICharsetService=r.ICoreService=r.ICoreMouseService=r.IBufferService=void 0;const l=o(8343);var u;r.IBufferService=(0,l.createDecorator)("BufferService"),r.ICoreMouseService=(0,l.createDecorator)("CoreMouseService"),r.ICoreService=(0,l.createDecorator)("CoreService"),r.ICharsetService=(0,l.createDecorator)("CharsetService"),r.IInstantiationService=(0,l.createDecorator)("InstantiationService"),function(n){n[n.TRACE=0]="TRACE",n[n.DEBUG=1]="DEBUG",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.OFF=5]="OFF"}(u||(r.LogLevelEnum=u={})),r.ILogService=(0,l.createDecorator)("LogService"),r.IOptionsService=(0,l.createDecorator)("OptionsService"),r.IOscLinkService=(0,l.createDecorator)("OscLinkService"),r.IUnicodeService=(0,l.createDecorator)("UnicodeService"),r.IDecorationService=(0,l.createDecorator)("DecorationService")},1480:(B,r,o)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.UnicodeService=void 0;const l=o(8460),u=o(225);class n{static extractShouldJoin(f){return(1&f)!=0}static extractWidth(f){return f>>1&3}static extractCharKind(f){return f>>3}static createPropertyValue(f,p,h=!1){return(16777215&f)<<3|(3&p)<<1|(h?1:0)}constructor(){this._providers=Object.create(null),this._active="",this._onChange=new l.EventEmitter,this.onChange=this._onChange.event;const f=new u.UnicodeV6;this.register(f),this._active=f.version,this._activeProvider=f}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(f){if(!this._providers[f])throw new Error(`unknown Unicode version "${f}"`);this._active=f,this._activeProvider=this._providers[f],this._onChange.fire(f)}register(f){this._providers[f.version]=f}wcwidth(f){return this._activeProvider.wcwidth(f)}getStringCellWidth(f){let p=0,h=0;const t=f.length;for(let s=0;s<t;++s){let e=f.charCodeAt(s);if(55296<=e&&e<=56319){if(++s>=t)return p+this.wcwidth(e);const v=f.charCodeAt(s);56320<=v&&v<=57343?e=1024*(e-55296)+v-56320+65536:p+=this.wcwidth(v)}const i=this.charProperties(e,h);let a=n.extractWidth(i);n.extractShouldJoin(i)&&(a-=n.extractWidth(h)),p+=a,h=i}return p}charProperties(f,p){return this._activeProvider.charProperties(f,p)}}r.UnicodeService=n}},z={};function q(B){var r=z[B];if(r!==void 0)return r.exports;var o=z[B]={exports:{}};return Z[B].call(o.exports,o,o.exports,q),o.exports}var Y={};return(()=>{var B=Y;Object.defineProperty(B,"__esModule",{value:!0}),B.Terminal=void 0;const r=q(9042),o=q(3236),l=q(844),u=q(5741),n=q(8285),d=q(7975),f=q(7090),p=["cols","rows"];class h extends l.Disposable{constructor(s){super(),this._core=this.register(new o.Terminal(s)),this._addonManager=this.register(new u.AddonManager),this._publicOptions={...this._core.options};const e=a=>this._core.options[a],i=(a,v)=>{this._checkReadonlyOptions(a),this._core.options[a]=v};for(const a in this._core.options){const v={get:e.bind(this,a),set:i.bind(this,a)};Object.defineProperty(this._publicOptions,a,v)}}_checkReadonlyOptions(s){if(p.includes(s))throw new Error(`Option "${s}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new d.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new f.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new n.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const s=this._core.coreService.decPrivateModes;let e="none";switch(this._core.coreMouseService.activeProtocol){case"X10":e="x10";break;case"VT200":e="vt200";break;case"DRAG":e="drag";break;case"ANY":e="any"}return{applicationCursorKeysMode:s.applicationCursorKeys,applicationKeypadMode:s.applicationKeypad,bracketedPasteMode:s.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:e,originMode:s.origin,reverseWraparoundMode:s.reverseWraparound,sendFocusMode:s.sendFocus,wraparoundMode:s.wraparound}}get options(){return this._publicOptions}set options(s){for(const e in s)this._publicOptions[e]=s[e]}blur(){this._core.blur()}focus(){this._core.focus()}input(s,e=!0){this._core.input(s,e)}resize(s,e){this._verifyIntegers(s,e),this._core.resize(s,e)}open(s){this._core.open(s)}attachCustomKeyEventHandler(s){this._core.attachCustomKeyEventHandler(s)}attachCustomWheelEventHandler(s){this._core.attachCustomWheelEventHandler(s)}registerLinkProvider(s){return this._core.registerLinkProvider(s)}registerCharacterJoiner(s){return this._checkProposedApi(),this._core.registerCharacterJoiner(s)}deregisterCharacterJoiner(s){this._checkProposedApi(),this._core.deregisterCharacterJoiner(s)}registerMarker(s=0){return this._verifyIntegers(s),this._core.registerMarker(s)}registerDecoration(s){return this._checkProposedApi(),this._verifyPositiveIntegers(s.x??0,s.width??0,s.height??0),this._core.registerDecoration(s)}hasSelection(){return this._core.hasSelection()}select(s,e,i){this._verifyIntegers(s,e,i),this._core.select(s,e,i)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(s,e){this._verifyIntegers(s,e),this._core.selectLines(s,e)}dispose(){super.dispose()}scrollLines(s){this._verifyIntegers(s),this._core.scrollLines(s)}scrollPages(s){this._verifyIntegers(s),this._core.scrollPages(s)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(s){this._verifyIntegers(s),this._core.scrollToLine(s)}clear(){this._core.clear()}write(s,e){this._core.write(s,e)}writeln(s,e){this._core.write(s),this._core.write(`\r
`,e)}paste(s){this._core.paste(s)}refresh(s,e){this._verifyIntegers(s,e),this._core.refresh(s,e)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(s){this._addonManager.loadAddon(this,s)}static get strings(){return r}_verifyIntegers(...s){for(const e of s)if(e===1/0||isNaN(e)||e%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...s){for(const e of s)if(e&&(e===1/0||isNaN(e)||e%1!=0||e<0))throw new Error("This API only accepts positive integers")}}B.Terminal=h})(),Y})())}(me)),me.exports}var Te=Be();function Me(V,te=!1){return{action:Le.RUN,args:{command:V,hidden:te}}}const Oe=V=>{const te=/(.*)\[Python Interpreter: (.*)\]/s,Z=V.match(te);return Z?Z[1]?.trim()||"":V},Pe={commands:[]},we=(V,te,Z=!1)=>{const{content:z,type:q}=V;q==="input"&&Z||te.writeln(Oe(z.replaceAll(`
`,`\r
`).trim()))},Ie={current:0},He=({commands:V}=Pe)=>{const{send:te}=De(),{curAgentState:Z}=Se(t=>t.agent),z=ne.useRef(null),q=ne.useRef(null),Y=ne.useRef(null),B=Ie,r=ne.useRef(null),o=ye.includes(Z),l=()=>new Te.Terminal({fontFamily:"Menlo, Monaco, 'Courier New', monospace",fontSize:14,theme:{background:"#24272E"}}),u=()=>{z.current&&(q.current&&z.current.loadAddon(q.current),Y.current&&z.current.open(Y.current))},n=t=>{const s=new ClipboardItem({"text/plain":new Blob([t],{type:"text/plain"})});navigator.clipboard.write([s])},d=t=>{navigator.clipboard.readText().then(t)},f=(t,s)=>{if(t.type==="keydown"&&(t.ctrlKey||t.metaKey)&&(t.code==="KeyV"&&d(i=>{z.current?.write(i),s(i)}),t.code==="KeyC")){const i=z.current?.getSelection();i&&n(i)}return!0},p=t=>{z.current?.write(`\r
`),te(Me(t))},h=t=>(z.current?.write("\b \b"),t.slice(0,-1));return ne.useEffect(()=>{if(z.current=l(),q.current=new Ae.FitAddon,Y.current){if(u(),V.length>0){for(let t=0;t<V.length;t+=1)V[t].type==="input"&&z.current.write("$ "),we(V[t],z.current,!1);B.current=V.length}z.current.write("$ ")}return()=>{z.current?.dispose()}},[]),ne.useEffect(()=>{if(z.current&&V.length>0&&B.current<V.length){let t="";for(let s=B.current;s<V.length;s+=1)t=V[s].type,we(V[s],z.current,!0);B.current=V.length,t==="output"&&z.current.write("$ ")}},[V,o]),ne.useEffect(()=>{let t=null;return t=new ResizeObserver(()=>{q.current?.fit()}),Y.current&&t.observe(Y.current),()=>{t?.disconnect()}},[]),ne.useEffect(()=>{if(z.current){r.current&&(r.current.dispose(),r.current=null);let t="";o?r.current=z.current.onKey(s=>{s.domEvent.preventDefault(),s.domEvent.stopPropagation()}):(r.current=z.current.onKey(({key:s,domEvent:e})=>{if(e.key==="Enter")p(t),t="";else if(e.key==="Backspace")t.length>0&&(t=h(t));else{if(s.charCodeAt(0)===22)return;t+=s,z.current?.write(s)}}),z.current.attachCustomKeyEventHandler(s=>f(s,e=>{t+=e})))}return()=>{r.current&&(r.current.dispose(),r.current=null)}},[o]),Y};function ct(){const{commands:V}=Se(Y=>Y.cmd),{curAgentState:te}=Se(Y=>Y.agent),Z=ye.includes(te),{t:z}=Re(),q=He({commands:V});return pe.jsxs("div",{className:"h-full p-2 min-h-0 flex-grow",children:[Z&&pe.jsx("div",{className:"w-full h-full flex items-center text-center justify-center text-2xl text-tertiary-light",children:z("DIFF_VIEWER$WAITING_FOR_RUNTIME")}),pe.jsx("div",{ref:q,className:Z?"w-0 h-0 opacity-0 overflow-hidden":"h-full w-full"})]})}export{ct as default};
