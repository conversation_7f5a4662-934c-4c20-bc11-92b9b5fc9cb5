import{j as e,R as h}from"./chunk-C37GKA54-CBbYr_fP.js";import{I as g}from"./declaration-xyc84-tJ.js";import{c as n}from"./utils-KsbccAr1.js";import{u}from"./useTranslation-BG59QWH_.js";function f({isToggled:s}){return e.jsx("div",{className:n("w-12 h-6 rounded-xl flex items-center p-1.5 cursor-pointer",s&&"justify-end bg-primary",!s&&"justify-start bg-base-secondary border border-tertiary-light"),children:e.jsx("div",{className:n("w-3 h-3 rounded-xl",s?"bg-base-secondary":"bg-tertiary-light")})})}function w({children:s,testId:i,name:c,onToggle:o,defaultIsToggled:l,isToggled:r,isBeta:d}){const{t:x}=u(),[a,m]=h.useState(l??!1),p=t=>{m(t),o?.(t)};return e.jsxs("label",{className:"flex items-center gap-2 w-fit cursor-pointer",children:[e.jsx("input",{hidden:!0,"data-testid":i,name:c,type:"checkbox",onChange:t=>p(t.target.checked),checked:r??a}),e.jsx(f,{isToggled:r??a}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-sm",children:s}),d&&e.jsx("span",{className:"text-[11px] leading-4 text-[#0D0F11] font-[500] tracking-tighter bg-primary px-1 rounded-full",children:x(g.BADGE$BETA)})]})]})}function S(){return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-[48px] h-[24px] skeleton-round"}),e.jsx("div",{className:"w-[100px] h-[20px] skeleton"})]})}export{S,w as a};
