import{u as c}from"./open-hands-axios-CtirLpss.js";import{u}from"./brand-button-3Z8FN4qR.js";import{p as E}from"./module-5laXsVNO.js";import{u as i,D as o}from"./use-settings-CSlhfPqo.js";import{O as m}from"./open-hands-Ce72Fmtl.js";const C=async _=>{const a={llm_model:_.LLM_MODEL,llm_base_url:_.LLM_BASE_URL,agent:_.AGENT||o.AGENT,language:_.LANGUAGE||o.LANGUAGE,confirmation_mode:_.CONFIRMATION_MODE,security_analyzer:_.SECURITY_ANALYZER,llm_api_key:_.llm_api_key===""?"":_.llm_api_key?.trim()||void 0,remote_runtime_resource_factor:_.REMOTE_RUNTIME_RESOURCE_FACTOR,enable_default_condenser:_.ENABLE_DEFAULT_CONDENSER,enable_sound_notifications:_.ENABLE_SOUND_NOTIFICATIONS,user_consents_to_analytics:_.user_consents_to_analytics,provider_tokens_set:_.PROVIDER_TOKENS_SET,mcp_config:_.MCP_CONFIG,enable_proactive_conversation_starters:_.ENABLE_PROACTIVE_CONVERSATION_STARTERS,search_api_key:_.SEARCH_API_KEY?.trim()||"",max_budget_per_task:_.MAX_BUDGET_PER_TASK};await m.saveSettings(a)},p=()=>{const _=c(),{data:a}=i();return u({mutationFn:async e=>{const r={...a,...e};if(e.MCP_CONFIG&&a?.MCP_CONFIG!==e.MCP_CONFIG){const n=!!e.MCP_CONFIG,t=e.MCP_CONFIG?.sse_servers?.length||0,s=e.MCP_CONFIG?.stdio_servers?.length||0;E.capture("mcp_config_updated",{has_mcp_config:n,sse_servers_count:t,stdio_servers_count:s})}await C(r)},onSuccess:async()=>{await _.invalidateQueries({queryKey:["settings"]})},meta:{disableToast:!0}})};export{p as u};
