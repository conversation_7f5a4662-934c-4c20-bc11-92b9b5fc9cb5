import{h as k,r as i}from"./chunk-C37GKA54-CBbYr_fP.js";var w={exports:{}},E={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var D;function T(){if(D)return E;D=1;var e=k();function u(o,c){return o===c&&(o!==0||1/o===1/c)||o!==o&&c!==c}var t=typeof Object.is=="function"?Object.is:u,n=e.useSyncExternalStore,r=e.useRef,s=e.useEffect,v=e.useMemo,S=e.useDebugValue;return E.useSyncExternalStoreWithSelector=function(o,c,p,l,f){var d=r(null);if(d.current===null){var a={hasValue:!1,value:null};d.current=a}else a=d.current;d=v(function(){function m(b){if(!g){if(g=!0,C=b,b=l(b),f!==void 0&&a.hasValue){var h=a.value;if(f(h,b))return R=h}return R=b}if(h=R,t(C,b))return h;var N=l(b);return f!==void 0&&f(h,N)?(C=b,h):(C=b,R=N)}var g=!1,C,R,M=p===void 0?null:p;return[function(){return m(c())},M===null?void 0:function(){return m(M())}]},[c,p,l,f]);var y=n(o,d[0],d[1]);return s(function(){a.hasValue=!0,a.value=y},[y]),S(y),y},E}var U;function H(){return U||(U=1,w.exports=T()),w.exports}var I=H();function P(e){e()}function _(){let e=null,u=null;return{clear(){e=null,u=null},notify(){P(()=>{let t=e;for(;t;)t.callback(),t=t.next})},get(){const t=[];let n=e;for(;n;)t.push(n),n=n.next;return t},subscribe(t){let n=!0;const r=u={callback:t,next:null,prev:u};return r.prev?r.prev.next=r:e=r,function(){!n||e===null||(n=!1,r.next?r.next.prev=r.prev:u=r.prev,r.prev?r.prev.next=r.next:e=r.next)}}}}var W={notify(){},get:()=>[]};function z(e,u){let t,n=W,r=0,s=!1;function v(y){p();const m=n.subscribe(y);let g=!1;return()=>{g||(g=!0,m(),l())}}function S(){n.notify()}function o(){a.onStateChange&&a.onStateChange()}function c(){return s}function p(){r++,t||(t=e.subscribe(o),n=_())}function l(){r--,t&&r===0&&(t(),t=void 0,n.clear(),n=W)}function f(){s||(s=!0,p())}function d(){s&&(s=!1,l())}const a={addNestedSub:v,notifyNestedSubs:S,handleChangeWrapper:o,isSubscribed:c,trySubscribe:f,tryUnsubscribe:d,getListeners:()=>n};return a}var O=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",q=O(),A=()=>typeof navigator<"u"&&navigator.product==="ReactNative",B=A(),G=()=>q||B?i.useLayoutEffect:i.useEffect,K=G(),F=Symbol.for("react-redux-context"),J=typeof globalThis<"u"?globalThis:{};function Q(){if(!i.createContext)return{};const e=J[F]??=new Map;let u=e.get(i.createContext);return u||(u=i.createContext(null),e.set(i.createContext,u)),u}var x=Q();function X(e){const{children:u,context:t,serverState:n,store:r}=e,s=i.useMemo(()=>{const o=z(r);return{store:r,subscription:o,getServerState:n?()=>n:void 0}},[r,n]),v=i.useMemo(()=>r.getState(),[r]);K(()=>{const{subscription:o}=s;return o.onStateChange=o.notifyNestedSubs,o.trySubscribe(),v!==r.getState()&&o.notifyNestedSubs(),()=>{o.tryUnsubscribe(),o.onStateChange=void 0}},[s,v]);const S=t||x;return i.createElement(S.Provider,{value:s},u)}var re=X;function V(e=x){return function(){return i.useContext(e)}}var L=V();function j(e=x){const u=e===x?L:V(e),t=()=>{const{store:n}=u();return n};return Object.assign(t,{withTypes:()=>t}),t}var Y=j();function Z(e=x){const u=e===x?Y:j(e),t=()=>u().dispatch;return Object.assign(t,{withTypes:()=>t}),t}var ne=Z(),$=(e,u)=>e===u;function ee(e=x){const u=e===x?L:V(e),t=(n,r={})=>{const{equalityFn:s=$}=typeof r=="function"?{equalityFn:r}:r,v=u(),{store:S,subscription:o,getServerState:c}=v;i.useRef(!0);const p=i.useCallback({[n.name](f){return n(f)}}[n.name],[n]),l=I.useSyncExternalStoreWithSelector(o.addNestedSub,S.getState,c||S.getState,p,s);return i.useDebugValue(l),l};return Object.assign(t,{withTypes:()=>t}),t}var ue=ee();export{re as P,ne as a,ue as u};
