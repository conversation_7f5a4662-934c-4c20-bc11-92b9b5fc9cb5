import{u as s}from"./open-hands-axios-CtirLpss.js";import{u as r}from"./brand-button-3Z8FN4qR.js";import{p as a}from"./module-5laXsVNO.js";import{O as n}from"./open-hands-Ce72Fmtl.js";import{u}from"./use-config-jdwF3W4-.js";const o={LOGIN_METHOD:"openhands_login_method"};var i=(e=>(e.GITHUB="github",e.GITLAB="gitlab",e.BITBUCKET="bitbucket",e))(i||{});const p=e=>{localStorage.setItem(o.LOGIN_METHOD,e)},I=()=>localStorage.getItem(o.LOGIN_METHOD),m=()=>{localStorage.removeItem(o.LOGIN_METHOD)},L=()=>{const e=s(),{data:t}=u();return r({mutationFn:()=>n.logout(t?.APP_MODE??"oss"),onSuccess:async()=>{e.removeQueries({queryKey:["tasks"]}),e.removeQueries({queryKey:["settings"]}),e.removeQueries({queryKey:["user"]}),e.removeQueries({queryKey:["secrets"]}),t?.APP_MODE==="saas"&&m(),a.reset(),window.location.reload()}})};export{i as L,o as a,I as g,p as s,L as u};
