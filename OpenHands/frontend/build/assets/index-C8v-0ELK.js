import{r as a,R}from"./chunk-C37GKA54-CBbYr_fP.js";function me(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function re(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ne(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?re(Object(r),!0).forEach(function(n){me(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):re(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function be(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,o;for(o=0;o<n.length;o++)i=n[o],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function we(e,t){if(e==null)return{};var r=be(e,t),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ye(e,t){return Oe(e)||je(e,t)||Me(e,t)||Se()}function Oe(e){if(Array.isArray(e))return e}function je(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,o=void 0;try{for(var c=e[Symbol.iterator](),s;!(n=(s=c.next()).done)&&(r.push(s.value),!(t&&r.length===t));n=!0);}catch(d){i=!0,o=d}finally{try{!n&&c.return!=null&&c.return()}finally{if(i)throw o}}return r}}function Me(e,t){if(e){if(typeof e=="string")return ie(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ie(e,t)}}function ie(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Se(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ee(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?oe(Object(r),!0).forEach(function(n){Ee(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oe(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Pe(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(n){return t.reduceRight(function(i,o){return o(i)},n)}}function L(e){return function t(){for(var r=this,n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return i.length>=e.length?e.apply(this,i):function(){for(var c=arguments.length,s=new Array(c),d=0;d<c;d++)s[d]=arguments[d];return t.apply(r,[].concat(i,s))}}}function U(e){return{}.toString.call(e).includes("Object")}function Re(e){return!Object.keys(e).length}function D(e){return typeof e=="function"}function Ie(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Ce(e,t){return U(t)||M("changeType"),Object.keys(t).some(function(r){return!Ie(e,r)})&&M("changeField"),t}function Te(e){D(e)||M("selectorType")}function Ae(e){D(e)||U(e)||M("handlerType"),U(e)&&Object.values(e).some(function(t){return!D(t)})&&M("handlersType")}function Le(e){e||M("initialIsRequired"),U(e)||M("initialType"),Re(e)&&M("initialContent")}function $e(e,t){throw new Error(e[t]||e.default)}var De={initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"},M=L($e)(De),z={changes:Ce,selector:Te,handler:Ae,initial:Le};function Ve(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};z.initial(e),z.handler(t);var r={current:e},n=L(ze)(r,t),i=L(qe)(r),o=L(z.changes)(e),c=L(xe)(r);function s(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(S){return S};return z.selector(b),b(r.current)}function d(b){Pe(n,i,o,c)(b)}return[s,d]}function xe(e,t){return D(t)?t(e.current):t}function qe(e,t){return e.current=ae(ae({},e.current),t),t}function ze(e,t,r){return D(t)?t(e.current):Object.keys(r).forEach(function(n){var i;return(i=t[n])===null||i===void 0?void 0:i.call(t,e.current[n])}),r}var Ne={create:Ve},Ue={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};function Fe(e){return function t(){for(var r=this,n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return i.length>=e.length?e.apply(this,i):function(){for(var c=arguments.length,s=new Array(c),d=0;d<c;d++)s[d]=arguments[d];return t.apply(r,[].concat(i,s))}}}function He(e){return{}.toString.call(e).includes("Object")}function We(e){return e||ue("configIsRequired"),He(e)||ue("configType"),e.urls?(Be(),{paths:{vs:e.urls.monacoBase}}):e}function Be(){console.warn(ce.deprecation)}function Ge(e,t){throw new Error(e[t]||e.default)}var ce={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:`Deprecation warning!
    You are using deprecated way of configuration.

    Instead of using
      monaco.config({ urls: { monacoBase: '...' } })
    use
      monaco.config({ paths: { vs: '...' } })

    For more please check the link https://github.com/suren-atoyan/monaco-loader#config
  `},ue=Fe(Ge)(ce),Ke={config:We},Ye=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return function(i){return r.reduceRight(function(o,c){return c(o)},i)}};function le(e,t){return Object.keys(t).forEach(function(r){t[r]instanceof Object&&e[r]&&Object.assign(t[r],le(e[r],t[r]))}),ne(ne({},e),t)}var ke={type:"cancelation",msg:"operation is manually canceled"};function X(e){var t=!1,r=new Promise(function(n,i){e.then(function(o){return t?i(ke):n(o)}),e.catch(i)});return r.cancel=function(){return t=!0},r}var Je=Ne.create({config:Ue,isInitialized:!1,resolve:null,reject:null,monaco:null}),se=ye(Je,2),V=se[0],F=se[1];function Qe(e){var t=Ke.config(e),r=t.monaco,n=we(t,["monaco"]);F(function(i){return{config:le(i.config,n),monaco:r}})}function Xe(){var e=V(function(t){var r=t.monaco,n=t.isInitialized,i=t.resolve;return{monaco:r,isInitialized:n,resolve:i}});if(!e.isInitialized){if(F({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),X(Z);if(window.monaco&&window.monaco.editor)return fe(window.monaco),e.resolve(window.monaco),X(Z);Ye(Ze,et)(tt)}return X(Z)}function Ze(e){return document.body.appendChild(e)}function _e(e){var t=document.createElement("script");return e&&(t.src=e),t}function et(e){var t=V(function(n){var i=n.config,o=n.reject;return{config:i,reject:o}}),r=_e("".concat(t.config.paths.vs,"/loader.js"));return r.onload=function(){return e()},r.onerror=t.reject,r}function tt(){var e=V(function(r){var n=r.config,i=r.resolve,o=r.reject;return{config:n,resolve:i,reject:o}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(r){fe(r),e.resolve(r)},function(r){e.reject(r)})}function fe(e){V().monaco||F({monaco:e})}function rt(){return V(function(e){var t=e.monaco;return t})}var Z=new Promise(function(e,t){return F({resolve:e,reject:t})}),de={config:Qe,init:Xe,__getMonacoInstance:rt},nt={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},_=nt,it={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},ot=it;function at({children:e}){return R.createElement("div",{style:ot.container},e)}var ut=at,ct=ut;function lt({width:e,height:t,isEditorReady:r,loading:n,_ref:i,className:o,wrapperProps:c}){return R.createElement("section",{style:{..._.wrapper,width:e,height:t},...c},!r&&R.createElement(ct,null,n),R.createElement("div",{ref:i,style:{..._.fullWidth,...!r&&_.hide},className:o}))}var st=lt,pe=a.memo(st);function ft(e){a.useEffect(e,[])}var ge=ft;function dt(e,t,r=!0){let n=a.useRef(!0);a.useEffect(n.current||!r?()=>{n.current=!1}:e,t)}var y=dt;function $(){}function P(e,t,r,n){return pt(e,n)||gt(e,t,r,n)}function pt(e,t){return e.editor.getModel(he(e,t))}function gt(e,t,r,n){return e.editor.createModel(t,r,n?he(e,n):void 0)}function he(e,t){return e.Uri.parse(t)}function ht({original:e,modified:t,language:r,originalLanguage:n,modifiedLanguage:i,originalModelPath:o,modifiedModelPath:c,keepCurrentOriginalModel:s=!1,keepCurrentModifiedModel:d=!1,theme:b="light",loading:S="Loading...",options:O={},height:H="100%",width:W="100%",className:B,wrapperProps:G={},beforeMount:K=$,onMount:Y=$}){let[w,I]=a.useState(!1),[C,p]=a.useState(!0),g=a.useRef(null),f=a.useRef(null),T=a.useRef(null),v=a.useRef(Y),u=a.useRef(K),E=a.useRef(!1);ge(()=>{let l=de.init();return l.then(h=>(f.current=h)&&p(!1)).catch(h=>h?.type!=="cancelation"&&console.error("Monaco initialization: error:",h)),()=>g.current?A():l.cancel()}),y(()=>{if(g.current&&f.current){let l=g.current.getOriginalEditor(),h=P(f.current,e||"",n||r||"text",o||"");h!==l.getModel()&&l.setModel(h)}},[o],w),y(()=>{if(g.current&&f.current){let l=g.current.getModifiedEditor(),h=P(f.current,t||"",i||r||"text",c||"");h!==l.getModel()&&l.setModel(h)}},[c],w),y(()=>{let l=g.current.getModifiedEditor();l.getOption(f.current.editor.EditorOption.readOnly)?l.setValue(t||""):t!==l.getValue()&&(l.executeEdits("",[{range:l.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),l.pushUndoStop())},[t],w),y(()=>{g.current?.getModel()?.original.setValue(e||"")},[e],w),y(()=>{let{original:l,modified:h}=g.current.getModel();f.current.editor.setModelLanguage(l,n||r||"text"),f.current.editor.setModelLanguage(h,i||r||"text")},[r,n,i],w),y(()=>{f.current?.editor.setTheme(b)},[b],w),y(()=>{g.current?.updateOptions(O)},[O],w);let x=a.useCallback(()=>{if(!f.current)return;u.current(f.current);let l=P(f.current,e||"",n||r||"text",o||""),h=P(f.current,t||"",i||r||"text",c||"");g.current?.setModel({original:l,modified:h})},[r,t,i,e,n,o,c]),q=a.useCallback(()=>{!E.current&&T.current&&(g.current=f.current.editor.createDiffEditor(T.current,{automaticLayout:!0,...O}),x(),f.current?.editor.setTheme(b),I(!0),E.current=!0)},[O,b,x]);a.useEffect(()=>{w&&v.current(g.current,f.current)},[w]),a.useEffect(()=>{!C&&!w&&q()},[C,w,q]);function A(){let l=g.current?.getModel();s||l?.original?.dispose(),d||l?.modified?.dispose(),g.current?.dispose()}return R.createElement(pe,{width:W,height:H,isEditorReady:w,loading:S,_ref:T,className:B,wrapperProps:G})}var vt=ht,jt=a.memo(vt);function mt(e){let t=a.useRef();return a.useEffect(()=>{t.current=e},[e]),t.current}var bt=mt,N=new Map;function wt({defaultValue:e,defaultLanguage:t,defaultPath:r,value:n,language:i,path:o,theme:c="light",line:s,loading:d="Loading...",options:b={},overrideServices:S={},saveViewState:O=!0,keepCurrentModel:H=!1,width:W="100%",height:B="100%",className:G,wrapperProps:K={},beforeMount:Y=$,onMount:w=$,onChange:I,onValidate:C=$}){let[p,g]=a.useState(!1),[f,T]=a.useState(!0),v=a.useRef(null),u=a.useRef(null),E=a.useRef(null),x=a.useRef(w),q=a.useRef(Y),A=a.useRef(),l=a.useRef(n),h=bt(o),ee=a.useRef(!1),k=a.useRef(!1);ge(()=>{let m=de.init();return m.then(j=>(v.current=j)&&T(!1)).catch(j=>j?.type!=="cancelation"&&console.error("Monaco initialization: error:",j)),()=>u.current?ve():m.cancel()}),y(()=>{let m=P(v.current,e||n||"",t||i||"",o||r||"");m!==u.current?.getModel()&&(O&&N.set(h,u.current?.saveViewState()),u.current?.setModel(m),O&&u.current?.restoreViewState(N.get(o)))},[o],p),y(()=>{u.current?.updateOptions(b)},[b],p),y(()=>{!u.current||n===void 0||(u.current.getOption(v.current.editor.EditorOption.readOnly)?u.current.setValue(n):n!==u.current.getValue()&&(k.current=!0,u.current.executeEdits("",[{range:u.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),u.current.pushUndoStop(),k.current=!1))},[n],p),y(()=>{let m=u.current?.getModel();m&&i&&v.current?.editor.setModelLanguage(m,i)},[i],p),y(()=>{s!==void 0&&u.current?.revealLine(s)},[s],p),y(()=>{v.current?.editor.setTheme(c)},[c],p);let te=a.useCallback(()=>{if(!(!E.current||!v.current)&&!ee.current){q.current(v.current);let m=o||r,j=P(v.current,n||e||"",t||i||"",m||"");u.current=v.current?.editor.create(E.current,{model:j,automaticLayout:!0,...b},S),O&&u.current.restoreViewState(N.get(m)),v.current.editor.setTheme(c),s!==void 0&&u.current.revealLine(s),g(!0),ee.current=!0}},[e,t,r,n,i,o,b,S,O,c,s]);a.useEffect(()=>{p&&x.current(u.current,v.current)},[p]),a.useEffect(()=>{!f&&!p&&te()},[f,p,te]),l.current=n,a.useEffect(()=>{p&&I&&(A.current?.dispose(),A.current=u.current?.onDidChangeModelContent(m=>{k.current||I(u.current.getValue(),m)}))},[p,I]),a.useEffect(()=>{if(p){let m=v.current.editor.onDidChangeMarkers(j=>{let J=u.current.getModel()?.uri;if(J&&j.find(Q=>Q.path===J.path)){let Q=v.current.editor.getModelMarkers({resource:J});C?.(Q)}});return()=>{m?.dispose()}}return()=>{}},[p,C]);function ve(){A.current?.dispose(),H?O&&N.set(o,u.current.saveViewState()):u.current.getModel()?.dispose(),u.current.dispose()}return R.createElement(pe,{width:W,height:B,isEditorReady:p,loading:d,_ref:E,className:G,wrapperProps:K})}var yt=wt,Mt=a.memo(yt);export{Mt as d,jt as w};
