import{R as N,j as e,w as k,i as P}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as F}from"./open-hands-axios-CtirLpss.js";import{u as U}from"./useQuery-Cu2nkJ8V.js";import{S as y}from"./secrets-service-CrMMoq9G.js";import{u as _}from"./use-user-providers-CVWOd-tS.js";import{u as A}from"./use-config-jdwF3W4-.js";import{u as I,B as p}from"./brand-button-3Z8FN4qR.js";import{I as m}from"./declaration-xyc84-tJ.js";import{S as L}from"./settings-input-i4i_IemM.js";import{c as $}from"./utils-KsbccAr1.js";import{O as q}from"./optional-tag-e1gRgM9y.js";import{u as O}from"./useTranslation-BG59QWH_.js";import{F as B,a as Q}from"./index-DvLMSsrd.js";import{M as V}from"./modal-backdrop-ve4Sk5I2.js";import"./open-hands-Ce72Fmtl.js";import"./use-settings-CSlhfPqo.js";import"./module-5laXsVNO.js";import"./mutation-B9dSlWD-.js";import"./i18nInstance-DBIXdvxg.js";import"./iconBase-2PDVWRGH.js";const M=()=>{const{data:t}=A(),{providers:s}=_(),a=t?.APP_MODE==="oss";return U({queryKey:["secrets"],queryFn:y.getSecrets,enabled:a||s.length>0})},K=()=>I({mutationFn:t=>y.deleteSecret(t)}),G=()=>I({mutationFn:({name:t,value:s,description:a})=>y.createSecret(t,s,a)}),z=()=>I({mutationFn:({secretToEdit:t,name:s,description:a})=>y.updateSecret(t,s,a)});function Y({mode:t,selectedSecret:s,onCancel:a}){const c=F(),{t:l}=O(),{data:h}=M(),{mutate:C}=G(),{mutate:g}=z(),[b,d]=N.useState(null),x=t==="edit"&&s&&h?.find(n=>n.name===s)?.description?.trim()||"",f=(n,o,i)=>{C({name:n,value:o,description:i},{onSettled:a,onSuccess:async()=>{await c.invalidateQueries({queryKey:["secrets"]})}})},E=(n,o,i)=>{c.setQueryData(["secrets"],u=>u?u.map(r=>r.name===n?{...r,name:o,description:i}:r):[])},w=()=>{c.invalidateQueries({queryKey:["secrets"]})},S=(n,o,i)=>{E(n,o,i),g({secretToEdit:n,name:o,description:i},{onSettled:a,onError:w})},T=n=>{n.preventDefault();const o=new FormData(n.currentTarget),i=o.get("secret-name")?.toString(),u=o.get("secret-value")?.toString().trim(),r=o.get("secret-description")?.toString();if(i){if(d(null),h?.some(j=>j.name===i&&j.name!==s)){d(l("SECRETS$SECRET_ALREADY_EXISTS"));return}if(t==="add"){if(!u){d(l("SECRETS$SECRET_VALUE_REQUIRED"));return}f(i,u,r||void 0)}else t==="edit"&&s&&S(s,i,r||void 0)}},v=t==="add"?"add-secret-form":"edit-secret-form";return e.jsxs("form",{"data-testid":v,onSubmit:T,className:"flex flex-col items-start gap-6",children:[e.jsx(L,{testId:"name-input",name:"secret-name",type:"text",label:"Name",className:"w-full max-w-[350px]",required:!0,defaultValue:t==="edit"&&s?s:"",placeholder:l("SECRETS$API_KEY_EXAMPLE"),pattern:"^\\S*$"}),b&&e.jsx("p",{className:"text-red-500 text-sm",children:b}),t==="add"&&e.jsxs("label",{className:"flex flex-col gap-2.5 w-full max-w-[680px]",children:[e.jsx("span",{className:"text-sm",children:l(m.FORM$VALUE)}),e.jsx("textarea",{"data-testid":"value-input",name:"secret-value",required:!0,className:$("resize-none","bg-tertiary border border-[#717888] rounded-sm p-2 placeholder:italic placeholder:text-tertiary-alt","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed"),rows:8})]}),e.jsxs("label",{className:"flex flex-col gap-2.5 w-full max-w-[680px]",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:l(m.FORM$DESCRIPTION)}),e.jsx(q,{})]}),e.jsx("input",{"data-testid":"description-input",name:"secret-description",defaultValue:x,className:$("resize-none","bg-tertiary border border-[#717888] rounded-sm p-2 placeholder:italic placeholder:text-tertiary-alt","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{testId:"cancel-button",type:"button",variant:"secondary",onClick:a,children:l(m.BUTTON$CANCEL)}),e.jsxs(p,{testId:"submit-button",type:"submit",variant:"primary",children:[t==="add"&&l("SECRETS$ADD_SECRET"),t==="edit"&&l("SECRETS$EDIT_SECRET")]})]})]})}function R(){return e.jsxs("div",{className:"border-t border-[#717888] last-of-type:border-b max-w-[830px] pr-2.5 py-[13px] flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center justify-between w-1/3",children:[e.jsx("span",{className:"skeleton h-4 w-1/2"}),e.jsx("span",{className:"skeleton h-4 w-1/4"})]}),e.jsxs("div",{className:"flex items-center gap-8",children:[e.jsx("span",{className:"skeleton h-4 w-4"}),e.jsx("span",{className:"skeleton h-4 w-4"})]})]})}function X({title:t,description:s,onEdit:a,onDelete:c}){return e.jsxs("tr",{"data-testid":"secret-item",className:"flex w-full items-center border-t border-tertiary",children:[e.jsx("td",{className:"p-3 w-1/4 text-sm text-content-2 truncate",title:t,children:t}),e.jsx("td",{className:"p-3 w-1/2 truncate overflow-hidden whitespace-nowrap text-sm text-content-2 opacity-80 italic",title:s||"",children:s||""}),e.jsxs("td",{className:"p-3 w-1/4 flex items-center justify-end gap-4",children:[e.jsx("button",{"data-testid":"edit-secret-button",type:"button",onClick:a,"aria-label":`Edit ${t}`,className:"cursor-pointer",children:e.jsx(B,{size:16})}),e.jsx("button",{"data-testid":"delete-secret-button",type:"button",onClick:c,"aria-label":`Delete ${t}`,className:"cursor-pointer",children:e.jsx(Q,{size:16})})]})]})}function W({text:t,onConfirm:s,onCancel:a}){const{t:c}=O();return e.jsx(V,{onClose:a,children:e.jsxs("div",{"data-testid":"confirmation-modal",className:"bg-base-secondary p-4 rounded-xl flex flex-col gap-4 border border-tertiary",children:[e.jsx("p",{children:t}),e.jsxs("div",{className:"w-full flex gap-2",children:[e.jsx(p,{testId:"cancel-button",type:"button",onClick:a,variant:"secondary",className:"grow",children:c(m.BUTTON$CANCEL)}),e.jsx(p,{testId:"confirm-button",type:"button",onClick:s,variant:"primary",className:"grow",children:c(m.BUTTON$CONFIRM)})]})]})})}function H(){const t=F(),{t:s}=O(),{data:a}=A(),{data:c,isLoading:l}=M(),{mutate:h}=K(),{providers:C}=_(),g=a?.APP_MODE==="saas",b=C.length>0,[d,x]=N.useState("list"),[f,E]=N.useState(null),[w,S]=N.useState(!1),T=r=>{t.setQueryData(["secrets"],D=>D?D.filter(j=>j.name!==r):[])},v=()=>{t.invalidateQueries({queryKey:["secrets"]})},n=r=>{T(r),h(r,{onSettled:()=>{S(!1)},onError:v})},o=()=>{f&&n(f)},i=()=>{S(!1)},u=g&&!b;return e.jsxs("div",{"data-testid":"secrets-settings-screen",className:"px-11 py-9 flex flex-col gap-5",children:[l&&d==="list"&&e.jsxs("ul",{children:[e.jsx(R,{}),e.jsx(R,{}),e.jsx(R,{})]}),u&&e.jsx(P,{to:"/settings/integrations","data-testid":"connect-git-button",type:"button",children:e.jsx(p,{type:"button",variant:"secondary",children:s(m.SECRETS$CONNECT_GIT_PROVIDER)})}),c?.length===0&&d==="list"&&e.jsx("p",{"data-testid":"no-secrets-message",children:s("SECRETS$NO_SECRETS_FOUND")}),!u&&d==="list"&&e.jsx(p,{testId:"add-secret-button",type:"button",variant:"primary",onClick:()=>x("add-secret-form"),isDisabled:l,children:s("SECRETS$ADD_NEW_SECRET")}),d==="list"&&e.jsx("div",{className:"border border-tertiary rounded-md overflow-hidden",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-base-tertiary",children:e.jsxs("tr",{className:"flex w-full items-center",children:[e.jsx("th",{className:"w-1/4 text-left p-3 text-sm font-medium",children:s(m.SETTINGS$NAME)}),e.jsx("th",{className:"w-1/2 text-left p-3 text-sm font-medium",children:s(m.SECRETS$DESCRIPTION)}),e.jsx("th",{className:"w-1/4 text-right p-3 text-sm font-medium",children:s(m.SETTINGS$ACTIONS)})]})}),e.jsx("tbody",{children:c?.map(r=>e.jsx(X,{title:r.name,description:r.description,onEdit:()=>{x("edit-secret-form"),E(r.name)},onDelete:()=>{S(!0),E(r.name)}},r.name))})]})}),(d==="add-secret-form"||d==="edit-secret-form")&&e.jsx(Y,{mode:d==="add-secret-form"?"add":"edit",selectedSecret:f,onCancel:()=>x("list")}),w&&e.jsx(W,{text:s("SECRETS$CONFIRM_DELETE_KEY"),onConfirm:o,onCancel:i})]})}const be=k(H);export{be as default};
