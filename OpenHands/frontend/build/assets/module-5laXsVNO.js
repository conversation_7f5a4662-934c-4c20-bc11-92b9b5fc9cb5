var g=typeof window<"u"?window:void 0,X=typeof globalThis<"u"?globalThis:g,Fr=Array.prototype,Ss=Fr.forEach,ks=Fr.indexOf,ie=X?.navigator,y=X?.document,Z=X?.location,Ci=X?.fetch,Ri=X!=null&&X.XMLHttpRequest&&"withCredentials"in new X.XMLHttpRequest?X.XMLHttpRequest:void 0,xs=X?.AbortController,K=ie?.userAgent,k=g??{},xe={DEBUG:!1,LIB_VERSION:"1.258.3"},pi="$copy_autocapture",zn=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],Ie=function(s){return s.GZipJS="gzip-js",s.Base64="base64",s}({}),Un=["fatal","error","warning","log","info","debug"];function I(s,e){return s.indexOf(e)!==-1}var ri=function(s){return s.trim()},Pi=function(s){return s.replace(/^\$/,"")},Gn=Array.isArray,Cr=Object.prototype,Rr=Cr.hasOwnProperty,ni=Cr.toString,T=Gn||function(s){return ni.call(s)==="[object Array]"},ae=s=>typeof s=="function",M=s=>s===Object(s)&&!T(s),tt=s=>{if(M(s)){for(var e in s)if(Rr.call(s,e))return!1;return!0}return!1},_=s=>s===void 0,D=s=>ni.call(s)=="[object String]",Ti=s=>D(s)&&s.trim().length===0,Ne=s=>s===null,C=s=>_(s)||Ne(s),G=s=>ni.call(s)=="[object Number]",ve=s=>ni.call(s)==="[object Boolean]",Vn=s=>s instanceof FormData,Wn=s=>I(zn,s),Pr=s=>{var e={t:function(t){if(g&&(xe.DEBUG||k.POSTHOG_DEBUG)&&!_(g.console)&&g.console){for(var i=("__rrweb_original__"in g.console[t])?g.console[t].__rrweb_original__:g.console[t],r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];i(s,...n)}},info:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t("log",...i)},warn:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t("warn",...i)},error:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t("error",...i)},critical:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];console.error(s,...i)},uninitializedWarning:t=>{e.error("You must initialize PostHog before calling "+t)},createLogger:t=>Pr(s+" "+t)};return e},w=Pr("[PostHog.js]"),j=w.createLogger,Yn=j("[ExternalScriptsLoader]"),Is=(s,e,t)=>{if(s.config.disable_external_dependency_loading)return Yn.warn(e+" was requested but loading of external scripts is disabled."),t("Loading of external scripts is disabled");var i=y?.querySelectorAll("script");if(i){for(var r=0;r<i.length;r++)if(i[r].src===e)return t()}var n=()=>{if(!y)return t("document not found");var a=y.createElement("script");if(a.type="text/javascript",a.crossOrigin="anonymous",a.src=e,a.onload=u=>t(void 0,u),a.onerror=u=>t(u),s.config.prepare_external_dependency_script&&(a=s.config.prepare_external_dependency_script(a)),!a)return t("prepare_external_dependency_script returned null");var o,l=y.querySelectorAll("body > script");l.length>0?(o=l[0].parentNode)==null||o.insertBefore(a,l[0]):y.body.appendChild(a)};y!=null&&y.body?n():y?.addEventListener("DOMContentLoaded",n)};function m(){return m=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)({}).hasOwnProperty.call(t,i)&&(s[i]=t[i])}return s},m.apply(null,arguments)}function Tr(s,e){if(s==null)return{};var t={};for(var i in s)if({}.hasOwnProperty.call(s,i)){if(e.indexOf(i)!==-1)continue;t[i]=s[i]}return t}k.__PosthogExtensions__=k.__PosthogExtensions__||{},k.__PosthogExtensions__.loadExternalDependency=(s,e,t)=>{var i="/static/"+e+".js?v="+s.version;if(e==="remote-config"&&(i="/array/"+s.config.token+"/config.js"),e==="toolbar"){var r=3e5;i=i+"&t="+Math.floor(Date.now()/r)*r}var n=s.requestRouter.endpointFor("assets",i);Is(s,n,t)},k.__PosthogExtensions__.loadSiteApp=(s,e,t)=>{var i=s.requestRouter.endpointFor("api",e);Is(s,i,t)};var Ut={};function Me(s,e,t){if(T(s)){if(Ss&&s.forEach===Ss)s.forEach(e,t);else if("length"in s&&s.length===+s.length){for(var i=0,r=s.length;i<r;i++)if(i in s&&e.call(t,s[i],i)===Ut)return}}}function R(s,e,t){if(!C(s)){if(T(s))return Me(s,e,t);if(Vn(s)){for(var i of s.entries())if(e.call(t,i[1],i[0])===Ut)return}else for(var r in s)if(Rr.call(s,r)&&e.call(t,s[r],r)===Ut)return}}var L=function(s){for(var e=arguments.length,t=new Array(e>1?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return Me(t,function(r){for(var n in r)r[n]!==void 0&&(s[n]=r[n])}),s},ai=function(s){for(var e=arguments.length,t=new Array(e>1?e-1:0),i=1;i<e;i++)t[i-1]=arguments[i];return Me(t,function(r){Me(r,function(n){s.push(n)})}),s};function Ht(s){for(var e=Object.keys(s),t=e.length,i=new Array(t);t--;)i[t]=[e[t],s[e[t]]];return i}var $s=function(s){try{return s()}catch{return}},Jn=function(s){return function(){try{for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return s.apply(this,t)}catch(r){w.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),w.critical(r)}}},ns=function(s){var e={};return R(s,function(t,i){(D(t)&&t.length>0||G(t))&&(e[i]=t)}),e};function Kn(s,e){return t=s,i=n=>D(n)&&!Ne(e)?n.slice(0,e):n,r=new Set,function n(a,o){return a!==Object(a)?i?i(a,o):a:r.has(a)?void 0:(r.add(a),T(a)?(l=[],Me(a,u=>{l.push(n(u))})):(l={},R(a,(u,c)=>{r.has(u)||(l[c]=n(u,c))})),l);var l}(t);var t,i,r}var Zn=["herokuapp.com","vercel.app","netlify.app"];function Xn(s){var e=s?.hostname;if(!D(e))return!1;var t=e.split(".").slice(-2).join(".");for(var i of Zn)if(t===i)return!1;return!0}function Or(s,e){for(var t=0;t<s.length;t++)if(e(s[t]))return s[t]}function A(s,e,t,i){var{capture:r=!1,passive:n=!0}=i??{};s?.addEventListener(e,t,{capture:r,passive:n})}var Ar="$people_distinct_id",ft="__alias",vt="__timers",Fs="$autocapture_disabled_server_side",Oi="$heatmaps_enabled_server_side",Cs="$exception_capture_enabled_server_side",Ai="$error_tracking_suppression_rules",Rs="$error_tracking_capture_extension_exceptions",Ps="$web_vitals_enabled_server_side",Mr="$dead_clicks_enabled_server_side",Ts="$web_vitals_allowed_metrics",Mi="$session_recording_enabled_server_side",Os="$console_log_recording_enabled_server_side",As="$session_recording_network_payload_capture",Ms="$session_recording_masking",Ds="$session_recording_canvas_recording",Ls="$replay_sample_rate",Ns="$replay_minimum_duration",qs="$replay_script_config",Gt="$sesid",_t="$session_is_sampled",Di="$session_recording_url_trigger_activated_session",Li="$session_recording_event_trigger_activated_session",it="$enabled_feature_flags",mt="$early_access_features",Ni="$feature_flag_details",yt="$stored_person_properties",Ue="$stored_group_properties",qi="$surveys",Pt="$surveys_activated",Vt="$flag_call_reported",Se="$user_state",ji="$client_session_props",Bi="$capture_rate_limit",Hi="$initial_campaign_params",zi="$initial_referrer_info",Wt="$initial_person_info",Yt="$epp",Dr="__POSTHOG_TOOLBAR__",Tt="$posthog_cookieless",Qn=[Ar,ft,"__cmpns",vt,Mi,Oi,Gt,it,Ai,Se,mt,Ni,Ue,yt,qi,Vt,ji,Bi,Hi,zi,Yt,Wt];function js(s){return s instanceof Element&&(s.id===Dr||!(s.closest==null||!s.closest(".toolbar-global-fade-container")))}function oi(s){return!!s&&s.nodeType===1}function De(s,e){return!!s&&!!s.tagName&&s.tagName.toLowerCase()===e.toLowerCase()}function Lr(s){return!!s&&s.nodeType===3}function Nr(s){return!!s&&s.nodeType===11}function as(s){return s?ri(s).split(/\s+/):[]}function Bs(s){var e=g?.location.href;return!!(e&&s&&s.some(t=>e.match(t)))}function Jt(s){var e="";switch(typeof s.className){case"string":e=s.className;break;case"object":e=(s.className&&"baseVal"in s.className?s.className.baseVal:null)||s.getAttribute("class")||"";break;default:e=""}return as(e)}function qr(s){return C(s)?null:ri(s).split(/(\s+)/).filter(e=>at(e)).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function li(s){var e="";return Gi(s)&&!Hr(s)&&s.childNodes&&s.childNodes.length&&R(s.childNodes,function(t){var i;Lr(t)&&t.textContent&&(e+=(i=qr(t.textContent))!==null&&i!==void 0?i:"")}),ri(e)}function jr(s){return _(s.target)?s.srcElement||null:(e=s.target)!=null&&e.shadowRoot?s.composedPath()[0]||null:s.target||null;var e}var Ui=["a","button","form","input","select","textarea","label"];function Br(s){var e=s.parentNode;return!(!e||!oi(e))&&e}function ea(s,e,t,i,r){var n,a,o;if(t===void 0&&(t=void 0),!g||!s||De(s,"html")||!oi(s)||(n=t)!=null&&n.url_allowlist&&!Bs(t.url_allowlist)||(a=t)!=null&&a.url_ignorelist&&Bs(t.url_ignorelist))return!1;if((o=t)!=null&&o.dom_event_allowlist){var l=t.dom_event_allowlist;if(l&&!l.some(b=>e.type===b))return!1}for(var u=!1,c=[s],d=!0,h=s;h.parentNode&&!De(h,"body");)if(Nr(h.parentNode))c.push(h.parentNode.host),h=h.parentNode.host;else{if(!(d=Br(h)))break;if(i||Ui.indexOf(d.tagName.toLowerCase())>-1)u=!0;else{var p=g.getComputedStyle(d);p&&p.getPropertyValue("cursor")==="pointer"&&(u=!0)}c.push(d),h=d}if(!function(b,E){var S=E?.element_allowlist;if(_(S))return!0;var x,$=function(W){if(S.some(B=>W.tagName.toLowerCase()===B))return{v:!0}};for(var P of b)if(x=$(P))return x.v;return!1}(c,t)||!function(b,E){var S=E?.css_selector_allowlist;if(_(S))return!0;var x,$=function(W){if(S.some(B=>W.matches(B)))return{v:!0}};for(var P of b)if(x=$(P))return x.v;return!1}(c,t))return!1;var v=g.getComputedStyle(s);if(v&&v.getPropertyValue("cursor")==="pointer"&&e.type==="click")return!0;var f=s.tagName.toLowerCase();switch(f){case"html":return!1;case"form":return(r||["submit"]).indexOf(e.type)>=0;case"input":case"select":case"textarea":return(r||["change","click"]).indexOf(e.type)>=0;default:return u?(r||["click"]).indexOf(e.type)>=0:(r||["click"]).indexOf(e.type)>=0&&(Ui.indexOf(f)>-1||s.getAttribute("contenteditable")==="true")}}function Gi(s){for(var e=s;e.parentNode&&!De(e,"body");e=e.parentNode){var t=Jt(e);if(I(t,"ph-sensitive")||I(t,"ph-no-capture"))return!1}if(I(Jt(s),"ph-include"))return!0;var i=s.type||"";if(D(i))switch(i.toLowerCase()){case"hidden":case"password":return!1}var r=s.name||s.id||"";return!(D(r)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g,"")))}function Hr(s){return!!(De(s,"input")&&!["button","checkbox","submit","reset"].includes(s.type)||De(s,"select")||De(s,"textarea")||s.getAttribute("contenteditable")==="true")}var zr="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",ta=new RegExp("^(?:"+zr+")$"),ia=new RegExp(zr),Ur="\\d{3}-?\\d{2}-?\\d{4}",sa=new RegExp("^("+Ur+")$"),ra=new RegExp("("+Ur+")");function at(s,e){return e===void 0&&(e=!0),!(C(s)||D(s)&&(s=ri(s),(e?ta:ia).test((s||"").replace(/[- ]/g,""))||(e?sa:ra).test(s)))}function Gr(s){var e=li(s);return at(e=(e+" "+Vr(s)).trim())?e:""}function Vr(s){var e="";return s&&s.childNodes&&s.childNodes.length&&R(s.childNodes,function(t){var i;if(t&&((i=t.tagName)==null?void 0:i.toLowerCase())==="span")try{var r=li(t);e=(e+" "+r).trim(),t.childNodes&&t.childNodes.length&&(e=(e+" "+Vr(t)).trim())}catch(n){w.error("[AutoCapture]",n)}}),e}function na(s){return function(e){var t=e.map(i=>{var r,n,a="";if(i.tag_name&&(a+=i.tag_name),i.attr_class)for(var o of(i.attr_class.sort(),i.attr_class))a+="."+o.replace(/"/g,"");var l=m({},i.text?{text:i.text}:{},{"nth-child":(r=i.nth_child)!==null&&r!==void 0?r:0,"nth-of-type":(n=i.nth_of_type)!==null&&n!==void 0?n:0},i.href?{href:i.href}:{},i.attr_id?{attr_id:i.attr_id}:{},i.attributes),u={};return Ht(l).sort((c,d)=>{var[h]=c,[p]=d;return h.localeCompare(p)}).forEach(c=>{var[d,h]=c;return u[Hs(d.toString())]=Hs(h.toString())}),a+=":",a+=Ht(u).map(c=>{var[d,h]=c;return d+'="'+h+'"'}).join("")});return t.join(";")}(function(e){return e.map(t=>{var i,r,n={text:(i=t.$el_text)==null?void 0:i.slice(0,400),tag_name:t.tag_name,href:(r=t.attr__href)==null?void 0:r.slice(0,2048),attr_class:aa(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return Ht(t).filter(a=>{var[o]=a;return o.indexOf("attr__")===0}).forEach(a=>{var[o,l]=a;return n.attributes[o]=l}),n})}(s))}function Hs(s){return s.replace(/"|\\"/g,'\\"')}function aa(s){var e=s.attr__class;return e?T(e)?e:as(e):void 0}class Wr{constructor(){this.clicks=[]}isRageClick(e,t,i){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(e-r.x)+Math.abs(t-r.y)<30&&i-r.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:i}),this.clicks.length===3)return!0}else this.clicks=[{x:e,y:t,timestamp:i}];return!1}}var oa=["localhost","127.0.0.1"],ot=s=>{var e=y?.createElement("a");return _(e)?null:(e.href=s,e)},la=function(s,e){var t,i;e===void 0&&(e="&");var r=[];return R(s,function(n,a){_(n)||_(a)||a==="undefined"||(t=encodeURIComponent((o=>o instanceof File)(n)?n.name:n.toString()),i=encodeURIComponent(a),r[r.length]=i+"="+t)}),r.join(e)},Kt=function(s,e){for(var t,i=((s.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),r=0;r<i.length;r++){var n=i[r].split("=");if(n[0]===e){t=n;break}}if(!T(t)||t.length<2)return"";var a=t[1];try{a=decodeURIComponent(a)}catch{w.error("Skipping decoding for malformed query param: "+a)}return a.replace(/\+/g," ")},os=function(s,e,t){if(!s||!e||!e.length)return s;for(var i=s.split("#"),r=i[0]||"",n=i[1],a=r.split("?"),o=a[1],l=a[0],u=(o||"").split("&"),c=[],d=0;d<u.length;d++){var h=u[d].split("=");T(h)&&(e.includes(h[0])?c.push(h[0]+"="+t):c.push(u[d]))}var p=l;return o!=null&&(p+="?"+c.join("&")),n!=null&&(p+="#"+n),p},Zt=function(s,e){var t=s.match(new RegExp(e+"=([^&]*)"));return t?t[1]:null},zs=j("[AutoCapture]");function gi(s,e){return e.length>s?e.slice(0,s)+"...":e}function ua(s){if(s.previousElementSibling)return s.previousElementSibling;var e=s;do e=e.previousSibling;while(e&&!oi(e));return e}function ca(s,e,t,i){var r=s.tagName.toLowerCase(),n={tag_name:r};Ui.indexOf(r)>-1&&!t&&(r.toLowerCase()==="a"||r.toLowerCase()==="button"?n.$el_text=gi(1024,Gr(s)):n.$el_text=gi(1024,li(s)));var a=Jt(s);a.length>0&&(n.classes=a.filter(function(c){return c!==""})),R(s.attributes,function(c){var d;if((!Hr(s)||["name","id","class","aria-label"].indexOf(c.name)!==-1)&&(i==null||!i.includes(c.name))&&!e&&at(c.value)&&(d=c.name,!D(d)||d.substring(0,10)!=="_ngcontent"&&d.substring(0,7)!=="_nghost")){var h=c.value;c.name==="class"&&(h=as(h).join(" ")),n["attr__"+c.name]=gi(1024,h)}});for(var o=1,l=1,u=s;u=ua(u);)o++,u.tagName===s.tagName&&l++;return n.nth_child=o,n.nth_of_type=l,n}function ha(s,e){for(var t,i,{e:r,maskAllElementAttributes:n,maskAllText:a,elementAttributeIgnoreList:o,elementsChainAsString:l}=e,u=[s],c=s;c.parentNode&&!De(c,"body");)Nr(c.parentNode)?(u.push(c.parentNode.host),c=c.parentNode.host):(u.push(c.parentNode),c=c.parentNode);var d,h=[],p={},v=!1,f=!1;if(R(u,$=>{var P=Gi($);$.tagName.toLowerCase()==="a"&&(v=$.getAttribute("href"),v=P&&v&&at(v)&&v),I(Jt($),"ph-no-capture")&&(f=!0),h.push(ca($,n,a,o));var W=function(B){if(!Gi(B))return{};var F={};return R(B.attributes,function(he){if(he.name&&he.name.indexOf("data-ph-capture-attribute")===0){var ee=he.name.replace("data-ph-capture-attribute-",""),ue=he.value;ee&&ue&&at(ue)&&(F[ee]=ue)}}),F}($);L(p,W)}),f)return{props:{},explicitNoCapture:f};if(a||(s.tagName.toLowerCase()==="a"||s.tagName.toLowerCase()==="button"?h[0].$el_text=Gr(s):h[0].$el_text=li(s)),v){var b,E;h[0].attr__href=v;var S=(b=ot(v))==null?void 0:b.host,x=g==null||(E=g.location)==null?void 0:E.host;S&&x&&S!==x&&(d=v)}return{props:L({$event_type:r.type,$ce_version:1},l?{}:{$elements:h},{$elements_chain:na(h)},(t=h[0])!=null&&t.$el_text?{$el_text:(i=h[0])==null?void 0:i.$el_text}:{},d&&r.type==="click"?{$external_click_url:d}:{},p)}}class da{constructor(e){this.i=!1,this.o=null,this.rageclicks=new Wr,this.h=!1,this.instance=e,this.m=null}get S(){var e,t,i=M(this.instance.config.autocapture)?this.instance.config.autocapture:{};return i.url_allowlist=(e=i.url_allowlist)==null?void 0:e.map(r=>new RegExp(r)),i.url_ignorelist=(t=i.url_ignorelist)==null?void 0:t.map(r=>new RegExp(r)),i}$(){if(this.isBrowserSupported()){if(g&&y){var e=i=>{i=i||g?.event;try{this.k(i)}catch(r){zs.error("Failed to capture event",r)}};if(A(y,"submit",e,{capture:!0}),A(y,"change",e,{capture:!0}),A(y,"click",e,{capture:!0}),this.S.capture_copied_text){var t=i=>{i=i||g?.event,this.k(i,pi)};A(y,"copy",t,{capture:!0}),A(y,"cut",t,{capture:!0})}}}else zs.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this.h=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[Fs]:!!e.autocapture_opt_out}),this.o=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this.m=e}getElementSelectors(e){var t,i=[];return(t=this.m)==null||t.forEach(r=>{var n=y?.querySelectorAll(r);n?.forEach(a=>{e===a&&i.push(r)})}),i}get isEnabled(){var e,t,i=(e=this.instance.persistence)==null?void 0:e.props[Fs],r=this.o;if(Ne(r)&&!ve(i)&&!this.instance.I())return!1;var n=(t=this.o)!==null&&t!==void 0?t:!!i;return!!this.instance.config.autocapture&&!n}k(e,t){if(t===void 0&&(t="$autocapture"),this.isEnabled){var i,r=jr(e);Lr(r)&&(r=r.parentNode||null),t==="$autocapture"&&e.type==="click"&&e instanceof MouseEvent&&this.instance.config.rageclick&&(i=this.rageclicks)!=null&&i.isRageClick(e.clientX,e.clientY,new Date().getTime())&&this.k(e,"$rageclick");var n=t===pi;if(r&&ea(r,e,this.S,n,n?["copy","cut"]:void 0)){var{props:a,explicitNoCapture:o}=ha(r,{e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(o)return!1;var l=this.getElementSelectors(r);if(l&&l.length>0&&(a.$element_selectors=l),t===pi){var u,c=qr(g==null||(u=g.getSelection())==null?void 0:u.toString()),d=e.type||"clipboard";if(!c)return!1;a.$selected_content=c,a.$copy_type=d}return this.instance.capture(t,a),!0}}}isBrowserSupported(){return ae(y?.querySelectorAll)}}Math.trunc||(Math.trunc=function(s){return s<0?Math.ceil(s):Math.floor(s)}),Number.isInteger||(Number.isInteger=function(s){return G(s)&&isFinite(s)&&Math.floor(s)===s});var Us="0123456789abcdef";class Xt{constructor(e){if(this.bytes=e,e.length!==16)throw new TypeError("not 128-bit length")}static fromFieldsV7(e,t,i,r){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(r)||e<0||t<0||i<0||r<0||e>0xffffffffffff||t>4095||i>1073741823||r>4294967295)throw new RangeError("invalid field value");var n=new Uint8Array(16);return n[0]=e/Math.pow(2,40),n[1]=e/Math.pow(2,32),n[2]=e/Math.pow(2,24),n[3]=e/Math.pow(2,16),n[4]=e/Math.pow(2,8),n[5]=e,n[6]=112|t>>>8,n[7]=t,n[8]=128|i>>>24,n[9]=i>>>16,n[10]=i>>>8,n[11]=i,n[12]=r>>>24,n[13]=r>>>16,n[14]=r>>>8,n[15]=r,new Xt(n)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+Us.charAt(this.bytes[t]>>>4)+Us.charAt(15&this.bytes[t]),t!==3&&t!==5&&t!==7&&t!==9||(e+="-");if(e.length!==36)throw new Error("Invalid UUIDv7 was generated");return e}clone(){return new Xt(this.bytes.slice(0))}equals(e){return this.compareTo(e)===0}compareTo(e){for(var t=0;t<16;t++){var i=this.bytes[t]-e.bytes[t];if(i!==0)return Math.sign(i)}return 0}}class pa{constructor(){this.P=0,this.R=0,this.T=new ga}generate(){var e=this.generateOrAbort();if(_(e)){this.P=0;var t=this.generateOrAbort();if(_(t))throw new Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.P)this.P=e,this.M();else{if(!(e+1e4>this.P))return;this.R++,this.R>4398046511103&&(this.P++,this.M())}return Xt.fromFieldsV7(this.P,Math.trunc(this.R/Math.pow(2,30)),this.R&Math.pow(2,30)-1,this.T.nextUint32())}M(){this.R=1024*this.T.nextUint32()+(1023&this.T.nextUint32())}}var Gs,Yr=s=>{if(typeof UUIDV7_DENY_WEAK_RNG<"u"&&UUIDV7_DENY_WEAK_RNG)throw new Error("no cryptographically strong RNG available");for(var e=0;e<s.length;e++)s[e]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return s};g&&!_(g.crypto)&&crypto.getRandomValues&&(Yr=s=>crypto.getRandomValues(s));class ga{constructor(){this.C=new Uint32Array(8),this.F=1/0}nextUint32(){return this.F>=this.C.length&&(Yr(this.C),this.F=0),this.C[this.F++]}}var Ae=()=>fa().toString(),fa=()=>(Gs||(Gs=new pa)).generate(),ht="",va=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i;function _a(s,e){if(e){var t=function(r,n){if(n===void 0&&(n=y),ht)return ht;if(!n||["localhost","127.0.0.1"].includes(r))return"";for(var a=r.split("."),o=Math.min(a.length,8),l="dmn_chk_"+Ae();!ht&&o--;){var u=a.slice(o).join("."),c=l+"=1;domain=."+u+";path=/";n.cookie=c+";max-age=3",n.cookie.includes(l)&&(n.cookie=c+";max-age=0",ht=u)}return ht}(s);if(!t){var i=(r=>{var n=r.match(va);return n?n[0]:""})(s);i!==t&&w.info("Warning: cookie subdomain discovery mismatch",i,t),t=i}return t?"; domain=."+t:""}return""}var me={O:()=>!!y,A:function(s){w.error("cookieStore error: "+s)},D:function(s){if(y){try{for(var e=s+"=",t=y.cookie.split(";").filter(n=>n.length),i=0;i<t.length;i++){for(var r=t[i];r.charAt(0)==" ";)r=r.substring(1,r.length);if(r.indexOf(e)===0)return decodeURIComponent(r.substring(e.length,r.length))}}catch{}return null}},L:function(s){var e;try{e=JSON.parse(me.D(s))||{}}catch{}return e},j:function(s,e,t,i,r){if(y)try{var n="",a="",o=_a(y.location.hostname,i);if(t){var l=new Date;l.setTime(l.getTime()+24*t*60*60*1e3),n="; expires="+l.toUTCString()}r&&(a="; secure");var u=s+"="+encodeURIComponent(JSON.stringify(e))+n+"; SameSite=Lax; path=/"+o+a;return u.length>3686.4&&w.warn("cookieStore warning: large cookie, len="+u.length),y.cookie=u,u}catch{return}},N:function(s,e){try{me.j(s,"",-1,e)}catch{return}}},fi=null,q={O:function(){if(!Ne(fi))return fi;var s=!0;if(_(g))s=!1;else try{var e="__mplssupport__";q.j(e,"xyz"),q.D(e)!=='"xyz"'&&(s=!1),q.N(e)}catch{s=!1}return s||w.error("localStorage unsupported; falling back to cookie store"),fi=s,s},A:function(s){w.error("localStorage error: "+s)},D:function(s){try{return g?.localStorage.getItem(s)}catch(e){q.A(e)}return null},L:function(s){try{return JSON.parse(q.D(s))||{}}catch{}return null},j:function(s,e){try{g?.localStorage.setItem(s,JSON.stringify(e))}catch(t){q.A(t)}},N:function(s){try{g?.localStorage.removeItem(s)}catch(e){q.A(e)}}},ma=["distinct_id",Gt,_t,Yt,Wt],Ot=m({},q,{L:function(s){try{var e={};try{e=me.L(s)||{}}catch{}var t=L(e,JSON.parse(q.D(s)||"{}"));return q.j(s,t),t}catch{}return null},j:function(s,e,t,i,r,n){try{q.j(s,e,void 0,void 0,n);var a={};ma.forEach(o=>{e[o]&&(a[o]=e[o])}),Object.keys(a).length&&me.j(s,a,t,i,r,n)}catch(o){q.A(o)}},N:function(s,e){try{g?.localStorage.removeItem(s),me.N(s,e)}catch(t){q.A(t)}}}),At={},ya={O:function(){return!0},A:function(s){w.error("memoryStorage error: "+s)},D:function(s){return At[s]||null},L:function(s){return At[s]||null},j:function(s,e){At[s]=e},N:function(s){delete At[s]}},je=null,H={O:function(){if(!Ne(je))return je;if(je=!0,_(g))je=!1;else try{var s="__support__";H.j(s,"xyz"),H.D(s)!=='"xyz"'&&(je=!1),H.N(s)}catch{je=!1}return je},A:function(s){w.error("sessionStorage error: ",s)},D:function(s){try{return g?.sessionStorage.getItem(s)}catch(e){H.A(e)}return null},L:function(s){try{return JSON.parse(H.D(s))||null}catch{}return null},j:function(s,e){try{g?.sessionStorage.setItem(s,JSON.stringify(e))}catch(t){H.A(t)}},N:function(s){try{g?.sessionStorage.removeItem(s)}catch(e){H.A(e)}}},Xe=function(s){return s[s.PENDING=-1]="PENDING",s[s.DENIED=0]="DENIED",s[s.GRANTED=1]="GRANTED",s}({});class ba{constructor(e){this._instance=e}get S(){return this._instance.config}get consent(){return this.U()?Xe.DENIED:this.q}isOptedOut(){return this.consent===Xe.DENIED||this.consent===Xe.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.B.j(this.H,e?1:0,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.B.N(this.H,this.S.cross_subdomain_cookie)}get H(){var{token:e,opt_out_capturing_cookie_prefix:t}=this._instance.config;return(t||"__ph_opt_in_out_")+e}get q(){var e=this.B.D(this.H);return e==="1"?Xe.GRANTED:e==="0"?Xe.DENIED:Xe.PENDING}get B(){if(!this.W){var e=this.S.opt_out_capturing_persistence_type;this.W=e==="localStorage"?q:me;var t=e==="localStorage"?me:q;t.D(this.H)&&(this.W.D(this.H)||this.optInOut(t.D(this.H)==="1"),t.N(this.H,this.S.cross_subdomain_cookie))}return this.W}U(){return!!this.S.respect_dnt&&!!Or([ie?.doNotTrack,ie?.msDoNotTrack,k.doNotTrack],e=>I([!0,1,"1","yes"],e))}}var Mt=j("[Dead Clicks]"),wa=()=>!0,Ea=s=>{var e,t=!((e=s.instance.persistence)==null||!e.get_property(Mr)),i=s.instance.config.capture_dead_clicks;return ve(i)?i:t};class Jr{get lazyLoadedDeadClicksAutocapture(){return this.G}constructor(e,t,i){this.instance=e,this.isEnabled=t,this.onCapture=i,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[Mr]:e?.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.J(()=>{this.V()})}J(e){var t,i;(t=k.__PosthogExtensions__)!=null&&t.initDeadClicksAutocapture&&e(),(i=k.__PosthogExtensions__)==null||i.loadExternalDependency==null||i.loadExternalDependency(this.instance,"dead-clicks-autocapture",r=>{r?Mt.error("failed to load script",r):e()})}V(){var e;if(y){if(!this.G&&(e=k.__PosthogExtensions__)!=null&&e.initDeadClicksAutocapture){var t=M(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this.G=k.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this.G.start(y),Mt.info("starting...")}}else Mt.error("`document` not found. Cannot start.")}stop(){this.G&&(this.G.stop(),this.G=void 0,Mt.info("stopping..."))}}function se(s,e,t,i,r){return e>t&&(w.warn("min cannot be greater than max."),e=t),G(s)?s>t?(i&&w.warn(i+" cannot be  greater than max: "+t+". Using max value instead."),t):s<e?(i&&w.warn(i+" cannot be less than min: "+e+". Using min value instead."),e):s:(i&&w.warn(i+" must be a number. using max or fallback. max: "+t+", fallback: "+r),se(r||t,e,t,i))}class Kr{constructor(e){this.K={},this.Y=()=>{Object.keys(this.K).forEach(t=>{var i=this.X(t)+this.Z;i>=this.tt?delete this.K[t]:this.it(t,i)})},this.X=t=>this.K[String(t)],this.it=(t,i)=>{this.K[String(t)]=i},this.consumeRateLimit=t=>{var i,r=(i=this.X(t))!==null&&i!==void 0?i:this.tt;if((r=Math.max(r-1,0))===0)return!0;this.it(t,r);var n,a=r===0;return a&&((n=this.et)==null||n.call(this,t)),a},this.rt=e,this.et=this.rt.et,this.tt=se(this.rt.bucketSize,0,100,"rate limiter bucket size"),this.Z=se(this.rt.refillRate,0,this.tt,"rate limiter refill rate"),this.st=se(this.rt.refillInterval,0,864e5,"rate limiter refill interval"),setInterval(()=>{this.Y()},this.st)}}var Dt=j("[ExceptionAutocapture]");class Sa{constructor(e){var t,i,r;this.nt=()=>{var n;if(g&&this.isEnabled&&(n=k.__PosthogExtensions__)!=null&&n.errorWrappingFunctions){var a=k.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,o=k.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,l=k.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.ot&&this.S.capture_unhandled_errors&&(this.ot=a(this.captureException.bind(this))),!this.lt&&this.S.capture_unhandled_rejections&&(this.lt=o(this.captureException.bind(this))),!this.ut&&this.S.capture_console_errors&&(this.ut=l(this.captureException.bind(this)))}catch(u){Dt.error("failed to start",u),this.ht()}}},this._instance=e,this.dt=!((t=this._instance.persistence)==null||!t.props[Cs]),this.S=this.vt(),this.ct=new Kr({refillRate:(i=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)!==null&&i!==void 0?i:1,bucketSize:(r=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)!==null&&r!==void 0?r:10,refillInterval:1e4}),this.startIfEnabled()}vt(){var e=this._instance.config.capture_exceptions,t={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return M(e)?t=m({},t,e):(_(e)?this.dt:e)&&(t=m({},t,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),t}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(Dt.info("enabled"),this.J(this.nt))}J(e){var t,i;(t=k.__PosthogExtensions__)!=null&&t.errorWrappingFunctions&&e(),(i=k.__PosthogExtensions__)==null||i.loadExternalDependency==null||i.loadExternalDependency(this._instance,"exception-autocapture",r=>{if(r)return Dt.error("failed to load script",r);e()})}ht(){var e,t,i;(e=this.ot)==null||e.call(this),this.ot=void 0,(t=this.lt)==null||t.call(this),this.lt=void 0,(i=this.ut)==null||i.call(this),this.ut=void 0}onRemoteConfig(e){var t=e.autocaptureExceptions;this.dt=!!t||!1,this.S=this.vt(),this._instance.persistence&&this._instance.persistence.register({[Cs]:this.dt}),this.startIfEnabled()}captureException(e){var t,i=this._instance.requestRouter.endpointFor("ui");e.$exception_personURL=i+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id();var r=(t=e.$exception_list[0].type)!==null&&t!==void 0?t:"Exception";this.ct.consumeRateLimit(r)?Dt.info("Skipping exception capture because of client rate limiting.",{exception:e.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(e)}}function Zr(s){return!_(Event)&&Xr(s,Event)}function Xr(s,e){try{return s instanceof e}catch{return!1}}function Qr(s){switch(Object.prototype.toString.call(s)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return Xr(s,Error)}}function zt(s,e){return Object.prototype.toString.call(s)==="[object "+e+"]"}function vi(s){return zt(s,"DOMError")}var Vs=/\(error: (.*)\)/,Ws=50,st="?";function _i(s,e,t,i){var r={platform:"web:javascript",filename:s,function:e==="<anonymous>"?st:e,in_app:!0};return _(t)||(r.lineno=t),_(i)||(r.colno=i),r}var ka=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,xa=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Ia=/\((\S*)(?::(\d+))(?::(\d+))\)/,$a=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Fa=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Ca=function(){for(var s=arguments.length,e=new Array(s),t=0;t<s;t++)e[t]=arguments[t];var i=e.sort((r,n)=>r[0]-n[0]).map(r=>r[1]);return function(r,n){n===void 0&&(n=0);for(var a=[],o=r.split(`
`),l=n;l<o.length;l++){var u=o[l];if(!(u.length>1024)){var c=Vs.test(u)?u.replace(Vs,"$1"):u;if(!c.match(/\S*Error: /)){for(var d of i){var h=d(c);if(h){a.push(h);break}}if(a.length>=Ws)break}}}return function(p){if(!p.length)return[];var v=Array.from(p);return v.reverse(),v.slice(0,Ws).map(f=>m({},f,{filename:f.filename||Ra(v).filename,function:f.function||st}))}(a)}}([30,s=>{var e=ka.exec(s);if(e){var[,t,i,r]=e;return _i(t,st,+i,+r)}var n=xa.exec(s);if(n){if(n[2]&&n[2].indexOf("eval")===0){var a=Ia.exec(n[2]);a&&(n[2]=a[1],n[3]=a[2],n[4]=a[3])}var[o,l]=Js(n[1]||st,n[2]);return _i(l,o,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,s=>{var e=$a.exec(s);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){var t=Fa.exec(e[3]);t&&(e[1]=e[1]||"eval",e[3]=t[1],e[4]=t[2],e[5]="")}var i=e[3],r=e[1]||st;return[r,i]=Js(r,i),_i(i,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]);function Ra(s){return s[s.length-1]||{}}var Lt,Ys,mi,Js=(s,e)=>{var t=s.indexOf("safari-extension")!==-1,i=s.indexOf("safari-web-extension")!==-1;return t||i?[s.indexOf("@")!==-1?s.split("@")[0]:st,t?"safari-extension:"+e:"safari-web-extension:"+e]:[s,e]},Pa=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function ls(s,e){e===void 0&&(e=0);var t=s.stacktrace||s.stack||"",i=function(a){return a&&Ta.test(a.message)?1:0}(s);try{var r=Ca,n=function(a,o){var l=function(u){var c=globalThis._posthogChunkIds;if(!c)return{};var d=Object.keys(c);return mi&&d.length===Ys||(Ys=d.length,mi=d.reduce((h,p)=>{Lt||(Lt={});var v=Lt[p];if(v)h[v[0]]=v[1];else for(var f=u(p),b=f.length-1;b>=0;b--){var E=f[b],S=E?.filename,x=c[p];if(S&&x){h[S]=x,Lt[p]=[S,x];break}}return h},{})),mi}(o);return a.forEach(u=>{u.filename&&(u.chunk_id=l[u.filename])}),a}(r(t,i),r);return n.slice(0,n.length-e)}catch{}return[]}var Ta=/Minified React error #\d+;/i;function Oa(s,e){var t,i,r=ls(s),n=(t=e?.handled)===null||t===void 0||t,a=(i=e?.synthetic)!==null&&i!==void 0&&i;return{type:e!=null&&e.overrideExceptionType?e.overrideExceptionType:s.name,value:function(o){var l=o.message;return l.error&&typeof l.error.message=="string"?String(l.error.message):String(l)}(s),stacktrace:{frames:r,type:"raw"},mechanism:{handled:n,synthetic:a}}}function en(s,e){var t=Oa(s,e);return s.cause&&Qr(s.cause)&&s.cause!==s?[t,...en(s.cause,{handled:e?.handled,synthetic:e?.synthetic})]:[t]}function yi(s,e){return{$exception_list:en(s,e),$exception_level:"error"}}function bi(s,e){var t,i,r,n=(t=e?.handled)===null||t===void 0||t,a=(i=e?.synthetic)===null||i===void 0||i,o={type:e!=null&&e.overrideExceptionType?e.overrideExceptionType:(r=e?.defaultExceptionType)!==null&&r!==void 0?r:"Error",value:s||e?.defaultExceptionMessage,mechanism:{handled:n,synthetic:a}};if(e!=null&&e.syntheticException){var l=ls(e.syntheticException,1);l.length&&(o.stacktrace={frames:l,type:"raw"})}return{$exception_list:[o],$exception_level:"error"}}function Aa(s){return D(s)&&!Ti(s)&&Un.indexOf(s)>=0}function Ma(s,e){var t,i,r=(t=e?.handled)===null||t===void 0||t,n=(i=e?.synthetic)===null||i===void 0||i,a=e!=null&&e.overrideExceptionType?e.overrideExceptionType:Zr(s)?s.constructor.name:"Error",o="Non-Error 'exception' captured with keys: "+function(c,d){d===void 0&&(d=40);var h=Object.keys(c);if(h.sort(),!h.length)return"[object has no keys]";for(var p=h.length;p>0;p--){var v=h.slice(0,p).join(", ");if(!(v.length>d))return p===h.length||v.length<=d?v:v.slice(0,d)+"..."}return""}(s),l={type:a,value:o,mechanism:{handled:r,synthetic:n}};if(e!=null&&e.syntheticException){var u=ls(e?.syntheticException,1);u.length&&(l.stacktrace={frames:u,type:"raw"})}return{$exception_list:[l],$exception_level:Aa(s.level)?s.level:"error"}}function Da(s,e){var{error:t,event:i}=s,r={$exception_list:[]},n=t||i;if(vi(n)||function(h){return zt(h,"DOMException")}(n)){var a=n;if(function(h){return"stack"in h}(n))r=yi(n,e);else{var o=a.name||(vi(a)?"DOMError":"DOMException"),l=a.message?o+": "+a.message:o;r=bi(l,m({},e,{overrideExceptionType:vi(a)?"DOMError":"DOMException",defaultExceptionMessage:l}))}return"code"in a&&(r.$exception_DOMException_code=""+a.code),r}if(function(h){return zt(h,"ErrorEvent")}(n)&&n.error)return yi(n.error,e);if(Qr(n))return yi(n,e);if(function(h){return zt(h,"Object")}(n)||Zr(n))return Ma(n,e);if(_(t)&&D(i)){var u="Error",c=i,d=i.match(Pa);return d&&(u=d[1],c=d[2]),bi(c,m({},e,{overrideExceptionType:u,defaultExceptionMessage:c}))}return bi(n,e)}function Ks(s,e,t){try{if(!(e in s))return()=>{};var i=s[e],r=t(i);return ae(r)&&(r.prototype=r.prototype||{},Object.defineProperties(r,{__posthog_wrapped__:{enumerable:!1,value:!0}})),s[e]=r,()=>{s[e]=i}}catch{return()=>{}}}class La{constructor(e){var t;this._instance=e,this.ft=(g==null||(t=g.location)==null?void 0:t.pathname)||""}get isEnabled(){return this._instance.config.capture_pageview==="history_change"}startIfEnabled(){this.isEnabled&&(w.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())}stop(){this.gt&&this.gt(),this.gt=void 0,w.info("History API monitoring stopped")}monitorHistoryChanges(){var e,t;if(g&&g.history){var i=this;(e=g.history.pushState)!=null&&e.__posthog_wrapped__||Ks(g.history,"pushState",r=>function(n,a,o){r.call(this,n,a,o),i._t("pushState")}),(t=g.history.replaceState)!=null&&t.__posthog_wrapped__||Ks(g.history,"replaceState",r=>function(n,a,o){r.call(this,n,a,o),i._t("replaceState")}),this.bt()}}_t(e){try{var t,i=g==null||(t=g.location)==null?void 0:t.pathname;if(!i)return;i!==this.ft&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:e}),this.ft=i}catch(r){w.error("Error capturing "+e+" pageview",r)}}bt(){if(!this.gt){var e=()=>{this._t("popstate")};A(g,"popstate",e),this.gt=()=>{g&&g.removeEventListener("popstate",e)}}}}function Qt(s){var e,t;return((e=JSON.stringify(s,(t=[],function(i,r){if(M(r)){for(;t.length>0&&t[t.length-1]!==this;)t.pop();return t.includes(r)?"[Circular]":(t.push(r),r)}return r})))==null?void 0:e.length)||0}function Vi(s,e){if(e===void 0&&(e=66060288e-1),s.size>=e&&s.data.length>1){var t=Math.floor(s.data.length/2),i=s.data.slice(0,t),r=s.data.slice(t);return[Vi({size:Qt(i),data:i,sessionId:s.sessionId,windowId:s.windowId}),Vi({size:Qt(r),data:r,sessionId:s.sessionId,windowId:s.windowId})].flatMap(n=>n)}return[s]}var ke=(s=>(s[s.DomContentLoaded=0]="DomContentLoaded",s[s.Load=1]="Load",s[s.FullSnapshot=2]="FullSnapshot",s[s.IncrementalSnapshot=3]="IncrementalSnapshot",s[s.Meta=4]="Meta",s[s.Custom=5]="Custom",s[s.Plugin=6]="Plugin",s))(ke||{}),ce=(s=>(s[s.Mutation=0]="Mutation",s[s.MouseMove=1]="MouseMove",s[s.MouseInteraction=2]="MouseInteraction",s[s.Scroll=3]="Scroll",s[s.ViewportResize=4]="ViewportResize",s[s.Input=5]="Input",s[s.TouchMove=6]="TouchMove",s[s.MediaInteraction=7]="MediaInteraction",s[s.StyleSheetRule=8]="StyleSheetRule",s[s.CanvasMutation=9]="CanvasMutation",s[s.Font=10]="Font",s[s.Log=11]="Log",s[s.Drag=12]="Drag",s[s.StyleDeclaration=13]="StyleDeclaration",s[s.Selection=14]="Selection",s[s.AdoptedStyleSheet=15]="AdoptedStyleSheet",s[s.CustomElement=16]="CustomElement",s))(ce||{}),Wi="[SessionRecording]",Yi="redacted",Nt={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:s=>s,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},Na=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],qa=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],ja=["/s/","/e/","/i/"];function Zs(s,e,t,i){if(C(s))return s;var r=e?.["content-length"]||function(n){return new Blob([n]).size}(s);return D(r)&&(r=parseInt(r)),r>t?Wi+" "+i+" body too large to record ("+r+" bytes)":s}function Xs(s,e){if(C(s))return s;var t=s;return at(t,!1)||(t=Wi+" "+e+" body "+Yi),R(qa,i=>{var r,n;(r=t)!=null&&r.length&&((n=t)==null?void 0:n.indexOf(i))!==-1&&(t=Wi+" "+e+" body "+Yi+" as might contain: "+i)}),t}var Ba=(s,e)=>{var t,i,r,n={payloadSizeLimitBytes:Nt.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...Nt.performanceEntryTypeToObserve],payloadHostDenyList:[...e.payloadHostDenyList||[],...Nt.payloadHostDenyList]},a=s.session_recording.recordHeaders!==!1&&e.recordHeaders,o=s.session_recording.recordBody!==!1&&e.recordBody,l=s.capture_performance!==!1&&e.recordPerformance,u=(t=n,r=Math.min(1e6,(i=t.payloadSizeLimitBytes)!==null&&i!==void 0?i:1e6),h=>(h!=null&&h.requestBody&&(h.requestBody=Zs(h.requestBody,h.requestHeaders,r,"Request")),h!=null&&h.responseBody&&(h.responseBody=Zs(h.responseBody,h.responseHeaders,r,"Response")),h)),c=h=>{return u(((f,b)=>{var E,S=ot(f.name),x=b.indexOf("http")===0?(E=ot(b))==null?void 0:E.pathname:b;x==="/"&&(x="");var $=S?.pathname.replace(x||"","");if(!(S&&$&&ja.some(P=>$.indexOf(P)===0)))return f})((v=(p=h).requestHeaders,C(v)||R(Object.keys(v??{}),f=>{Na.includes(f.toLowerCase())&&(v[f]=Yi)}),p),s.api_host));var p,v},d=ae(s.session_recording.maskNetworkRequestFn);return d&&ae(s.session_recording.maskCapturedNetworkRequestFn)&&w.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),d&&(s.session_recording.maskCapturedNetworkRequestFn=h=>{var p=s.session_recording.maskNetworkRequestFn({url:h.name});return m({},h,{name:p?.url})}),n.maskRequestFn=ae(s.session_recording.maskCapturedNetworkRequestFn)?h=>{var p,v=c(h);return v&&(p=s.session_recording.maskCapturedNetworkRequestFn==null?void 0:s.session_recording.maskCapturedNetworkRequestFn(v))!==null&&p!==void 0?p:void 0}:h=>function(p){if(!_(p))return p.requestBody=Xs(p.requestBody,"Request"),p.responseBody=Xs(p.responseBody,"Response"),p}(c(h)),m({},Nt,n,{recordHeaders:a,recordBody:o,recordPerformance:l,recordInitialRequests:l})};class Ha{constructor(e,t){var i,r;t===void 0&&(t={}),this.yt={},this.wt=n=>{if(!this.yt[n]){var a,o;this.yt[n]=!0;var l=this.St(n);(a=(o=this.rt).onBlockedNode)==null||a.call(o,n,l)}},this.$t=n=>{var a=this.St(n);if(a?.nodeName!=="svg"&&a instanceof Element){var o=a.closest("svg");if(o)return[this._rrweb.mirror.getId(o),o]}return[n,a]},this.St=n=>this._rrweb.mirror.getNode(n),this.xt=n=>{var a,o,l,u,c,d,h,p;return((a=(o=n.removes)==null?void 0:o.length)!==null&&a!==void 0?a:0)+((l=(u=n.attributes)==null?void 0:u.length)!==null&&l!==void 0?l:0)+((c=(d=n.texts)==null?void 0:d.length)!==null&&c!==void 0?c:0)+((h=(p=n.adds)==null?void 0:p.length)!==null&&h!==void 0?h:0)},this.throttleMutations=n=>{if(n.type!==3||n.data.source!==0)return n;var a=n.data,o=this.xt(a);a.attributes&&(a.attributes=a.attributes.filter(u=>{var[c]=this.$t(u.id);return!this.ct.consumeRateLimit(c)&&u}));var l=this.xt(a);return l!==0||o===l?n:void 0},this._rrweb=e,this.rt=t,this.ct=new Kr({bucketSize:(i=this.rt.bucketSize)!==null&&i!==void 0?i:100,refillRate:(r=this.rt.refillRate)!==null&&r!==void 0?r:10,refillInterval:1e3,et:this.wt})}}var oe=Uint8Array,Q=Uint16Array,lt=Uint32Array,us=new oe([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),cs=new oe([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Qs=new oe([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),tn=function(s,e){for(var t=new Q(31),i=0;i<31;++i)t[i]=e+=1<<s[i-1];var r=new lt(t[30]);for(i=1;i<30;++i)for(var n=t[i];n<t[i+1];++n)r[n]=n-t[i]<<5|i;return[t,r]},sn=tn(us,2),za=sn[0],Ji=sn[1];za[28]=258,Ji[258]=28;for(var er=tn(cs,0)[1],rn=new Q(32768),O=0;O<32768;++O){var Be=(43690&O)>>>1|(21845&O)<<1;Be=(61680&(Be=(52428&Be)>>>2|(13107&Be)<<2))>>>4|(3855&Be)<<4,rn[O]=((65280&Be)>>>8|(255&Be)<<8)>>>1}var kt=function(s,e,t){for(var i=s.length,r=0,n=new Q(e);r<i;++r)++n[s[r]-1];var a,o=new Q(e);for(r=0;r<e;++r)o[r]=o[r-1]+n[r-1]<<1;for(a=new Q(i),r=0;r<i;++r)a[r]=rn[o[s[r]-1]++]>>>15-s[r];return a},Ve=new oe(288);for(O=0;O<144;++O)Ve[O]=8;for(O=144;O<256;++O)Ve[O]=9;for(O=256;O<280;++O)Ve[O]=7;for(O=280;O<288;++O)Ve[O]=8;var ei=new oe(32);for(O=0;O<32;++O)ei[O]=5;var Ua=kt(Ve,9),Ga=kt(ei,5),nn=function(s){return(s/8>>0)+(7&s&&1)},an=function(s,e,t){(t==null||t>s.length)&&(t=s.length);var i=new(s instanceof Q?Q:s instanceof lt?lt:oe)(t-e);return i.set(s.subarray(e,t)),i},Ee=function(s,e,t){t<<=7&e;var i=e/8>>0;s[i]|=t,s[i+1]|=t>>>8},dt=function(s,e,t){t<<=7&e;var i=e/8>>0;s[i]|=t,s[i+1]|=t>>>8,s[i+2]|=t>>>16},wi=function(s,e){for(var t=[],i=0;i<s.length;++i)s[i]&&t.push({s:i,f:s[i]});var r=t.length,n=t.slice();if(!r)return[new oe(0),0];if(r==1){var a=new oe(t[0].s+1);return a[t[0].s]=1,[a,1]}t.sort(function(P,W){return P.f-W.f}),t.push({s:-1,f:25001});var o=t[0],l=t[1],u=0,c=1,d=2;for(t[0]={s:-1,f:o.f+l.f,l:o,r:l};c!=r-1;)o=t[t[u].f<t[d].f?u++:d++],l=t[u!=c&&t[u].f<t[d].f?u++:d++],t[c++]={s:-1,f:o.f+l.f,l:o,r:l};var h=n[0].s;for(i=1;i<r;++i)n[i].s>h&&(h=n[i].s);var p=new Q(h+1),v=Ki(t[c-1],p,0);if(v>e){i=0;var f=0,b=v-e,E=1<<b;for(n.sort(function(P,W){return p[W.s]-p[P.s]||P.f-W.f});i<r;++i){var S=n[i].s;if(!(p[S]>e))break;f+=E-(1<<v-p[S]),p[S]=e}for(f>>>=b;f>0;){var x=n[i].s;p[x]<e?f-=1<<e-p[x]++-1:++i}for(;i>=0&&f;--i){var $=n[i].s;p[$]==e&&(--p[$],++f)}v=e}return[new oe(p),v]},Ki=function(s,e,t){return s.s==-1?Math.max(Ki(s.l,e,t+1),Ki(s.r,e,t+1)):e[s.s]=t},tr=function(s){for(var e=s.length;e&&!s[--e];);for(var t=new Q(++e),i=0,r=s[0],n=1,a=function(l){t[i++]=l},o=1;o<=e;++o)if(s[o]==r&&o!=e)++n;else{if(!r&&n>2){for(;n>138;n-=138)a(32754);n>2&&(a(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(a(r),--n;n>6;n-=6)a(8304);n>2&&(a(n-3<<5|8208),n=0)}for(;n--;)a(r);n=1,r=s[o]}return[t.subarray(0,i),e]},pt=function(s,e){for(var t=0,i=0;i<e.length;++i)t+=s[i]*e[i];return t},Zi=function(s,e,t){var i=t.length,r=nn(e+2);s[r]=255&i,s[r+1]=i>>>8,s[r+2]=255^s[r],s[r+3]=255^s[r+1];for(var n=0;n<i;++n)s[r+n+4]=t[n];return 8*(r+4+i)},ir=function(s,e,t,i,r,n,a,o,l,u,c){Ee(e,c++,t),++r[256];for(var d=wi(r,15),h=d[0],p=d[1],v=wi(n,15),f=v[0],b=v[1],E=tr(h),S=E[0],x=E[1],$=tr(f),P=$[0],W=$[1],B=new Q(19),F=0;F<S.length;++F)B[31&S[F]]++;for(F=0;F<P.length;++F)B[31&P[F]]++;for(var he=wi(B,7),ee=he[0],ue=he[1],re=19;re>4&&!ee[Qs[re-1]];--re);var we,de,pe,Je,$e=u+5<<3,qe=pt(r,Ve)+pt(n,ei)+a,ge=pt(r,h)+pt(n,f)+a+14+3*re+pt(B,ee)+(2*B[16]+3*B[17]+7*B[18]);if($e<=qe&&$e<=ge)return Zi(e,c,s.subarray(l,l+u));if(Ee(e,c,1+(ge<qe)),c+=2,ge<qe){we=kt(h,p),de=h,pe=kt(f,b),Je=f;var Fe=kt(ee,ue);for(Ee(e,c,x-257),Ee(e,c+5,W-1),Ee(e,c+10,re-4),c+=14,F=0;F<re;++F)Ee(e,c+3*F,ee[Qs[F]]);c+=3*re;for(var Ke=[S,P],Y=0;Y<2;++Y){var ne=Ke[Y];for(F=0;F<ne.length;++F){var te=31&ne[F];Ee(e,c,Fe[te]),c+=ee[te],te>15&&(Ee(e,c,ne[F]>>>5&127),c+=ne[F]>>>12)}}}else we=Ua,de=Ve,pe=Ga,Je=ei;for(F=0;F<o;++F)if(i[F]>255){te=i[F]>>>18&31,dt(e,c,we[te+257]),c+=de[te+257],te>7&&(Ee(e,c,i[F]>>>23&31),c+=us[te]);var Ze=31&i[F];dt(e,c,pe[Ze]),c+=Je[Ze],Ze>3&&(dt(e,c,i[F]>>>5&8191),c+=cs[Ze])}else dt(e,c,we[i[F]]),c+=de[i[F]];return dt(e,c,we[256]),c+de[256]},Va=new lt([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Wa=function(){for(var s=new lt(256),e=0;e<256;++e){for(var t=e,i=9;--i;)t=(1&t&&3988292384)^t>>>1;s[e]=t}return s}(),Ya=function(){var s=4294967295;return{p:function(e){for(var t=s,i=0;i<e.length;++i)t=Wa[255&t^e[i]]^t>>>8;s=t},d:function(){return 4294967295^s}}},Ja=function(s,e,t,i,r){return function(n,a,o,l,u,c){var d=n.length,h=new oe(l+d+5*(1+Math.floor(d/7e3))+u),p=h.subarray(l,h.length-u),v=0;if(!a||d<8)for(var f=0;f<=d;f+=65535){var b=f+65535;b<d?v=Zi(p,v,n.subarray(f,b)):(p[f]=c,v=Zi(p,v,n.subarray(f,d)))}else{for(var E=Va[a-1],S=E>>>13,x=8191&E,$=(1<<o)-1,P=new Q(32768),W=new Q($+1),B=Math.ceil(o/3),F=2*B,he=function(di){return(n[di]^n[di+1]<<B^n[di+2]<<F)&$},ee=new lt(25e3),ue=new Q(288),re=new Q(32),we=0,de=0,pe=(f=0,0),Je=0,$e=0;f<d;++f){var qe=he(f),ge=32767&f,Fe=W[qe];if(P[ge]=Fe,W[qe]=ge,Je<=f){var Ke=d-f;if((we>7e3||pe>24576)&&Ke>423){v=ir(n,p,0,ee,ue,re,de,pe,$e,f-$e,v),pe=we=de=0,$e=f;for(var Y=0;Y<286;++Y)ue[Y]=0;for(Y=0;Y<30;++Y)re[Y]=0}var ne=2,te=0,Ze=x,Ce=ge-Fe&32767;if(Ke>2&&qe==he(f-Ce))for(var qn=Math.min(S,Ke)-1,jn=Math.min(32767,f),Bn=Math.min(258,Ke);Ce<=jn&&--Ze&&ge!=Fe;){if(n[f+ne]==n[f+ne-Ce]){for(var Re=0;Re<Bn&&n[f+Re]==n[f+Re-Ce];++Re);if(Re>ne){if(ne=Re,te=Ce,Re>qn)break;var Hn=Math.min(Ce,Re-2),ys=0;for(Y=0;Y<Hn;++Y){var hi=f-Ce+Y+32768&32767,bs=hi-P[hi]+32768&32767;bs>ys&&(ys=bs,Fe=hi)}}}Ce+=(ge=Fe)-(Fe=P[ge])+32768&32767}if(te){ee[pe++]=268435456|Ji[ne]<<18|er[te];var ws=31&Ji[ne],Es=31&er[te];de+=us[ws]+cs[Es],++ue[257+ws],++re[Es],Je=f+ne,++we}else ee[pe++]=n[f],++ue[n[f]]}}v=ir(n,p,c,ee,ue,re,de,pe,$e,f-$e,v)}return an(h,0,l+nn(v)+u)}(s,e.level==null?6:e.level,e.mem==null?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(s.length)))):12+e.mem,t,i,!0)},Xi=function(s,e,t){for(;t;++e)s[e]=t,t>>>=8},Ka=function(s,e){var t=e.filename;if(s[0]=31,s[1]=139,s[2]=8,s[8]=e.level<2?4:e.level==9?2:0,s[9]=3,e.mtime!=0&&Xi(s,4,Math.floor(new Date(e.mtime||Date.now())/1e3)),t){s[3]=8;for(var i=0;i<=t.length;++i)s[i+10]=t.charCodeAt(i)}},Za=function(s){return 10+(s.filename&&s.filename.length+1||0)};function on(s,e){e===void 0&&(e={});var t=Ya(),i=s.length;t.p(s);var r=Ja(s,e,Za(e),8),n=r.length;return Ka(r,e),Xi(r,n-8,t.d()),Xi(r,n-4,i),r}function ln(s,e){var t=s.length;if(typeof TextEncoder<"u")return new TextEncoder().encode(s);for(var i=new oe(s.length+(s.length>>>1)),r=0,n=function(u){i[r++]=u},a=0;a<t;++a){if(r+5>i.length){var o=new oe(r+8+(t-a<<1));o.set(i),i=o}var l=s.charCodeAt(a);l<128||e?n(l):l<2048?(n(192|l>>>6),n(128|63&l)):l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&s.charCodeAt(++a))>>>18),n(128|l>>>12&63),n(128|l>>>6&63),n(128|63&l)):(n(224|l>>>12),n(128|l>>>6&63),n(128|63&l))}return an(i,0,r)}function Xa(s,e){return function(t){for(var i=0,r=0;r<t.length;r++)i=(i<<5)-i+t.charCodeAt(r),i|=0;return Math.abs(i)}(s)%100<se(100*e,0,100)}var Le="disabled",hs="sampled",ti="active",We="buffering",ds="paused",ps="trigger",ye=ps+"_activated",V=ps+"_pending",be=ps+"_"+Le;function sr(s,e){return e.some(t=>t.matching==="regex"&&new RegExp(t.url).test(s))}class un{constructor(e){this.kt=e}triggerStatus(e){var t=this.kt.map(i=>i.triggerStatus(e));return t.includes(ye)?ye:t.includes(V)?V:be}stop(){this.kt.forEach(e=>e.stop())}}class cn{constructor(e){this.kt=e}triggerStatus(e){var t=new Set;for(var i of this.kt)t.add(i.triggerStatus(e));switch(t.delete(be),t.size){case 0:return be;case 1:return Array.from(t)[0];default:return V}}stop(){this.kt.forEach(e=>e.stop())}}class Qa{triggerStatus(){return V}stop(){}}class eo{constructor(e){this.Et=[],this.It=[],this.urlBlocked=!1,this._instance=e}onRemoteConfig(e){var t,i;this.Et=((t=e.sessionRecording)==null?void 0:t.urlTriggers)||[],this.It=((i=e.sessionRecording)==null?void 0:i.urlBlocklist)||[]}Pt(e){var t;return this.Et.length===0?be:((t=this._instance)==null?void 0:t.get_property(Di))===e?ye:V}triggerStatus(e){var t=this.Pt(e),i=t===ye?ye:t===V?V:be;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:i}),i}checkUrlTriggerConditions(e,t,i){if(g!==void 0&&g.location.href){var r=g.location.href,n=this.urlBlocked,a=sr(r,this.It);n&&a||(a&&!n?e():!a&&n&&t(),sr(r,this.Et)&&i("url"))}}stop(){}}class to{constructor(e){this.linkedFlag=null,this.linkedFlagSeen=!1,this.Rt=()=>{},this._instance=e}triggerStatus(){var e=V;return C(this.linkedFlag)&&(e=be),this.linkedFlagSeen&&(e=ye),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:e}),e}onRemoteConfig(e,t){var i;if(this.linkedFlag=((i=e.sessionRecording)==null?void 0:i.linkedFlag)||null,!C(this.linkedFlag)&&!this.linkedFlagSeen){var r=D(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,n=D(this.linkedFlag)?null:this.linkedFlag.variant;this.Rt=this._instance.onFeatureFlags((a,o)=>{var l=!1;if(M(o)&&r in o){var u=o[r];l=ve(u)?u===!0:n?u===n:!!u}this.linkedFlagSeen=l,l&&t(r,n)})}}stop(){this.Rt()}}class io{constructor(e){this.Tt=[],this._instance=e}onRemoteConfig(e){var t;this.Tt=((t=e.sessionRecording)==null?void 0:t.eventTriggers)||[]}Mt(e){var t;return this.Tt.length===0?be:((t=this._instance)==null?void 0:t.get_property(Li))===e?ye:V}triggerStatus(e){var t=this.Mt(e),i=t===ye?ye:t===V?V:be;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:i}),i}stop(){}}function so(s){return s.isRecordingEnabled?We:Le}function ro(s){if(!s.receivedFlags)return We;if(!s.isRecordingEnabled)return Le;if(s.urlTriggerMatching.urlBlocked)return ds;var e=s.isSampled===!0,t=new un([s.eventTriggerMatching,s.urlTriggerMatching,s.linkedFlagMatching]).triggerStatus(s.sessionId);return e?hs:t===ye?ti:t===V?We:s.isSampled===!1?Le:ti}function no(s){if(!s.receivedFlags)return We;if(!s.isRecordingEnabled)return Le;if(s.urlTriggerMatching.urlBlocked)return ds;var e=new cn([s.eventTriggerMatching,s.urlTriggerMatching,s.linkedFlagMatching]).triggerStatus(s.sessionId),t=e!==be,i=ve(s.isSampled);return t&&e===V?We:t&&e===be||i&&!s.isSampled?Le:s.isSampled===!0?hs:ti}var bt="[SessionRecording]",z=j(bt);function qt(){var s;return k==null||(s=k.__PosthogExtensions__)==null||(s=s.rrweb)==null?void 0:s.record}var ao=3e5,oo=[ce.MouseMove,ce.MouseInteraction,ce.Scroll,ce.ViewportResize,ce.Input,ce.TouchMove,ce.MediaInteraction,ce.Drag],rr=s=>({rrwebMethod:s,enqueuedAt:Date.now(),attempt:1});function He(s){return function(e,t){for(var i="",r=0;r<e.length;){var n=e[r++];i+=String.fromCharCode(n)}return i}(on(ln(JSON.stringify(s))))}function nr(s){return s.type===ke.Custom&&s.data.tag==="sessionIdle"}class lo{get sessionId(){return this.Ct}get Ft(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Ot}get At(){if(!this._instance.sessionManager)throw new Error(bt+" must be started with a valid sessionManager.");return this._instance.sessionManager}get Dt(){var e,t;return this.Lt.triggerStatus(this.sessionId)===V?6e4:(e=(t=this._instance.config.session_recording)==null?void 0:t.full_snapshot_interval_millis)!==null&&e!==void 0?e:ao}get jt(){var e=this._instance.get_property(_t);return ve(e)?e:null}get Nt(){var e,t,i=(e=this.C)==null?void 0:e.data[((t=this.C)==null?void 0:t.data.length)-1],{sessionStartTimestamp:r}=this.At.checkAndGetSessionAndWindowId(!0);return i?i.timestamp-r:null}get zt(){var e=!!this._instance.get_property(Mi),t=!this._instance.config.disable_session_recording;return g&&e&&t}get Ut(){var e=!!this._instance.get_property(Os),t=this._instance.config.enable_recording_console_log;return t??e}get qt(){var e,t,i,r,n,a,o=this._instance.config.session_recording.captureCanvas,l=this._instance.get_property(Ds),u=(e=(t=o?.recordCanvas)!==null&&t!==void 0?t:l?.enabled)!==null&&e!==void 0&&e,c=(i=(r=o?.canvasFps)!==null&&r!==void 0?r:l?.fps)!==null&&i!==void 0?i:4,d=(n=(a=o?.canvasQuality)!==null&&a!==void 0?a:l?.quality)!==null&&n!==void 0?n:.4;if(typeof d=="string"){var h=parseFloat(d);d=isNaN(h)?.4:h}return{enabled:u,fps:se(c,0,12,"canvas recording fps",4),quality:se(d,0,1,"canvas recording quality",.4)}}get Bt(){var e,t,i=this._instance.get_property(As),r={recordHeaders:(e=this._instance.config.session_recording)==null?void 0:e.recordHeaders,recordBody:(t=this._instance.config.session_recording)==null?void 0:t.recordBody},n=r?.recordHeaders||i?.recordHeaders,a=r?.recordBody||i?.recordBody,o=M(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,l=!!(ve(o)?o:i?.capturePerformance);return n||a||l?{recordHeaders:n,recordBody:a,recordPerformance:l}:void 0}get Ht(){var e,t,i,r,n,a,o=this._instance.get_property(Ms),l={maskAllInputs:(e=this._instance.config.session_recording)==null?void 0:e.maskAllInputs,maskTextSelector:(t=this._instance.config.session_recording)==null?void 0:t.maskTextSelector,blockSelector:(i=this._instance.config.session_recording)==null?void 0:i.blockSelector},u=(r=l?.maskAllInputs)!==null&&r!==void 0?r:o?.maskAllInputs,c=(n=l?.maskTextSelector)!==null&&n!==void 0?n:o?.maskTextSelector,d=(a=l?.blockSelector)!==null&&a!==void 0?a:o?.blockSelector;return _(u)&&_(c)&&_(d)?void 0:{maskAllInputs:u==null||u,maskTextSelector:c,blockSelector:d}}get Wt(){var e=this._instance.get_property(Ls);return G(e)?e:null}get Gt(){var e=this._instance.get_property(Ns);return G(e)?e:null}get status(){return this.Jt?this.Vt({receivedFlags:this.Jt,isRecordingEnabled:this.zt,isSampled:this.jt,urlTriggerMatching:this.Kt,eventTriggerMatching:this.Yt,linkedFlagMatching:this.Xt,sessionId:this.sessionId}):We}constructor(e){if(this.Vt=so,this.Jt=!1,this.Qt=[],this.Zt="unknown",this.ti=Date.now(),this.Lt=new Qa,this.ii=void 0,this.ei=void 0,this.ri=void 0,this.si=void 0,this.ni=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.oi=()=>{this.ai()},this.li=()=>{this.ui("browser offline",{})},this.hi=()=>{this.ui("browser online",{})},this.di=()=>{if(y!=null&&y.visibilityState){var r="window "+y.visibilityState;this.ui(r,{})}},this._instance=e,this.Ot=!1,this.vi="/s/",this.ci=void 0,this.Jt=!1,!this._instance.sessionManager)throw z.error("started without valid sessionManager"),new Error(bt+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(bt+" cannot be used with __preview_experimental_cookieless_mode.");this.Xt=new to(this._instance),this.Kt=new eo(this._instance),this.Yt=new io(this._instance);var{sessionId:t,windowId:i}=this.At.checkAndGetSessionAndWindowId();this.Ct=t,this.fi=i,this.C=this.pi(),this.Ft>=this.At.sessionTimeoutMs&&z.warn("session_idle_threshold_ms ("+this.Ft+") is greater than the session timeout ("+this.At.sessionTimeoutMs+"). Session will never be detected as idle")}startIfEnabledOrStop(e){this.zt?(this.gi(e),A(g,"beforeunload",this.oi),A(g,"offline",this.li),A(g,"online",this.hi),A(g,"visibilitychange",this.di),this.mi(),this.bi(),C(this.ii)&&(this.ii=this._instance.on("eventCaptured",t=>{try{if(t.event==="$pageview"){var i=t!=null&&t.properties.$current_url?this.yi(t?.properties.$current_url):"";if(!i)return;this.ui("$pageview",{href:i})}}catch(r){z.error("Could not add $pageview to rrweb session",r)}})),this.ei||(this.ei=this.At.onSessionId((t,i,r)=>{var n,a;r&&(this.ui("$session_id_change",{sessionId:t,windowId:i,changeReason:r}),(n=this._instance)==null||(n=n.persistence)==null||n.unregister(Li),(a=this._instance)==null||(a=a.persistence)==null||a.unregister(Di))}))):this.stopRecording()}stopRecording(){var e,t,i,r;this.Ot&&this.ci&&(this.ci(),this.ci=void 0,this.Ot=!1,g?.removeEventListener("beforeunload",this.oi),g?.removeEventListener("offline",this.li),g?.removeEventListener("online",this.hi),g?.removeEventListener("visibilitychange",this.di),this.pi(),clearInterval(this.wi),(e=this.ii)==null||e.call(this),this.ii=void 0,(t=this.ni)==null||t.call(this),this.ni=void 0,(i=this.ei)==null||i.call(this),this.ei=void 0,(r=this.si)==null||r.call(this),this.si=void 0,this.Yt.stop(),this.Kt.stop(),this.Xt.stop(),z.info("stopped"))}Si(){var e;(e=this._instance.persistence)==null||e.unregister(_t)}$i(e){var t,i=this.Ct!==e,r=this.Wt;if(G(r)){var n=this.jt,a=i||!ve(n),o=a?Xa(e,r):n;a&&(o?this.xi(hs):z.warn("Sample rate ("+r+") has determined that this sessionId ("+e+") will not be sent to the server."),this.ui("samplingDecisionMade",{sampleRate:r,isSampled:o})),(t=this._instance.persistence)==null||t.register({[_t]:o})}else this.Si()}onRemoteConfig(e){var t,i,r,n;this.ui("$remote_config_received",e),this.ki(e),(t=e.sessionRecording)!=null&&t.endpoint&&(this.vi=(n=e.sessionRecording)==null?void 0:n.endpoint),this.mi(),((i=e.sessionRecording)==null?void 0:i.triggerMatchType)==="any"?(this.Vt=ro,this.Lt=new un([this.Yt,this.Kt])):(this.Vt=no,this.Lt=new cn([this.Yt,this.Kt])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:(r=e.sessionRecording)==null?void 0:r.triggerMatchType}),this.Kt.onRemoteConfig(e),this.Yt.onRemoteConfig(e),this.Xt.onRemoteConfig(e,(a,o)=>{this.xi("linked_flag_matched",{flag:a,variant:o})}),this.Jt=!0,this.startIfEnabledOrStop()}mi(){G(this.Wt)&&C(this.si)&&(this.si=this.At.onSessionId(e=>{this.$i(e)}))}ki(e){if(this._instance.persistence){var t,i=this._instance.persistence,r=()=>{var n,a,o,l,u,c,d,h,p,v=(n=e.sessionRecording)==null?void 0:n.sampleRate,f=C(v)?null:parseFloat(v);C(f)&&this.Si();var b=(a=e.sessionRecording)==null?void 0:a.minimumDurationMilliseconds;i.register({[Mi]:!!e.sessionRecording,[Os]:(o=e.sessionRecording)==null?void 0:o.consoleLogRecordingEnabled,[As]:m({capturePerformance:e.capturePerformance},(l=e.sessionRecording)==null?void 0:l.networkPayloadCapture),[Ms]:(u=e.sessionRecording)==null?void 0:u.masking,[Ds]:{enabled:(c=e.sessionRecording)==null?void 0:c.recordCanvas,fps:(d=e.sessionRecording)==null?void 0:d.canvasFps,quality:(h=e.sessionRecording)==null?void 0:h.canvasQuality},[Ls]:f,[Ns]:_(b)?null:b,[qs]:(p=e.sessionRecording)==null?void 0:p.scriptConfig})};r(),(t=this.ri)==null||t.call(this),this.ri=this.At.onSessionId(r)}}log(e,t){var i;t===void 0&&(t="log"),(i=this._instance.sessionRecording)==null||i.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:t,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}gi(e){if(!_(Object.assign)&&!_(Array.from)&&!(this.Ot||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var t;this.Ot=!0,this.At.checkAndGetSessionAndWindowId(),qt()?this.Ei():(t=k.__PosthogExtensions__)==null||t.loadExternalDependency==null||t.loadExternalDependency(this._instance,this.Ii,i=>{if(i)return z.error("could not load recorder",i);this.Ei()}),z.info("starting"),this.status===ti&&this.xi(e||"recording_initialized")}}get Ii(){var e;return((e=this._instance)==null||(e=e.persistence)==null||(e=e.get_property(qs))==null?void 0:e.script)||"recorder"}Pi(e){var t;return e.type===3&&oo.indexOf((t=e.data)==null?void 0:t.source)!==-1}Ri(e){var t=this.Pi(e);t||this.Zt||e.timestamp-this.ti>this.Ft&&(this.Zt=!0,clearInterval(this.wi),this.ui("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this.ti,threshold:this.Ft,bufferLength:this.C.data.length,bufferSize:this.C.size}),this.ai());var i=!1;if(t&&(this.ti=e.timestamp,this.Zt)){var r=this.Zt==="unknown";this.Zt=!1,r||(this.ui("sessionNoLongerIdle",{reason:"user activity",type:e.type}),i=!0)}if(!this.Zt){var{windowId:n,sessionId:a}=this.At.checkAndGetSessionAndWindowId(!t,e.timestamp),o=this.Ct!==a,l=this.fi!==n;this.fi=n,this.Ct=a,o||l?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):i&&this.Ti()}}Mi(e){try{return e.rrwebMethod(),!0}catch(t){return this.Qt.length<10?this.Qt.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):z.warn("could not emit queued rrweb event.",t,e),!1}}ui(e,t){return this.Mi(rr(()=>qt().addCustomEvent(e,t)))}Ci(){return this.Mi(rr(()=>qt().takeFullSnapshot()))}Ei(){var e,t,i,r,n={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},a=this._instance.config.session_recording;for(var[o,l]of Object.entries(a||{}))o in n&&(o==="maskInputOptions"?n.maskInputOptions=m({password:!0},l):n[o]=l);this.qt&&this.qt.enabled&&(n.recordCanvas=!0,n.sampling={canvas:this.qt.fps},n.dataURLOptions={type:"image/webp",quality:this.qt.quality}),this.Ht&&(n.maskAllInputs=(t=this.Ht.maskAllInputs)===null||t===void 0||t,n.maskTextSelector=(i=this.Ht.maskTextSelector)!==null&&i!==void 0?i:void 0,n.blockSelector=(r=this.Ht.blockSelector)!==null&&r!==void 0?r:void 0);var u=qt();if(u){this.Fi=(e=this.Fi)!==null&&e!==void 0?e:new Ha(u,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:(d,h)=>{var p="Too many mutations on node '"+d+"'. Rate limiting. This could be due to SVG animations or something similar";z.info(p,{node:h}),this.log(bt+" "+p,"warn")}});var c=this.Oi();this.ci=u(m({emit:d=>{this.onRRwebEmit(d)},plugins:c},n)),this.ti=Date.now(),this.Zt=ve(this.Zt)?this.Zt:"unknown",this.ui("$session_options",{sessionRecordingOptions:n,activePlugins:c.map(d=>d?.name)}),this.ui("$posthog_config",{config:this._instance.config})}else z.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}Ti(){if(this.wi&&clearInterval(this.wi),this.Zt!==!0){var e=this.Dt;e&&(this.wi=setInterval(()=>{this.Ci()},e))}}Oi(){var e,t,i=[],r=(e=k.__PosthogExtensions__)==null||(e=e.rrwebPlugins)==null?void 0:e.getRecordConsolePlugin;r&&this.Ut&&i.push(r());var n=(t=k.__PosthogExtensions__)==null||(t=t.rrwebPlugins)==null?void 0:t.getRecordNetworkPlugin;return this.Bt&&ae(n)&&(!oa.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?i.push(n(Ba(this._instance.config,this.Bt))):z.info("NetworkCapture not started because we are on localhost.")),i}onRRwebEmit(e){var t;if(this.Ai(),e&&M(e)){if(e.type===ke.Meta){var i=this.yi(e.data.href);if(this.Di=i,!i)return;e.data.href=i}else this.Li();if(this.Kt.checkUrlTriggerConditions(()=>this.ji(),()=>this.Ni(),h=>this.zi(h)),!this.Kt.urlBlocked||(r=e).type===ke.Custom&&r.data.tag==="recording paused"){var r;e.type===ke.FullSnapshot&&this.Ti(),e.type===ke.FullSnapshot&&this.Jt&&this.Lt.triggerStatus(this.sessionId)===V&&this.pi();var n=this.Fi?this.Fi.throttleMutations(e):e;if(n){var a=function(h){var p=h;if(p&&M(p)&&p.type===6&&M(p.data)&&p.data.plugin==="rrweb/console@1"){p.data.payload.payload.length>10&&(p.data.payload.payload=p.data.payload.payload.slice(0,10),p.data.payload.payload.push("...[truncated]"));for(var v=[],f=0;f<p.data.payload.payload.length;f++)p.data.payload.payload[f]&&p.data.payload.payload[f].length>2e3?v.push(p.data.payload.payload[f].slice(0,2e3)+"...[truncated]"):v.push(p.data.payload.payload[f]);return p.data.payload.payload=v,h}return h}(n);if(this.Ri(a),this.Zt!==!0||nr(a)){if(nr(a)){var o=a.data.payload;if(o){var l=o.lastActivityTimestamp,u=o.threshold;a.timestamp=l+u}}var c=(t=this._instance.config.session_recording.compress_events)===null||t===void 0||t?function(h){if(Qt(h)<1024)return h;try{if(h.type===ke.FullSnapshot)return m({},h,{data:He(h.data),cv:"2024-10"});if(h.type===ke.IncrementalSnapshot&&h.data.source===ce.Mutation)return m({},h,{cv:"2024-10",data:m({},h.data,{texts:He(h.data.texts),attributes:He(h.data.attributes),removes:He(h.data.removes),adds:He(h.data.adds)})});if(h.type===ke.IncrementalSnapshot&&h.data.source===ce.StyleSheetRule)return m({},h,{cv:"2024-10",data:m({},h.data,{adds:h.data.adds?He(h.data.adds):void 0,removes:h.data.removes?He(h.data.removes):void 0})})}catch(p){z.error("could not compress event - will use uncompressed event",p)}return h}(a):a,d={$snapshot_bytes:Qt(c),$snapshot_data:c,$session_id:this.Ct,$window_id:this.fi};this.status!==Le?this.Ui(d):this.pi()}}}}}Li(){if(!this._instance.config.capture_pageview&&g){var e=this.yi(g.location.href);this.Di!==e&&(this.ui("$url_changed",{href:e}),this.Di=e)}}Ai(){if(this.Qt.length){var e=[...this.Qt];this.Qt=[],e.forEach(t=>{Date.now()-t.enqueuedAt<=2e3&&this.Mi(t)})}}yi(e){var t=this._instance.config.session_recording;if(t.maskNetworkRequestFn){var i,r={url:e};return(i=r=t.maskNetworkRequestFn(r))==null?void 0:i.url}return e}pi(){return this.C={size:0,data:[],sessionId:this.Ct,windowId:this.fi},this.C}ai(){this.qi&&(clearTimeout(this.qi),this.qi=void 0);var e=this.Gt,t=this.Nt,i=G(t)&&t>=0,r=G(e)&&i&&t<e;return this.status===We||this.status===ds||this.status===Le||r?(this.qi=setTimeout(()=>{this.ai()},2e3),this.C):(this.C.data.length>0&&Vi(this.C).forEach(n=>{this.Bi({$snapshot_bytes:n.size,$snapshot_data:n.data,$session_id:n.sessionId,$window_id:n.windowId,$lib:"web",$lib_version:xe.LIB_VERSION})}),this.pi())}Ui(e){var t,i=2+(((t=this.C)==null?void 0:t.data.length)||0);!this.Zt&&(this.C.size+e.$snapshot_bytes+i>943718.4||this.C.sessionId!==this.Ct)&&(this.C=this.ai()),this.C.size+=e.$snapshot_bytes,this.C.data.push(e.$snapshot_data),this.qi||this.Zt||(this.qi=setTimeout(()=>{this.ai()},2e3))}Bi(e){this._instance.capture("$snapshot",e,{_url:this._instance.requestRouter.endpointFor("api",this.vi),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}zi(e){var t;this.Lt.triggerStatus(this.sessionId)===V&&((t=this._instance)==null||(t=t.persistence)==null||t.register({[e==="url"?Di:Li]:this.Ct}),this.ai(),this.xi(e+"_trigger_matched"))}ji(){this.Kt.urlBlocked||(this.Kt.urlBlocked=!0,clearInterval(this.wi),z.info("recording paused due to URL blocker"),this.ui("recording paused",{reason:"url blocker"}))}Ni(){this.Kt.urlBlocked&&(this.Kt.urlBlocked=!1,this.Ci(),this.Ti(),this.ui("recording resumed",{reason:"left blocked url"}),z.info("recording resumed"))}bi(){this.Yt.Tt.length!==0&&C(this.ni)&&(this.ni=this._instance.on("eventCaptured",e=>{try{this.Yt.Tt.includes(e.event)&&this.zi("event")}catch(t){z.error("Could not activate event trigger",t)}}))}overrideLinkedFlag(){this.Xt.linkedFlagSeen=!0,this.Ci(),this.xi("linked_flag_overridden")}overrideSampling(){var e;(e=this._instance.persistence)==null||e.register({[_t]:!0}),this.Ci(),this.xi("sampling_overridden")}overrideTrigger(e){this.zi(e)}xi(e,t){this._instance.register_for_session({$session_recording_start_reason:e}),z.info(e.replace("_"," "),t),I(["recording_initialized","session_id_changed"],e)||this.ui(e,t)}get sdkDebugProperties(){var{sessionStartTimestamp:e}=this.At.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.C.data.length,$sdk_debug_replay_internal_buffer_size:this.C.size,$sdk_debug_current_session_duration:this.Nt,$sdk_debug_session_start:e}}}var Ei=j("[SegmentIntegration]");function uo(s,e){var t=s.config.segment;if(!t)return e();(function(i,r){var n=i.config.segment;if(!n)return r();var a=l=>{var u=()=>l.anonymousId()||Ae();i.config.get_device_id=u,l.id()&&(i.register({distinct_id:l.id(),$device_id:u()}),i.persistence.set_property(Se,"identified")),r()},o=n.user();"then"in o&&ae(o.then)?o.then(l=>a(l)):a(o)})(s,()=>{t.register((i=>{Promise&&Promise.resolve||Ei.warn("This browser does not have Promise support, and can not use the segment integration");var r=(n,a)=>{if(!a)return n;n.event.userId||n.event.anonymousId===i.get_distinct_id()||(Ei.info("No userId set, resetting PostHog"),i.reset()),n.event.userId&&n.event.userId!==i.get_distinct_id()&&(Ei.info("UserId set, identifying with PostHog"),i.identify(n.event.userId));var o=i.calculateEventProperties(a,n.event.properties);return n.event.properties=Object.assign({},o,n.event.properties),n};return{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:n=>r(n,n.event.event),page:n=>r(n,"$pageview"),identify:n=>r(n,"$identify"),screen:n=>r(n,"$screen")}})(s)).then(()=>{e()})})}var hn="posthog-js";function dn(s,e){var{organization:t,projectId:i,prefix:r,severityAllowList:n=["error"]}=e===void 0?{}:e;return a=>{var o,l,u,c,d;if(!(n==="*"||n.includes(a.level))||!s.__loaded)return a;a.tags||(a.tags={});var h=s.requestRouter.endpointFor("ui","/project/"+s.config.token+"/person/"+s.get_distinct_id());a.tags["PostHog Person URL"]=h,s.sessionRecordingStarted()&&(a.tags["PostHog Recording URL"]=s.get_session_replay_url({withTimestamp:!0}));var p=((o=a.exception)==null?void 0:o.values)||[],v=p.map(b=>m({},b,{stacktrace:b.stacktrace?m({},b.stacktrace,{type:"raw",frames:(b.stacktrace.frames||[]).map(E=>m({},E,{platform:"web:javascript"}))}):void 0})),f={$exception_message:((l=p[0])==null?void 0:l.value)||a.message,$exception_type:(u=p[0])==null?void 0:u.type,$exception_personURL:h,$exception_level:a.level,$exception_list:v,$sentry_event_id:a.event_id,$sentry_exception:a.exception,$sentry_exception_message:((c=p[0])==null?void 0:c.value)||a.message,$sentry_exception_type:(d=p[0])==null?void 0:d.type,$sentry_tags:a.tags};return t&&i&&(f.$sentry_url=(r||"https://sentry.io/organizations/")+t+"/issues/?project="+i+"&query="+a.event_id),s.exceptions.sendExceptionEvent(f),a}}class co{constructor(e,t,i,r,n){this.name=hn,this.setupOnce=function(a){a(dn(e,{organization:t,projectId:i,prefix:r,severityAllowList:n}))}}}var ho=g!=null&&g.location?Zt(g.location.hash,"__posthog")||Zt(location.hash,"state"):null,ar="_postHogToolbarParams",or=j("[Toolbar]"),Oe=function(s){return s[s.UNINITIALIZED=0]="UNINITIALIZED",s[s.LOADING=1]="LOADING",s[s.LOADED=2]="LOADED",s}(Oe||{});class po{constructor(e){this.instance=e}Hi(e){k.ph_toolbar_state=e}Wi(){var e;return(e=k.ph_toolbar_state)!==null&&e!==void 0?e:Oe.UNINITIALIZED}maybeLoadToolbar(e,t,i){if(e===void 0&&(e=void 0),t===void 0&&(t=void 0),i===void 0&&(i=void 0),!g||!y)return!1;e=e??g.location,i=i??g.history;try{if(!t){try{g.localStorage.setItem("test","test"),g.localStorage.removeItem("test")}catch{return!1}t=g?.localStorage}var r,n=ho||Zt(e.hash,"__posthog")||Zt(e.hash,"state"),a=n?$s(()=>JSON.parse(atob(decodeURIComponent(n))))||$s(()=>JSON.parse(decodeURIComponent(n))):null;return a&&a.action==="ph_authorize"?((r=a).source="url",r&&Object.keys(r).length>0&&(a.desiredHash?e.hash=a.desiredHash:i?i.replaceState(i.state,"",e.pathname+e.search):e.hash="")):((r=JSON.parse(t.getItem(ar)||"{}")).source="localstorage",delete r.userIntent),!(!r.token||this.instance.config.token!==r.token)&&(this.loadToolbar(r),!0)}catch{return!1}}Gi(e){var t=k.ph_load_toolbar||k.ph_load_editor;!C(t)&&ae(t)?t(e,this.instance):or.warn("No toolbar load function found")}loadToolbar(e){var t=!(y==null||!y.getElementById(Dr));if(!g||t)return!1;var i=this.instance.requestRouter.region==="custom"&&this.instance.config.advanced_disable_toolbar_metrics,r=m({token:this.instance.config.token},e,{apiURL:this.instance.requestRouter.endpointFor("ui")},i?{instrument:!1}:{});if(g.localStorage.setItem(ar,JSON.stringify(m({},r,{source:void 0}))),this.Wi()===Oe.LOADED)this.Gi(r);else if(this.Wi()===Oe.UNINITIALIZED){var n;this.Hi(Oe.LOADING),(n=k.__PosthogExtensions__)==null||n.loadExternalDependency==null||n.loadExternalDependency(this.instance,"toolbar",a=>{if(a)return or.error("[Toolbar] Failed to load",a),void this.Hi(Oe.UNINITIALIZED);this.Hi(Oe.LOADED),this.Gi(r)}),A(g,"turbolinks:load",()=>{this.Hi(Oe.UNINITIALIZED),this.loadToolbar(r)})}return!0}Ji(e){return this.loadToolbar(e)}maybeLoadEditor(e,t,i){return e===void 0&&(e=void 0),t===void 0&&(t=void 0),i===void 0&&(i=void 0),this.maybeLoadToolbar(e,t,i)}}var go=j("[TracingHeaders]");class fo{constructor(e){this.Vi=void 0,this.Ki=void 0,this.nt=()=>{var t,i;_(this.Vi)&&((t=k.__PosthogExtensions__)==null||(t=t.tracingHeadersPatchFns)==null||t._patchXHR(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager)),_(this.Ki)&&((i=k.__PosthogExtensions__)==null||(i=i.tracingHeadersPatchFns)==null||i._patchFetch(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager))},this._instance=e}J(e){var t,i;(t=k.__PosthogExtensions__)!=null&&t.tracingHeadersPatchFns&&e(),(i=k.__PosthogExtensions__)==null||i.loadExternalDependency==null||i.loadExternalDependency(this._instance,"tracing-headers",r=>{if(r)return go.error("failed to load script",r);e()})}startIfEnabledOrStop(){var e,t;this._instance.config.__add_tracing_headers?this.J(this.nt):((e=this.Vi)==null||e.call(this),(t=this.Ki)==null||t.call(this),this.Vi=void 0,this.Ki=void 0)}}var Pe=j("[Web Vitals]"),lr=9e5;class vo{constructor(e){var t;this.Yi=!1,this.i=!1,this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Xi=()=>{clearTimeout(this.Qi),this.C.metrics.length!==0&&(this._instance.capture("$web_vitals",this.C.metrics.reduce((i,r)=>m({},i,{["$web_vitals_"+r.name+"_event"]:m({},r),["$web_vitals_"+r.name+"_value"]:r.value}),{})),this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Zi=i=>{var r,n=(r=this._instance.sessionManager)==null?void 0:r.checkAndGetSessionAndWindowId(!0);if(_(n))Pe.error("Could not read session ID. Dropping metrics!");else{this.C=this.C||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var a=this.te();_(a)||(C(i?.name)||C(i?.value)?Pe.error("Invalid metric received",i):this.ie&&i.value>=this.ie?Pe.error("Ignoring metric with value >= "+this.ie,i):(this.C.url!==a&&(this.Xi(),this.Qi=setTimeout(this.Xi,this.flushToCaptureTimeoutMs)),_(this.C.url)&&(this.C.url=a),this.C.firstMetricTimestamp=_(this.C.firstMetricTimestamp)?Date.now():this.C.firstMetricTimestamp,i.attribution&&i.attribution.interactionTargetElement&&(i.attribution.interactionTargetElement=void 0),this.C.metrics.push(m({},i,{$current_url:a,$session_id:n.sessionId,$window_id:n.windowId,timestamp:Date.now()})),this.C.metrics.length===this.allowedMetrics.length&&this.Xi()))}},this.nt=()=>{var i,r,n,a,o=k.__PosthogExtensions__;_(o)||_(o.postHogWebVitalsCallbacks)||({onLCP:i,onCLS:r,onFCP:n,onINP:a}=o.postHogWebVitalsCallbacks),i&&r&&n&&a?(this.allowedMetrics.indexOf("LCP")>-1&&i(this.Zi.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&r(this.Zi.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&n(this.Zi.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&a(this.Zi.bind(this)),this.i=!0):Pe.error("web vitals callbacks not loaded - not starting")},this._instance=e,this.Yi=!((t=this._instance.persistence)==null||!t.props[Ps]),this.startIfEnabled()}get allowedMetrics(){var e,t,i=M(this._instance.config.capture_performance)?(e=this._instance.config.capture_performance)==null?void 0:e.web_vitals_allowed_metrics:void 0;return _(i)?((t=this._instance.persistence)==null?void 0:t.props[Ts])||["CLS","FCP","INP","LCP"]:i}get flushToCaptureTimeoutMs(){return(M(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get ie(){var e=M(this._instance.config.capture_performance)&&G(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:lr;return 0<e&&e<=6e4?lr:e}get isEnabled(){var e=Z?.protocol;if(e!=="http:"&&e!=="https:")return Pe.info("Web Vitals are disabled on non-http/https protocols"),!1;var t=M(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:ve(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return ve(t)?t:this.Yi}startIfEnabled(){this.isEnabled&&!this.i&&(Pe.info("enabled, starting..."),this.J(this.nt))}onRemoteConfig(e){var t=M(e.capturePerformance)&&!!e.capturePerformance.web_vitals,i=M(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[Ps]:t}),this._instance.persistence.register({[Ts]:i})),this.Yi=t,this.startIfEnabled()}J(e){var t,i;(t=k.__PosthogExtensions__)!=null&&t.postHogWebVitalsCallbacks&&e(),(i=k.__PosthogExtensions__)==null||i.loadExternalDependency==null||i.loadExternalDependency(this._instance,"web-vitals",r=>{r?Pe.error("failed to load script",r):e()})}te(){var e=g?g.location.href:void 0;return e||Pe.error("Could not determine current URL"),e}}var _o=j("[Heatmaps]");function ur(s){return M(s)&&"clientX"in s&&"clientY"in s&&G(s.clientX)&&G(s.clientY)}class mo{constructor(e){var t;this.rageclicks=new Wr,this.Yi=!1,this.i=!1,this.ee=null,this.instance=e,this.Yi=!((t=this.instance.persistence)==null||!t.props[Oi])}get flushIntervalMilliseconds(){var e=5e3;return M(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return _(this.instance.config.capture_heatmaps)?_(this.instance.config.enable_heatmaps)?this.Yi:this.instance.config.enable_heatmaps:this.instance.config.capture_heatmaps!==!1}startIfEnabled(){if(this.isEnabled){if(this.i)return;_o.info("starting..."),this.re(),this.ee=setInterval(this.se.bind(this),this.flushIntervalMilliseconds)}else{var e,t;clearInterval((e=this.ee)!==null&&e!==void 0?e:void 0),(t=this.ne)==null||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[Oi]:t}),this.Yi=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.C;return this.C=void 0,e}oe(e){this.ae(e.originalEvent,"deadclick")}re(){g&&y&&(A(g,"beforeunload",this.se.bind(this)),A(y,"click",e=>this.ae(e||g?.event),{capture:!0}),A(y,"mousemove",e=>this.le(e||g?.event),{capture:!0}),this.ne=new Jr(this.instance,wa,this.oe.bind(this)),this.ne.startIfEnabled(),this.i=!0)}ue(e,t){var i=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),a=function(o,l,u){for(var c=o;c&&oi(c)&&!De(c,"body");){if(c===u)return!1;if(I(l,g?.getComputedStyle(c).position))return!0;c=Br(c)}return!1}(jr(e),["fixed","sticky"],n);return{x:e.clientX+(a?0:r),y:e.clientY+(a?0:i),target_fixed:a,type:t}}ae(e,t){var i;if(t===void 0&&(t="click"),!js(e.target)&&ur(e)){var r=this.ue(e,t);(i=this.rageclicks)!=null&&i.isRageClick(e.clientX,e.clientY,new Date().getTime())&&this.he(m({},r,{type:"rageclick"})),this.he(r)}}le(e){!js(e.target)&&ur(e)&&(clearTimeout(this.de),this.de=setTimeout(()=>{this.he(this.ue(e,"mousemove"))},500))}he(e){if(g){var t=g.location.href;this.C=this.C||{},this.C[t]||(this.C[t]=[]),this.C[t].push(e)}}se(){this.C&&!tt(this.C)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class yo{constructor(e){this._instance=e}doPageView(e,t){var i,r=this.ve(e,t);return this.ce={pathname:(i=g?.location.pathname)!==null&&i!==void 0?i:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),r}doPageLeave(e){var t;return this.ve(e,(t=this.ce)==null?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:(e=this.ce)==null?void 0:e.pageViewId}}ve(e,t){var i=this.ce;if(!i)return{$pageview_id:t};var r={$pageview_id:t,$prev_pageview_id:i.pageViewId},n=this._instance.scrollManager.getContext();if(n&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:a,lastScrollY:o,maxScrollY:l,maxContentHeight:u,lastContentY:c,maxContentY:d}=n;if(!(_(a)||_(o)||_(l)||_(u)||_(c)||_(d))){a=Math.ceil(a),o=Math.ceil(o),l=Math.ceil(l),u=Math.ceil(u),c=Math.ceil(c),d=Math.ceil(d);var h=a<=1?1:se(o/a,0,1),p=a<=1?1:se(l/a,0,1),v=u<=1?1:se(c/u,0,1),f=u<=1?1:se(d/u,0,1);r=L(r,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:h,$prev_pageview_max_scroll:l,$prev_pageview_max_scroll_percentage:p,$prev_pageview_last_content:c,$prev_pageview_last_content_percentage:v,$prev_pageview_max_content:d,$prev_pageview_max_content_percentage:f})}}return i.pathname&&(r.$prev_pageview_pathname=i.pathname),i.timestamp&&(r.$prev_pageview_duration=(e.getTime()-i.timestamp.getTime())/1e3),r}}var bo=function(s){var e,t,i,r,n="";for(e=t=0,i=(s=(s+"").replace(/\r\n/g,`
`).replace(/\r/g,`
`)).length,r=0;r<i;r++){var a=s.charCodeAt(r),o=null;a<128?t++:o=a>127&&a<2048?String.fromCharCode(a>>6|192,63&a|128):String.fromCharCode(a>>12|224,a>>6&63|128,63&a|128),Ne(o)||(t>e&&(n+=s.substring(e,t)),n+=o,e=t=r+1)}return t>e&&(n+=s.substring(e,s.length)),n},wo=!!Ri||!!Ci,cr="text/plain",ii=(s,e)=>{var[t,i]=s.split("?"),r=m({},e);i?.split("&").forEach(a=>{var[o]=a.split("=");delete r[o]});var n=la(r);return t+"?"+(n=n?(i?i+"&":"")+n:i)},wt=(s,e)=>JSON.stringify(s,(t,i)=>typeof i=="bigint"?i.toString():i,e),Si=s=>{var{data:e,compression:t}=s;if(e){if(t===Ie.GZipJS){var i=on(ln(wt(e)),{mtime:0}),r=new Blob([i],{type:cr});return{contentType:cr,body:r,estimatedSize:r.size}}if(t===Ie.Base64){var n=function(l){var u,c,d,h,p,v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=0,b=0,E="",S=[];if(!l)return l;l=bo(l);do u=(p=l.charCodeAt(f++)<<16|l.charCodeAt(f++)<<8|l.charCodeAt(f++))>>18&63,c=p>>12&63,d=p>>6&63,h=63&p,S[b++]=v.charAt(u)+v.charAt(c)+v.charAt(d)+v.charAt(h);while(f<l.length);switch(E=S.join(""),l.length%3){case 1:E=E.slice(0,-2)+"==";break;case 2:E=E.slice(0,-1)+"="}return E}(wt(e)),a=(l=>"data="+encodeURIComponent(typeof l=="string"?l:wt(l)))(n);return{contentType:"application/x-www-form-urlencoded",body:a,estimatedSize:new Blob([a]).size}}var o=wt(e);return{contentType:"application/json",body:o,estimatedSize:new Blob([o]).size}}},xt=[];Ci&&xt.push({transport:"fetch",method:s=>{var e,t,{contentType:i,body:r,estimatedSize:n}=(e=Si(s))!==null&&e!==void 0?e:{},a=new Headers;R(s.headers,function(c,d){a.append(d,c)}),i&&a.append("Content-Type",i);var o=s.url,l=null;if(xs){var u=new xs;l={signal:u.signal,timeout:setTimeout(()=>u.abort(),s.timeout)}}Ci(o,m({method:s?.method||"GET",headers:a,keepalive:s.method==="POST"&&(n||0)<52428.8,body:r,signal:(t=l)==null?void 0:t.signal},s.fetchOptions)).then(c=>c.text().then(d=>{var h={statusCode:c.status,text:d};if(c.status===200)try{h.json=JSON.parse(d)}catch(p){w.error(p)}s.callback==null||s.callback(h)})).catch(c=>{w.error(c),s.callback==null||s.callback({statusCode:0,text:c})}).finally(()=>l?clearTimeout(l.timeout):null)}}),Ri&&xt.push({transport:"XHR",method:s=>{var e,t=new Ri;t.open(s.method||"GET",s.url,!0);var{contentType:i,body:r}=(e=Si(s))!==null&&e!==void 0?e:{};R(s.headers,function(n,a){t.setRequestHeader(a,n)}),i&&t.setRequestHeader("Content-Type",i),s.timeout&&(t.timeout=s.timeout),t.withCredentials=!0,t.onreadystatechange=()=>{if(t.readyState===4){var n={statusCode:t.status,text:t.responseText};if(t.status===200)try{n.json=JSON.parse(t.responseText)}catch{}s.callback==null||s.callback(n)}},t.send(r)}}),ie!=null&&ie.sendBeacon&&xt.push({transport:"sendBeacon",method:s=>{var e=ii(s.url,{beacon:"1"});try{var t,{contentType:i,body:r}=(t=Si(s))!==null&&t!==void 0?t:{},n=typeof r=="string"?new Blob([r],{type:i}):r;ie.sendBeacon(e,n)}catch{}}});var ut=function(s,e){if(!function(t){try{new RegExp(t)}catch{return!1}return!0}(e))return!1;try{return new RegExp(e).test(s)}catch{return!1}};function hr(s,e,t){return wt({distinct_id:s,userPropertiesToSet:e,userPropertiesToSetOnce:t})}var Eo={exact:(s,e)=>e.some(t=>s.some(i=>t===i)),is_not:(s,e)=>e.every(t=>s.every(i=>t!==i)),regex:(s,e)=>e.some(t=>s.some(i=>ut(t,i))),not_regex:(s,e)=>e.every(t=>s.every(i=>!ut(t,i))),icontains:(s,e)=>e.map(jt).some(t=>s.map(jt).some(i=>t.includes(i))),not_icontains:(s,e)=>e.map(jt).every(t=>s.map(jt).every(i=>!t.includes(i)))},jt=s=>s.toLowerCase(),dr=j("[Error tracking]");class So{constructor(e){var t,i;this.fe=[],this._instance=e,this.fe=(t=(i=this._instance.persistence)==null?void 0:i.get_property(Ai))!==null&&t!==void 0?t:[]}onRemoteConfig(e){var t,i,r,n=(t=(i=e.errorTracking)==null?void 0:i.suppressionRules)!==null&&t!==void 0?t:[],a=(r=e.errorTracking)==null?void 0:r.captureExtensionExceptions;this.fe=n,this._instance.persistence&&this._instance.persistence.register({[Ai]:this.fe,[Rs]:a})}get pe(){var e,t=!!this._instance.get_property(Rs),i=this._instance.config.error_tracking.captureExtensionExceptions;return(e=i??t)!==null&&e!==void 0&&e}sendExceptionEvent(e){if(this.ge(e))dr.info("Skipping exception capture because a suppression rule matched");else{if(this.pe||!this._e(e))return this._instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"});dr.info("Skipping exception capture because it was thrown by an extension")}}ge(e){var t=e.$exception_list;if(!t||!T(t)||t.length===0)return!1;var i=t.reduce((r,n)=>{var{type:a,value:o}=n;return D(a)&&a.length>0&&r.$exception_types.push(a),D(o)&&o.length>0&&r.$exception_values.push(o),r},{$exception_types:[],$exception_values:[]});return this.fe.some(r=>{var n=r.values.map(a=>{var o,l=Eo[a.operator],u=T(a.value)?a.value:[a.value],c=(o=i[a.key])!==null&&o!==void 0?o:[];return u.length>0&&l(u,c)});return r.type==="OR"?n.some(Boolean):n.every(Boolean)})}_e(e){var t=e.$exception_list;return!(!t||!T(t))&&t.flatMap(i=>{var r,n;return(r=(n=i.stacktrace)==null?void 0:n.frames)!==null&&r!==void 0?r:[]}).some(i=>i.filename&&i.filename.startsWith("chrome-extension://"))}}var le="Mobile",si="iOS",_e="Android",$t="Tablet",pn=_e+" "+$t,gn="iPad",fn="Apple",vn=fn+" Watch",Ft="Safari",ct="BlackBerry",_n="Samsung",mn=_n+"Browser",yn=_n+" Internet",Ye="Chrome",ko=Ye+" OS",bn=Ye+" "+si,gs="Internet Explorer",wn=gs+" "+le,fs="Opera",xo=fs+" Mini",vs="Edge",En="Microsoft "+vs,rt="Firefox",Sn=rt+" "+si,Ct="Nintendo",Rt="PlayStation",nt="Xbox",kn=_e+" "+le,xn=le+" "+Ft,Et="Windows",Qi=Et+" Phone",pr="Nokia",es="Ouya",In="Generic",Io=In+" "+le.toLowerCase(),$n=In+" "+$t.toLowerCase(),ts="Konqueror",J="(\\d+(\\.\\d+)?)",ki=new RegExp("Version/"+J),$o=new RegExp(nt,"i"),Fo=new RegExp(Rt+" \\w+","i"),Co=new RegExp(Ct+" \\w+","i"),_s=new RegExp(ct+"|PlayBook|BB10","i"),Ro={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},Po=(s,e)=>e&&I(e,fn)||function(t){return I(t,Ft)&&!I(t,Ye)&&!I(t,_e)}(s),Fn=function(s,e){return e=e||"",I(s," OPR/")&&I(s,"Mini")?xo:I(s," OPR/")?fs:_s.test(s)?ct:I(s,"IE"+le)||I(s,"WPDesktop")?wn:I(s,mn)?yn:I(s,vs)||I(s,"Edg/")?En:I(s,"FBIOS")?"Facebook "+le:I(s,"UCWEB")||I(s,"UCBrowser")?"UC Browser":I(s,"CriOS")?bn:I(s,"CrMo")||I(s,Ye)?Ye:I(s,_e)&&I(s,Ft)?kn:I(s,"FxiOS")?Sn:I(s.toLowerCase(),ts.toLowerCase())?ts:Po(s,e)?I(s,le)?xn:Ft:I(s,rt)?rt:I(s,"MSIE")||I(s,"Trident/")?gs:I(s,"Gecko")?rt:""},To={[wn]:[new RegExp("rv:"+J)],[En]:[new RegExp(vs+"?\\/"+J)],[Ye]:[new RegExp("("+Ye+"|CrMo)\\/"+J)],[bn]:[new RegExp("CriOS\\/"+J)],"UC Browser":[new RegExp("(UCBrowser|UCWEB)\\/"+J)],[Ft]:[ki],[xn]:[ki],[fs]:[new RegExp("(Opera|OPR)\\/"+J)],[rt]:[new RegExp(rt+"\\/"+J)],[Sn]:[new RegExp("FxiOS\\/"+J)],[ts]:[new RegExp("Konqueror[:/]?"+J,"i")],[ct]:[new RegExp(ct+" "+J),ki],[kn]:[new RegExp("android\\s"+J,"i")],[yn]:[new RegExp(mn+"\\/"+J)],[gs]:[new RegExp("(rv:|MSIE )"+J)],Mozilla:[new RegExp("rv:"+J)]},Oo=function(s,e){var t=Fn(s,e),i=To[t];if(_(i))return null;for(var r=0;r<i.length;r++){var n=i[r],a=s.match(n);if(a)return parseFloat(a[a.length-2])}return null},gr=[[new RegExp(nt+"; "+nt+" (.*?)[);]","i"),s=>[nt,s&&s[1]||""]],[new RegExp(Ct,"i"),[Ct,""]],[new RegExp(Rt,"i"),[Rt,""]],[_s,[ct,""]],[new RegExp(Et,"i"),(s,e)=>{if(/Phone/.test(e)||/WPDesktop/.test(e))return[Qi,""];if(new RegExp(le).test(e)&&!/IEMobile\b/.test(e))return[Et+" "+le,""];var t=/Windows NT ([0-9.]+)/i.exec(e);if(t&&t[1]){var i=t[1],r=Ro[i]||"";return/arm/i.test(e)&&(r="RT"),[Et,r]}return[Et,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,s=>{if(s&&s[3]){var e=[s[3],s[4],s[5]||"0"];return[si,e.join(".")]}return[si,""]}],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,s=>{var e="";return s&&s.length>=3&&(e=_(s[2])?s[3]:s[2]),["watchOS",e]}],[new RegExp("("+_e+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+_e+")","i"),s=>{if(s&&s[2]){var e=[s[2],s[3],s[4]||"0"];return[_e,e.join(".")]}return[_e,""]}],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,s=>{var e=["Mac OS X",""];if(s&&s[1]){var t=[s[1],s[2],s[3]||"0"];e[1]=t.join(".")}return e}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[ko,""]],[/Linux|debian/i,["Linux",""]]],fr=function(s){return Co.test(s)?Ct:Fo.test(s)?Rt:$o.test(s)?nt:new RegExp(es,"i").test(s)?es:new RegExp("("+Qi+"|WPDesktop)","i").test(s)?Qi:/iPad/.test(s)?gn:/iPod/.test(s)?"iPod Touch":/iPhone/.test(s)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(s)?vn:_s.test(s)?ct:/(kobo)\s(ereader|touch)/i.test(s)?"Kobo":new RegExp(pr,"i").test(s)?pr:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(s)||/(kf[a-z]+)( bui|\)).+silk\//i.test(s)?"Kindle Fire":/(Android|ZTE)/i.test(s)?!new RegExp(le).test(s)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(s)?/pixel[\daxl ]{1,6}/i.test(s)&&!/pixel c/i.test(s)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(s)||/lmy47v/i.test(s)&&!/QTAQZ3/i.test(s)?_e:pn:_e:new RegExp("(pda|"+le+")","i").test(s)?Io:new RegExp($t,"i").test(s)&&!new RegExp($t+" pc","i").test(s)?$n:""},Bt="https?://(.*)",ui=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],Ao=ai(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],ui),ms="<masked>",Mo=["li_fat_id"];function Cn(s,e,t){if(!y)return{};var i,r=e?ai([],ui,t||[]):[],n=Rn(os(y.URL,r,ms),s),a=(i={},R(Mo,function(o){var l=me.D(o);i[o]=l||null}),i);return L(a,n)}function Rn(s,e){var t=Ao.concat(e||[]),i={};return R(t,function(r){var n=Kt(s,r);i[r]=n||null}),i}function Pn(s){var e=function(n){return n?n.search(Bt+"google.([^/?]*)")===0?"google":n.search(Bt+"bing.com")===0?"bing":n.search(Bt+"yahoo.com")===0?"yahoo":n.search(Bt+"duckduckgo.com")===0?"duckduckgo":null:null}(s),t=e!="yahoo"?"q":"p",i={};if(!Ne(e)){i.$search_engine=e;var r=y?Kt(y.referrer,t):"";r.length&&(i.ph_keyword=r)}return i}function vr(){return navigator.language||navigator.userLanguage}function Tn(){return y?.referrer||"$direct"}function On(s,e){var t=s?ai([],ui,e||[]):[],i=Z?.href.substring(0,1e3);return{r:Tn().substring(0,1e3),u:i?os(i,t,ms):void 0}}function An(s){var e,{r:t,u:i}=s,r={$referrer:t,$referring_domain:t==null?void 0:t=="$direct"?"$direct":(e=ot(t))==null?void 0:e.host};if(i){r.$current_url=i;var n=ot(i);r.$host=n?.host,r.$pathname=n?.pathname;var a=Rn(i);L(r,a)}if(t){var o=Pn(t);L(r,o)}return r}function Mn(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch{return}}function Do(){try{return new Date().getTimezoneOffset()}catch{return}}function Lo(s,e){if(!K)return{};var t,i,r,n=s?ai([],ui,e||[]):[],[a,o]=function(l){for(var u=0;u<gr.length;u++){var[c,d]=gr[u],h=c.exec(l),p=h&&(ae(d)?d(h,l):d);if(p)return p}return["",""]}(K);return L(ns({$os:a,$os_version:o,$browser:Fn(K,navigator.vendor),$device:fr(K),$device_type:(i=K,r=fr(i),r===gn||r===pn||r==="Kobo"||r==="Kindle Fire"||r===$n?$t:r===Ct||r===nt||r===Rt||r===es?"Console":r===vn?"Wearable":r?le:"Desktop"),$timezone:Mn(),$timezone_offset:Do()}),{$current_url:os(Z?.href,n,ms),$host:Z?.host,$pathname:Z?.pathname,$raw_user_agent:K.length>1e3?K.substring(0,997)+"...":K,$browser_version:Oo(K,navigator.vendor),$browser_language:vr(),$browser_language_prefix:(t=vr(),typeof t=="string"?t.split("-")[0]:void 0),$screen_height:g?.screen.height,$screen_width:g?.screen.width,$viewport_height:g?.innerHeight,$viewport_width:g?.innerWidth,$lib:"web",$lib_version:xe.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var fe=j("[FeatureFlags]"),xi="$active_feature_flags",Qe="$override_feature_flags",_r="$feature_flag_payloads",gt="$override_feature_flag_payloads",mr="$feature_flag_request_id",yr=s=>{var e={};for(var[t,i]of Ht(s||{}))i&&(e[t]=i);return e},No=s=>{var e=s.flags;return e?(s.featureFlags=Object.fromEntries(Object.keys(e).map(t=>{var i;return[t,(i=e[t].variant)!==null&&i!==void 0?i:e[t].enabled]})),s.featureFlagPayloads=Object.fromEntries(Object.keys(e).filter(t=>e[t].enabled).filter(t=>{var i;return(i=e[t].metadata)==null?void 0:i.payload}).map(t=>{var i;return[t,(i=e[t].metadata)==null?void 0:i.payload]}))):fe.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),s},qo=function(s){return s.FeatureFlags="feature_flags",s.Recordings="recordings",s}({});class jo{constructor(e){this.me=!1,this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this._instance=e,this.featureFlagEventHandlers=[]}flags(){if(this._instance.config.__preview_remote_config)this.$e=!0;else{var e=!this.ke&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.Ee({disableFlags:e})}}get hasLoadedFlags(){return this.be}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var e=this._instance.get_property(Ni),t=this._instance.get_property(Qe),i=this._instance.get_property(gt);if(!i&&!t)return e||{};var r=L({},e||{}),n=[...new Set([...Object.keys(i||{}),...Object.keys(t||{})])];for(var a of n){var o,l,u=r[a],c=t?.[a],d=_(c)?(o=u?.enabled)!==null&&o!==void 0&&o:!!c,h=_(c)?u.variant:typeof c=="string"?c:void 0,p=i?.[a],v=m({},u,{enabled:d,variant:d?h??u?.variant:void 0});d!==u?.enabled&&(v.original_enabled=u?.enabled),h!==u?.variant&&(v.original_variant=u?.variant),p&&(v.metadata=m({},u?.metadata,{payload:p,original_payload:u==null||(l=u.metadata)==null?void 0:l.payload})),r[a]=v}return this.me||(fe.warn(" Overriding feature flag details!",{flagDetails:e,overriddenPayloads:i,finalDetails:r}),this.me=!0),r}getFlagVariants(){var e=this._instance.get_property(it),t=this._instance.get_property(Qe);if(!t)return e||{};for(var i=L({},e),r=Object.keys(t),n=0;n<r.length;n++)i[r[n]]=t[r[n]];return this.me||(fe.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:i}),this.me=!0),i}getFlagPayloads(){var e=this._instance.get_property(_r),t=this._instance.get_property(gt);if(!t)return e||{};for(var i=L({},e||{}),r=Object.keys(t),n=0;n<r.length;n++)i[r[n]]=t[r[n]];return this.me||(fe.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:i}),this.me=!0),i}reloadFeatureFlags(){this.we||this._instance.config.advanced_disable_feature_flags||this.ke||(this.ke=setTimeout(()=>{this.Ee()},5))}Ie(){clearTimeout(this.ke),this.ke=void 0}ensureFlagsLoaded(){this.be||this.ye||this.ke||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this.we=e}Ee(e){var t;if(this.Ie(),!this._instance.I())if(this.ye)this.Se=!0;else{var i={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:m({},((t=this._instance.persistence)==null?void 0:t.get_initial_props())||{},this._instance.get_property(yt)||{}),group_properties:this._instance.get_property(Ue)};(e!=null&&e.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(i.disable_flags=!0);var r=this._instance.config.__preview_remote_config,n=r?"/flags/?v=2":"/flags/?v=2&config=true",a=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",o=this._instance.requestRouter.endpointFor("api",n+a);r&&(i.timezone=Mn()),this.ye=!0,this._instance.Pe({method:"POST",url:o,data:i,compression:this._instance.config.disable_compression?void 0:Ie.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:l=>{var u,c,d=!0;if(l.statusCode===200&&(this.Se||(this.$anon_distinct_id=void 0),d=!1),this.ye=!1,this.$e||(this.$e=!0,this._instance.Re((c=l.json)!==null&&c!==void 0?c:{})),!i.disable_flags||this.Se)if(this.xe=!d,l.json&&(u=l.json.quotaLimited)!=null&&u.includes(qo.FeatureFlags))fe.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.");else{var h;i.disable_flags||this.receivedFeatureFlags((h=l.json)!==null&&h!==void 0?h:{},d),this.Se&&(this.Se=!1,this.Ee())}}})}}getFeatureFlag(e,t){if(t===void 0&&(t={}),this.be||this.getFlags()&&this.getFlags().length>0){var i=this.getFlagVariants()[e],r=""+i,n=this._instance.get_property(mr)||void 0,a=this._instance.get_property(Vt)||{};if((t.send_event||!("send_event"in t))&&(!(e in a)||!a[e].includes(r))){var o,l,u,c,d,h,p,v,f;T(a[e])?a[e].push(r):a[e]=[r],(o=this._instance.persistence)==null||o.register({[Vt]:a});var b=this.getFeatureFlagDetails(e),E={$feature_flag:e,$feature_flag_response:i,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_request_id:n,$feature_flag_bootstrapped_response:((l=this._instance.config.bootstrap)==null||(l=l.featureFlags)==null?void 0:l[e])||null,$feature_flag_bootstrapped_payload:((u=this._instance.config.bootstrap)==null||(u=u.featureFlagPayloads)==null?void 0:u[e])||null,$used_bootstrap_value:!this.xe};_(b==null||(c=b.metadata)==null?void 0:c.version)||(E.$feature_flag_version=b.metadata.version);var S,x=(d=b==null||(h=b.reason)==null?void 0:h.description)!==null&&d!==void 0?d:b==null||(p=b.reason)==null?void 0:p.code;x&&(E.$feature_flag_reason=x),b!=null&&(v=b.metadata)!=null&&v.id&&(E.$feature_flag_id=b.metadata.id),_(b?.original_variant)&&_(b?.original_enabled)||(E.$feature_flag_original_response=_(b.original_variant)?b.original_enabled:b.original_variant),b!=null&&(f=b.metadata)!=null&&f.original_payload&&(E.$feature_flag_original_payload=b==null||(S=b.metadata)==null?void 0:S.original_payload),this._instance.capture("$feature_flag_called",E)}return i}fe.warn('getFeatureFlag for key "'+e+`" failed. Feature flags didn't load in time.`)}getFeatureFlagDetails(e){return this.getFlagsWithDetails()[e]}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}getRemoteConfigPayload(e,t){var i=this._instance.config.token;this._instance.Pe({method:"POST",url:this._instance.requestRouter.endpointFor("api","/flags/?v=2&config=true"),data:{distinct_id:this._instance.get_distinct_id(),token:i},compression:this._instance.config.disable_compression?void 0:Ie.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:r=>{var n,a=(n=r.json)==null?void 0:n.featureFlagPayloads;t(a?.[e]||void 0)}})}isFeatureEnabled(e,t){if(t===void 0&&(t={}),this.be||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);fe.warn('isFeatureEnabled for key "'+e+`" failed. Feature flags didn't load in time.`)}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(t=>t!==e)}receivedFeatureFlags(e,t){if(this._instance.persistence){this.be=!0;var i=this.getFlagVariants(),r=this.getFlagPayloads(),n=this.getFlagsWithDetails();(function(a,o,l,u,c){l===void 0&&(l={}),u===void 0&&(u={}),c===void 0&&(c={});var d=No(a),h=d.flags,p=d.featureFlags,v=d.featureFlagPayloads;if(p){var f=a.requestId;if(T(p)){fe.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var b={};if(p)for(var E=0;E<p.length;E++)b[p[E]]=!0;o&&o.register({[xi]:p,[it]:b})}else{var S=p,x=v,$=h;a.errorsWhileComputingFlags&&(S=m({},l,S),x=m({},u,x),$=m({},c,$)),o&&o.register(m({[xi]:Object.keys(yr(S)),[it]:S||{},[_r]:x||{},[Ni]:$||{}},f?{[mr]:f}:{}))}}})(e,this._instance.persistence,i,r,n),this.Te(t)}}override(e,t){t===void 0&&(t=!1),fe.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})}overrideFeatureFlags(e){if(!this._instance.__loaded||!this._instance.persistence)return fe.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(e===!1)return this._instance.persistence.unregister(Qe),this._instance.persistence.unregister(gt),void this.Te();if(e&&typeof e=="object"&&("flags"in e||"payloads"in e)){var t,i=e;if(this.me=!!((t=i.suppressWarning)!==null&&t!==void 0&&t),"flags"in i){if(i.flags===!1)this._instance.persistence.unregister(Qe);else if(i.flags)if(T(i.flags)){for(var r={},n=0;n<i.flags.length;n++)r[i.flags[n]]=!0;this._instance.persistence.register({[Qe]:r})}else this._instance.persistence.register({[Qe]:i.flags})}return"payloads"in i&&(i.payloads===!1?this._instance.persistence.unregister(gt):i.payloads&&this._instance.persistence.register({[gt]:i.payloads})),void this.Te()}this.Te()}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this.be){var{flags:t,flagVariants:i}=this.Me();e(t,i)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t,i){var r,n=(this._instance.get_property(mt)||[]).find(u=>u.flagKey===e),a={["$feature_enrollment/"+e]:t},o={$feature_flag:e,$feature_enrollment:t,$set:a};n&&(o.$early_access_feature_name=n.name),i&&(o.$feature_enrollment_stage=i),this._instance.capture("$feature_enrollment_update",o),this.setPersonPropertiesForFlags(a,!1);var l=m({},this.getFlagVariants(),{[e]:t});(r=this._instance.persistence)==null||r.register({[xi]:Object.keys(yr(l)),[it]:l}),this.Te()}getEarlyAccessFeatures(e,t,i){t===void 0&&(t=!1);var r=this._instance.get_property(mt),n=i?"&"+i.map(a=>"stage="+a).join("&"):"";if(r&&!t)return e(r);this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+n),method:"GET",callback:a=>{var o,l;if(a.json){var u=a.json.earlyAccessFeatures;return(o=this._instance.persistence)==null||o.unregister(mt),(l=this._instance.persistence)==null||l.register({[mt]:u}),e(u)}}})}Me(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(i=>t[i]),flagVariants:Object.keys(t).filter(i=>t[i]).reduce((i,r)=>(i[r]=t[r],i),{})}}Te(e){var{flags:t,flagVariants:i}=this.Me();this.featureFlagEventHandlers.forEach(r=>r(t,i,{errorsLoading:e}))}setPersonPropertiesForFlags(e,t){t===void 0&&(t=!0);var i=this._instance.get_property(yt)||{};this._instance.register({[yt]:m({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(yt)}setGroupPropertiesForFlags(e,t){t===void 0&&(t=!0);var i=this._instance.get_property(Ue)||{};Object.keys(i).length!==0&&Object.keys(i).forEach(r=>{i[r]=m({},i[r],e[r]),delete e[r]}),this._instance.register({[Ue]:m({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this._instance.get_property(Ue)||{};this._instance.register({[Ue]:m({},t,{[e]:{}})})}else this._instance.unregister(Ue)}reset(){this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this.$anon_distinct_id=void 0,this.Ie(),this.me=!1}}var Bo=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class Ii{constructor(e,t){this.S=e,this.props={},this.Ce=!1,this.Fe=(i=>{var r="";return i.token&&(r=i.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),i.persistence_name?"ph_"+i.persistence_name:"ph_"+r+"_posthog"})(e),this.B=this.Oe(e),this.load(),e.debug&&w.info("Persistence loaded",e.persistence,m({},this.props)),this.update_config(e,e,t),this.save()}isDisabled(){return!!this.Ae}Oe(e){Bo.indexOf(e.persistence.toLowerCase())===-1&&(w.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return t==="localstorage"&&q.O()?q:t==="localstorage+cookie"&&Ot.O()?Ot:t==="sessionstorage"&&H.O()?H:t==="memory"?ya:t==="cookie"?me:Ot.O()?Ot:me}properties(){var e={};return R(this.props,function(t,i){if(i===it&&M(t))for(var r=Object.keys(t),n=0;n<r.length;n++)e["$feature/"+r[n]]=t[r[n]];else o=i,l=!1,(Ne(a=Qn)?l:ks&&a.indexOf===ks?a.indexOf(o)!=-1:(R(a,function(u){if(l||(l=u===o))return Ut}),l))||(e[i]=t);var a,o,l}),e}load(){if(!this.Ae){var e=this.B.L(this.Fe);e&&(this.props=L({},e))}}save(){this.Ae||this.B.j(this.Fe,this.props,this.De,this.Le,this.je,this.S.debug)}remove(){this.B.N(this.Fe,!1),this.B.N(this.Fe,!0)}clear(){this.remove(),this.props={}}register_once(e,t,i){if(M(e)){_(t)&&(t="None"),this.De=_(i)?this.Ne:i;var r=!1;if(R(e,(n,a)=>{this.props.hasOwnProperty(a)&&this.props[a]!==t||(this.props[a]=n,r=!0)}),r)return this.save(),!0}return!1}register(e,t){if(M(e)){this.De=_(t)?this.Ne:t;var i=!1;if(R(e,(r,n)=>{e.hasOwnProperty(n)&&this.props[n]!==r&&(this.props[n]=r,i=!0)}),i)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.Ce){var e=Cn(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);tt(ns(e))||this.register(e),this.Ce=!0}}update_search_keyword(){var e;this.register((e=y?.referrer)?Pn(e):{})}update_referrer_info(){var e;this.register_once({$referrer:Tn(),$referring_domain:y!=null&&y.referrer&&((e=ot(y.referrer))==null?void 0:e.host)||"$direct"},void 0)}set_initial_person_info(){this.props[Hi]||this.props[zi]||this.register_once({[Wt]:On(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var e={};R([zi,Hi],a=>{var o=this.props[a];o&&R(o,function(l,u){e["$initial_"+Pi(u)]=l})});var t,i,r=this.props[Wt];if(r){var n=(t=An(r),i={},R(t,function(a,o){i["$initial_"+Pi(o)]=a}),i);L(e,n)}return e}safe_merge(e){return R(this.props,function(t,i){i in e||(e[i]=t)}),e}update_config(e,t,i){if(this.Ne=this.De=e.cookie_expiration,this.set_disabled(e.disable_persistence||!!i),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var r=this.Oe(e),n=this.props;this.clear(),this.B=r,this.props=n,this.save()}}set_disabled(e){this.Ae=e,this.Ae?this.remove():this.save()}set_cross_subdomain(e){e!==this.Le&&(this.Le=e,this.remove(),this.save())}set_secure(e){e!==this.je&&(this.je=e,this.remove(),this.save())}set_event_timer(e,t){var i=this.props[vt]||{};i[e]=t,this.props[vt]=i,this.save()}remove_event_timer(e){var t=(this.props[vt]||{})[e];return _(t)||(delete this.props[vt][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}class Dn{constructor(){this.ze={},this.ze={}}on(e,t){return this.ze[e]||(this.ze[e]=[]),this.ze[e].push(t),()=>{this.ze[e]=this.ze[e].filter(i=>i!==t)}}emit(e,t){for(var i of this.ze[e]||[])i(t);for(var r of this.ze["*"]||[])r(e,t)}}class Ge{constructor(e){this.Ue=new Dn,this.qe=(t,i)=>this.Be(t,i)&&this.He(t,i)&&this.We(t,i),this.Be=(t,i)=>i==null||!i.event||t?.event===i?.event,this._instance=e,this.Ge=new Set,this.Je=new Set}init(){var e;if(!_((e=this._instance)==null?void 0:e.Ve)){var t;(t=this._instance)==null||t.Ve((i,r)=>{this.on(i,r)})}}register(e){var t,i;if(!_((t=this._instance)==null?void 0:t.Ve)&&(e.forEach(a=>{var o,l;(o=this.Je)==null||o.add(a),(l=a.steps)==null||l.forEach(u=>{var c;(c=this.Ge)==null||c.add(u?.event||"")})}),(i=this._instance)!=null&&i.autocapture)){var r,n=new Set;e.forEach(a=>{var o;(o=a.steps)==null||o.forEach(l=>{l!=null&&l.selector&&n.add(l?.selector)})}),(r=this._instance)==null||r.autocapture.setElementSelectors(n)}}on(e,t){var i;t!=null&&e.length!=0&&(this.Ge.has(e)||this.Ge.has(t?.event))&&this.Je&&((i=this.Je)==null?void 0:i.size)>0&&this.Je.forEach(r=>{this.Ke(t,r)&&this.Ue.emit("actionCaptured",r.name)})}Ye(e){this.onAction("actionCaptured",t=>e(t))}Ke(e,t){if(t?.steps==null)return!1;for(var i of t.steps)if(this.qe(e,i))return!0;return!1}onAction(e,t){return this.Ue.on(e,t)}He(e,t){if(t!=null&&t.url){var i,r=e==null||(i=e.properties)==null?void 0:i.$current_url;if(!r||typeof r!="string"||!Ge.Xe(r,t?.url,t?.url_matching||"contains"))return!1}return!0}static Xe(e,t,i){switch(i){case"regex":return!!g&&ut(e,t);case"exact":return t===e;case"contains":var r=Ge.Qe(t).replace(/_/g,".").replace(/%/g,".*");return ut(e,r);default:return!1}}static Qe(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}We(e,t){if((t!=null&&t.href||t!=null&&t.tag_name||t!=null&&t.text)&&!this.Ze(e).some(n=>!(t!=null&&t.href&&!Ge.Xe(n.href||"",t?.href,t?.href_matching||"exact"))&&(t==null||!t.tag_name||n.tag_name===t?.tag_name)&&!(t!=null&&t.text&&!Ge.Xe(n.text||"",t?.text,t?.text_matching||"exact")&&!Ge.Xe(n.$el_text||"",t?.text,t?.text_matching||"exact"))))return!1;if(t!=null&&t.selector){var i,r=e==null||(i=e.properties)==null?void 0:i.$element_selectors;if(!r||!r.includes(t?.selector))return!1}return!0}Ze(e){return e?.properties.$elements==null?[]:e?.properties.$elements}}(function(s){return s.Button="button",s.Tab="tab",s.Selector="selector",s})({});(function(s){return s.TopLeft="top_left",s.TopRight="top_right",s.TopCenter="top_center",s.MiddleLeft="middle_left",s.MiddleRight="middle_right",s.MiddleCenter="middle_center",s.Left="left",s.Center="center",s.Right="right",s.NextToTrigger="next_to_trigger",s})({});var $i=function(s){return s.Popover="popover",s.API="api",s.Widget="widget",s.ExternalSurvey="external_survey",s}({});(function(s){return s.Open="open",s.MultipleChoice="multiple_choice",s.SingleChoice="single_choice",s.Rating="rating",s.Link="link",s})({});(function(s){return s.NextQuestion="next_question",s.End="end",s.ResponseBased="response_based",s.SpecificQuestion="specific_question",s})({});(function(s){return s.Once="once",s.Recurring="recurring",s.Always="always",s})({});var Fi=function(s){return s.SHOWN="survey shown",s.DISMISSED="survey dismissed",s.SENT="survey sent",s}({}),br=function(s){return s.SURVEY_ID="$survey_id",s.SURVEY_NAME="$survey_name",s.SURVEY_RESPONSE="$survey_response",s.SURVEY_ITERATION="$survey_iteration",s.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",s.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",s.SURVEY_SUBMISSION_ID="$survey_submission_id",s.SURVEY_QUESTIONS="$survey_questions",s.SURVEY_COMPLETED="$survey_completed",s}({}),N=j("[Surveys]"),is="seenSurvey_",Ho=(s,e)=>{var t="$survey_"+e+"/"+s.id;return s.current_iteration&&s.current_iteration>0&&(t="$survey_"+e+"/"+s.id+"/"+s.current_iteration),t},zo=[$i.Popover,$i.Widget,$i.API];class Uo{constructor(e){this._instance=e,this.tr=new Map,this.ir=new Map}register(e){var t;_((t=this._instance)==null?void 0:t.Ve)||(this.er(e),this.rr(e))}rr(e){var t=e.filter(i=>{var r,n;return((r=i.conditions)==null?void 0:r.actions)&&((n=i.conditions)==null||(n=n.actions)==null||(n=n.values)==null?void 0:n.length)>0});t.length!==0&&(this.sr==null&&(this.sr=new Ge(this._instance),this.sr.init(),this.sr.Ye(i=>{this.onAction(i)})),t.forEach(i=>{var r,n,a,o,l;i.conditions&&(r=i.conditions)!=null&&r.actions&&(n=i.conditions)!=null&&(n=n.actions)!=null&&n.values&&((a=i.conditions)==null||(a=a.actions)==null||(a=a.values)==null?void 0:a.length)>0&&((o=this.sr)==null||o.register(i.conditions.actions.values),(l=i.conditions)==null||(l=l.actions)==null||(l=l.values)==null||l.forEach(u=>{if(u&&u.name){var c=this.ir.get(u.name);c&&c.push(i.id),this.ir.set(u.name,c||[i.id])}}))}))}er(e){var t;e.filter(i=>{var r,n;return((r=i.conditions)==null?void 0:r.events)&&((n=i.conditions)==null||(n=n.events)==null||(n=n.values)==null?void 0:n.length)>0}).length!==0&&((t=this._instance)==null||t.Ve((i,r)=>{this.onEvent(i,r)}),e.forEach(i=>{var r;(r=i.conditions)==null||(r=r.events)==null||(r=r.values)==null||r.forEach(n=>{if(n&&n.name){var a=this.tr.get(n.name);a&&a.push(i.id),this.tr.set(n.name,a||[i.id])}})}))}onEvent(e,t){var i,r=((i=this._instance)==null||(i=i.persistence)==null?void 0:i.props[Pt])||[];if(e==="survey shown"&&t&&r.length>0){var n;N.info("survey event matched, removing survey from activated surveys",{event:e,eventPayload:t,existingActivatedSurveys:r});var a=t==null||(n=t.properties)==null?void 0:n.$survey_id;if(a){var o=r.indexOf(a);o>=0&&(r.splice(o,1),this.nr(r))}}else this.tr.has(e)&&(N.info("survey event matched, updating activated surveys",{event:e,surveys:this.tr.get(e)}),this.nr(r.concat(this.tr.get(e)||[])))}onAction(e){var t,i=((t=this._instance)==null||(t=t.persistence)==null?void 0:t.props[Pt])||[];this.ir.has(e)&&this.nr(i.concat(this.ir.get(e)||[]))}nr(e){var t;(t=this._instance)==null||(t=t.persistence)==null||t.register({[Pt]:[...new Set(e)]})}getSurveys(){var e,t=(e=this._instance)==null||(e=e.persistence)==null?void 0:e.props[Pt];return t||[]}getEventToSurveys(){return this.tr}ar(){return this.sr}}class Go{constructor(e){this.lr=void 0,this._surveyManager=null,this.ur=!1,this.hr=!1,this.dr=[],this._instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){var t=e.surveys;if(C(t))return N.warn("Flags not loaded yet. Not loading surveys.");var i=T(t);this.lr=i?t.length>0:t,N.info("flags response received, isSurveysEnabled: "+this.lr),this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");for(var e=[],t=0;t<localStorage.length;t++){var i=localStorage.key(t);(i!=null&&i.startsWith(is)||i!=null&&i.startsWith("inProgressSurvey_"))&&e.push(i)}e.forEach(r=>localStorage.removeItem(r))}loadIfEnabled(){if(!this._surveyManager)if(this.hr)N.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)N.info("Disabled. Not loading surveys.");else{var e=k?.__PosthogExtensions__;if(e){var t=this.lr;if(!_(t)){this.hr=!0;try{var i=e.generateSurveys;if(i)return void this.vr(i,t);var r=e.loadExternalDependency;if(!r)return void this.cr("PostHog loadExternalDependency extension not found.");r(this._instance,"surveys",n=>{n||!e.generateSurveys?this.cr("Could not load surveys script",n):this.vr(e.generateSurveys,t)})}catch(n){throw this.cr("Error initializing surveys",n),n}finally{this.hr=!1}}}else N.error("PostHog Extensions not found.")}}vr(e,t){this._surveyManager=e(this._instance,t),this._surveyEventReceiver=new Uo(this._instance),N.info("Surveys loaded successfully"),this.pr({isLoaded:!0})}cr(e,t){N.error(e,t),this.pr({isLoaded:!1,error:e})}onSurveysLoaded(e){return this.dr.push(e),this._surveyManager&&this.pr({isLoaded:!0}),()=>{this.dr=this.dr.filter(t=>t!==e)}}getSurveys(e,t){if(t===void 0&&(t=!1),this._instance.config.disable_surveys)return N.info("Disabled. Not loading surveys."),e([]);var i=this._instance.get_property(qi);if(i&&!t)return e(i,{isLoaded:!0});if(this.ur)return e([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this.ur=!0,this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:r=>{var n;this.ur=!1;var a=r.statusCode;if(a!==200||!r.json){var o="Surveys API could not be loaded, status: "+a;return N.error(o),e([],{isLoaded:!1,error:o})}var l,u=r.json.surveys||[],c=u.filter(d=>function(h){return!(!h.start_date||h.end_date)}(d)&&(function(h){var p;return!((p=h.conditions)==null||(p=p.events)==null||(p=p.values)==null||!p.length)}(d)||function(h){var p;return!((p=h.conditions)==null||(p=p.actions)==null||(p=p.values)==null||!p.length)}(d)));return c.length>0&&((l=this._surveyEventReceiver)==null||l.register(c)),(n=this._instance.persistence)==null||n.register({[qi]:u}),e(u,{isLoaded:!0})}})}catch(r){throw this.ur=!1,r}}pr(e){for(var t of this.dr)try{e.isLoaded?this.getSurveys(t):t([],e)}catch(i){N.error("Error in survey callback",i)}}getActiveMatchingSurveys(e,t){if(t===void 0&&(t=!1),!C(this._surveyManager))return this._surveyManager.getActiveMatchingSurveys(e,t);N.warn("init was not called")}gr(e){var t=null;return this.getSurveys(i=>{var r;t=(r=i.find(n=>n.id===e))!==null&&r!==void 0?r:null}),t}_r(e){if(C(this._surveyManager))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var t=typeof e=="string"?this.gr(e):e;return t?this._surveyManager.checkSurveyEligibility(t):{eligible:!1,reason:"Survey not found"}}canRenderSurvey(e){if(C(this._surveyManager))return N.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var t=this._r(e);return{visible:t.eligible,disabledReason:t.reason}}canRenderSurveyAsync(e,t){return C(this._surveyManager)?(N.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise(i=>{this.getSurveys(r=>{var n,a=(n=r.find(l=>l.id===e))!==null&&n!==void 0?n:null;if(a){var o=this._r(a);i({visible:o.eligible,disabledReason:o.reason})}else i({visible:!1,disabledReason:"Survey not found"})},t)})}renderSurvey(e,t){if(C(this._surveyManager))N.warn("init was not called");else{var i=this.gr(e);if(i)if(zo.includes(i.type)){var r=y?.querySelector(t);r?this._surveyManager.renderSurvey(i,r):N.warn("Survey element not found")}else N.warn("Surveys of type "+i.type+" cannot be rendered in the app");else N.warn("Survey not found")}}}var wr=j("[RateLimiter]");class Vo{constructor(e){var t,i;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=r=>{var n=r.text;if(n&&n.length)try{(JSON.parse(n).quota_limited||[]).forEach(a=>{wr.info((a||"events")+" is quota limited."),this.serverLimits[a]=new Date().getTime()+6e4})}catch(a){return void wr.warn('could not rate limit - continuing. Error: "'+a?.message+'"',{text:n})}},this.instance=e,this.captureEventsPerSecond=((t=e.config.rate_limiting)==null?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max(((i=e.config.rate_limiting)==null?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(e){var t,i,r;e===void 0&&(e=!1);var n=new Date().getTime(),a=(t=(i=this.instance.persistence)==null?void 0:i.get_property(Bi))!==null&&t!==void 0?t:{tokens:this.captureEventsBurstLimit,last:n};a.tokens+=(n-a.last)/1e3*this.captureEventsPerSecond,a.last=n,a.tokens>this.captureEventsBurstLimit&&(a.tokens=this.captureEventsBurstLimit);var o=a.tokens<1;return o||e||(a.tokens=Math.max(0,a.tokens-1)),!o||this.lastEventRateLimited||e||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,(r=this.instance.persistence)==null||r.set_property(Bi,a),{isRateLimited:o,remainingTokens:a.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return t!==!1&&new Date().getTime()<t}}var ze=j("[RemoteConfig]");class Wo{constructor(e){this._instance=e}get remoteConfig(){var e;return(e=k._POSTHOG_REMOTE_CONFIG)==null||(e=e[this._instance.config.token])==null?void 0:e.config}mr(e){var t,i;(t=k.__PosthogExtensions__)!=null&&t.loadExternalDependency?(i=k.__PosthogExtensions__)==null||i.loadExternalDependency==null||i.loadExternalDependency(this._instance,"remote-config",()=>e(this.remoteConfig)):(ze.error("PostHog Extensions not found. Cannot load remote config."),e())}br(e){this._instance.Pe({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return ze.info("Using preloaded remote config",this.remoteConfig),void this.Re(this.remoteConfig);if(this._instance.I())return void ze.warn("Remote config is disabled. Falling back to local config.");this.mr(e=>{if(!e)return ze.info("No config found after loading remote JS config. Falling back to JSON."),void this.br(t=>{this.Re(t)});this.Re(e)})}catch(e){ze.error("Error loading remote config",e)}}Re(e){e?this._instance.config.__preview_remote_config?(this._instance.Re(e),e.hasFeatureFlags!==!1&&this._instance.featureFlags.ensureFlagsLoaded()):ze.info("__preview_remote_config is disabled. Logging config instead",e):ze.error("Failed to fetch remote config from PostHog.")}}var ss=3e3;class Yo{constructor(e,t){this.yr=!0,this.wr=[],this.Sr=se(t?.flush_interval_ms||ss,250,5e3,"flush interval",ss),this.$r=e}enqueue(e){this.wr.push(e),this.kr||this.Er()}unload(){this.Ir();var e=this.wr.length>0?this.Pr():{},t=Object.values(e);[...t.filter(i=>i.url.indexOf("/e")===0),...t.filter(i=>i.url.indexOf("/e")!==0)].map(i=>{this.$r(m({},i,{transport:"sendBeacon"}))})}enable(){this.yr=!1,this.Er()}Er(){var e=this;this.yr||(this.kr=setTimeout(()=>{if(this.Ir(),this.wr.length>0){var t=this.Pr(),i=function(){var n=t[r],a=new Date().getTime();n.data&&T(n.data)&&R(n.data,o=>{o.offset=Math.abs(o.timestamp-a),delete o.timestamp}),e.$r(n)};for(var r in t)i()}},this.Sr))}Ir(){clearTimeout(this.kr),this.kr=void 0}Pr(){var e={};return R(this.wr,t=>{var i,r=t,n=(r?r.batchKey:null)||r.url;_(e[n])&&(e[n]=m({},r,{data:[]})),(i=e[n].data)==null||i.push(r.data)}),this.wr=[],e}}var Jo=["retriesPerformedSoFar"];class Ko{constructor(e){this.Rr=!1,this.Tr=3e3,this.wr=[],this._instance=e,this.wr=[],this.Mr=!0,!_(g)&&"onLine"in g.navigator&&(this.Mr=g.navigator.onLine,A(g,"online",()=>{this.Mr=!0,this.se()}),A(g,"offline",()=>{this.Mr=!1}))}get length(){return this.wr.length}retriableRequest(e){var{retriesPerformedSoFar:t}=e,i=Tr(e,Jo);G(t)&&t>0&&(i.url=ii(i.url,{retry_count:t})),this._instance.Pe(m({},i,{callback:r=>{r.statusCode!==200&&(r.statusCode<400||r.statusCode>=500)&&(t??0)<10?this.Cr(m({retriesPerformedSoFar:t},i)):i.callback==null||i.callback(r)}}))}Cr(e){var t=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=t+1;var i=function(a){var o=3e3*Math.pow(2,a),l=o/2,u=Math.min(18e5,o),c=(Math.random()-.5)*(u-l);return Math.ceil(u+c)}(t),r=Date.now()+i;this.wr.push({retryAt:r,requestOptions:e});var n="Enqueued failed request for retry in "+i;navigator.onLine||(n+=" (Browser is offline)"),w.warn(n),this.Rr||(this.Rr=!0,this.Fr())}Fr(){this.Or&&clearTimeout(this.Or),this.Or=setTimeout(()=>{this.Mr&&this.wr.length>0&&this.se(),this.Fr()},this.Tr)}se(){var e=Date.now(),t=[],i=this.wr.filter(n=>n.retryAt<e||(t.push(n),!1));if(this.wr=t,i.length>0)for(var{requestOptions:r}of i)this.retriableRequest(r)}unload(){for(var{requestOptions:e}of(this.Or&&(clearTimeout(this.Or),this.Or=void 0),this.wr))try{this._instance.Pe(m({},e,{transport:"sendBeacon"}))}catch(t){w.error(t)}this.wr=[]}}class Zo{constructor(e){this.Ar=()=>{var t,i,r,n;this.Dr||(this.Dr={});var a=this.scrollElement(),o=this.scrollY(),l=a?Math.max(0,a.scrollHeight-a.clientHeight):0,u=o+(a?.clientHeight||0),c=a?.scrollHeight||0;this.Dr.lastScrollY=Math.ceil(o),this.Dr.maxScrollY=Math.max(o,(t=this.Dr.maxScrollY)!==null&&t!==void 0?t:0),this.Dr.maxScrollHeight=Math.max(l,(i=this.Dr.maxScrollHeight)!==null&&i!==void 0?i:0),this.Dr.lastContentY=u,this.Dr.maxContentY=Math.max(u,(r=this.Dr.maxContentY)!==null&&r!==void 0?r:0),this.Dr.maxContentHeight=Math.max(c,(n=this.Dr.maxContentHeight)!==null&&n!==void 0?n:0)},this._instance=e}getContext(){return this.Dr}resetContext(){var e=this.Dr;return setTimeout(this.Ar,0),e}startMeasuringScrollPosition(){A(g,"scroll",this.Ar,{capture:!0}),A(g,"scrollend",this.Ar,{capture:!0}),A(g,"resize",this.Ar)}scrollElement(){if(!this._instance.config.scroll_root_selector)return g?.document.documentElement;var e=T(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector];for(var t of e){var i=g?.document.querySelector(t);if(i)return i}}scrollY(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return g&&(g.scrollY||g.pageYOffset||g.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return g&&(g.scrollX||g.pageXOffset||g.document.documentElement.scrollLeft)||0}}var Xo=s=>On(s?.config.mask_personal_data_properties,s?.config.custom_personal_data_properties);class Qo{constructor(e,t,i,r){this.Lr=n=>{var a=this.jr();if(!a||a.sessionId!==n){var o={sessionId:n,props:this.Nr(this._instance)};this.zr.register({[ji]:o})}},this._instance=e,this.Ur=t,this.zr=i,this.Nr=r||Xo,this.Ur.onSessionId(this.Lr)}jr(){return this.zr.props[ji]}getSetOnceProps(){var e,t=(e=this.jr())==null?void 0:e.props;return t?"r"in t?An(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}}getSessionProps(){var e={};return R(ns(this.getSetOnceProps()),(t,i)=>{i==="$current_url"&&(i="url"),e["$session_entry_"+Pi(i)]=t}),e}}var Er=j("[SessionId]");class el{constructor(e,t,i){var r;if(this.qr=[],this.Br=(c,d)=>Math.abs(c-d)>this.sessionTimeoutMs,!e.persistence)throw new Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw new Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.S=e.config,this.zr=e.persistence,this.fi=void 0,this.Ct=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Hr=t||Ae,this.Wr=i||Ae;var n=this.S.persistence_name||this.S.token,a=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*se(a,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Gr(),this.Jr="ph_"+n+"_window_id",this.Vr="ph_"+n+"_primary_window_exists",this.Kr()){var o=H.L(this.Jr),l=H.L(this.Vr);o&&!l?this.fi=o:H.N(this.Jr),H.j(this.Vr,!0)}if((r=this.S.bootstrap)!=null&&r.sessionID)try{var u=(c=>{var d=c.replace(/-/g,"");if(d.length!==32)throw new Error("Not a valid UUID");if(d[12]!=="7")throw new Error("Not a UUIDv7");return parseInt(d.substring(0,12),16)})(this.S.bootstrap.sessionID);this.Yr(this.S.bootstrap.sessionID,new Date().getTime(),u)}catch(c){Er.error("Invalid sessionID in bootstrap",c)}this.Xr()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return _(this.qr)&&(this.qr=[]),this.qr.push(e),this.Ct&&e(this.Ct,this.fi),()=>{this.qr=this.qr.filter(t=>t!==e)}}Kr(){return this.S.persistence!=="memory"&&!this.zr.Ae&&H.O()}Qr(e){e!==this.fi&&(this.fi=e,this.Kr()&&H.j(this.Jr,e))}Zr(){return this.fi?this.fi:this.Kr()?H.L(this.Jr):null}Yr(e,t,i){e===this.Ct&&t===this._sessionActivityTimestamp&&i===this._sessionStartTimestamp||(this._sessionStartTimestamp=i,this._sessionActivityTimestamp=t,this.Ct=e,this.zr.register({[Gt]:[t,e,i]}))}ts(){if(this.Ct&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.Ct,this._sessionStartTimestamp];var e=this.zr.props[Gt];return T(e)&&e.length===2&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this.Yr(null,null,null)}Xr(){A(g,"beforeunload",()=>{this.Kr()&&H.N(this.Vr)},{capture:!1})}checkAndGetSessionAndWindowId(e,t){if(e===void 0&&(e=!1),t===void 0&&(t=null),this.S.__preview_experimental_cookieless_mode)throw new Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var i=t||new Date().getTime(),[r,n,a]=this.ts(),o=this.Zr(),l=G(a)&&a>0&&Math.abs(i-a)>864e5,u=!1,c=!n,d=!e&&this.Br(i,r);c||d||l?(n=this.Hr(),o=this.Wr(),Er.info("new session ID generated",{sessionId:n,windowId:o,changeReason:{noSessionId:c,activityTimeout:d,sessionPastMaximumLength:l}}),a=i,u=!0):o||(o=this.Wr(),u=!0);var h=r===0||!e||l?i:r,p=a===0?new Date().getTime():a;return this.Qr(o),this.Yr(n,h,p),e||this.Gr(),u&&this.qr.forEach(v=>v(n,o,u?{noSessionId:c,activityTimeout:d,sessionPastMaximumLength:l}:void 0)),{sessionId:n,windowId:o,sessionStartTimestamp:p,changeReason:u?{noSessionId:c,activityTimeout:d,sessionPastMaximumLength:l}:void 0,lastActivityTimestamp:r}}Gr(){clearTimeout(this.es),this.es=setTimeout(()=>{var[e]=this.ts();this.Br(new Date().getTime(),e)&&this.resetSessionId()},1.1*this.sessionTimeoutMs)}}var tl=["$set_once","$set"],Te=j("[SiteApps]");class il{constructor(e){this._instance=e,this.rs=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}ss(e,t){if(t){var i=this.globalsForEvent(t);this.rs.push(i),this.rs.length>1e3&&(this.rs=this.rs.slice(10))}}get siteAppLoaders(){var e;return(e=k._POSTHOG_REMOTE_CONFIG)==null||(e=e[this._instance.config.token])==null?void 0:e.siteApps}init(){if(this.isEnabled){var e=this._instance.Ve(this.ss.bind(this));this.ns=()=>{e(),this.rs=[],this.ns=void 0}}}globalsForEvent(e){var t,i,r,n,a,o,l;if(!e)throw new Error("Event payload is required");var u={},c=this._instance.get_property("$groups")||[],d=this._instance.get_property("$stored_group_properties")||{};for(var[h,p]of Object.entries(d))u[h]={id:c[h],type:h,properties:p};var{$set_once:v,$set:f}=e;return{event:m({},Tr(e,tl),{properties:m({},e.properties,f?{$set:m({},(t=(i=e.properties)==null?void 0:i.$set)!==null&&t!==void 0?t:{},f)}:{},v?{$set_once:m({},(r=(n=e.properties)==null?void 0:n.$set_once)!==null&&r!==void 0?r:{},v)}:{}),elements_chain:(a=(o=e.properties)==null?void 0:o.$elements_chain)!==null&&a!==void 0?a:"",distinct_id:(l=e.properties)==null?void 0:l.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:u}}setupSiteApp(e){var t=this.apps[e.id],i=()=>{var o;!t.errored&&this.rs.length&&(Te.info("Processing "+this.rs.length+" events for site app with id "+e.id),this.rs.forEach(l=>t.processEvent==null?void 0:t.processEvent(l)),t.processedBuffer=!0),Object.values(this.apps).every(l=>l.processedBuffer||l.errored)&&((o=this.ns)==null||o.call(this))},r=!1,n=o=>{t.errored=!o,t.loaded=!0,Te.info("Site app with id "+e.id+" "+(o?"loaded":"errored")),r&&i()};try{var{processEvent:a}=e.init({posthog:this._instance,callback:o=>{n(o)}});a&&(t.processEvent=a),r=!0}catch(o){Te.error("Error while initializing PostHog app with config id "+e.id,o),n(!1)}if(r&&t.loaded)try{i()}catch(o){Te.error("Error while processing buffered events PostHog app with config id "+e.id,o),t.errored=!0}}os(){var e=this.siteAppLoaders||[];for(var t of e)this.apps[t.id]={id:t.id,loaded:!1,errored:!1,processedBuffer:!1};for(var i of e)this.setupSiteApp(i)}ls(e){if(Object.keys(this.apps).length!==0){var t=this.globalsForEvent(e);for(var i of Object.values(this.apps))try{i.processEvent==null||i.processEvent(t)}catch(r){Te.error("Error while processing event "+e.event+" for site app "+i.id,r)}}}onRemoteConfig(e){var t,i,r,n=this;if((t=this.siteAppLoaders)!=null&&t.length)return this.isEnabled?(this.os(),void this._instance.on("eventCaptured",u=>this.ls(u))):void Te.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if((i=this.ns)==null||i.call(this),(r=e.siteApps)!=null&&r.length)if(this.isEnabled){var a=function(u){var c;k["__$$ph_site_app_"+u]=n._instance,(c=k.__PosthogExtensions__)==null||c.loadSiteApp==null||c.loadSiteApp(n._instance,l,d=>{if(d)return Te.error("Error while initializing PostHog app with config id "+u,d)})};for(var{id:o,url:l}of e.siteApps)a(o)}else Te.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}var sl=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],Sr=function(s,e){if(!s)return!1;var t=s.toLowerCase();return sl.concat(e||[]).some(i=>{var r=i.toLowerCase();return t.indexOf(r)!==-1})},Ln=function(s,e){if(!s)return!1;var t=s.userAgent;if(t&&Sr(t,e))return!0;try{var i=s?.userAgentData;if(i!=null&&i.brands&&i.brands.some(r=>Sr(r?.brand,e)))return!0}catch{}return!!s.webdriver},St=function(s){return s.US="us",s.EU="eu",s.CUSTOM="custom",s}({}),kr="i.posthog.com";class rl{constructor(e){this.us={},this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return e==="https://app.posthog.com"?"https://us.i.posthog.com":e}get uiHost(){var e,t=(e=this.instance.config.ui_host)==null?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace("."+kr,".posthog.com")),t==="https://app.posthog.com"?"https://us.posthog.com":t}get region(){return this.us[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.us[this.apiHost]=St.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.us[this.apiHost]=St.EU:this.us[this.apiHost]=St.CUSTOM),this.us[this.apiHost]}endpointFor(e,t){if(t===void 0&&(t=""),t&&(t=t[0]==="/"?t:"/"+t),e==="ui")return this.uiHost+t;if(this.region===St.CUSTOM)return this.apiHost+t;var i=kr+t;switch(e){case"assets":return"https://"+this.region+"-assets."+i;case"api":return"https://"+this.region+"."+i}}}var nl={icontains:(s,e)=>!!g&&e.href.toLowerCase().indexOf(s.toLowerCase())>-1,not_icontains:(s,e)=>!!g&&e.href.toLowerCase().indexOf(s.toLowerCase())===-1,regex:(s,e)=>!!g&&ut(e.href,s),not_regex:(s,e)=>!!g&&!ut(e.href,s),exact:(s,e)=>e.href===s,is_not:(s,e)=>e.href!==s};class U{constructor(e){var t=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(i){i===void 0&&(i=!1),t.getWebExperiments(r=>{U.hs("retrieved web experiments from the server"),t.ds=new Map,r.forEach(n=>{if(n.feature_flag_key){var a;t.ds&&(U.hs("setting flag key ",n.feature_flag_key," to web experiment ",n),(a=t.ds)==null||a.set(n.feature_flag_key,n));var o=t._instance.getFeatureFlag(n.feature_flag_key);D(o)&&n.variants[o]&&t.vs(n.name,o,n.variants[o].transforms)}else if(n.variants)for(var l in n.variants){var u=n.variants[l];U.cs(u)&&t.vs(n.name,l,u.transforms)}})},i)},this._instance=e,this._instance.onFeatureFlags(i=>{this.onFeatureFlags(i)})}onFeatureFlags(e){if(this._is_bot())U.hs("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(C(this.ds))return this.ds=new Map,this.loadIfEnabled(),void this.previewWebExperiment();U.hs("applying feature flags",e),e.forEach(t=>{var i;if(this.ds&&(i=this.ds)!=null&&i.has(t)){var r,n=this._instance.getFeatureFlag(t),a=(r=this.ds)==null?void 0:r.get(t);n&&a!=null&&a.variants[n]&&this.vs(a.name,n,a.variants[n].transforms)}})}}previewWebExperiment(){var e=U.getWindowLocation();if(e!=null&&e.search){var t=Kt(e?.search,"__experiment_id"),i=Kt(e?.search,"__experiment_variant");t&&i&&(U.hs("previewing web experiments "+t+" && "+i),this.getWebExperiments(r=>{this.fs(parseInt(t),i,r)},!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,i){if(this._instance.config.disable_web_experiments&&!i)return e([]);var r=this._instance.get_property("$web_experiments");if(r&&!t)return e(r);this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:n=>{if(n.statusCode!==200||!n.json)return e([]);var a=n.json.experiments||[];return e(a)}})}fs(e,t,i){var r=i.filter(n=>n.id===e);r&&r.length>0&&(U.hs("Previewing web experiment ["+r[0].name+"] with variant ["+t+"]"),this.vs(r[0].name,t,r[0].variants[t].transforms))}static cs(e){return!C(e.conditions)&&U.ps(e)&&U.gs(e)}static ps(e){var t;if(C(e.conditions)||C((t=e.conditions)==null?void 0:t.url))return!0;var i,r,n,a=U.getWindowLocation();return!!a&&((i=e.conditions)==null||!i.url||nl[(r=(n=e.conditions)==null?void 0:n.urlMatchType)!==null&&r!==void 0?r:"icontains"](e.conditions.url,a))}static getWindowLocation(){return g?.location}static gs(e){var t;if(C(e.conditions)||C((t=e.conditions)==null?void 0:t.utm))return!0;var i=Cn();if(i.utm_source){var r,n,a,o,l,u,c,d,h=(r=e.conditions)==null||(r=r.utm)==null||!r.utm_campaign||((n=e.conditions)==null||(n=n.utm)==null?void 0:n.utm_campaign)==i.utm_campaign,p=(a=e.conditions)==null||(a=a.utm)==null||!a.utm_source||((o=e.conditions)==null||(o=o.utm)==null?void 0:o.utm_source)==i.utm_source,v=(l=e.conditions)==null||(l=l.utm)==null||!l.utm_medium||((u=e.conditions)==null||(u=u.utm)==null?void 0:u.utm_medium)==i.utm_medium,f=(c=e.conditions)==null||(c=c.utm)==null||!c.utm_term||((d=e.conditions)==null||(d=d.utm)==null?void 0:d.utm_term)==i.utm_term;return h&&v&&f&&p}return!1}static hs(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];w.info("[WebExperiments] "+e,i)}vs(e,t,i){this._is_bot()?U.hs("Refusing to render web experiment since the viewer is a likely bot"):t!=="control"?i.forEach(r=>{if(r.selector){var n;U.hs("applying transform of variant "+t+" for experiment "+e+" ",r);var a=(n=document)==null?void 0:n.querySelectorAll(r.selector);a?.forEach(o=>{var l=o;r.html&&(l.innerHTML=r.html),r.css&&l.setAttribute("style",r.css)})}}):U.hs("Control variants leave the page unmodified.")}_is_bot(){return ie&&this._instance?Ln(ie,this._instance.config.custom_blocked_useragents):void 0}}var al=j("[PostHog ExternalIntegrations]"),ol={intercom:"intercom-integration",crispChat:"crisp-chat-integration"};class ll{constructor(e){this._instance=e}J(e,t){var i;(i=k.__PosthogExtensions__)==null||i.loadExternalDependency==null||i.loadExternalDependency(this._instance,e,r=>{if(r)return al.error("failed to load script",r);t()})}startIfEnabledOrStop(){var e=this,t=function(a){var o,l,u;!r||(o=k.__PosthogExtensions__)!=null&&(o=o.integrations)!=null&&o[a]||e.J(ol[a],()=>{var c;(c=k.__PosthogExtensions__)==null||(c=c.integrations)==null||(c=c[a])==null||c.start(e._instance)}),!r&&(l=k.__PosthogExtensions__)!=null&&(l=l.integrations)!=null&&l[a]&&((u=k.__PosthogExtensions__)==null||(u=u.integrations)==null||(u=u[a])==null||u.stop())};for(var[i,r]of Object.entries((n=this._instance.config.integrations)!==null&&n!==void 0?n:{})){var n;t(i)}}}var It={},rs=()=>{},et="posthog",Nn=!wo&&K?.indexOf("MSIE")===-1&&K?.indexOf("Mozilla")===-1,xr=s=>{var e;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:Xn(y?.location),persistence:"localStorage+cookie",persistence_name:"",loaded:rs,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:s!=="2025-05-24"||"history_change",capture_pageleave:"if_capture_pageview",defaults:s??"unset",debug:Z&&D(Z?.search)&&Z.search.indexOf("__posthog_debug=true")!==-1||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:(g==null||(e=g.location)==null?void 0:e.protocol)==="https:",ip:!1,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:t=>{var i="Bad HTTP status: "+t.statusCode+" "+t.text;w.error(i)},get_device_id:t=>t,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:ss},error_tracking:{},_onCapture:rs}},Ir=s=>{var e={};_(s.process_person)||(e.person_profiles=s.process_person),_(s.xhr_headers)||(e.request_headers=s.xhr_headers),_(s.cookie_name)||(e.persistence_name=s.cookie_name),_(s.disable_cookie)||(e.disable_persistence=s.disable_cookie),_(s.store_google)||(e.save_campaign_params=s.store_google),_(s.verbose)||(e.debug=s.verbose);var t=L({},e,s);return T(s.property_blacklist)&&(_(s.property_denylist)?t.property_denylist=s.property_blacklist:T(s.property_denylist)?t.property_denylist=[...s.property_blacklist,...s.property_denylist]:w.error("Invalid value for property_denylist config: "+s.property_denylist)),t};class ul{constructor(){this.__forceAllowLocalhost=!1}get _s(){return this.__forceAllowLocalhost}set _s(e){w.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class ci{get decideEndpointWasHit(){var e,t;return(e=(t=this.featureFlags)==null?void 0:t.hasLoadedFlags)!==null&&e!==void 0&&e}get flagsEndpointWasHit(){var e,t;return(e=(t=this.featureFlags)==null?void 0:t.hasLoadedFlags)!==null&&e!==void 0&&e}constructor(){this.webPerformance=new ul,this.bs=!1,this.version=xe.LIB_VERSION,this.ys=new Dn,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=xr(),this.SentryIntegration=co,this.sentryIntegration=e=>function(t,i){var r=dn(t,i);return{name:hn,processEvent:n=>r(n)}}(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this.ws=!1,this.Ss=null,this.$s=null,this.xs=null,this.featureFlags=new jo(this),this.toolbar=new po(this),this.scrollManager=new Zo(this),this.pageViewManager=new yo(this),this.surveys=new Go(this),this.experiments=new U(this),this.exceptions=new So(this),this.rateLimiter=new Vo(this),this.requestRouter=new rl(this),this.consent=new ba(this),this.externalIntegrations=new ll(this),this.people={set:(e,t,i)=>{var r=D(e)?{[e]:t}:e;this.setPersonProperties(r),i?.({})},set_once:(e,t,i)=>{var r=D(e)?{[e]:t}:e;this.setPersonProperties(void 0,r),i?.({})}},this.on("eventCaptured",e=>w.info('send "'+e?.event+'"',e))}init(e,t,i){if(i&&i!==et){var r,n=(r=It[i])!==null&&r!==void 0?r:new ci;return n._init(e,t,i),It[i]=n,It[et][i]=n,n}return this._init(e,t,i)}_init(e,t,i){var r,n;if(t===void 0&&(t={}),_(e)||Ti(e))return w.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return w.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this.ks=t,this.Es=[],t.person_profiles&&(this.$s=t.person_profiles),this.set_config(L({},xr(t.defaults),Ir(t),{name:i,token:e})),this.config.on_xhr_error&&w.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=t.disable_compression?void 0:Ie.GZipJS;var a=this.Is();this.persistence=new Ii(this.config,a),this.sessionPersistence=this.config.persistence==="sessionStorage"||this.config.persistence==="memory"?this.persistence:new Ii(m({},this.config,{persistence:"sessionStorage"}),a);var o=m({},this.persistence.props),l=m({},this.sessionPersistence.props);if(this.register({$initialization_time:new Date().toISOString()}),this.Ps=new Yo(S=>this.Rs(S),this.config.request_queue_config),this.Ts=new Ko(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new el(this),this.sessionPropsManager=new Qo(this,this.sessionManager,this.persistence)),new fo(this).startIfEnabledOrStop(),this.siteApps=new il(this),(r=this.siteApps)==null||r.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new lo(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new da(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new mo(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new vo(this),this.exceptionObserver=new Sa(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new Jr(this,Ea),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new La(this),this.historyAutocapture.startIfEnabled(),xe.DEBUG=xe.DEBUG||this.config.debug,xe.DEBUG&&w.info("Starting in debug mode",{this:this,config:t,thisC:m({},this.config),p:o,s:l}),((n=t.bootstrap)==null?void 0:n.distinctID)!==void 0){var u,c,d=this.config.get_device_id(Ae()),h=(u=t.bootstrap)!=null&&u.isIdentifiedID?d:t.bootstrap.distinctID;this.persistence.set_property(Se,(c=t.bootstrap)!=null&&c.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:t.bootstrap.distinctID,$device_id:h})}if(this.Ms()){var p,v,f=Object.keys(((p=t.bootstrap)==null?void 0:p.featureFlags)||{}).filter(S=>{var x;return!((x=t.bootstrap)==null||(x=x.featureFlags)==null||!x[S])}).reduce((S,x)=>{var $;return S[x]=(($=t.bootstrap)==null||($=$.featureFlags)==null?void 0:$[x])||!1,S},{}),b=Object.keys(((v=t.bootstrap)==null?void 0:v.featureFlagPayloads)||{}).filter(S=>f[S]).reduce((S,x)=>{var $,P;return($=t.bootstrap)!=null&&($=$.featureFlagPayloads)!=null&&$[x]&&(S[x]=(P=t.bootstrap)==null||(P=P.featureFlagPayloads)==null?void 0:P[x]),S},{});this.featureFlags.receivedFeatureFlags({featureFlags:f,featureFlagPayloads:b})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Tt,$device_id:null},"");else if(!this.get_distinct_id()){var E=this.config.get_device_id(Ae());this.register_once({distinct_id:E,$device_id:E},""),this.persistence.set_property(Se,"anonymous")}return A(g,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),t.segment?uo(this,()=>this.Cs()):this.Cs(),ae(this.config._onCapture)&&this.config._onCapture!==rs&&(w.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",S=>this.config._onCapture(S.event,S))),this.config.ip&&w.warn('The `ip` config option has NO EFFECT AT ALL and has been deprecated. Use a custom transformation or "Discard IP data" project setting instead. See https://posthog.com/tutorials/web-redact-properties#hiding-customer-ip-address for more information.'),this}Re(e){var t,i,r,n,a,o,l,u;if(!y||!y.body)return w.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(()=>{this.Re(e)},500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=I(e.supportedCompression,Ie.GZipJS)?Ie.GZipJS:I(e.supportedCompression,Ie.Base64)?Ie.Base64:void 0),(t=e.analytics)!=null&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this.$s?this.$s:"identified_only"}),(i=this.siteApps)==null||i.onRemoteConfig(e),(r=this.sessionRecording)==null||r.onRemoteConfig(e),(n=this.autocapture)==null||n.onRemoteConfig(e),(a=this.heatmaps)==null||a.onRemoteConfig(e),this.surveys.onRemoteConfig(e),(o=this.webVitalsAutocapture)==null||o.onRemoteConfig(e),(l=this.exceptionObserver)==null||l.onRemoteConfig(e),this.exceptions.onRemoteConfig(e),(u=this.deadClicksAutocapture)==null||u.onRemoteConfig(e)}Cs(){try{this.config.loaded(this)}catch(e){w.critical("`loaded` function failed",e)}this.Fs(),this.config.capture_pageview&&setTimeout(()=>{this.consent.isOptedIn()&&this.Os()},1),new Wo(this).load(),this.featureFlags.flags()}Fs(){var e;this.has_opted_out_capturing()||this.config.request_batching&&((e=this.Ps)==null||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||Me(this.__request_queue,e=>this.Rs(e)),this.__request_queue=[],this.Fs()}_handle_unload(){var e,t;this.config.request_batching?(this.As()&&this.capture("$pageleave"),(e=this.Ps)==null||e.unload(),(t=this.Ts)==null||t.unload()):this.As()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}Pe(e){this.__loaded&&(Nn?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=ii(e.url,{ip:this.config.ip?1:0}),e.headers=m({},this.config.request_headers),e.compression=e.compression==="best-available"?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(t=>{var i,r,n,a=m({},t);a.timeout=a.timeout||6e4,a.url=ii(a.url,{_:new Date().getTime().toString(),ver:xe.LIB_VERSION,compression:a.compression});var o=(i=a.transport)!==null&&i!==void 0?i:"fetch",l=(r=(n=Or(xt,u=>u.transport===o))==null?void 0:n.method)!==null&&r!==void 0?r:xt[0].method;if(!l)throw new Error("No available transport method");l(a)})(m({},e,{callback:t=>{var i,r;this.rateLimiter.checkForLimiting(t),t.statusCode>=400&&((i=(r=this.config).on_request_error)==null||i.call(r,t)),e.callback==null||e.callback(t)}}))))}Rs(e){this.Ts?this.Ts.retriableRequest(e):this.Pe(e)}_execute_array(e){var t,i=[],r=[],n=[];Me(e,o=>{o&&(t=o[0],T(t)?n.push(o):ae(o)?o.call(this):T(o)&&t==="alias"?i.push(o):T(o)&&t.indexOf("capture")!==-1&&ae(this[t])?n.push(o):r.push(o))});var a=function(o,l){Me(o,function(u){if(T(u[0])){var c=l;R(u,function(d){c=c[d[0]].apply(c,d.slice(1))})}else this[u[0]].apply(this,u.slice(1))},l)};a(i,this),a(r,this),a(n,this)}Ms(){var e,t;return((e=this.config.bootstrap)==null?void 0:e.featureFlags)&&Object.keys((t=this.config.bootstrap)==null?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,i){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.Ps){if(!this.consent.isOptedOut())if(!_(e)&&D(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var n=i!=null&&i.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(n==null||!n.isRateLimited){t!=null&&t.$current_url&&!D(t?.$current_url)&&(w.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),t==null||delete t.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var a=new Date,o=i?.timestamp||a,l=Ae(),u={uuid:l,event:e,properties:this.calculateEventProperties(e,t||{},o,l)};n&&(u.properties.$lib_rate_limit_remaining_tokens=n.remainingTokens),i?.$set&&(u.$set=i?.$set);var c,d,h=this.Ds(i?.$set_once);if(h&&(u.$set_once=h),(u=Kn(u,i!=null&&i._noTruncate?null:this.config.properties_string_max_length)).timestamp=o,_(i?.timestamp)||(u.properties.$event_time_override_provided=!0,u.properties.$event_time_override_system_time=a),e===Fi.DISMISSED||e===Fi.SENT){var p=t?.[br.SURVEY_ID],v=t?.[br.SURVEY_ITERATION];localStorage.setItem((d=""+is+(c={id:p,current_iteration:v}).id,c.current_iteration&&c.current_iteration>0&&(d=""+is+c.id+"_"+c.current_iteration),d),"true"),u.$set=m({},u.$set,{[Ho({id:p,current_iteration:v},e===Fi.SENT?"responded":"dismissed")]:!0})}var f=m({},u.properties.$set,u.$set);if(tt(f)||this.setPersonPropertiesForFlags(f),!C(this.config.before_send)){var b=this.Ls(u);if(!b)return;u=b}this.ys.emit("eventCaptured",u);var E={method:"POST",url:(r=i?._url)!==null&&r!==void 0?r:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:u,compression:"best-available",batchKey:i?._batchKey};return!this.config.request_batching||i&&(i==null||!i._batchKey)||i!=null&&i.send_instantly?this.Rs(E):this.Ps.enqueue(E),u}w.critical("This capture call is ignored due to client rate limiting.")}}else w.error("No event name provided to posthog.capture")}else w.uninitializedWarning("posthog.capture")}Ve(e){return this.on("eventCaptured",t=>e(t.event,t))}calculateEventProperties(e,t,i,r,n){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return t;var a=n?void 0:this.persistence.remove_event_timer(e),o=m({},t);if(o.token=this.config.token,o.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(o.$cookieless_mode=!0),e==="$snapshot"){var l=m({},this.persistence.properties(),this.sessionPersistence.properties());return o.distinct_id=l.distinct_id,(!D(o.distinct_id)&&!G(o.distinct_id)||Ti(o.distinct_id))&&w.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),o}var u,c=Lo(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:d,windowId:h}=this.sessionManager.checkAndGetSessionAndWindowId(n,i.getTime());o.$session_id=d,o.$window_id=h}this.sessionPropsManager&&L(o,this.sessionPropsManager.getSessionProps());try{var p;this.sessionRecording&&L(o,this.sessionRecording.sdkDebugProperties),o.$sdk_debug_retry_queue_size=(p=this.Ts)==null?void 0:p.length}catch(E){o.$sdk_debug_error_capturing_properties=String(E)}if(this.requestRouter.region===St.CUSTOM&&(o.$lib_custom_api_host=this.config.api_host),u=e!=="$pageview"||n?e!=="$pageleave"||n?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(i):this.pageViewManager.doPageView(i,r),o=L(o,u),e==="$pageview"&&y&&(o.title=y.title),!_(a)){var v=i.getTime()-a;o.$duration=parseFloat((v/1e3).toFixed(3))}K&&this.config.opt_out_useragent_filter&&(o.$browser_type=this._is_bot()?"bot":"browser"),(o=L({},c,this.persistence.properties(),this.sessionPersistence.properties(),o)).$is_identified=this._isIdentified(),T(this.config.property_denylist)?R(this.config.property_denylist,function(E){delete o[E]}):w.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var f=this.config.sanitize_properties;f&&(w.error("sanitize_properties is deprecated. Use before_send instead"),o=f(o,e));var b=this.js();return o.$process_person_profile=b,b&&!n&&this.Ns("_calculate_event_properties"),o}Ds(e){var t;if(!this.persistence||!this.js()||this.bs)return e;var i=this.persistence.get_initial_props(),r=(t=this.sessionPropsManager)==null?void 0:t.getSetOnceProps(),n=L({},i,r||{},e||{}),a=this.config.sanitize_properties;return a&&(w.error("sanitize_properties is deprecated. Use before_send instead"),n=a(n,"$set_once")),this.bs=!0,tt(n)?void 0:n}register(e,t){var i;(i=this.persistence)==null||i.register(e,t)}register_once(e,t,i){var r;(r=this.persistence)==null||r.register_once(e,t,i)}register_for_session(e){var t;(t=this.sessionPersistence)==null||t.register(e)}unregister(e){var t;(t=this.persistence)==null||t.unregister(e)}unregister_for_session(e){var t;(t=this.sessionPersistence)==null||t.unregister(e)}zs(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch{return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t,i){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t,i)}getEarlyAccessFeatures(e,t,i){return t===void 0&&(t=!1),this.featureFlags.getEarlyAccessFeatures(e,t,i)}on(e,t){return this.ys.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSurveysLoaded(e){return this.surveys.onSurveysLoaded(e)}onSessionId(e){var t,i;return(t=(i=this.sessionManager)==null?void 0:i.onSessionId(e))!==null&&t!==void 0?t:()=>{}}getSurveys(e,t){t===void 0&&(t=!1),this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e,t){t===void 0&&(t=!1),this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){return this.surveys.canRenderSurvey(e)}canRenderSurveyAsync(e,t){return t===void 0&&(t=!1),this.surveys.canRenderSurveyAsync(e,t)}identify(e,t,i){if(!this.__loaded||!this.persistence)return w.uninitializedWarning("posthog.identify");if(G(e)&&(e=e.toString(),w.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e)if(["distinct_id","distinctid"].includes(e.toLowerCase()))w.critical('The string "'+e+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(e!==Tt){if(this.Ns("posthog.identify")){var r=this.get_distinct_id();if(this.register({$user_id:e}),!this.get_property("$device_id")){var n=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:n},"")}e!==r&&e!==this.get_property(ft)&&(this.unregister(ft),this.register({distinct_id:e}));var a=(this.persistence.get_property(Se)||"anonymous")==="anonymous";e!==r&&a?(this.persistence.set_property(Se,"identified"),this.setPersonPropertiesForFlags(m({},i||{},t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:r},{$set:t||{},$set_once:i||{}}),this.xs=hr(e,t,i),this.featureFlags.setAnonymousDistinctId(r)):(t||i)&&this.setPersonProperties(t,i),e!==r&&(this.reloadFeatureFlags(),this.unregister(Vt))}}else w.critical('The string "'+Tt+'" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else w.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){if((e||t)&&this.Ns("posthog.setPersonProperties")){var i=hr(this.get_distinct_id(),e,t);this.xs!==i?(this.setPersonPropertiesForFlags(m({},t||{},e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}),this.xs=i):w.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}}group(e,t,i){if(e&&t){if(this.Ns("posthog.group")){var r=this.getGroups();r[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:m({},r,{[e]:t})}),i&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:i}),this.setGroupPropertiesForFlags({[e]:i})),r[e]===t||i||this.reloadFeatureFlags()}}else w.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e,t){t===void 0&&(t=!0),this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e,t){t===void 0&&(t=!0),this.Ns("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){var t,i,r,n;if(w.info("reset"),!this.__loaded)return w.uninitializedWarning("posthog.reset");var a=this.get_property("$device_id");if(this.consent.reset(),(t=this.persistence)==null||t.clear(),(i=this.sessionPersistence)==null||i.clear(),this.surveys.reset(),this.featureFlags.reset(),(r=this.persistence)==null||r.set_property(Se,"anonymous"),(n=this.sessionManager)==null||n.resetSessionId(),this.xs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Tt,$device_id:null},"");else{var o=this.config.get_device_id(Ae());this.register_once({distinct_id:o,$device_id:e?o:a},"")}this.register({$last_posthog_reset:new Date().toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return(e=(t=this.sessionManager)==null?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)!==null&&e!==void 0?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:i}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+t);if(e!=null&&e.withTimestamp&&i){var n,a=(n=e.timestampLookBack)!==null&&n!==void 0?n:10;if(!i)return r;r+="?t="+Math.max(Math.floor((new Date().getTime()-i)/1e3)-a,0)}return r}alias(e,t){return e===this.get_property(Ar)?(w.critical("Attempting to create alias for existing People user - aborting."),-2):this.Ns("posthog.alias")?(_(t)&&(t=this.get_distinct_id()),e!==t?(this.zs(ft,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(w.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t=m({},this.config);if(M(e)){var i,r,n,a,o;L(this.config,Ir(e));var l=this.Is();(i=this.persistence)==null||i.update_config(this.config,t,l),this.sessionPersistence=this.config.persistence==="sessionStorage"||this.config.persistence==="memory"?this.persistence:new Ii(m({},this.config,{persistence:"sessionStorage"}),l),q.O()&&q.D("ph_debug")==="true"&&(this.config.debug=!0),this.config.debug&&(xe.DEBUG=!0,w.info("set_config",{config:e,oldConfig:t,newConfig:m({},this.config)})),(r=this.sessionRecording)==null||r.startIfEnabledOrStop(),(n=this.autocapture)==null||n.startIfEnabled(),(a=this.heatmaps)==null||a.startIfEnabled(),this.surveys.loadIfEnabled(),this.Us(),(o=this.externalIntegrations)==null||o.startIfEnabledOrStop()}}startSessionRecording(e){var t=e===!0,i={sampling:t||!(e==null||!e.sampling),linked_flag:t||!(e==null||!e.linked_flag),url_trigger:t||!(e==null||!e.url_trigger),event_trigger:t||!(e==null||!e.event_trigger)};if(Object.values(i).some(Boolean)){var r,n,a,o,l;(r=this.sessionManager)==null||r.checkAndGetSessionAndWindowId(),i.sampling&&((n=this.sessionRecording)==null||n.overrideSampling()),i.linked_flag&&((a=this.sessionRecording)==null||a.overrideLinkedFlag()),i.url_trigger&&((o=this.sessionRecording)==null||o.overrideTrigger("url")),i.event_trigger&&((l=this.sessionRecording)==null||l.overrideTrigger("event"))}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!((e=this.sessionRecording)==null||!e.started)}captureException(e,t){var i=new Error("PostHog syntheticException");return this.exceptions.sendExceptionEvent(m({},Da((r=>r instanceof Error)(e)?{error:e,event:e.message}:{event:e},{syntheticException:i}),t))}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return(t=this.persistence)==null?void 0:t.props[e]}getSessionProperty(e){var t;return(t=this.sessionPersistence)==null?void 0:t.props[e]}toString(){var e,t=(e=this.config.name)!==null&&e!==void 0?e:et;return t!==et&&(t=et+"."+t),t}_isIdentified(){var e,t;return((e=this.persistence)==null?void 0:e.get_property(Se))==="identified"||((t=this.sessionPersistence)==null?void 0:t.get_property(Se))==="identified"}js(){var e,t;return!(this.config.person_profiles==="never"||this.config.person_profiles==="identified_only"&&!this._isIdentified()&&tt(this.getGroups())&&((e=this.persistence)==null||(e=e.props)==null||!e[ft])&&((t=this.persistence)==null||(t=t.props)==null||!t[Yt]))}As(){return this.config.capture_pageleave===!0||this.config.capture_pageleave==="if_capture_pageview"&&(this.config.capture_pageview===!0||this.config.capture_pageview==="history_change")}createPersonProfile(){this.js()||this.Ns("posthog.createPersonProfile")&&this.setPersonProperties({},{})}Ns(e){return this.config.person_profiles==="never"?(w.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this.zs(Yt,!0),!0)}Is(){var e=this.consent.isOptedOut(),t=this.config.opt_out_persistence_by_default;return this.config.disable_persistence||e&&!!t}Us(){var e,t,i,r,n=this.Is();return((e=this.persistence)==null?void 0:e.Ae)!==n&&((i=this.persistence)==null||i.set_disabled(n)),((t=this.sessionPersistence)==null?void 0:t.Ae)!==n&&((r=this.sessionPersistence)==null||r.set_disabled(n)),n}opt_in_capturing(e){var t;this.consent.optInOut(!0),this.Us(),(_(e?.captureEventName)||e!=null&&e.captureEventName)&&this.capture((t=e?.captureEventName)!==null&&t!==void 0?t:"$opt_in",e?.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this.Os()}opt_out_capturing(){this.consent.optInOut(!1),this.Us()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.Us()}_is_bot(){return ie?Ln(ie,this.config.custom_blocked_useragents):void 0}Os(){y&&(y.visibilityState==="visible"?this.ws||(this.ws=!0,this.capture("$pageview",{title:y.title},{send_instantly:!0}),this.Ss&&(y.removeEventListener("visibilitychange",this.Ss),this.Ss=null)):this.Ss||(this.Ss=this.Os.bind(this),A(y,"visibilitychange",this.Ss)))}debug(e){e===!1?(g?.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(g?.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}I(){var e,t,i,r,n,a,o,l=this.ks||{};return"advanced_disable_flags"in l?!!l.advanced_disable_flags:this.config.advanced_disable_flags!==!1?!!this.config.advanced_disable_flags:this.config.advanced_disable_decide===!0?(w.warn("Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version."),!0):(i="advanced_disable_decide",r=!1,n=w,a=(t="advanced_disable_flags")in(e=l)&&!_(e[t]),o=i in e&&!_(e[i]),a?e[t]:o?(n&&n.warn("Config field '"+i+"' is deprecated. Please use '"+t+"' instead. The old field will be removed in a future major version."),e[i]):r)}Ls(e){if(C(this.config.before_send))return e;var t=T(this.config.before_send)?this.config.before_send:[this.config.before_send],i=e;for(var r of t){if(i=r(i),C(i)){var n="Event '"+e.event+"' was rejected in beforeSend function";return Wn(e.event)?w.warn(n+". This can cause unexpected behavior."):w.info(n),null}i.properties&&!tt(i.properties)||w.warn("Event '"+e.event+"' has no properties after beforeSend function, this is likely an error.")}return i}getPageViewId(){var e;return(e=this.pageViewManager.ce)==null?void 0:e.pageViewId}captureTraceFeedback(e,t){this.capture("$ai_feedback",{$ai_trace_id:String(e),$ai_feedback_text:t})}captureTraceMetric(e,t,i){this.capture("$ai_metric",{$ai_trace_id:String(e),$ai_metric_name:t,$ai_metric_value:String(i)})}}(function(s,e){for(var t=0;t<e.length;t++)s.prototype[e[t]]=Jn(s.prototype[e[t]])})(ci,["identify"]);var $r,cl=($r=It[et]=new ci,function(){function s(){s.done||(s.done=!0,Nn=!1,R(It,function(e){e._dom_loaded()}))}y!=null&&y.addEventListener?y.readyState==="complete"?s():A(y,"DOMContentLoaded",s,{capture:!1}):g&&w.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}(),$r);export{cl as p};
