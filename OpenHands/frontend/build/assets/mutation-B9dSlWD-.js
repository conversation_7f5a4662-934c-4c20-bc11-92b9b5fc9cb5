import{R as a,c as r,n as u}from"./open-hands-axios-CtirLpss.js";var d=class extends a{#e;#t;#i;constructor(t){super(),this.mutationId=t.mutationId,this.#t=t.mutationCache,this.#e=[],this.state=t.state||h(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#e.includes(t)||(this.#e.push(t),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#e=this.#e.filter(i=>i!==t),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#e.length||(this.state.status==="pending"?this.scheduleGc():this.#t.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(t){const i=()=>{this.#s({type:"continue"})};this.#i=r({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(e,n)=>{this.#s({type:"failed",failureCount:e,error:n})},onPause:()=>{this.#s({type:"pause"})},onContinue:i,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});const s=this.state.status==="pending",o=!this.#i.canStart();try{if(s)i();else{this.#s({type:"pending",variables:t,isPaused:o}),await this.#t.config.onMutate?.(t,this);const n=await this.options.onMutate?.(t);n!==this.state.context&&this.#s({type:"pending",context:n,variables:t,isPaused:o})}const e=await this.#i.start();return await this.#t.config.onSuccess?.(e,t,this.state.context,this),await this.options.onSuccess?.(e,t,this.state.context),await this.#t.config.onSettled?.(e,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(e,null,t,this.state.context),this.#s({type:"success",data:e}),e}catch(e){try{throw await this.#t.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#t.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#s({type:"error",error:e})}}finally{this.#t.runNext(this)}}#s(t){const i=s=>{switch(t.type){case"failed":return{...s,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...s,isPaused:!0};case"continue":return{...s,isPaused:!1};case"pending":return{...s,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...s,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...s,data:void 0,error:t.error,failureCount:s.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=i(this.state),u.batch(()=>{this.#e.forEach(s=>{s.onMutationUpdate(t)}),this.#t.notify({mutation:this,type:"updated",action:t})})}};function h(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}export{d as M,h as g};
